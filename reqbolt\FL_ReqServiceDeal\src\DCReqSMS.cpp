#include "DCReqSMS.h" 
#include "ErrorCode.h"
#include <sys/time.h>
#include "DCLogMacro.h"
#include "BizDataDef.h"
#include "TSMPara.h"

DCReqSMS::DCReqSMS()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE , "", "", "");
}

DCReqSMS::~DCReqSMS()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE , "", "", "");
}

int DCReqSMS::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_NULL_MSG, "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;
	
	//PERF LOG
	// bizMsg->m_perf.GetTimeT2_B();
	
	int ret = SwitchReqType(bizMsg);
	
	bizMsg->m_resultcode = ret;	

	//PERF LOG
	// bizMsg->m_perf.GetTimeT2_E();

	
	//流程结束
	//BizPerfLog(bizMsg, __FUNCTION__, __FILE__, __LINE__);
	return ret;
}

int DCReqSMS::SwitchReqType(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

