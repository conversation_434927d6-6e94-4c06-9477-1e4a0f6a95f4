#ifndef __FUNC_SQLINDEX_H__
#define __FUNC_SQLINDEX_H__

//------------------------------------COMMON  BEGIN[000~100]----------------------------------------

#define COM_LOAD_SPR_ACCESS_NUMBER				"q_spr_access_number"		// call default, rfun_AccessNbr, lfun_AccessNbr
#define COM_LOAD_SPZ_COUNTRY 					"q_spz_country"
#define COM_LOAD_SPZ_CITY 						"q_spz_city"
#define COM_LOAD_SM_SERVICE_QUOTA_CONF			"q_sm_service_quota_conf"		// call default, rfun_ServiceQuotaConf, lfun_ServiceQuotaConf
#define COM_LOAD_SM_AOC_GLOBAL_CFG				"q_sm_aoc_global_cfg"		// call default, rfun_AocPara, lfun_<PERSON><PERSON>ara
#define COM_LOAD_HLP_ACCTITEM_MAP 				"q_hlp_acctitem_map" 
#define COM_LOAD_SM_SYSTEM_PARAMETER 			"q_sm_system_parameter"
#define COM_LOAD_SPR_MSISDN_AREA_MAP 			"q_spr_msisdn_area_map"
#define COM_LOAD_SPR_URGENT_NUMBER 				"q_spr_urgent_number"
#define COM_LOAD_SM_USER_STATE 					"q_sm_user_state"
#define COM_LOAD_SPR_MSC 						"q_spr_msc"
#define COM_LOAD_SPR_EDGE_ROAM_01 				"q_spr_edge_roam_01"
#define COM_LOAD_SPR_EDGE_ROAM_02 				"q_spr_edge_roam_02"
#define COM_LOAD_SPR_EDGE_ROAM_DATA  			"q_spr_edge_roam_data"
#define COM_LOAD_SPR_NP 						"q_spr_np" 
#define COM_LOAD_SPR_PDSN 						"q_spr_pdsn"
#define COM_LOAD_SPR_SGSN 						"q_spr_sgsn"
#define COM_LOAD_SPR_DSL 						"q_spr_dsl"
#define COM_LOAD_SPR_LAC 						"q_spr_lac"
#define COM_LOAD_SPR_CELL 						"q_spr_cell"
#define COM_LOAD_SM_RESULTCODE_MAP 				"q_sm_resultcode_map"

#define COM_USER_SELECT_TB_PRD_PRD_INST_ACCOUNT "q_tb_prd_prd_inst_account"
#define COM_USER_SELECT_TB_PRD_PRD_INST_ACCNBR 	"q_tb_prd_prd_inst_accnbr"
#define COM_USER_SELECT_TB_PRD_PRD_INST_ACCAREA "q_tb_prd_prd_inst_accarea"
#define COM_USER_SELECT_TB_PRD_PRD_INST		    "q_tb_prd_prd_inst"
#define COM_USER_SELECT_PROD_INST_STATE			"q_prod_inst_state"

#define COM_AOC_INSERT_HLP_SMS_SEND 			"i_hlp_sms_send"  //除订购业务之外用的
#define COM_AOC_INSERT_HLP_SMS_AOC_SEND 		"i_hlp_sms_aoc_send"   //订购aoc的时候写入
#define COM_AOC_SELECT_HLP_MVNO_PARAMETER_CFG 	"q_hlp_mvno_parameter_cfg"
#define COM_FORBID_SELECT_SM_FORBIDDEN  		"q_sm_forbidden" //禁播
#define COM_TRACE_INSERT_CM_TRACE_MESSAGE 		"i_cm_trace_message"  //信令跟踪
#define COM_TRACE_INSERT_OCS_TRACE_MESSAGE 		"i_ocs_trace_message" //信令跟踪
#define COM_TRACE_SELECT_OCS_TRACE_NUMBER_01 	"q_ocs_trace_number_01"  //信令跟踪
#define COM_TRACE_SELECT_OCS_TRACE_NUMBER_02 	"q_ocs_trace_number_02"  //信令跟踪
#define COM_USER_SELECT_TB_NBR_LATN_REL 		"q_tb_nbr_latn_rel" 
#define COM_USER_SELECT_TB_IF_REALNAME 			"q_tb_if_realname" //实名制
#define COM_USER_SELECT_TB_PRD_PRD_INST_BY_SERVICE_NBR "q_tb_prd_prd_inst_by_service_nbr" 
#define COM_USER_SELECT_IMSI "q_prod_inst_attr" 
#define COM_SELECT_AREA_ID "q_country_area_id" 
#define COM_SELECT_ACC_NBR "q_prod_inst_acc_nbr"

#define COM_CDR_VOICE 		"q_cdr_voice" 
#define COM_CDR_DATA 		"q_cdr_data" 
#define COM_CDR_PGW 		"q_cdr_pgw" 
#define COM_CDR_5G 			"q_cdr_5g"
#define COM_CDR_5G_XDR		"q_cdr_5g_xdr"

#define COM_CDR_SMS 		"q_cdr_sms" 
#define COM_CDR_ISMP 		"q_cdr_ismp" 
#define COM_CDR_DSL 		"q_cdr_dsl" 
#define COM_CDR_ISMPHB 		"q_cdr_ismpHB"

#define COM_OLD_CDR_VOICE       "q_old_cdr_voice" 
#define COM_OLD_CDR_DATA        "q_old_cdr_data" 
#define COM_OLD_CDR_PGW         "q_old_cdr_pgw" 
#define COM_OLD_CDR_SMS         "q_old_cdr_sms" 
#define COM_OLD_CDR_ISMP        "q_old_cdr_ismp" 
#define COM_OLD_CDR_DSL         "q_old_cdr_dsl" 
#define COM_OLD_CDR_ISMPHB      "q_old_cdr_ismpHB"


#define Voice_QueryPayFlag      "q_voice_payflag" 
#define SMS_QueryPayFlag        "q_sms_payflag" 
#define DATA_QueryPayFlag       "q_data_payflag" 
#define PGW_QueryPayFlag        "q_pgw_payflag" 
#define ISMP_QueryPayFlag       "q_ismp_payflag"      
#define DSL_QueryPayFlag        "q_dsl_payflag" 
#define DATA_5G_QueryPayFlag        "q_5g_payflag"

#define COM_LOAD_SPR_TAC 	"q_spr_tac"


//------------------------------------COMMON  END----------------------------------------



//------------------------------------VOICE  BEGIN[200~299]----------------------------------------
#define Voice_InsertSession		      	"i_voice_insertsession"		// call insertSession_Voice
#define Voice_InsertSession_OffLine		"i_voice_insertsession_offline"
#define Voice_InsertSession_Free	  	"i_voice_insertsession_free"		// call insertSessionFree_Voice
#define Reject_Calls_Insert           	"i_reject_calls_insert"

#define Voice_GetSessionInfo  	       	"q_voice_getsessioninfo"		// call getSessionStatus
#define Voice_GetSessionInfo_Aoc 	   	"q_voice_getsessioninfo_aoc"	
#define Voice_GetSessionInfo_Trace     	"q_voice_getsessioninfo_trace"
#define GET_OFR_NAME 	               	"q_get_ofr_name"
#define Voice_GetSessionInfo_Cdr       	"q_voice_getsessioninfo_cdr"
#define VOICE_GetSessionSpiltFlag		"q_voice_spilt_flag"
#define VOICE_MergeSessionInfo			"q_voice_spilt_session"


#define Voice_UpdateSession		        "u_voice_updatesession"		// call updateSession_Voice
#define Voice_UpdateSessionOffLine		"u_voice_updatesessionoffline"
#define Voice_UpdateSession_event	    "u_voice_updatesession_event"
#define Voice_UpdateSession_update	    "u_voice_updatesession_update"
#define Voice_UpdateSession_Free 	    "u_voice_updatesession_free"		// call updateSession2_Voice
#define Voice_UpdateSession_LongCdr 	"u_voice_updatesession_longcdr"	
#define Voice_UpdateSession_Ept 	    "u_voice_updatesession_ept"
#define Voice_UpdateSession_EptResult   "u_voice_updatesession_eptresult"

#define Voice_DeleteSession		        "d_voice_deletesession"		// call deleteSession
#define VOICE_DelSession_ans			"d_voice_spilt_session"

//------------------------------------VOICE END----------------------------------------




//------------------------------------SMS  BEGIN[300~399]----------------------------------------
#define SMS_InsertSession		        "i_sms_insertsession"		// call insertSession_SMS
#define SMS_InsertSession_Free		    "i_sms_insertsession_free"	
#define SMS_Insert_OcsSmDebitInfo		"i_sms_insert_ocssmdebitinfo"	

#define SMS_GetSessionInfo_Aoc		    "q_sms_getsessioninfo_aoc"	
#define SMS_GetSessionInfo_Trace        "q_sms_getsessioninfo_trace"
#define SMS_GetOcsSmDebitInfo           "q_sms_getocssmdebitinfo"
#define SMS_GetSessionInfo_Cdr          "q_sms_getsessioninfo_cdr"

#define SMS_UpdateSession		        "u_sms_updatesession"		// call updateSession_SMS

#define SMS_RBANS						"q_sms_rbans_001"

#define SMS_DeleteSession		        "d_sms_deletesession"		// call deleteSession

//------------------------------------SMS  END-------------------------------------------------

//------------------------------------5G  BEGIN[]----------------------------------------
//Common
#define _5G_S_RECV_STATE								"q_5g_recv_rgstate"
#define _5G_CCR_SelectDynamicStep						"q_5g_dynamic_step_select"
#define _5G_DeleteChildSession 							"d_5g_childsession"
#define _5G_GetParSmFilterRgCondition 				    "q_par_sm_filter_rg_condition"


//Req
#define _5G_CCR_InsertChildSession						"i_5g_childsession"
#define _5G_CCR_SelectChildSession						"q_5g_ccr_childsession"
#define _5G_CCR_UpdateChildSession_RG					"u_5g_ccr_chlidsession_rg"


#define _5G_FREE_Init_InsertSession 					"i_5g_free_init_session"
#define _5G_FREE_Update_ChildSession					"u_5g_free_childsession"
#define _5G_FREE_InsertChildSession						"i_5g_free_childsession"

//Ans
#define _5G_ANS_Update_ChildSession						"u_5g_update_childsession"
#define _5G_ANS_Update_ChildSession_NoGsv               "u_5g_update_childsession_nogsv"
#define _5G_SelectSessionStoreRbansRg				    "q_5g_session_rbansrg"
#define _5G_ANS_Term_ChildSession                       "u_5g_term_childsession"



//Ept
#define _5G_EPT_Select_ChildSessionAll					"q_5g_ept_childsessionall"
#define _5G_EPT_UPDATE_CODE_CHILD_ALL					"u_5g_ept_child_all"
#define _5G_EPT_UPDATE_CODE_CHILD						"u_5g_ept_child"

//------------------------------------5G  end----------------------------------------


//------------------------------------PGW  BEGIN[400~499]----------------------------------------
//PGW
#define PGW_CCR_Init_InsertSession					"i_pgw_insertsession"                          //400~409
#define PGW_CCR_FirstUpdate_SelectSession_main		"q_pgw_ccr_firstupdate_main"
#define PGW_CCR_FirstUpdate_InsertSession_Child		"i_pgw_ccr_firstupdate_child"
#define PGW_CCR_SelectSession				        "q_pgw_ccr_select"
#define PGW_InsertSessionStoreOFFL					"i_pgw_session_offline"

#define PGW_Select_SessionStoreRecord_RG_OffLine	"q_pgw_session_offline"
#define PGW_UpdateSessionStoreRbansOffLine			"u_pgw_session_offline"
#define PGW_SelectSession_RG				        "q_pgw_session_rg"
#define PGW_UpdateSession_RG                        "u_pgw_session_ans_rg"
#define PGW_SelectSessionStoreRbansRgUpdate			"q_pgw_session_rbansrgupdate"
#define PGW_SelectSessionStoreRbansRgTerm 		    "q_pgw_session_rbansrgterm"              

#define PGW_CCA_Update_UpdateSession				"u_pgw_cca_updatesession"
#define PGW_CCA_Term_SelectSession  				"q_pgw_cca_term"
#define PGW_CCA_Update_SelectSessionLong_01 		"q_pgw_cca_update_long_01"
#define PGW_CCA_Update_SelectSessionLong_02 		"q_pgw_cca_update_long_02"

#define PGW_CDR_SelectSessionALL 					"q_pgw_cdr_selectsessionall"  //410~419
#define PGW_CDR_SelectSession 						"q_pgw_cdr_selectsession"
#define PGW_CDR_UpdateCdrSession 					"u_pgw_cdr_cdrsession"
#define PGW_CDR_InsertCdrSession 					"i_pgw_cdr_cdrsession"
#define PGW_CDR_UpdateSessionInfo 					"u_pgw_cdr_sessioninfo"
#define PGW_CDR_LONG_UpdateSessionTime 				"u_pgw_cdr_long_sessiontime"
#define PGW_CDR_LONG_DeleteCDR 						"d_pgw_cdr_long"
#define PGW_CDR_DeleteSession 						"d_pgw_cdrsession"

#define PGW_EXP_SelectSessionRgNum 					"q_pgw_exp_sessionrgnum"       //420~429        
#define PGW_EXP_SelectSessionReqType 				"q_pgw_exp_sessionreqtype"
#define PGW_EXP_UpdateSessionCCAFlag 				"u_pgw_exp_sessionccaflag"
#define PGW_EXP_SelectSessionRER 					"q_pgw_exp_sessionrer"
#define PGW_EXP_SelectSessionRERBalanceNotEnough 	"q_pgw_exp_rerbalancenotenough"
#define PGW_EXP_UpdateSessionFlag 					"u_pgw_exp_sessionflag"
#define PGW_EXP_UpdateSessionResultCode 			"u_pgw_exp_resultcode"

#define PGW_FREE_Init_InsertSession 				"i_pgw_free_init_session"  //430~434
#define PGW_FREE_FirstUpdate_InsertSessionChild 	"i_pgw_free_firstupdate_child"   
#define PGW_FREE_Update_UpdateSession               "u_pgw_free_session"
#define PGW_AOC_SelectSessionAllChild 				"q_pgw_aoc_sessionallchild"  //435~439
#define PGW_AOC_SelectSession 						"q_pgw_aoc_session"

#define PGW_TRAC_EXP 								"q_pgw_trac_exp" //440~449 公共部分

#define PGW_TIMETER_SELECT_LongCdr 					"q_pgw_timeter_longcdr"
#define PGW_TIMETER_SELECT_Timeout 					"q_pgw_timeter_timeout"
#define PGW_TIMETER_UPDATE_RAR  					"u_pgw_timeter_rar"
#define PGW_CCA_TERM_UpdateSession					"u_pgw_cca_term_session"
#define PGW_AOC_SelectAHSessionAllChild 			"q_pgw_aoc_ahsessionallchild"  //435~439
#define PGW_TIMETER_SelectChildNum 					"q_pgw_timeter_childnum"
#define PGW_TIMETER_UPDATE_ASR 						"u_pgw_timeter_asr"
#define PGW_TIMETER_SelectTimeoutChildNum 			"q_pgw_timeter_timeoutchildnum"

#define PGW_UpdateMainSessionNum					"u_pgw_mainsessionnum"
#define PGW_UpdateSessionStore_RG				    "u_pgw_session_rg"

#define PGW_CDR_LOCA_UpdateSessionTime				"u_pgw_cdrsessiontime"
#define PGW_SELECT_SESSION_STORE_SERVERID 		    "q_pgw_session_serverid"
#define PGW_CUTCDR 		                            "q_pgw_cdrcut_select"
#define PGW_EPT_UPDATE_CODE 		                "u_pgw_ept"
#define PGW_U_SEND_RGNUM					        "u_pgw_send_rgnum"
#define PGW_U_RECV_RGNUM					        "u_pgw_recv_rgnum"
#define PGW_S_RECV_RGNUM			                "q_pgw_recv_rgnum"
#define PGW_Select_Latn				                "q_pgw_session_latn"
#define PGW_Update_SESSION_RBANS_STATUS		        "u_pgw_session_rbans_status"
//------------------------------------PGW  END----------------------------------------




//------------------------------------ISMP  BEGIN[500~599]----------------------------------------
#define ISMP_InsertSession				"i_ismp_session"		// call insertSession_ISMP
#define ISMP_InsertSessionOffLine		"i_ismp_session_offline"
#define ISMP_UpdateSessionAns			"u_ismp_session_ans"		// call updateSession_ISMP
#define ISMP_UpdateSessionAnsOffLine	"u_ismp_session_ans_offline"
#define ISMP_UpdateSessionReq			"u_ismp_session_req"// call insertSessionEvent_ISMP
#define ISMP_InsertSessionFree			"i_ismp_sessionfree"		// call insertSessionFree_ISMP
#define ISMP_getSessionAoc          	"q_ismp_sessionaoc"         //getSessionAoc_Ismp
#define ISMP_UpdateSession2				"u_ismp_session2"		// call updateSession2_ISMP
#define ISMP_DeleteSession				"d_ismp_session"		// call deleteSession
// #define ISMP_GetSessionState  			507		// call getSessionStatus
#define ISMP_GetSessionRecord			"q_ismp_getsessionrecord"		// call getSessionRecord_ISMP
#define ISMP_UpdateSessionFree			"u_ismp_sessionfree"		// call updateSession2_ISMP
#define ISMP_GetSessionTrace			"q_ismp_getsessiontrace"		// call getSessionRecord_ISMP
#define ISMP__GetProfrRbAns        		"q_get_ofr_name"
#define ISMP__GetPresentReSourceAns		"q_ismp_getpresentresourceans"
#define ISMP__GetSessionInfo_Cdr        "q_ismp_getsessioninfo_cdr"
#define ISMP_RBANS						"q_ismp_rbans_001"
#define ISMP_Ratable_RBANS				"q_ismp_ratable_rbans"
//------------------------------------ISMP  END----------------------------------------




//------------------------------------DSL  END[600~699]----------------------------------------
#define DSL_InsertSession		    "i_dsl_session"		// call insertSession_DSL
#define DSL_InsertSession_Offline	"i_dsl_session_offline"
#define DSL_InsertSession_Free		"i_dsl_session_free"	


#define DSL_GetSessionInfo  	    "q_dsl_getsessioninfo"		// call getSessionStatus
#define DSL_GetSessionInfo_Cdr  	"q_dsl_getsessioninfo_cdr"	

#define DSL_UpdateSession_ans		"u_dsl_session_ans"		// call updateSession_DSL
#define DSL_UpdateSession_ans_OffLine "u_dsl_session_ans_offline"
#define DSL_UpdateSession_req       "u_dsl_session_req"
#define DSL_UpdateSession_Free		"u_dsl_session_free"
#define DSL_UpdateSession_Ept		"u_dsl_session_ept"
#define DSL_UpdateSession_EptResult "u_dsl_session_eptresult"
#define DSL_GetSessionSpiltFlag     "q_dsl_spilt_flag"
#define DSL_MergeSessionInfo		"q_dsl_spilt_session"
#define DSL_DelSession_ans			"d_del_spilt_session"

#define DSL_DeleteSession		    "d_dsl_session"		// call deleteSession
#define DSL_CDR_UpdateUSU           "u_dsl_cdr_update_usu"

//------------------------------------DSL  END----------------------------------------

//------------------------------------DATA  BEGIN[700~799]----------------------------------------
#define DATA_UpdateSessionStoreCdr_006			"u_data_sessionstorecdr_006"       
#define DATA_InsertSessionStore  				"i_data_session"                
#define DATA_InsertSessionStoreFree 			"i_data_session_free" 
#define DATA_InsertSessionStoreOFFL         		"i_data_session_offline"
#define DATA_INsertSessionStoreOFFL2			"i_data_session_offline2"
#define DATA_UpdateSessionStoreRbrNoenough 		"u_data_rbrnoenough"                                                         
#define DATA_InsertSessionStoreRg 				"i_data_session_rg"               
#define DATA_InsertSessionStoreRgFree 			"i_data_session_rg_free"                                                           
#define DATA_SelectSessionStoreRecord_002 		"q_data_session_record_002"       
#define DATA_SelectSessionStoreRecord_003 		"q_data_session_record_003"       
#define CCG_SelectSessionStoreRecord_003 		"q_ccg_session_record_003"        
#define DATA_SelectSessionStoreRecord_004 		"q_data_session_record_004"       
#define DATA_SelectSessionStoreBalanceNotEnough "q_data_balancenotenough" 
#define DATA_SelectSessionStoreRecord_005 		"q_data_session_record_005"
#define DATA_UpdateSessionStoreRecord_002		"u_data_session_record_002"     
#define DATA_SelectSessionStoreRbans_001 		"q_data_session_rbans_001"             
#define DATA_SelectSessionStoreRbansRgTerm 		"q_data_session_rbansrgterm"              
#define DATA_UpdateSessionStoreRbansRg 			"u_data_session_rbansrg"
#define DATA_UpdateSessionStoreRbansRg_OffLinec "u_data_session_rbansrg_offlinec"
#define DATA_UpdateSessionStoreRbansRg_OffLinem "u_data_session_rbansrg_offlinem" 
#define DATA_UpdateSessionStoreRbansOffLine     "u_data_session_rbansoffline"
#define DATA_UpdateSessionStoreRbansOffLineT	"u_data_session_rbansofflinet"
#define DATA_SelectSessionStoreCdr_001 			"q_data_session_cdr_001"          
#define DATA_SelectSessionStoreCdrTel_002 		"q_data_session_cdrtel_002"       
#define DATA_UpdateSessionStoreCdr_001 			"u_data_session_cdr_001"            
#define DATA_UpdateSessionStoreLongCdr_001 		"u_data_session_longcdr_001"      
#define DATA_UpdateSessionStoreLongCdr_002 		"u_data_session_longcdr_002"           
#define DATA_SelectSessionStoreCdrRg 			"q_data_session_cdrrg"            
#define DATA_SelectSessionStoreCdrLong 			"q_data_session_cdrlong"          
#define DATA_SelectSessionStoreEpt_001 			"q_data_session_ept_001"          
#define DATA_SelectSessionStoreAoc_001 			"q_data_session_aoc_001"          
#define DATA_SelectSessionStoreTrace_001 		"q_data_session_trace_001"        
#define DATA_DeleteSessionStore 				"d_data_session"
#define DATA_DeleteSessionStoreRG 				"d_data_session_rg"
#define DATA_UpdateSessionStorecdr_004 			"u_data_session_cdr_004"          
#define DATA_UpdateSessionStorecdr_003 			"u_data_session_cdr_003"          
#define DATA_UpdateSessionStorecdr_005 			"u_data_session_cdr_005"          
#define DATA_UpdateSessionStorecdrChild 		"u_data_session_cdrchild"         
#define DATA_UpdateSessionStoreEpt_001 			"u_data_session_ept_001"          
#define DATA_UpdateSessionStoreEpt_002 			"u_data_session_ept_002"                  
#define DATA_DeleteSessionStoreCdr 				"d_data_session_cdr"  
#define DATA_SelectSessionNum 					"q_data_session_num"
#define AAA_SelectSessionStoreCdr  				"q_aaa_session_cdr"
#define DATA_FREE_Update_UpdateSession 			"u_data_free_session"
#define DATA_SelectSessionAHStoreCdrRg 			"q_data_sessionahstorecdrrg"     
#define DATA_UpdateSessionAHStoreRbansRg 		"u_data_sessionah_rbansrg"   
#define DATA_UpdateMainSessionNum 				"u_data_mainsessionnum"  
#define DATA_SELECT_SESSION_STORE_SERVERID 		"q_data_session_serverid"
#define DATA_Select_PLCA_USER_POSITION 	 		"q_data_plca_user_position"  

#define DATA_Select_SessionStoreRecord_RG       "query_data_session_rg"
#define DATA_Select_SessionStoreRecord_RG_OffLine       "query_data_session_rg_offline"
#define DATA_Select_SessionStoreRecord_RG_OffLinem       "query_data_session_rg_offlinem"
#define DATA_UpdateSessionStoreRecord_RG		"u_session_rg"
#define DATA_CDR_UpdateCdrSession				"u_data_cdrsession"
#define DATA_CUTCDR 		                    			"q_data_cdrcut_select"
#define DATA_U_SEND_RGNUM						"u_data_send_rgnum"
#define DATA_S_RECV_RGNUM						"q_data_recv_rgnum"
#define DATA_U_RECV_RGNUM						"u_data_recv_rgnum"
#define DATA_EPT_UPDATE_CODE					"u_data_ept"
#define DATA_Select_Latn							"q_data_session_latn"
//------------------------------------CCG  END----------------------------------------

//--------------------------------Timer BEGIN(超时管理)-----------------------
#define   DATA_UpdateSessionTrerTimer 	"u_data_sessiontrertimer"
#define   PGW_UpdateSessionTrerTimer 	"u_pgw_sessiontrertimer"
#define   DATA_SelectSessionTimer 		"q_data_sessiontimer"
#define   DATA_UpdateSessionTimer 		"u_data_sessiontimer"
#define   ISMP_SelectSessionTimer 		"q_ismp_sessiontimer"
#define   VOICE_SelectSessionTimer 		"q_voice_sessiontimer"
#define   SMS_SelectSessionTimer 		"q_sms_sessiontimer"
#define   DSL_SelectSessionTimer 		"q_dsl_sessiontimer"
#define   DATA_DeleteSession 			"d_data_session"

#define DATA_TIMETER_SelectChildNum 				"q_data_timeter_childnum"
#define DATA_TIMETER_UPDATE_ASR 					"u_data_timeter_asr"
#define DATA_TIMETER_SelectTimeoutChildNum 			"q_data_timeter_timeoutchildnum"
//--------------------------------TRERTimer END(超时管理)-----------------------

//------不上送TermRER时更新会话表BEGIN-----------------------
#define   VOICE_UpdateSessionWithoutRER 	"u_voice_sessionwithoutrer"
#define   DSL_UpdateSessionWithoutRER 		"u_dsl_sessionwithoutrer"  
#define   DATA_UpdateSessionWithoutRER 		"u_data_sessionwithoutrer"
#define   PGW_UpdateSessionWithoutRER 		"u_pgw_sessionwithoutrer"
#define   ISMP_UpdateSessionWithoutRER 		"u_ismp_sessionwithoutrer"

//------不上送TermRER时更新会话表END-----------------------

//-----量本，累积量，SUM文件信息--------------
#define Voice_Query_RatingMsg    "q_voice_ratingMsg"
#define DATA_Query_RatingMsg		"q_data_ratingMsg"
#define PGW_Query_RatingMsg		"q_pgw_ratingMsg"
#define DSL_Query_RatingMsg		"q_dsl_ratingMsg"
#define _5G_Query_RatingMsg		"q_5g_ratingMsg"

#define Voice_Insert_RatingMsg	"i_voice_ratingMsg"
#define DATA_Insert_RatingMsg	"i_data_ratingMsg"
#define PGW_Insert_RatingMsg		"i_pgw_ratingMsg"
#define DSL_Insert_RatingMsg		"i_dsl_ratingMsg"
#define _5G_Insert_RatingMsg		"i_5g_ratingMsg"

#define Voice_Delete_RatingMsg	"d_voice_ratingMsg"
#define DATA_Delete_RatingMsg	"d_data_ratingMsg"
#define PGW_Delete_RatingMsg		"d_pgw_ratingMsg"
#define DSL_Delete_RatingMsg		"d_dsl_ratingMsg"
#define _5G_Delete_RatingMsg		"d_5g_ratingMsg"

#define Voice_Update_RatingMsg "u_voice_ratingMsg"
#define DATA_Update_RatingMsg  "u_data_ratingMsg"
#define DSL_Update_RatingMsg	"u_dsl_ratingMsg"
#define PGW_Update_RatingMsg	"u_pgw_ratingMsg"
#define _5G_Update_RatingMsg	"u_5g_ratingMsg"

#define DATA_Delete_RatingMsg_ALL	"d_data_ratingMsg_all"
#define PGW_Delete_RatingMsg_ALL	"d_pgw_ratingMsg_all"
#define _5G_Delete_RatingMsg_ALL		"d_5g_ratingMsg_all"

#define _5G_Query_RatingMsg_ALL		"q_5g_ratingMsg_all"
#define DATA_Query_RatingMsg_ALL		"q_data_ratingMsg_all"
#define PGW_Query_RatingMsg_ALL		"q_pgw_ratingMsg_all"
#endif // __FUNC_SQLINDEX_H__
