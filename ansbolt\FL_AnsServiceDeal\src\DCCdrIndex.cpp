#include "DCCdrIndex.h"
#include "DCSeriaOp.h"
#include "CdrIndexDef.h"
DCCdrIndex::DCCdrIndex()
{
	_en = new DCSeriaEncoder(ESeriaBinString);

}

DCCdrIndex::~DCCdrIndex()
{
	delete _en;
}

void DCCdrIndex::SetIndexInfo(STBizMsg *bizMsg,const char *startTime,const char *endTime,const char *append)
{
	strcpy(idxRecord.sBeginTime,startTime);
	strcpy(idxRecord.sEndTime,endTime);
	idxRecord.lnSourceId = atol(bizMsg->m_sourceId.c_str());
	strcpy(idxRecord.sAccNbr,bizMsg->m_subNumber);
	idxRecord.iClockInterval = 0;

	idxRecord.latn = bizMsg->m_ilatnId;
	idxRecord.iFlag = 0;
	string suff;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			sprintf(idxRecord.sRecodeKey,"%s|%s||&&%s|%s||&&",bizMsg->m_calledNumber,bizMsg->m_eventType,bizMsg->m_calledNumber,bizMsg->m_eventType);
			suff = "gaa";
			break;
		case SMS:
			sprintf(idxRecord.sRecodeKey,"%s|&&%s|",bizMsg->m_calledNumber,bizMsg->m_calledNumber);
			suff = "csm";
			break;
		case DATA:
			sprintf(idxRecord.sRecodeKey,"%s|%s&&%s|%s",bizMsg->m_sessionID,bizMsg->m_subNumber,bizMsg->m_sessionID,bizMsg->m_subNumber);
			suff = "g1x";
			break;
		case DSL:
			sprintf(idxRecord.sRecodeKey,"&&");
			suff = "idr";
			break;
		case ISMP:
			sprintf(idxRecord.sRecodeKey,"|%s|%s|%s&&",bizMsg->m_callingNumber,append,bizMsg->m_spid);
			suff = "ismp";
			//append is product_id
			break;
		case PGW:
			sprintf(idxRecord.sRecodeKey,"|%lld||%s|&&|%lld||%s|",bizMsg->m_ratingGroup,append,bizMsg->m_ratingGroup,append);
			suff = "lte";
			//append is imsi
			break;
		case DATA_5G:
			sprintf(idxRecord.sRecodeKey,"|%lld||%s|&&|%lld||%s|",bizMsg->m_ratingGroup,append,bizMsg->m_ratingGroup,append);
			suff = "5g";
			break;
	}
	if(suff.size()>0)
	{
		SendFileManer(bizMsg,suff);
	}

}
void DCCdrIndex::SendFileManer(STBizMsg *bizMsg,string suff)
{
	ocs::CdrIndexDef cdridx;
	struct tm tp;
	char v_date[16] = {0};
	strptime(idxRecord.sBeginTime,"%Y%m%d%H%M%S",&tp);
	time_t timep = mktime(&tp);
	long begindate = atol(idxRecord.sBeginTime) / 1000000;
	long begin = atol(idxRecord.sBeginTime) / 10000;
	long end = atol(idxRecord.sEndTime) / 10000;

	STIndRecord idx = idxRecord;
	cdridx.filesuffix = suff;
	while(begin < end)
	{
		tm *p;
		timep += 3600;
		p=localtime(&timep);
		sprintf(v_date,"%d%02d%02d%02d0000",(1900+p->tm_year) , (1+p->tm_mon) ,p->tm_mday,p->tm_hour);
		strcpy(idx.sEndTime,v_date);
		if(cdridx.num == 0)
		{
			cdridx.cdridx.resize(sizeof(STIndRecord)+1,0);
			memcpy((void*)cdridx.cdridx.c_str(),(void*)&idxRecord,sizeof(STIndRecord));
			char sDate[14] = {0};
			sprintf(sDate,"%010ld",begindate);
			cdridx.cdrdate = sDate;
			cdridx.num++;
		}else
		{
			char sDate[14] = {0};
			sprintf(sDate,"%010ld",begindate);
			cdridx.cdrdate += sDate;
			cdridx.num++;
		}
		strcpy(idx.sBeginTime,idx.sEndTime);
		begin = atol(v_date) / 10000;
		begindate = atol(v_date) / 1000000;
	}
	if(cdridx.num == 0)
	{
		cdridx.cdridx.resize(sizeof(STIndRecord)+1,0);
		memcpy((void*)cdridx.cdridx.c_str(),(void*)&idxRecord,sizeof(STIndRecord));
		char sDate[14] = {0};
		sprintf(sDate,"%010ld",begindate);
		cdridx.cdrdate = sDate;
		cdridx.num++;
	}else
	{
		char sDate[14] = {0};
		sprintf(sDate,"%010ld",begindate);
		cdridx.cdrdate += sDate;
		cdridx.num++;
	}
	EmitFilManer(bizMsg,cdridx);
}
void DCCdrIndex::EmitFilManer(STBizMsg *bizMsg,ocs::CdrIndexDef &cdridx)
{
	std::string sendmsg;
	//DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","short cdr index num[%d],cdr index num[%d]",cdridx.shortnum,cdridx.num);
	if(0!=cdridx.num || 0!=cdridx.shortnum)
	{
		_en->clear();
		_en->encode(cdridx);
		//std::string sndmsg = _uuid;
		sendmsg = HexEncode(_en->data(),_en->size());
		//	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","send filemaner bolt ,size[%d]",sndmsg.c_str(),sndmsg.size());
		bizMsg->m_pSendMsg->insert(pair<string,string>("FileManer",sendmsg));
		/*
		tydic::storm::Tuple tRnd;
		tRnd.SetValues(sseq);
		tRnd.SetValues(sndmsg);
		tydic::storm::EmitDirect(-1,tRnd,"FileManerStream");
		*/
	}
}
void DCCdrIndex::ToString(std::string & str)
{
	idxRecord.ToString(str);
}