/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCRoamDealFlow.h
*Indentifier：
*
*Description：
*		应答流程组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ROAM_DEAL_FLOW_H__
#define __DC_ROAM_DEAL_FLOW_H__
#include "DCBasePlugin.h"
#include "TSMPara.h"
#include "DCRoamJudge.h"





class DCRoamDealFlow :  public DCBasePlugin
{
	public:	
		DCRoamDealFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCRoamDealFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);

	private:
		DCRoamJudge *m_roamjudge;
		TSMPara *m_smpara;	
		AREA_INFO*		m_area;
};

#endif


