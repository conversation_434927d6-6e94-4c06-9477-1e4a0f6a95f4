include ../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include
COMMON_SRC_PATH=$(LBSPUBROOT)/SM/common/src
COMMON_OBJ_PATH=$(LBSPUBROOT)/SM/common/obj

COMMON_SRC=DCCommonIF.cpp  \
           TConfig.cpp TThread.cpp  TSMPara.cpp DCAnsPara.cpp numcheck.cpp tinystr.cpp  tinyxml.cpp tinyxmlerror.cpp tinyxmlparser.cpp md5.cpp
           
COMMON_SRCS=$(addprefix $(COMMON_SRC_PATH)/, $(COMMON_SRC))
COMMON_OBJS=$(patsubst $(COMMON_SRC_PATH)/%.cpp, $(COMMON_OBJ_PATH)/%.o, $(COMMON_SRCS))

TLIB= $(RELEASE_PATH)/lib/libCommonIF.a

INCLUDE =-I$(COMMON_INC) \
         -I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
		 -I$(TXML)/include \
         -I$(DFM_INC_PATH) 

LIBPATH=-L$(AVRO)/lib -L$(TXML)/lib
LIBSLIST=-ltinyxml

tmpvar:=$(call CreateDir, $(COMMON_OBJ_PATH))

CFLAGS += -std=c++11

.PHONY: all clean dup

all:$(TLIB)	
$(TLIB): $(COMMON_OBJS)
	@echo "build libCommonIF.a----"
	$(AR) $(TLIB) $(COMMON_OBJS)
$(COMMON_OBJS):$(COMMON_OBJ_PATH)/%.o:$(COMMON_SRC_PATH)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	$(LIBPATH) $(LIBSLIST)

clean:
	@rm -rf $(COMMON_OBJS)
       
dup:

