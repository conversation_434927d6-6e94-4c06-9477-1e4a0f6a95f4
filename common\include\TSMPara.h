/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       TSMPara.h
*Indentifier：
*
*Description：
*       SM系统参数及业务参数
*Version：
*       V1.0
*Author:
*
*Finished：
*
*History:
*
********************************************/
#ifndef _TSMPARA_H_
#define _TSMPARA_H_

#include <map>
#include <string>
#include <sys/stat.h>
#include <stdio.h>
#include <string.h>
#include <algorithm>
#include <math.h>
#include <stdlib.h>

#include "SMParaStruct.h"
#include "BizDataDef.h"
#include <vector>
#include "DCDBManer.h"
#include "DCRFData.h"
#include "DCOcpMsgDef.h"
using namespace ocs;
using namespace std;



typedef struct SMPARA
{
	SCommonPara		    m_commonPara;
	SINPara				m_INPara;
	SPSPara				m_PSPara;
	S5GPara				m_5GPara;
	SISMPPara			m_ISMPPara;
	SP2PSMSPara			m_P2PSMSPara;
	SDSLPara			m_DSLPara;

	map<int, ACCESS_INFO*> 				m_access;
	map<int, COUNTRY_INFO*> 			m_country;
	map<int, AREA_INFO*> 				m_area;
	multimap<long long, TSERVICE_QUOTA_CONF*>	m_service_quota_conf;
	multimap<string, TSERVICE_QUOTA_CONF*>	m_service_quota_conf_ofr;
	map<int, SResultCode*> 				m_result_code_map;
	map<int, int>							m_call_number_prefix;
	map<int, int>							m_tariffid_map;
	map<string,int>						m_business_type_map;
	map<int, SAocPara*>					m_aoc_para_map;
	map<string, int>					m_SPCProductId_map;
	map<string, int>					m_ProductOfferId_map;

	map<string, int>                    m_expirestate;
	map<string, int>					m_filtedstauscd;
	map<string, int> 				m_svecont_attr_map;

	multimap<long long, MSISDN_MAP_INFO*> m_msisdn_area_map;			/*左键为号段头，*/
	multimap<long long, URGENT_NUMBER_INFO*> m_spr_urgent_map;   //存在多个数据，/*左键为号码，*/
	map<string, MSC_INFO*>m_spr_msc_vlr_map;					/*左键为msc code，*/
	map<string, MSC_INFO> m_spr_tac_vlr_map;					/*左键为tac code，*/

	multimap<int, USER_STATE_INFO*> m_user_state_map;   			/*左键为用户类型，*/
	multimap<string, EDGE_ROAM_INFO*> m_edge_roam_lac_map;   //存在多个数据 /*左键为lac code，*/
	multimap<string, EDGE_ROAM_INFO*> m_edge_roam_cellid_map;   //存在多个数据/*左键为cellid code，*/
	multimap<string, EDGE_ROAM_INFO*> m_edge_roam_map_data;   //存在多个数据/*左键为cellid code，*/

	map<long, string> 					m_spr_np_map;
	map<string, PDSN_INFO*>				m_spr_pdsn_map;
	map<string, SGSN_INFO*>				m_spr_sgsn_map;
	map<string, DSL_INFO*>				m_spr_dsl_map;
	map<string, int>					m_spr_lac_map;
	map<string, int>					m_spr_cell_map;
	map<string, SGSN_INFO*>				m_spr_sgsn_ipv6_map;
	vector<string>            m_vecOfrID2G;
	vector<string>            m_vecIsmpRefund;
	map<long long, int>		  m_ps_short_cdr;
	vector<int> 						v_mask;					//存储 ipv6 地址掩码
	map<string,bool>            m_mapsubfix;

	int flag ; //是否在使用中
	vector<string>            m_vecCdrByService;//按照RG出单，根据service-context-id

	map<string,string>		  m_voice_free_cdr_map;
	map<string,string>		  m_sms_free_cdr_map;
	vector<int>				  m_vecEptBillResultCode;			//ABM异常对账的RB 结果码
	vector<int>            m_vecCutArea;

	//边界漫游判断后付费替换预付费
	multimap<string,sRoamBorderLine> m_mapRoamBorderNew;
	multimap<string,sRoamBorderLineData> m_mapRoamBorderData;
	map<string,MSC_COUNTRY*>  m_msc_country_map;//根据msc查出国家码和运营商
	map<string,string>	m_sector_id_map;// 语音业务国际漫游增强资费区
	map<string,string>	m_sector_id_csm_map;// 语音业务国际漫游增强资费区
	map<long,int>        m_mapProductID;
}TSMPARA;

typedef struct
{
	UDBSQL* m_pSELECT_SPR_ACCESS_NUMBER;
	UDBSQL* m_pSELECT_SPZ_COUNTRY;
	UDBSQL* m_pSELECT_SPZ_CITY;
	UDBSQL* m_pSELECT_SM_SERVICE_QUOTA_CONF;
	UDBSQL* m_pSELECT_SM_AOC_GLOBAL_CFG;
	UDBSQL* m_pSELECT_HLP_ACCTITEM_MAP;
	UDBSQL* m_pSELECT_SM_SYSTEM_PARAMETER;
	UDBSQL* m_pSELECT_SPR_MSISDN_AREA_MAP;
	UDBSQL* m_pSELECT_PAR_GSM_CODE_MAP;
	UDBSQL* m_pSELECT_PAR_RANGE_CODE_MAP;
	UDBSQL* m_pSELECT_SPR_URGENT_NUMBER;
	UDBSQL* m_pSELECT_SM_USER_STATE;
	UDBSQL* m_pSELECT_SPR_MSC;
	UDBSQL* m_pSELECT_SPR_EDGE_ROAM;
	UDBSQL* m_pSELECT_SPR_EDGE_ROAM_002;
	UDBSQL* m_pSELECT_SPR_EDGE_ROAM_DATA;
	UDBSQL* m_pSELECT_SPR_NP;
	UDBSQL* m_pSELECT_SPR_PDSN;
	UDBSQL* m_pSELECT_SPR_SGSN;
	UDBSQL* m_pSELECT_SPR_DSL;
	UDBSQL* m_pSELECT_SPR_LAC;
	UDBSQL* m_pSELECT_SPR_TAC;
	UDBSQL* m_pSELECT_SPR_CELL;
	UDBSQL* m_pSELECT_SPR_CELL_MSC;
	UDBSQL* m_pSELECT_SM_RESULTCODE_MAP;
	UDBSQL* m_pSELECT_SMS_SEND_PRO_MAP;
	UDBSQL* m_pSELECT_PRD_PRD_INST_ATTR_DEF;
	UDBSQL* m_pSELECT_TB_BIL_SVECONT_ATTR_MAP;
	UDBSQL* m_pSELECT_PAR_ROAM_BORDERLINE;
	UDBSQL* m_pSELECT_PAR_COUNTRY_CODE_MAP;
	UDBSQL* m_pSELECT_SECTOR_ID_MAP;
	UDBSQL* m_pSELECT_PAR_ROAM_BORDERLINE_AAA;
	UDBSQL* m_pSELECT_SECTOR_ID_CSM_MAP;
    UDBSQL* m_pSELECT_SPR_SGSN_IPV6;
    UDBSQL* m_pSELECT_5G_NR;
   	UDBSQL* m_pSELECT_SPR_TAC_5GSA;
	UDBSQL* m_pSELECT_CNPLMN_5G_NR;
}SMParaSQL;


class TSMPara : public BData
{
	public:
		map<int, ACCESS_INFO*> * GetAccess();
		map<int, COUNTRY_INFO*> * GetCountry();
		const COUNTRY_INFO * GetCountryInfo(int country);
		map<int, AREA_INFO*> * GetArea();
		const AREA_INFO 	*GetAreaInfo(int area);

		map<int, int> * GetCallPrefix();
		SAocPara * GetAocPara(int serviceType, int reqType, int callType,int islowbalance);
		SCommonPara *GetCommonPara();
		SINPara * GetINPara();
		SPSPara * GetPSPara();
		S5GPara * Get5GPara();
		SDSLPara * GetDSLPara();
		SISMPPara* GetISMPPara();
		SP2PSMSPara* GetP2PSMSPara();
		TSERVICE_QUOTA_CONF * GetServiceQuotaConf(long long ratingGroup,long Balance=-1);
		TSERVICE_QUOTA_CONF * GetServiceQuotaByOfr(const char * OfferId,long Balance=-1);

		MSISDN_MAP_INFO* GetMsisdnAreaMap(long long beginMsisdn,int nfind=0);
		int GetRangeCodeMap(long long beginMsisdn, MSISDN_MAP_INFO& msisdn_info);
		int GetGsmCodeMap(long long beginMsisdn, MSISDN_MAP_INFO& msisdn_info);
		MSC_INFO* GetMscVlrCodeInfo(const char * msc_code);
		PDSN_INFO* GetSprPDSN(const char * pdsn);
		SGSN_INFO* GetSprSGSN(const char * sgsn);
		SGSN_INFO* GetSprSGSN_IPV6(const char* sgsn);
		DSL_INFO* GetSprDSL(const char * dsl);
		CELL_INFO* GetSprCELLMSC(const char* cellid);

		int GetEdgeRoamLac(const char * lac, int homeArea,AREA_INFO* visit);
		int GetRoamMsc(const char * msc, int homeArea,AREA_INFO* visit);
		int GetEdgeRoamCellid(const char * cellid, int homeArea,AREA_INFO* visit);
		int GetEdgeRoamDataCellid(const char * cellid, const char* mscinfo,int homeArea,AREA_INFO* visit,int roamtype);

		int GetServiceType(const char* str);
		int GetTariffIDMap(int tariffid);
		int IsTranslateDataCard();
		int getPlatformOverLoad();
		int GetISMPSPCDebit(char* SPCProductId);
		int JudgeISMPModTime(const char * productOfferID);
		int GetOCPResultCode(int resultCode);
		int checkUrgentNumberInfo(long long subnumber, long long calling, long long called, int prov, int area, int servicetype);
        int GetSprNp(long sub, char* rcarrier, long time, int iRcarrierLen);
        void ParseString(string& szSourString,map<string,int> &mapDest,const char* szSeparatorOne, const char* szSeparatorTwo);
		int checkUserStateInfo(int bizType, char* basicState, char* extState, char* stausCd, char* stopType);

		int GetSprLAC(const char * lac);
		int GetSprCELL(const char * cellid);
		int GetOfrID2G(char * ofrid);

		int GetCutArea(int area);

		int GetIsmpRefund(char * ServiceContext);
		int prepare();
		int GetPSShortCdr(long long ratinggroup);
		int GetSMSSendPro(int bizType, int errCode, char* para_key);
		int GetTacVlrCodeInfo(const char* tac,MSC_INFO &tacinfo);
        int GetProvCodeInfo(char* ipv4,char* ipv6,PROV_INFO &provinfo);
		int getsuffix(char* postfix);

		int getCdrService(char* ServiceContext);

		int getAttrID(char *szAttrID);

		int GetHightAmountService(char * ServiceContext);

		int GetBillAttr(string serviceAttr);

		int GetRoamBorderType(const char *vi_sMsisdn,const char *vi_sMscId, const char *vi_LacId,const char *vi_CellId, const SPhone& home, AREA_INFO* visit);
		int IsExpire(string basic_state);
		int IsFilted(long productid);
		int IsFiltedByStauscd(string staus_cd);
		MSC_COUNTRY * GetMscCountryInfo(string msc_code);
		string GetSectorId(string visited_carrier_code,string  sponsor_code);
		string GetRoamBorderData(string bsid, int ilatn);
		string GetTariffInfoCSM(string visited_carrier_code, int sponsor_code);
		int GetTac_5GSA(const char* tac,int provCode, MSC_INFO &tacinfo);
		int GetNrByCellIDAndCnplmn(string strCNPlmn,string nrcell,SGSN_INFO &sgsn);
		int GetNrCellID(string nrcell,SGSN_INFO &sgsn);
		/*初始化*/
	    virtual int init(DCDBManer* dbm);

		/*按索引更新数据*/
		virtual int work(EBDIDX idx);

		/*按索引获取数据指针*/
	    virtual void* data(EBDIDX idx)
		{
			return m_para[idx];
		}

		/*按索引清空数据*/
	    virtual void clear(EBDIDX idx);


		TSMPara();

		~TSMPara();


	private:
		int LoadAccess();
		int LoadCountry();
		int LoadArea();         					 /*SPZ_CITY*/

		int LoadServiceQuotaConf();
		int LoadAocPara();
		int LoadSystemPara(const char *paraGroup, const char *paraKey, SSystemPara *systemPara);
		int LoadCommonPara();
		int LoadINPara();
		int LoadPSPara();
		int Load5GPara();
		int LoadISMPPara();
		int LoadCallPrefix();
		int LoadResultCodeMap();
		int LoadServiceType();
		int LoadTariffIDMap();
		int LoadP2PSMSPara();
		int LoadOfrID2G();
		int LoadBillAttr();

		int LoadCutArea();

		int LoadDSLPara();//dsl计费号码后缀
		int ParseAreaSubfix(const char* value);

		int LoadMsisdnMap();  						/*SPR_MSISDN_AREA_MAP*/
		//int LoadSprNP();							/*SPR_NP*/
		int LoadSprUrgentNumber(); 			/*SPR_URGENT_NUMBER*/
		int LoadMscVlrCode();						/*SPR_MSC*/
		int LoadEdgeRoamLac();						/*SPR_EDGE_ROAM      LAC*/
		int LoadEdgeRoamCellid();					/*SPR_EDGE_ROAM      Cellid*/
		int LoadSmUserState(); 						/*SM_USER_STATE*/
		int LoadForbiddenRule();                    /*SM_FORBIDDEN_RULE*/

		int LoadSprPDSN();
		int LoadSprSGSN();
		int LoadSprDSL();
		int LoadSprLAC();

		int LoadTacVlrCode();
		int LoadSprCELL();

		int LoadSMSSendPro();
		int LoadSprCELLMsc();

		int LoadEdgeRoamData();//数据业务边漫表

		int LoadAttrDef();

		int LodeSizeComp();//刷新前后数据对比

		int LoadRoamBorder();

		int LoadExpireState();//加载无效用户状态
		int LoadFiltedStauscd();//加载无效状态

		int LoadFilterProductId();//加载无效的productID

		int LoadMscCountryInfo();//加载MSC和country信息。

		int LoadTariffInfo();//加载资费信息
		int LoadRoamBorderData();
		int LoadTariffInfoCSM();
		int LoadGsmCodeMap();
		int LoadRangeCodeMap();
		int LoadSprSGSN_IPV6();

		int  ParseServiceType(int busineseType, const char* value);
		int  ParseSPCProductId(const char* value);
		int ParseProductOfferId(const char * value);
		void ParseString(string& szSourString,vector<string> &vecDestString,const char* szSeparator);
		void ParseString(string& szSourString,vector<int> &vecDest,const char* szSeparator);
		void ParseString(string& szSourString,map<long,int> &mapDest,const char* szSeparator);
		int  ParseShortCDR(const char *value);
		void ParseLongCDR(char * value);
		int  ParseState(const char* value);
		int  ParseStauscd(const char* value);
		void PushAddress(string ipv6Address,SGSN_INFO* sgsninfo);
		string MaskAddress(string ipv6Address,int mask);
		string RegularAddr(string beginAddr,string endAddr);
		void PushMask(int mask);
	private:
		SMParaSQL m_sql;
		DCDBManer * m_dbm;
		TSMPARA *m_para[2];
		TSMPARA * m_pSMPARA;
};

#endif
