#include "DCUUIDUtil.h"

UUIDUtil::UUIDUtil()
{
	strcpy(m_sDecToBase64, "abcdefghijklmnopqrstuvwxyz012345");
	memset(m_sHexUid, 0, sizeof(m_sHexUid));
}

UINT8_T UUIDUtil::HexToDec(char hex)
{
	if (hex >= '0' && hex <= '9')
	{
		return hex - '0';
	}
	
	if (hex >= 'A' && hex <= 'F')
	{
		return hex - 'A' + 10;
	}
	
	if (hex >= 'a' && hex <= 'f')
	{
		return hex - 'a' + 10;
	}
	
	return -1;
}

// 取高64位
UINT64_T UUIDUtil::GetMostSignificantBits()
{
	UINT64_T lnMost = 0;
	for (int iPos = 0; iPos < 16; iPos++)
	{
		lnMost <<= 4;
		int iDec = HexToDec(m_sHexUid[iPos]);
		if (iDec < 0)
		{
			return -1;
		}
		
		lnMost = lnMost | iDec;
	}
	
	return lnMost;
}

// 取低64位
UINT64_T UUIDUtil::GetLeastSignificantBits()
{
	UINT64_T lnLeast = 0;
	for (int iPos = 16; iPos < 32; iPos++)
	{
		lnLeast <<= 4;
		int iDec = HexToDec(m_sHexUid[iPos]);
		if (iDec < 0)
		{
			return -1;
		}
		
		lnLeast = lnLeast | iDec;
	}
	
	return lnLeast;
}

string UUIDUtil::CreateUUID()
{
	char buftmp[64] = {0};
	uuid_t uuid;
	uuid_generate(uuid);
	uuid_unparse(uuid, buftmp);

	string strUuid = buftmp;
	return strUuid;	
}

string UUIDUtil::CreateShortUUID()
{
	char szUUID[64] = {0};
	uuid_t uuid;
	uuid_generate(uuid);
	uuid_unparse(uuid, szUUID);
	char szUidResult[27] = {0};
	int iUidPos = 25;   // 生成长度为26字符的uid
	
	memset(m_sHexUid, 0, sizeof(m_sHexUid));
	int iNewPos = 0;
	for (int iPos = 0; szUUID[iPos]; iPos++)
	{
		if (szUUID[iPos] == '-')
		{
			continue;
		}
		
		m_sHexUid[iNewPos++] = szUUID[iPos];
	}
	
	UINT64_T lnMost = GetMostSignificantBits();
	UINT64_T lnLeast = GetLeastSignificantBits();

	for(int iRightCnt = 0; iRightCnt < 12; iRightCnt++) 
	{
		szUidResult[iUidPos--] = m_sDecToBase64[(UINT32_T)(lnLeast & 0x1f)];
		lnLeast >>= 5;
	}
	
	UINT64_T lnLowMost = (lnMost & 0x01) << 4;
	szUidResult[iUidPos--] = m_sDecToBase64[(UINT32_T)(lnLowMost | lnLeast)];
	lnMost >>= 1;
	
	for(int iRightCnt = 0; iRightCnt < 12; iRightCnt++) 
	{
		szUidResult[iUidPos--] = m_sDecToBase64[(UINT32_T)(lnMost & 0x1f)];
		lnMost >>= 5;
	}

	lnMost = lnMost & 0x1f;
	szUidResult[iUidPos--] = m_sDecToBase64[lnMost];
	
	return szUidResult;	
}


