/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		TBizLogPerf.cpp
*Indentifier：
*		
*Description：
*		业务消息
*Version：
*		V1.0
*Author:
*		liyn
*Finished：
*		2014-06-26
*History:
********************************************/
#include <stddef.h>
#include "TLogPerf.h"


TLogPerf::TLogPerf()
{
	m_TS = 0;
	m_TE = 0;
	
}

TLogPerf::~TLogPerf()
{
	
}


//获取开始时间
void TLogPerf::GetTimeTS()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_TS = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	DCLOG_DEBUG("m_T0[%lu]",m_TS);
}


//获取结束时间
void TLogPerf::GetTimeTE()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_TE= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	DCLOG_DEBUG("m_TE[%lu]",m_TE);
}


void TLogPerf::GetTimeT1_B()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T1_B= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T1_B[%lu]",m_T1_B);

}

void TLogPerf::GetTimeT1_E()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T1_E= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T1_E[%lu]",m_T1_E);

}


void TLogPerf::GetTimeT2_B()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T2_B= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T2_B[%lu]",m_T2_B);
}

void TLogPerf::GetTimeT2_E()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T2_E= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T2_E[%lu]",m_T2_E);
}

void TLogPerf::GetTimeT3_B()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T3_B= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T3_B[%lu]",m_T3_B);
}

void TLogPerf::GetTimeT3_E()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T3_E= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T3_E[%lu]",m_T3_E);
}

void TLogPerf::GetTimeT4_B()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T4_B= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T4_B[%lu]",m_T1_B);
}

void TLogPerf::GetTimeT4_E()
{
	struct timeval tm;
	gettimeofday(&tm,NULL);
	m_T4_E= (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);

	DCLOG_DEBUG("m_T4_E[%lu]",m_T1_E);
}

int TLogPerf::composeMsg(char *msg,char *buf)
{
	//15:37:19.966013-->153719966
	buf[0]=0;
	struct timeval tm;
	gettimeofday(&tm,NULL);
	unsigned long timesdeal = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	
	sprintf(buf,"%18lu%s",timesdeal,msg);
	DCLOG_DEBUG("msg[%s]",buf);

	return 0;
}

int TLogPerf::composeMsg(char *type,char *msg,char *buf)
{
	//15:37:19.966013-->153719966
	buf[0]=0;
	int n = 0;
	struct timeval tm;
	gettimeofday(&tm,NULL);
	time_t cur = tm.tv_sec;
	struct tm *pt = NULL;
	pt = localtime(&cur);

	char time[12]={0};
	char hex[12]={0};
	sprintf(time, "%02d%02d%02d%06d", 
		pt->tm_hour,
		pt->tm_min, 
		pt->tm_sec,
		tm.tv_usec);
	
	sprintf(buf,"%s%9s%s",type,time,msg);
	DCLOG_DEBUG("msg[%s]",buf);

	return 0;
}

int TLogPerf::decomposeMsg(const char *msg,BoltMsg &boltMsg)
{
	return 0;
}	
   
