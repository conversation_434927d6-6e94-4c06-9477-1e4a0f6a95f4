/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqFmt.h
*Indentifier：
*
*Description：
*		消息解析类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_FMT_H__
#define __DC_REQ_FMT_H__


#include "DCBizMsgDef.h"
#include "DCOcpMsgDef.h"
#include "TSMPara.h"
using namespace ocs;


class DCReqFmt
{
	public:

		DCReqFmt();
		virtual ~DCReqFmt();

	public:

		int FormatCCR(void *data);


	protected:

		virtual int FormatRSU(STBizMsg* bizMsg);
		virtual int FormatUSU(STBizMsg* bizMsg,SCCRDataUnit *tmpUSU);
		virtual int str2type(const char* str,TSMPara *smpara);
		virtual int timestamp(char* buf,  time_t et);
		int sendCCA(STBizMsg* bizMsg);
		

	private:
		

		int FormatCommon(STBizMsg* bizMsg,SCCRBase* base);
		int CheckMustAVP(SCCRBase* base,STBizMsg* bizMsg);

		int FormatServiceInfo(void* data);

		int FormatVOICE(SCCRVOICE* voice,STBizMsg* bizMsg);
		int FormatDATA(SCCRDATA *data,STBizMsg* bizMsg);
		int FormatUserLocation(string rATType, ocs::userLoc &userLocInfoMation);
        int FormatDATA_5G(SCCRBase* base, STBizMsg* bizMsg, SCCR5GInfo* data);
		int FormatSMS(SCCRSMS *sms,STBizMsg* bizMsg);
		int FormatISMP(SCCRISMP *ismp,STBizMsg* bizMsg);
		int FormatDSL(SCCRDSL *dsl,STBizMsg* bizMsg);
		int FormatRATA(SCCRISMP *ismp,STBizMsg* bizMsg);
		int judgeCutCdr(STBizMsg* bizMsg);
		time_t tamptotime(unsigned long time);

		int GetAreaCode(SPhone & phone, STBizMsg * bizMsg);

		int sendCCAOffLine(STBizMsg* bizMsg);
		bool IfOtherProvinces(STBizMsg* bizMsg,string gsmCode);

};

#endif

