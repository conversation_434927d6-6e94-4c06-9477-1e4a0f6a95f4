#include "DCBizCdrNormalDATA.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "DCOcpMsgDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"
using namespace ocs;

static void selftimeAdd(char* begin_time,int seconds)
{
  int year,mon,day,hh,mm,ss;
  char temp[5] = {0};
  char szOldTime[15] = {0};
  struct tm tm1;
  struct tm tm2;
  time_t ltime;

  strcpy(szOldTime,begin_time);
  strncpy(temp,begin_time,4);

  year=atol(temp);
  year=year-1900;
  memset(temp,'\0',5);
  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;
  strncpy(temp,begin_time+6,2);

  day=atol(temp);
  strncpy(temp,begin_time+8,2);

  hh=atol(temp);
  strncpy(temp,begin_time+10,2);

  mm=atol(temp);
  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  memset(&tm1,0,sizeof(tm));

  tm1.tm_sec=ss;
  tm1.tm_min=mm;
  tm1.tm_hour=hh;
  tm1.tm_mday=day;
  tm1.tm_mon=mon;
  tm1.tm_year=year;

  ltime=mktime(&tm1);
  ltime+=seconds;

  localtime_r(&ltime,&tm2);

  sprintf(begin_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2.tm_year+1900,tm2.tm_mon+1,
            tm2.tm_mday,tm2.tm_hour,tm2.tm_min,tm2.tm_sec);

}

DCBizCdrNormalDATA::DCBizCdrNormalDATA()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormalDATA::~DCBizCdrNormalDATA()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

int DCBizCdrNormalDATA::ComposeDATA(STBizMsg* bizMsg)
{
	int ret = 0;

/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		return RET_SUCCESS;
	}
*/
	//查询所有子会话需要预处理的字段,保存在map中
	ret = SelectPretreatColumn(bizMsg);
	if(ret)
	{
		return ret;
	}


	if(bizMsg->m_serviceContextID == CCG)
	{

		ret = PretreatCCG(bizMsg);  // 按RG 出单
	}
	else
	{
		ret = PretreatAAA(bizMsg); //不按RG 出单
	}

	if(bizMsg->m_longCDR == 0)
	{

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "begin delete session ");
		DeleteSession(bizMsg);
	}

	return ret;
}

int DCBizCdrNormalDATA::SelectPretreatColumn(STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_256] = {0};
	char szSessionId[BIZ_TEMP_LEN_128] = {0};
	char szCdrSessionId[BIZ_TEMP_LEN_128] = {0};
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	m_mapCdrInfo.clear();
	int ret = 0;
	UDBSQL *pQuery = dbm->GetSQL(AAA_SelectSessionStoreCdr);

	try
	{
             if(bizMsg->m_serviceContextID == DATA)
             {
                    //AAA业务单RG
		     sprintf(szSessionId, "%s;%%", bizMsg->m_sessionID);
             }
             else if(1 == bizMsg->m_longCDR || 2 == bizMsg->m_longCDR || 5 == bizMsg->m_longCDR)
             {
                    // CCG 超长截单，只出当前RG
                     memset(szSessionId,0,sizeof(szSessionId));
        		if(strlen(bizMsg->m_ProductOfferId)>0)
        		{
        			sprintf(szSessionId, "%s;%s", bizMsg->m_sessionID, bizMsg->m_ProductOfferId);
                             strcpy(bizMsg->m_childsessionID, szSessionId);
        		}
        		else if(bizMsg->m_ratingGroup > 0)
        		{
        			sprintf(szSessionId, "%s;%lld", bizMsg->m_sessionID, bizMsg->m_ratingGroup);
                             strcpy(bizMsg->m_childsessionID, szSessionId);
        		}
        		else
        		{
        			sprintf(szSessionId, "%s", bizMsg->m_childsessionID);
        		}
             }
             else
             {
                    // CCG 全部RG 出单
                    sprintf(szSessionId, "%s;%%", bizMsg->m_sessionID);
             }

		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, szSessionId);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "select child session[%s]", szSessionId);

		//查询子会话
		pQuery->Execute();
		while(pQuery->Next())
		{

			pQuery->GetValue(28, value);
			int sessionFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionflag [%s]", value );
			if(0==sessionFlag)
			{

				pQuery->GetValue(29, value);
				bizMsg->m_serviceContextID = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "service type is[%s](ps: AAA is 3,CCG is 7)", value);
				continue;//是主会话，不组装会话信息
			}

			//话单信息组装
			DataCDRInfo stCdrInfo;
			memset(&stCdrInfo,0x00,sizeof(DataCDRInfo));


			//RE_INT_ROAM_TYPE
			pQuery->GetValue(1, value);
			stCdrInfo.nRomatype = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.nRomatype [%d]", stCdrInfo.nRomatype );

			//SM_INT_CHARGE_TYPE 替换为RE_INT_LAST_GSU_UNIT
			pQuery->GetValue(2, value);
			stCdrInfo.nChargeType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.nChargeType [%d]", stCdrInfo.nChargeType );

			//CDR_PUB_STR_BALANCEINFO
			pQuery->GetValue(3, value);
			strcpy(stCdrInfo.balanceInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.balanceInfo [%s]", stCdrInfo.balanceInfo );

			//CDR_PUB_STR_BALANCEINFO2
			pQuery->GetValue(4, value);
			strcpy(stCdrInfo.balanceInfo2,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.balanceInfo2 [%s]", stCdrInfo.balanceInfo2 );

			//CDR_PUB_STR_ACCUMLATORINFO
			pQuery->GetValue(5, stCdrInfo.accumuInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.accumuInfo [%s]", stCdrInfo.accumuInfo );

			//CDR_PUB_STR_TARIFFID
			pQuery->GetValue(6, stCdrInfo.tarifInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.tarifInfo [%s]", stCdrInfo.tarifInfo );

			//CDR_PUB_STR_CHARGEINFO
			pQuery->GetValue(7, value);
			strcpy(stCdrInfo.chargeInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.chargeInfo [%s]", stCdrInfo.chargeInfo );

			//SM_LNG_ALL_USU_TIME
			pQuery->GetValue(8, value);
			stCdrInfo.duration = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.duration[%ld]", stCdrInfo.duration);

			//SM_LNG_ALL_USU_TOTAL_OCT
			pQuery->GetValue(9, value);
			stCdrInfo.total = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.total[%ld]", stCdrInfo.total);

			//SM_LNG_ALL_USU_INPUT_OCT
			pQuery->GetValue(10, value);
			stCdrInfo.input = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.input[%ld]", stCdrInfo.input);

			//SM_LNG_ALL_USU_OUTPUT_OCT
			pQuery->GetValue(11, value);
			stCdrInfo.output = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.output[%ld]", stCdrInfo.output);

			//OCP_INT_RATING_GROUP
			pQuery->GetValue(12, value);
			strcpy(stCdrInfo.rating,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.rating[%s]", stCdrInfo.rating);

			//OCS_SM_CDR_VERSION_SEQ_ID.nextval
			pQuery->GetValue(13, value);
			stCdrInfo.lCdrVersionSerial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCS_SM_CDR_VERSION_SEQ_ID.nextval[%ld]", stCdrInfo.lCdrVersionSerial);

			//CDR_DISCOUNT_FEE
			pQuery->GetValue(14, value);
			stCdrInfo.discount_fee = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "discount_fee[%ld]", stCdrInfo.discount_fee);

			//CDR_PUB_STR_PRICING_PLAN_ID
			pQuery->GetValue(15, value);
			strcpy(stCdrInfo.planInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "planInfo[%s]", stCdrInfo.planInfo);

			if(1 == bizMsg->m_longCDR || 2 == bizMsg->m_longCDR) //超长话单的，会话表中结果码为0，这里需要特殊处理为2001，term时候才更新为2001
			{
				stCdrInfo.nResultCode = 2001;
			}
			else
			{
				//SM_INT_RESULT_CODE
				pQuery->GetValue(17, value);
				stCdrInfo.nResultCode= atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nResultCode[%d]", stCdrInfo.nResultCode);
			}
			pQuery->GetValue(18, value);
			strcpy(stCdrInfo.szProductOfferId,value);//OCP_STR_PRODUCT_OFFER_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "product offer id[%s]", stCdrInfo.szProductOfferId);

			pQuery->GetValue(19, value); //OCP_STR_SESSION_ID
			strcpy(stCdrInfo.sessionId,value);
			strcpy(szCdrSessionId,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SessionId[%s]", szCdrSessionId);

			//CDR_LNG_ALL_USU_OUTPUT_OCT
			pQuery->GetValue(23, value);
			stCdrInfo.usedoutput= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedoutput[%ld]", stCdrInfo.usedoutput);

			//CDR_LNG_ALL_USU_INPUT_OCT
			pQuery->GetValue(24, value);
			stCdrInfo.usedinput= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedinput[%ld]", stCdrInfo.usedinput);

			//CDR_LNG_ALL_USU_TOTAL_OCT
			pQuery->GetValue(25, value);
			stCdrInfo.usedtotal= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedtotal[%ld]", stCdrInfo.usedtotal);

			//CDR_LNG_ALL_USU_TIME
			pQuery->GetValue(26, value);
			stCdrInfo.usedduration= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedduration[%ld]", stCdrInfo.usedduration);
			pQuery->GetValue(27, value);
			strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionCurrent[%s]", value);
			stCdrInfo.Day[0] = value[6];
			stCdrInfo.Day[1] = value[7];
			stCdrInfo.Day[2] = '\0';
			int day = atoi(stCdrInfo.Day);
			sprintf(stCdrInfo.Day,"%d",day);
			pQuery->GetValue(36, value);
			stCdrInfo.Payment= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "Payment[%ld]", stCdrInfo.Payment);

			if(stCdrInfo.Payment == 0)
			{
				strcpy(value,stCdrInfo.sessionCurrent);
				value[6] = '\0';
				stCdrInfo.Payment= atol(value);
			}
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "Day[%s]", stCdrInfo.Day);

			pQuery->GetValue(30, value);
			strncpy(stCdrInfo.sessionStart,value,sizeof(stCdrInfo.sessionStart));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionStart[%s]", value);


			//CDR_PUB_INT_SEQ
			pQuery->GetValue(32, value);
			stCdrInfo.seqNum= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "seqNum[%d]", stCdrInfo.seqNum);

			pQuery->GetValue(33, value);
			stCdrInfo.PayFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "payflag[%d]", stCdrInfo.PayFlag);

                     pQuery->GetValue(34, value);
                     strcpy(stCdrInfo.userLocationInfo, value);
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userLocationInfo[%s]", stCdrInfo.userLocationInfo);

            pQuery->GetValue(35, value);
            strcpy(stCdrInfo.orichargeInfo, value);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "orichargeInfo[%s]", value);


			//话单信息保存到map
			if(strlen(stCdrInfo.szProductOfferId))
			{
				m_mapCdrInfo.insert(pair<string, DataCDRInfo>(stCdrInfo.szProductOfferId, stCdrInfo));
			}
			else
			{
				m_mapCdrInfo.insert(pair<string, DataCDRInfo>(stCdrInfo.rating, stCdrInfo));
			}
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "select DATA execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	if(0 == m_mapCdrInfo.size())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child no exist rg[%ld],reqytpe[%d],reqnum[%d]", bizMsg->m_ratingGroup,bizMsg->m_requestType,bizMsg->m_requestNumber);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session num[%d]", m_mapCdrInfo.size());
	}

	return 0;
}

int DCBizCdrNormalDATA::PutCdr_CCG(STBizMsg* bizMsg, DataCDRInfo &datacdr,string & rg)
{
	int ret = 0;
	vector<SCDRField*> * field = TCDRDict::instance()->GetDATAField();
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	vector<STariffAccumCDRInfo> vecTaiAuCdr;
	vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

	//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
	ParaseTariffAccumCdrInfo(bizMsg,datacdr,vecTaiAuCdr);

	long nLeftDuration = datacdr.duration;
	long nDuration = datacdr.duration;

	long nLeftTotal = datacdr.total;
	long nTotal = datacdr.total;
	long nInPut = datacdr.input;
	long nOutPut = datacdr.output;
	long nLeftInput = nInPut;

	long nStartTime = atol(datacdr.sessionStart);
	long nCurrTime = atol(datacdr.sessionCurrent);
	//生成剃重索引文件
	//DCCdrIndex cdrIndex;
	//cdrIndex.SetIndexInfo(bizMsg,datacdr.sessionStart,datacdr.sessionCurrent);

	UDBSQL *pQuery = dbm->GetSQL(COM_CDR_DATA);
	try
	{
		string  sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, datacdr.sessionId);
		pQuery->Execute();
		int i = 1;
		if(pQuery->Next())
		{
			int cdrSeqNum=0;
			if(datacdr.seqNum > 0)
			{
				cdrSeqNum = datacdr.seqNum;
			}

			if(vecTaiAuCdr.size() > 0)
			{
				long nSize = vecTaiAuCdr.size();
				long nIndex = 0;
				for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
				{
					nIndex ++ ;
					cdrSeqNum++;
					SCDRData cdr = {0};
					datacdr.nCutnum = nSize;

					if(nIndex < nSize)
					{
						if(TaiAuCdrIter->measure == 3)
						{
							datacdr.total = TaiAuCdrIter->billingDuration;
							nLeftTotal -= TaiAuCdrIter->billingDuration;
							if(nLeftTotal<0) nLeftTotal = 0;

							if(datacdr.total == 0)
							{
								datacdr.duration = 0;
								datacdr.input = 0;
								datacdr.output = 0;
							}
							else if(nLeftTotal == 0)
							{
								datacdr.duration = nLeftDuration;
								datacdr.input = nLeftInput;

								if(datacdr.total < datacdr.input)
								{
									datacdr.input = datacdr.total;
								}
								datacdr.output = datacdr.total - datacdr.input;
							}
							else
							{
								datacdr.duration = Div(nDuration*(long)datacdr.total,  nTotal, '3');
								datacdr.input = Div(nInPut*(long)datacdr.total,  nTotal, '3');
								datacdr.output = datacdr.total - datacdr.input;
							}
							nLeftInput -= datacdr.input;
							if(nLeftInput < 0) nLeftInput = 0;

							nLeftDuration -= datacdr.duration;
							if(nLeftDuration < 0) nLeftDuration = 0;

						}
						else if (TaiAuCdrIter->measure == 1)
						{
							datacdr.duration = TaiAuCdrIter->billingDuration;
							nLeftDuration -= TaiAuCdrIter->billingDuration;
							if(nLeftDuration < 0)
								nLeftDuration = 0;
						}
					}
					else
					{
						//最后一个
						if(TaiAuCdrIter->measure == 3)
						{
							datacdr.total = nLeftTotal;
							datacdr.duration = nLeftDuration;
							datacdr.input = nLeftInput > nLeftTotal ? nLeftTotal : nLeftInput;
							datacdr.output = datacdr.total - datacdr.input;
						}
						else if (TaiAuCdrIter->measure == 1)
						{
							datacdr.duration = nLeftDuration;
						}

					}

					if(nSize > 1)
					{
						// 重新计算结束时间
						strcpy(datacdr.sessionCurrent, datacdr.sessionStart);
						selftimeAdd(datacdr.sessionCurrent, (int)datacdr.duration);
					}

					ComposeCCGCDR(bizMsg,datacdr,pQuery,*TaiAuCdrIter,field,cdrSeqNum,rg.c_str(),cdr);
					int len = strlen(cdr.m_body);
					/*
					cdr.m_body[len - 1] = '\r';
					cdr.m_body[len] = '\n';
					cdr.m_body[len + 1] = '\0';
					*/
					cdr.m_body[len - 1] = '\0';
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr field:\n[%s]", cdr.m_body);
					//发送话单数据
					UCDRData scdr;
					scdr.body = cdr.m_body;
					ProduceCdr( bizMsg, scdr,datacdr.PayFlag);

					if(nSize > 1)
					{
						// 设置下次开始时间
						strcpy(datacdr.sessionStart, datacdr.sessionCurrent);
					}
				}
			}
			else
			{
				cdrSeqNum++;
				SCDRData cdr = {0};

                STariffAccumCDRInfo TaiAuCdrInfo;
                DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
                SCommonPara* commonPara = smpara->GetCommonPara();
                char fence = commonPara->CdrFence;
                SOriChargeInfo tmpOriChargeInfo[100];//扩大一点。避免因数据异常导致core
                //增强实扣为0的acct_item_Id
                DCCommonIF::ParseOriChargeInfo(datacdr.orichargeInfo,tmpOriChargeInfo,fence);
                TaiAuCdrInfo.acctItemId = tmpOriChargeInfo[0].acctItemId;
				TaiAuCdrInfo.basefee = tmpOriChargeInfo[0].oriAmount;

                datacdr.nCutnum = 1;
				ComposeCCGCDR(bizMsg,datacdr,pQuery,TaiAuCdrInfo,field,cdrSeqNum,rg.c_str(),cdr);
				int len = strlen(cdr.m_body);
				//cdr.m_body[len - 1] = '\r';
				//cdr.m_body[len] = '\n';
				//cdr.m_body[len + 1] = '\0';
				cdr.m_body[len - 1] = '\0';
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr field:\n[%s]", cdr.m_body);
				//发送话单数据
				UCDRData scdr;
				scdr.body = cdr.m_body;
				ProduceCdr(bizMsg, scdr,datacdr.PayFlag);
			}

			datacdr.seqNum = cdrSeqNum;
			datacdr.total = nTotal;
                     datacdr.input = nInPut;
                     datacdr.output = nOutPut;
			datacdr.duration = nDuration;

                     sprintf(datacdr.sessionStart, "%ld", nStartTime);
                     sprintf(datacdr.sessionCurrent, "%ld", nCurrTime);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "cdr child session is not exist[%s]", datacdr.sessionId);
			return 0;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "select  execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return 0;
}


int DCBizCdrNormalDATA::PutCdr_AAA(STBizMsg* bizMsg, DataCDRInfo &datacdr,string & rg)
{
	int ret = 0;
	vector<SCDRField*> * field = TCDRDict::instance()->GetDATAField();
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	vector<STariffAccumCDRInfo> vecTaiAuCdr;
	vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

	//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
	ParaseTariffAccumCdrInfo(bizMsg,datacdr,vecTaiAuCdr);

	long nLeftDuration = datacdr.duration;
	long nDuration = datacdr.duration;

	long nLeftTotal = datacdr.total;
	long nTotal = datacdr.total;
       long nInPut = datacdr.input;
       long nOutPut = datacdr.output;
       long nLeftInput = nInPut;

       long nStartTime = atol(datacdr.sessionStart);
       long nCurrTime = atol(datacdr.sessionCurrent);


	UDBSQL *pQuery = dbm->GetSQL(COM_CDR_DATA);
	try
	{
		string  sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, datacdr.sessionId);
		pQuery->Execute();

		if(pQuery->Next())
		{
			int cdrSeqNum=0;

			if(vecTaiAuCdr.size() > 0)
			{
				long nSize = vecTaiAuCdr.size();
				long nIndex = 0;
				for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
				{
					nIndex ++ ;
					cdrSeqNum++;
					SCDRData cdr = {0};
					datacdr.nCutnum = nSize;

					if(nIndex < nSize)
					{
						if(TaiAuCdrIter->measure == 3)
						{
							datacdr.total = TaiAuCdrIter->billingDuration;
							nLeftTotal -= TaiAuCdrIter->billingDuration;
                                                 if(nLeftTotal <0) nLeftTotal = 0;

                                                 if(datacdr.total == 0)
                                                  {
                                                           datacdr.duration = 0;
                                                           datacdr.input = 0;
                                                           datacdr.output = 0;
                                                  }
                                                  else if(nLeftTotal == 0)
                                                  {
                                                           datacdr.duration = nLeftDuration;
                                                           datacdr.input = nLeftInput;

                                                           if(datacdr.total < datacdr.input)
                                                           {
                                                                   datacdr.input = datacdr.total;
                                                           }
                                                           datacdr.output = datacdr.total - datacdr.input;
                                                  }
                                                  else
                                                  {
                                                           datacdr.duration = Div(nDuration*(long)datacdr.total,  nTotal, '3');
                                                           datacdr.input = Div(nInPut*(long)datacdr.total,  nTotal, '3');
                                                           datacdr.output = datacdr.total - datacdr.input;
                                                  }
                                                  nLeftInput -= datacdr.input;
                                                  if(nLeftInput < 0) nLeftInput = 0;

                                                  nLeftDuration -= datacdr.duration;
                                                  if(nLeftDuration < 0) nLeftDuration = 0;

						}
						else if (TaiAuCdrIter->measure == 1)
						{
							datacdr.duration = TaiAuCdrIter->billingDuration;
							nLeftDuration -= TaiAuCdrIter->billingDuration;
                                                 if(nLeftDuration < 0) nLeftDuration = 0;
						}
					}
					else
					{
						//最后一个
						if(TaiAuCdrIter->measure == 3)
						{
							datacdr.total = nLeftTotal;
                                                 datacdr.duration = nLeftDuration;
                                                 datacdr.input = nLeftInput > nLeftTotal ? nLeftTotal : nLeftInput;
                                                 datacdr.output = datacdr.total - datacdr.input;
						}
						else if (TaiAuCdrIter->measure == 1)
						{
							datacdr.duration = nLeftDuration;
						}

					}

                                   if(nSize > 1)
                                   {
                                            // 重新计算结束时间
                                            strcpy(datacdr.sessionCurrent, datacdr.sessionStart);
                                            selftimeAdd(datacdr.sessionCurrent, (int)datacdr.duration);
                                   }

					ComposeAAACDR(bizMsg,datacdr,pQuery,*TaiAuCdrIter,field,cdrSeqNum,rg.c_str(),cdr);
					int len = strlen(cdr.m_body);
					/*
					cdr.m_body[len - 1] = '\r';
					cdr.m_body[len] = '\n';
					cdr.m_body[len + 1] = '\0';
					*/
					cdr.m_body[len - 1] = '\0';
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr field:\n[%s]", cdr.m_body);

					//发送话单数据
					UCDRData scdr;
					scdr.body = cdr.m_body;
					ProduceCdr( bizMsg, scdr,datacdr.PayFlag);

                   if(nSize > 1)
                   {
                        // 设置下次开始时间
                        strcpy(datacdr.sessionStart, datacdr.sessionCurrent);
                   }
				}
			}
			else
			{
				cdrSeqNum++;
				SCDRData cdr = {0};
				STariffAccumCDRInfo TaiAuCdrInfo;

                DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
                SCommonPara* commonPara = smpara->GetCommonPara();
                char fence = commonPara->CdrFence;
                SOriChargeInfo tmpOriChargeInfo[100];//扩大一点。避免因数据异常导致core
                //增强实扣为0的acct_item_Id
                DCCommonIF::ParseOriChargeInfo(datacdr.orichargeInfo,tmpOriChargeInfo,fence);
                TaiAuCdrInfo.acctItemId = tmpOriChargeInfo[0].acctItemId;
				TaiAuCdrInfo.basefee = tmpOriChargeInfo[0].oriAmount;
				
				datacdr.nCutnum = 0;
				ComposeAAACDR(bizMsg,datacdr,pQuery,TaiAuCdrInfo,field,cdrSeqNum,rg.c_str(),cdr);
				int len = strlen(cdr.m_body);
				//cdr.m_body[len - 1] = '\r';
				//cdr.m_body[len] = '\n';
				//cdr.m_body[len + 1] = '\0';
				cdr.m_body[len - 1] = '\0';
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr field:\n[%s]", cdr.m_body);

				//发送话单数据
				UCDRData scdr;
				scdr.body = cdr.m_body;
				ProduceCdr( bizMsg, scdr,datacdr.PayFlag);
			}
			datacdr.seqNum = cdrSeqNum;

			datacdr.total = nTotal;
                     datacdr.input = nInPut;
                     datacdr.output = nOutPut;
			datacdr.duration = nDuration;

                     sprintf(datacdr.sessionStart, "%ld", nStartTime);
                     sprintf(datacdr.sessionCurrent, "%ld", nCurrTime);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "cdr child session is not exist[%s]", datacdr.sessionId);
			return 0;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "select  execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return 0;
}

// 不按RG 出单
int DCBizCdrNormalDATA::PretreatAAA(STBizMsg* bizMsg)
{
	int roamType = 0;
	long ratingGroup = 0;
	SCCRDataUnit USU;
	char rating[BIZ_TEMP_LEN_1024] = {0};
	int length = 0;
	char vlr[32]={0};
	char ChildsessionID[BIZ_TEMP_LEN_256] = {0};
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	char fence = smpara->GetCommonPara()->CdrFence;


	//规整话单字段
	DataCDRInfo totalCDR;
	//memset(&totalCDR, 0x0, sizeof(totalCDR));

	map<string, DataCDRInfo>::iterator iter2;

	for(iter2 = m_mapCdrInfo.begin(); iter2 != m_mapCdrInfo.end(); iter2++)
	{
		SCCRDataUnit TUSU;
		totalCDR.nChargeType = iter2->second.nChargeType;
		totalCDR.nRomatype  =  iter2->second.nRomatype;
		strncpy(totalCDR.Day ,iter2->second.Day, sizeof(totalCDR.Day));
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "Day[%s]",totalCDR.Day);

		sprintf(totalCDR.sessionId, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child[%s]",totalCDR.sessionId);

		//使用量信息
		TUSU.duration = iter2->second.duration;
		TUSU.unitTotal = iter2->second.total;
		TUSU.unitInput = iter2->second.input;
		TUSU.unitOutput = iter2->second.output;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
			iter2->second.rating,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

		TUSU.duration -= iter2->second.usedduration;
		TUSU.unitTotal -= iter2->second.usedtotal;
		TUSU.unitInput -= iter2->second.usedinput;
		TUSU.unitOutput -= iter2->second.usedoutput;

              iter2->second.outputtemp=TUSU.unitOutput;
		iter2->second.inputtemp=TUSU.unitInput;
		iter2->second.totaltemp=TUSU.unitTotal;
		iter2->second.durationtemp=TUSU.duration;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg[%s],usu:out[%ld], in[%ld], total[%ld], duration[%d] used:out[%ld], in[%ld], total[%ld], duration[%d] ",
			iter2->second.rating,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration,iter2->second.usedoutput,iter2->second.usedinput,
			iter2->second.usedtotal,iter2->second.usedduration);


		if((TUSU.duration <= 0) && (TUSU.unitTotal <= 0) )
		{
                      iter2->second.duration=0;
		        iter2->second.total=0;
		        iter2->second.input=0;
		        iter2->second.output=0;
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "skip cdr, rg[%s], duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]",  iter2->second.rating,TUSU.duration, TUSU.unitTotal, TUSU.unitInput, TUSU.unitOutput);
			continue;
		}

              //流量计费时长修正
              if(iter2->second.nChargeType != 1)
              {
                    long nEndTime = DCCommonIF::dateToSec(iter2->second.sessionCurrent);
		      long nBeginTime = DCCommonIF::dateToSec(iter2->second.sessionStart);
		      long nDuration = nEndTime - nBeginTime ;
                    TUSU.duration = nDuration < 0 ? 0 : nDuration;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "fix duration, rg[%s], duration[%d]", iter2->second.rating, TUSU.duration);
              }

              strcpy(totalCDR.sessionStart, iter2->second.sessionStart);
              strcpy(totalCDR.sessionCurrent, iter2->second.sessionCurrent);

              // 赋值
              iter2->second.duration=TUSU.duration;
		iter2->second.total=TUSU.unitTotal;
		iter2->second.input=TUSU.unitInput;
		iter2->second.output=TUSU.unitOutput;

		// 总使用量
		USU.duration += TUSU.duration;
		USU.unitTotal += TUSU.unitTotal;
		USU.unitInput += TUSU.unitInput;
		USU.unitOutput += TUSU.unitOutput;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"total:out[%ld], in[%ld], total[%ld], duration[%d]",
			USU.unitOutput, USU.unitInput,USU.unitTotal,USU.duration);

		totalCDR.discount_fee += iter2->second.discount_fee;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"discountfee[%llu],total discountfee[%llu]", iter2->second.discount_fee, totalCDR.discount_fee);

		//扣费信息
		GetBalanceInfo(totalCDR.balanceInfo, iter2->second.balanceInfo);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"balanceInfo[%s],balanceInfoALL[%s]", iter2->second.balanceInfo, totalCDR.balanceInfo);

		GetAccumulateInfo(totalCDR.accumuInfo, iter2->second.accumuInfo);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"accumlatorInfo[%s],accumlatorInfoALL[%s]", iter2->second.accumuInfo,totalCDR.accumuInfo);

		GetTariffInfo(totalCDR.tarifInfo, iter2->second.tarifInfo);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"tariffIdInfo[%s],tariffIdInfoALL[%s]", iter2->second.tarifInfo,totalCDR.tarifInfo);

		GetChargeInfo(totalCDR.chargeInfo, iter2->second.chargeInfo);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"chargeInfo[%s],balanceInfoALL[%s]", iter2->second.chargeInfo,totalCDR.chargeInfo);

		GetBlanceInfo2(totalCDR.balanceInfo2, iter2->second.balanceInfo2);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"balanceInfo2[%s],balanceInfo2ALL[%s]", iter2->second.balanceInfo2,totalCDR.balanceInfo2);

		CombinePlanIDInfo(totalCDR.planInfo, iter2->second.planInfo);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"pricingPlanID[%s],pricingPlanIDALL[%s]", iter2->second.planInfo,totalCDR.planInfo);
		
		GetOriChargeInfo(totalCDR.orichargeInfo, iter2->second.orichargeInfo);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID,
			"orichargeInfo[%s],orichargeInfoALL[%s]", iter2->second.orichargeInfo,totalCDR.orichargeInfo);

		sprintf(ChildsessionID, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "find child used,session[%s]",ChildsessionID);

		if(length < 120)
		{
			length += sprintf(rating+length, "%s:%u%c%lld%c%lld%c%lld;", iter2->second.rating, TUSU.duration, fence, TUSU.unitInput, fence, TUSU.unitOutput, fence, TUSU.unitTotal);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg:%ld, dura:%u, input:%lld, output:%lld, total:%lld", ratingGroup, TUSU.duration, TUSU.unitInput, TUSU.unitOutput, TUSU.unitTotal);
		}
		totalCDR.Payment=iter2->second.Payment;

		totalCDR.seqNum = iter2->second.seqNum;
		totalCDR.PayFlag= iter2->second.PayFlag;

		// 账目类型和费用字段
		ParaseFeeItemByRG(bizMsg,totalCDR);


	}

	if(strlen(rating) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "DATA short cdr", "");
		return RET_SUCCESS;
	}
	strncpy(totalCDR.rating, rating, sizeof(totalCDR.rating));
	totalCDR.rating[sizeof(totalCDR.rating)-1] = 0x0;

	//规整字段信息
	//charge type
	if(1 == totalCDR.nChargeType)//时长
	{
		totalCDR.nChargeType = 2;
	}
	else//流量
	{
		totalCDR.nChargeType = 3;
	}


	//romatype
	roamType =  totalCDR.nRomatype;
	switch(roamType) //漫游
	{
		case 0://非漫游
			roamType = 0;
			break;
		case 1://省内漫游
			roamType = 1;
			break;
		case 3://省际漫游来访
			roamType = 2;
			break;
		case 4://省际漫游出访
			roamType = 5;
			break;
		case 5://国际漫游来访
			roamType = 7;
			break;
		case 6://国际漫游出访
			roamType = 4;
			break;
		case 7://省内边界漫游
			roamType = 8;
			break;
		case 8://省际边界漫游
			roamType = 9;
			break;
		case 9://港澳台漫游
			roamType = 3;
			break;
		default:
			break;
	}
	totalCDR.nRomatype = roamType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr Romatype[%d]", totalCDR.nRomatype);
	strcpy(totalCDR.szVlr, vlr);


	//流量基础单位转换(KB)
	if(1 == smpara->GetPSPara()->iRGUNITSwitch)
	{
              USU.unitInput = (USU.unitInput + 1023)/1024;
		USU.unitOutput = (USU.unitOutput + 1023)/1024;
              USU.unitTotal = (USU.unitTotal + 1023)/1024;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "change USU duration :%u, Input:%lld, Output:%lld, Total:%lld", USU.duration, USU.unitInput, USU.unitOutput, USU.unitTotal);
	}

	totalCDR.duration = USU.duration;
	totalCDR.input = USU.unitInput;
	totalCDR.output = USU.unitOutput;
	totalCDR.total = USU.unitTotal;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TotalCDR Info duration :%u,Input:%lld, Output:%lld, Total:%lld", totalCDR.duration, totalCDR.input, totalCDR.output, totalCDR.total);
	//累积量信息单位
	if(1 == smpara->GetPSPara()->iAccuUNITSwitch)
	{
		ChangeAmoutUnit(totalCDR.accumuInfo, smpara);
	}

    strcpy(totalCDR.orichargeInfo, iter2->second.orichargeInfo);

	//账目类型和费用字段
	string rg = "";//iter2->first;
	PutCdr_AAA(bizMsg, totalCDR,rg);

	for(iter2 = m_mapCdrInfo.begin(); iter2 != m_mapCdrInfo.end(); iter2++)
	{
		ChildsessionID[0]=0;
		sprintf(ChildsessionID, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);


		if(!iter2->second.output && !iter2->second.input && !iter2->second.total && !iter2->second.duration)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU=0,skip", "");
			continue;
		}
		UDBSQL *pExe = dbm->GetSQL(DATA_CDR_UpdateCdrSession);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				// 更新使用量信息到CDR_PGW_SESSION_STORE
				pExe->DivTable(bizMsg->m_sessionID);
				pExe->UnBindParam();
				pExe->BindParam(1, (long)iter2->second.outputtemp);
				pExe->BindParam(2, (long)iter2->second.inputtemp);
				pExe->BindParam(3, (long)iter2->second.totaltemp);
				pExe->BindParam(4, (long)iter2->second.durationtemp);
				pExe->BindParam(5, iter2->second.sessionCurrent);
				pExe->BindParam(6, totalCDR.seqNum);
				pExe->BindParam(7, "");
				pExe->BindParam(8, ChildsessionID);
				pExe->Execute();
				pExe->Connection()->Commit();
				success = true;
			}
			catch (UDBException &e)
			{
				pExe->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
				return RB_SM_UNABLE_TO_COMPLY;
			}
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update SM_PGW_SESSION_STORE out[%ld], in[%ld], total[%ld], duration[%ld]", (long)iter2->second.output
			,(long)iter2->second.input,(long)iter2->second.total,(long)iter2->second.duration);

		//清空相关信息并且更新序列
		updateCdrInfo(bizMsg,ChildsessionID);
	}



	//info 日志
	DCDATLOG("SM00015:%d%s%s%ld%s%s%s%s%s", bizMsg->m_longCDR,totalCDR.sessionStart, totalCDR.sessionCurrent,\
							totalCDR.total,totalCDR.planInfo, totalCDR.tarifInfo, totalCDR.chargeInfo, totalCDR.accumuInfo, totalCDR.orichargeInfo);
	return 0;
}


//按照RG出单,不出超长话单
int DCBizCdrNormalDATA::PretreatCCG(STBizMsg* bizMsg)
{
	long ratingGroup = 0;
	SCCRDataUnit TUSU;
	SCCRDataUnit USU;
	char vlr[32]={0};
	long long lCdrVersionSerial = 1;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	//规整话单字段
	map<string, DataCDRInfo>::iterator iter2;
	for(iter2 = m_mapCdrInfo.begin(); iter2 != m_mapCdrInfo.end(); iter2++)
	{
		//使用量信息
		TUSU.duration = iter2->second.duration;
		TUSU.unitTotal = iter2->second.total;
		TUSU.unitInput = iter2->second.input;
		TUSU.unitOutput = iter2->second.output;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total:out[%ld], in[%ld], total[%ld], duration[%d]", TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);
		TUSU.duration -= iter2->second.usedduration;
		TUSU.unitTotal -= iter2->second.usedtotal;
		TUSU.unitInput -= iter2->second.usedinput;
		TUSU.unitOutput -= iter2->second.usedoutput;

              iter2->second.outputtemp=TUSU.unitOutput;
		iter2->second.inputtemp=TUSU.unitInput;
		iter2->second.totaltemp=TUSU.unitTotal;
		iter2->second.durationtemp=TUSU.duration;

		// discountfee
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "discountfee[%llu]", iter2->second.discount_fee);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usu:out[%ld], in[%ld], total[%ld], duration[%d] used:out[%ld], in[%ld], total[%ld], duration[%d] ",
			TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration,iter2->second.usedoutput,iter2->second.usedinput,
			iter2->second.usedtotal,iter2->second.usedduration);
		//扣费信息

		//balanceInfo
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "balanceInfo[%s]", iter2->second.balanceInfo);

		//accumlatorInfo
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "accumlatorInfo[%s]", iter2->second.accumuInfo);

		//tarifInfo
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "tariffIdInfo[%s]", iter2->second.tarifInfo);

		//chargeInfo
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "chargeInfo[%s]", iter2->second.chargeInfo);

		//balanceInfo2
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "balanceInfo2[%s]", iter2->second.balanceInfo2);

		//planInfo
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "planinfo[%s]", iter2->second.planInfo);


		ratingGroup = atol(iter2->second.rating);
		if(iter2->second.szProductOfferId[0])
		{
			sprintf(iter2->second.sessionId, "%s;%s", bizMsg->m_sessionID, iter2->second.szProductOfferId);
			strcpy(iter2->second.rating, iter2->second.szProductOfferId);
		}
		else
		{
			sprintf(iter2->second.sessionId, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "find child used,session[%s]",iter2->second.sessionId);

		if((TUSU.duration <= 0) && (TUSU.unitTotal <= 0) )
		{
                      iter2->second.output = 0;
		        iter2->second.input = 0;
		        iter2->second.total = 0;
		        iter2->second.duration = 0;
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "skip cdr, rg[%s], duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]",  iter2->second.rating, TUSU.duration, TUSU.unitTotal, TUSU.unitInput, TUSU.unitOutput);
			continue;
		}

              //流量计费时长修正
              if(iter2->second.nChargeType != 1)
              {
                    long nEndTime = DCCommonIF::dateToSec(iter2->second.sessionCurrent);
		      long nBeginTime = DCCommonIF::dateToSec(iter2->second.sessionStart);
		      long nDuration = nEndTime - nBeginTime ;
                    TUSU.duration = nDuration < 0 ? 0 : nDuration;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "fix duration, rg[%s], duration[%d]", iter2->second.rating, TUSU.duration);
              }

		iter2->second.output = TUSU.unitOutput;
		iter2->second.input = TUSU.unitInput;
		iter2->second.total = TUSU.unitTotal;
		iter2->second.duration = TUSU.duration;
		//流量基础单位转换(KB)
		if(1 == smpara->GetPSPara()->iRGUNITSwitch)
		{
                     iter2->second.input = (iter2->second.input + 1023)/1024;
			iter2->second.output = (iter2->second.output + 1023)/1024;
                     iter2->second.total = (iter2->second.total + 1023)/1024;
		}

		//累积量信息单位
		if(1 == smpara->GetPSPara()->iAccuUNITSwitch)
		{
			ChangeAmoutUnit(iter2->second.accumuInfo, smpara);
		}

		//账目类型和费用字段
		ParaseFeeItemByRG(bizMsg,iter2->second);


		//规整字段信息
		//charge type
		if(1 == iter2->second.nChargeType)//时长
		{
				iter2->second.nChargeType = 2;
		}
		else//流量
		{
				iter2->second.nChargeType = 3;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "ChargeType[%d]", iter2->second.nChargeType);

		//romatype
		int roamType =  iter2->second.nRomatype;
		switch(roamType) //漫游
		{
		case 0://非漫游
			roamType = 0;
			break;
		case 1://省内漫游
			roamType = 1;
			break;
		case 3://省际漫游来访
			roamType = 2;
			break;
		case 4://省际漫游出访
			roamType = 5;
			break;
		case 5://国际漫游来访
			roamType = 7;
			break;
		case 6://国际漫游出访
			roamType = 4;
			break;
		case 7://省内边界漫游
			roamType = 8;
			break;
		case 8://省际边界漫游
			roamType = 9;
			break;
		case 9://港澳台漫游
			roamType = 3;
			break;
		default:
			break;
		}
		iter2->second.nRomatype = roamType;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "Romatype[%d]", iter2->second.nRomatype);
		strcpy(iter2->second.szVlr, vlr);
		string rg = iter2->first;
		PutCdr_CCG(bizMsg, iter2->second,rg);


		UDBSQL *pExe = dbm->GetSQL(DATA_CDR_UpdateCdrSession);
		//更新使用量信息到CDR_PGW_SESSION_STORE
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				pExe->DivTable(bizMsg->m_sessionID);
				pExe->UnBindParam();
				pExe->BindParam(1, (long)iter2->second.outputtemp);
				pExe->BindParam(2, (long)iter2->second.inputtemp);
				pExe->BindParam(3, (long)iter2->second.totaltemp);
				pExe->BindParam(4, (long)iter2->second.durationtemp);
				pExe->BindParam(5, iter2->second.sessionCurrent);
				pExe->BindParam(6, iter2->second.seqNum);
				pExe->BindParam(7, "");
				pExe->BindParam(8, iter2->second.sessionId);
				pExe->Execute();
				pExe->Connection()->Commit();
				success = true;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update SM_PGW_SESSION_STORE out[%ld], in[%ld], total[%ld], duration[%ld]", (long)TUSU.unitOutput, (long)TUSU.unitInput, (long)TUSU.unitTotal, (long)TUSU.duration);
			}
			catch (UDBException &e)
			{
				pExe->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select  execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			} // 清空相关信息并且更新序列
		}

		updateCdrInfo(bizMsg,iter2->second.sessionId);


	}

	//info 日志
	DCDATLOG("SM00015:%d%s%s%ld%s%s%s%s%s", bizMsg->m_longCDR,iter2->second.sessionStart, iter2->second.sessionCurrent,\
							iter2->second.total,iter2->second.planInfo, iter2->second.tarifInfo, iter2->second.chargeInfo, iter2->second.accumuInfo, iter2->second.orichargeInfo);
	return 0;
}

int DCBizCdrNormalDATA::updateCdrInfo(STBizMsg* bizMsg,char *sessinID)
{

	UDBSQL *pExecute = NULL;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	pExecute = dbm->GetSQL(DATA_UpdateSessionStoreCdr_006);

	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新子会话总时长
			pExecute->DivTable(bizMsg->m_sessionID);
			pExecute->UnBindParam();
			pExecute->BindParam(1, sessinID);
			pExecute->Execute();
			pExecute->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			pExecute->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select  execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else	
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child ok,set balance=NULL,etc");

}

int DCBizCdrNormalDATA::ComposeCCGCDR(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,const char* rg,SCDRData &cdr)
{
	int nFieldNum = 0;
	char timestamp[BIZ_TEMP_LEN_32]				= {0};
	char value[BIZ_TEMP_LEN_1024] = {0};
	int length = 0;
	char USUDefault[32] = {0};
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;
	int iPsAreaSwitch = smpara->GetPSPara()->iPsVisitArea;

	int i=1;
	vector<SCDRField*>::iterator iter;

	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");
	
	for(iter=field->begin(); iter!=field->end(); iter++)
	{
		if(iCdrVersionType>0)
		{
			if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)))
			{

				char szVersion[32] = {0};
				int iDay = timestampf();
				sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//组装version信息
				strcpy(value, szVersion);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
				i++;
				continue;
			}
		}

		if(1==iPsAreaSwitch)    // 安徽不用
		{
			if(0 == strcmp("RE_STR_SUB_VLR", (*iter)->value))
			{
				DealwithField(stCdrInfo.szVlr);
				strcat(cdr.m_body, stCdrInfo.szVlr);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,stCdrInfo.szVlr,i);
				i++;
				continue;
			}
		}

		//add by wangpx,清单会话id拼接2位截单序列和2位拆单序列
		if(0 == strcmp("OCP_STR_SESSION_ID", (*iter)->value))
		{
			pQuery->GetValue(i, value);
			sprintf(value,"%s;%02d;%02d",value,stCdrInfo.seqNum,cdrSeqNum);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if((0 == strcmp("CDR_BILL_CYCLE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%ld", stCdrInfo.Payment);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if((0 == strcmp("DAY", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",stCdrInfo.Day);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",bizMsg->m_xdrsource);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}

              if(0 == strcmp("RE_LNG_CALL_START_TIME", (*iter)->value))
		{
			strcpy(value, stCdrInfo.sessionStart);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

              if(0 == strcmp("RE_LNG_CURRENT_CCR_TIME", (*iter)->value))
		{
			strcpy(value, stCdrInfo.sessionCurrent);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_TIME", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.duration);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_TOTAL_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",(stCdrInfo.total+1023)/1024);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_INPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.input);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_OUTPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.output);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_STR_RATING_GROUP", (*iter)->value))
		{
			sprintf(value, "%s", rg);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_BALANCEINFO", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.balanceInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			//sprintf(value, "%s", TaiAuCdrInfo.sz_accumInfo);
			sprintf(value, "%s", stCdrInfo.accumuInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);

			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_TARIFFID", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.tarifInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_CHARGEINFO", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.chargeInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_BALANCEINFO2", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.balanceInfo2);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("RE_INT_ROAM_TYPE", (*iter)->value))
		{
			sprintf(value, "%d",  stCdrInfo.nRomatype);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_INT_CHARGE_TYPE", (*iter)->value))
		{
			sprintf(value, "%d", stCdrInfo.nChargeType);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_DISCOUNT_FEE", (*iter)->value))
		{
			sprintf(value, "%ld", stCdrInfo.discount_fee);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_LTE_FLAG",(*iter)->value))
		{
			strcat(cdr.m_body,"0");
			strcat(cdr.m_body,"|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_LTE_FLAG[0]", "");
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d",cdrSeqNum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s]", value);
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_3GPP_RAT_TYPE", (*iter)->value))
		{
			char tmp[BIZ_TEMP_LEN_256] = {0};
			int dest = 0;
			pQuery->GetValue(i, tmp);
			if(0 == strcmp(tmp,"66"))
			{
				dest = 102;
				sprintf(value, "%d", dest);
			}
			else
			{
				sprintf(value, "%s", tmp);
			}
			//hextod(tmp,dest);
			//sprintf(value, "%d", dest);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_MCC", (*iter)->value))
		{
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_MCC[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_MNC", (*iter)->value))
		{
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_MNC[%s]", value);
			continue;
		}
		if (0 == strcmp("CDR_STR_BSID", (*iter)->value))
		{
                     strcpy(value, stCdrInfo.userLocationInfo);
                     DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_STR_BSID[%s]", value);
			continue;
		}

		if (0 == strcmp("CDR_STR_TAI_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_STR_TAI_ID[%s]", value);
			continue;
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());
			continue;
		}

		if((*iter)->flag)
		{
			//i++;
			nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
			if(nFieldNum)//特殊清单字段处理
			{
				if(nFieldNum < 9)
				//清单中加入Fee字段或者ACCT_Item_Type_ID字段
					AddFeeItemByRG(stCdrInfo.stFeeItem,&cdr,nFieldNum);
				else
					AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
				//i--;
				continue;
			}
			else
			{
				pQuery->GetValue(i, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
			}
		}
		else if(0 == (*iter)->flag)
		{
			strcpy(value, (*iter)->value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
		}
	}

}


int DCBizCdrNormalDATA::ComposeAAACDR(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,const char* rg,SCDRData &cdr)
{
	int nFieldNum = 0;
	char timestamp[BIZ_TEMP_LEN_32] 			= {0};
	char value[BIZ_TEMP_LEN_1024] = {0};
	int length = 0;
	char USUDefault[32] = {0};
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;

	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;

	int i=1;
	vector<SCDRField*>::iterator iter;

	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");
	
	for(iter=field->begin(); iter!=field->end(); iter++)
	{
		if(iCdrVersionType>0)
		{
			if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)))
			{

				char szVersion[32] = {0};
				int iDay = timestampf();
				sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//组装version信息
				strcpy(value, szVersion);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
				i++;
				continue;
			}
		}

		 if((0 == strcmp("CDR_BILL_CYCLE", (*iter)->value)))
		 {
			memset(value,0x00,sizeof(value));
			sprintf(value,"%ld", stCdrInfo.Payment);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		 }

              if(0 == strcmp("RE_LNG_CALL_START_TIME", (*iter)->value))
		{
			strcpy(value, stCdrInfo.sessionStart);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

              if(0 == strcmp("RE_LNG_CURRENT_CCR_TIME", (*iter)->value))
		{
			strcpy(value, stCdrInfo.sessionCurrent);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		//add by wangpx,清单会话id拼接2位截单序列和2位拆单序列
		if(0 == strcmp("OCP_STR_SESSION_ID", (*iter)->value))
		{
			pQuery->GetValue(i, value);
			sprintf(value,"%s;%02d;%02d",value,stCdrInfo.seqNum,cdrSeqNum);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_TIME", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.duration);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_3GPP_RAT_TYPE", (*iter)->value))
		{
			char tmp[BIZ_TEMP_LEN_256] = {0};
			int dest = 0;
			pQuery->GetValue(i, tmp);
			if(0 == strcmp(tmp,"66"))
			{
				dest = 102;
				sprintf(value, "%d", dest);
			}
			else
			{
				sprintf(value, "%s", tmp);
			}
			//hextod(tmp,dest);
			//sprintf(value, "%d", dest);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("SM_LNG_ALL_USU_TOTAL_OCT", (*iter)->value))
		{
			sprintf(value, "%lld", (stCdrInfo.total+1023)/1024);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_INPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.input);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",bizMsg->m_xdrsource);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if(0 == strcmp("SM_LNG_ALL_USU_OUTPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.output);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_STR_RATING_GROUP", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.rating);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_BALANCEINFO", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.balanceInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			//sprintf(value, "%s", TaiAuCdrInfo.sz_accumInfo);
			sprintf(value, "%s", stCdrInfo.accumuInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			//i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_TARIFFID", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.tarifInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if((0 == strcmp("DAY", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",stCdrInfo.Day);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGEINFO", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.chargeInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_BALANCEINFO2", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.balanceInfo2);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("RE_INT_ROAM_TYPE", (*iter)->value))
		{
			sprintf(value, "%d",  stCdrInfo.nRomatype);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_INT_CHARGE_TYPE", (*iter)->value))
		{
			sprintf(value, "%d", stCdrInfo.nChargeType);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_DISCOUNT_FEE", (*iter)->value))
		{
			sprintf(value, "%ld", stCdrInfo.discount_fee);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_LTE_FLAG",(*iter)->value))
		{
			strcat(cdr.m_body,"0");
			strcat(cdr.m_body,"|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_LTE_FLAG[0]", "");
			//i++;
			continue;

		}

		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nChargeType==2? 1: 2);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s]", value);
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_MCC", (*iter)->value))
		{
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_MCC[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_MNC", (*iter)->value))
		{
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_MNC[%s]", value);
			continue;
		}
		if (0 == strcmp("CDR_STR_BSID", (*iter)->value))
		{
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_STR_BSID[%s]", value);
			continue;
		}

		if (0 == strcmp("CDR_STR_TAI_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_STR_TAI_ID[%s]", value);
			continue;
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());
			continue;
		}

		if((*iter)->flag)
		{
			//i++;
			nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
			if(nFieldNum)//特殊清单字段处理
			{
				if(nFieldNum < 9)
				//清单中加入Fee字段或者ACCT_Item_Type_ID字段
				AddFeeItemByRG(stCdrInfo.stFeeItem,&cdr,nFieldNum);

				AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
				//i--;
				continue;
			}
			else
			{
				pQuery->GetValue(i, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
			}
		}
		else if(0 == (*iter)->flag)
		{
			strcpy(value, (*iter)->value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
		}
	}

}



