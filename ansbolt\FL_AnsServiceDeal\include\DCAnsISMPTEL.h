/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsISMPTEL.h
*Indentifier：
*
*Description：
*		增值业务处理类
*Version：
*		V1.0
*Author:
*		YF.Du
		ST.J
*Finished：
*		2009年8月20日
*History:
********************************************/
#ifndef __DC_ANS_ISMP_TEL_H__
#define __DC_ANS_ISMP_TEL_H__

#include "DCAnsISMP.h"
#include "DCBizMsgDef.h"

class DCAnsISMPTEL:public DCAnsISMP
{
	public:

		DCAnsISMPTEL();
		virtual ~DCAnsISMPTEL();

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);
		int RatableCCA(STBizMsg* bizMsg);
};

#endif

