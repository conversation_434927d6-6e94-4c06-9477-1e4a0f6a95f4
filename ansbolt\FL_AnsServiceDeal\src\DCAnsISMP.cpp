#include "DCAnsISMP.h"
#include <sys/time.h>
#include "ErrorCode.h"
#include "DCBizMsgDef.h"
#include "func_sqlindex.h"
#include "DCCommonIF.h"
#include "DCAnsPara.h"
#include "DCUDB.h"
#include "DCDBManer.h"
#include "DCLogMacro.h"

using namespace ocs;

DCAnsISMP::DCAnsISMP()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "DCAnsISMP", "");
}

DCAnsISMP::~DCAnsISMP()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "~DCAnsISMP", "");
}

int DCAnsISMP::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG,  "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}
	STBizMsg *bizMsg = (STBizMsg*)data; 
	
	//PERF LOG
	// bizMsg->m_perf.GetTimeT2_B();
	
	int ret = ComposeCCA(bizMsg);

	// bizMsg->m_perf.GetTimeT2_E(); 

	if(RET_CDR == ret || RET_SUCCESS == ret)
	{	

	}
	else
	{	
		bizMsg->m_resultcode= ret;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
		return ret;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
	return ret;
}

int DCAnsISMP::ComposeCCA(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

