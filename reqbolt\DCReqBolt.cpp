﻿#include "DCReqBolt.h"
#include "DCStormProtocol.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include <ace/Log_Msg.h>
#include <ace/Log_Msg_Backend.h>
#include <ace/Log_Record.h>
#include "DCParseXml.h"
#include "DCSeriaOp.h"
#include "ErrorCode.h"
#include <vector>
#include "DCPerfStatistic.h"
#include "DCDBManer.h"
#include "DCMCastEvtFun.h"
#include "DCTCompress.h"
#include <sys/types.h>
#include <ifaddrs.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "DCEventTracking.h"
#include "DCEvtCheck.h"
#include "DCServEvtCheck.h"
#include "DCCtgCheckInfo.h"
#include "DCOBJSet.h"
#include "DCCommonIF.h"
using namespace std;

void svc();
static void* work_routine(void* handle)
{
	DCReqBolt* imp = static_cast<DCReqBolt*>(handle);
	imp->svc();
	return NULL;
}


class DCACELogAgent : public ACE_Log_Msg_Backend
{
public:
	DCACELogAgent(){}
	virtual ~DCACELogAgent(){}

	virtual int 	open (const ACE_TCHAR *logger_key){}
	virtual int 	reset (void){}
	virtual int 	close (void){}
	virtual ssize_t log (ACE_Log_Record &logr)
	{
		int level = 0;
		switch (logr.priority())
		{
			case 1:
				level = DCLOG_LEVEL_TRACE;
				break;
			case 2:
				level = DCLOG_LEVEL_DEBUG;
				break;
			case 3:
				level = DCLOG_LEVEL_DVIEW;
				break;
			case 4:
				level = DCLOG_LEVEL_INFO;
				break;
			case 5:
				level = DCLOG_LEVEL_WARN;
				break;
			case 6:
				level = DCLOG_LEVEL_ERROR;
				break;
			case 7:
				level = DCLOG_LEVEL_FATAL;
				break;
			default:
				level = DCLOG_LEVEL_ERROR;
				break;
		}
		DCSYSLOG(level, 0, "L%lu|%s", logr.priority(), logr.msg_data());
            return logr.msg_data_len();
	}
};

DCReqBolt::DCReqBolt():m_en(NULL)
{

}

DCReqBolt::~DCReqBolt()
{
	if(m_en)
	{
		delete m_en;
		m_en = NULL;
	}
}


int DCReqBolt::Refresh(const char * path)
{
	return 0;
}

int DCReqBolt::SetWacther()
{
	//监听
	return 0;
}


int DCReqBolt::Initialize(const tydic::storm::DCStormConfig& config)
{
	int ret = 0;
	char buf[512]={0};
	m_checktime = 0;
	ACE_LOG_MSG->clr_flags(ACE_Log_Msg::STDERR);
	ACE_LOG_MSG->msg_backend( new DCACELogAgent() );
	ACE_LOG_MSG->priority_mask( LM_DEBUG|LM_INFO|LM_NOTICE|LM_WARNING|LM_ERROR|LM_CRITICAL, ACE_Log_Msg::PROCESS);
	ACE_LOG_MSG->open("ace", ACE_Log_Msg::CUSTOM);

	char *szconfig =getenv("OCS_CONFIG");
	if(NULL==szconfig)
	{
	  return -1;
	}
	string val = (const_cast<tydic::storm::DCStormConfig&>(config)).GetConfig("billing.cfg.spec");
	sprintf(buf,"%s/sm_cfg%s.xml",szconfig,val.c_str());
	ret = DCParseXml::Instance()->Init("SM",buf);
	if(ret)
	{
		return -1;
	}

    int taskid = atoi(config.GetTaskId().c_str());
	const char* topology = GetTopology().c_str();

	const char* logpath = DCParseXml::Instance()->GetParam("logAddr","Common/log");
	int loglevel  = atoi(DCParseXml::Instance()->GetParam("level","Common/log"));
	//--------------------------------性能日志级别、阈值设置-------------------------------------//
	int perf_level = 0;//性能日志级别设置
	const char* perf_level_param = DCParseXml::Instance()->GetParam("perf","Common/log");
	if(perf_level_param)
	{
		perf_level = atoi(perf_level_param);
	}
	int perf_threshold = 50;//性能日志阈值获得
	const char* perf_threshold_param = DCParseXml::Instance()->GetParam("perf.ms","Common/log");
	if(perf_threshold_param)
	{
		perf_threshold = atoi(perf_threshold_param);
	}
	//日志初始化
	ret = DCLOGINIT("ocs","sm_reqbolt",loglevel,logpath);
	if(ret)
	{
	  return -1;
	}

	DCLOG_SETLEVEL(DCLOG_CLASS_PERF,perf_level);
	DCLOG_SETCTL(DCLOG_MASK_PERF,perf_threshold*1000);//单位转化为微秒

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d, module:%s",logpath, loglevel, "sm_reqbolt");
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init log successful, topology[%s] taskid[%d]", topology, taskid);
	//插件初始化
	if(val.empty())
		ret = m_pm.init(szconfig, "sm",DFM_USE_DBM|DFM_USE_REFRESH|DFM_USE_NOFLOW);
	else
	{
		sprintf(buf,"sm|%s",val.c_str());
		ret = m_pm.init(szconfig, buf,DFM_USE_DBM|DFM_USE_REFRESH|DFM_USE_NOFLOW);
	}
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin failed");
		return 1;
	}

	ret = m_pm.load_flow("reqbolt");
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin reqbolt failed");
		return 1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init plugin successful");

	strncpy(m_Topic,DCParseXml::Instance()->GetParam("cdr","SM/queue"),sizeof(m_Topic));

	strncpy(m_payflagTopic,DCParseXml::Instance()->GetParam("PayFlagCdr","SM/queue"),sizeof(m_payflagTopic));
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get cdr topic[%s],  PayFlagCdr[%s]",m_Topic,m_payflagTopic);

	strncpy(m_testTopic,DCParseXml::Instance()->GetParam("AnalogCdr","SM/queue"),sizeof(m_testTopic));
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get AnalogCdr topic[%s]",m_testTopic);

	m_en = new DCSeriaEncoder(ESeriaBinString);

	// 获取本机IP
	GetHostIp(m_strIP);

	// 埋点线程
	//DCEvtTrk::instance()->Head("Billing","SM","DCReqBolt",m_strIP.c_str(),topology);
	ret = pthread_create(&m_tid, NULL, work_routine, this);
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,ret,"","create thread failed: %d", ret);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,ret,"","create thread success");
	}


	//指标定义begin
	const char* szParamValue = NULL;
	//埋点延时
	szParamValue = DCParseXml::Instance()->GetParam("KpiDelayMs","SM/BPoint");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get BPoint/KpiDelayMs fail.");
		return -1;
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get BPoint/KpiDelayMs: %s",szParamValue);
        DCKpiSender::instance()->SetParam("delay",    szParamValue);
	}

	//埋点开关
	szParamValue = DCParseXml::Instance()->GetParam("KpiFlag","SM/BPoint");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get BPoint/KpiFlag fail.");
	    return -1;
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get BPoint/KpiFlag: %s",szParamValue);
		DCKpiSender::instance()->SetParam("flag",     szParamValue);
	}

	//处理的本地网列表
	szParamValue = DCParseXml::Instance()->GetParam("KpiLatn","SM/BPoint");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get BPoint/KpiLatn fail.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get BPoint/KpiLatn: %s",szParamValue);
	}
	std::list<string> v_latn;
	string latn = szParamValue;
	SplitString(latn,'|',v_latn);

	// bolt设置指标发送接口
	//DCKpiSender::instance()->SetSenderCallback(my_kpi_callback_sendkpi, this);


	if (DCKpiSender::instance()->Init() != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "DCKpiSender init fail. error_code=%d, error_info=%s", DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
		return -1;
	}

	if (DCServEvtCheck::instance()->Init(2) < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "DCServEvtCheck init fail.");
		return -1;
	}

	//性能区间配置
	string strPerfRange = "0,10,20,30,40";
	szParamValue = DCParseXml::Instance()->GetParam("perf_range","SM/BPoint");
	if(szParamValue==NULL || strlen(szParamValue)==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Get SM/BPoint/perf_range fail use default value 0,10,20,30,40.");
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get SM/BPoint/perf_range: %s",szParamValue);
		strPerfRange = szParamValue;
	}
	//性能单位
	string strPerfUnit="ms";
	szParamValue = DCParseXml::Instance()->GetParam("perf_unit","SM/BPoint");
	if(szParamValue==NULL || strlen(szParamValue)==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Get SM/BPoint/perf_unit fail use default value ms.");
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get SM/BPoint/perf_unit: %s",szParamValue);
		strPerfUnit = szParamValue;
	}
	m_perf.PerfInit(strPerfRange, strPerfUnit);
	
	szParamValue = DCParseXml::Instance()->GetParam("CTfpAuditProvCode", "SM/CtgEvtCheck");
	if (szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig", "Get CtgEvtCheck/CTfpAuditProvCode fail.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig", "Get CtgEvtCheck/CTfpAuditProvCode: %s", szParamValue);
		DCServEvtCheck::instance()->SetPublicCtgInfo("PROV_CODE", szParamValue);
	}

    m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
	if (m_ptrBPMon)
	{
		DCKpiSender::instance()->group_all_init(m_ptrBPMon, "TraceKpi", v_latn);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","monitor init fail. error code:%d, error info:%s",DCKpiSender::instance()->ErrorCode(),DCKpiSender::instance()->ErrorInfo());
		return -1;
	}
	//注册统计指标
	m_tstat = m_pm.get_statistic("SMAPP")->get_position("reqbolt");


       //注册监听事件
      /*BoltEventFun evfun(m_pm.get_mcm(), m_pm.get_drf());
      evfun.register_event(topology, taskid, "reqbolt");*/

	DCMCastManer* m_mcm = new DCMCastManer();
	const char* mcast = getenv("OCS_MCAST_CMD_ADDR");
	if(mcast)
	{
		ret = m_mcm->init(mcast);
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,ret,"","init	DCMCastManer failed: %s", strerror(errno));
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init  DCMCastManer success");

		LogEventFun evfun(m_mcm);
		evfun.register_event("reqbolt");
	}

	return 0;
}
void DCReqBolt::SplitString(const std::string& str, char sep, std::list<std::string>& vec)
{
	size_t pos = 0;
	size_t prev = 0;
	std::string sub;
	bool brun = true;
	vec.clear();
	while(brun)
	{
		pos = str.find(sep, prev);
        if(pos == std::string::npos)
		{
			sub = str.substr(prev);
			brun = false;
		}
		else
		{
			sub = str.substr(prev, pos-prev);
			prev = pos+1;
		}
		vec.push_back(sub);
	}
}
int DCReqBolt::Process(tydic::storm::Tuple &tuple)
{
	DCPerfTimeVCollect collet(m_tstat, true);
	m_perf.PerfBegin();
	int msgsize = tuple.GetSize();
	std::string msginfo = tuple.GetValues(0);
	int taskid  = tuple.GetTaskID(msginfo);
	std::string recvCCRMsg = tuple.GetValues(1);
	std::string sendmsg;
	char buf[20]={0};
	char servAddr[25]={0};
	std::multimap<string,string> mSendMsg;
	DCEvtCheck::instance()->ClearImp();
	DCServEvtCheck::instance()->ClearImp();
	DCServEvtCheck::instance()->ClearCache();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","recv ccr sseq[%s] msg[%s],taskid[%d]", msginfo.c_str(), recvCCRMsg.c_str(),taskid);
	DCBaseFlow* flow = m_pm.get_flow("reqbolt");
	if(!flow)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","did not find flow[FL_ReqBaseFLOW]");
		return 1;
	}
	strncpy(servAddr, recvCCRMsg.c_str(), 25);
	recvCCRMsg.erase(0, 25);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","recv ccr msg[%s],taskid[%d]", recvCCRMsg.c_str(),taskid);

	// 获取头部固定16字节
	strncpy(buf, recvCCRMsg.c_str(), 16);

	// 删除头部16字节
	recvCCRMsg.erase(0, 16);

	//数据库重连
	time_t cursec = time(NULL);
	if((m_checktime+300) < cursec)
	{
		m_pm.get_dbm()->CheckReset();
		m_checktime = cursec;
	}
	else
	{
		m_pm.get_dbm()->FastReset();
	}

	char szTaskId[16] = {0};
	sprintf(szTaskId,"%d",taskid);
	mSendMsg.insert(pair<string,string>("TaskId",szTaskId));

    int ret = flow->call(&recvCCRMsg, &mSendMsg);

	std::multimap<string,string>::iterator iter = mSendMsg.find("SENDMSG");
	if(iter != mSendMsg.end())
	{
		sendmsg = iter->second;
	}

	string sBillingNbr;
	iter = mSendMsg.find("BillingNbr");
	if(iter != mSendMsg.end())
	{
		sBillingNbr = iter->second;
	}

	string strUID ;
	iter = mSendMsg.find("UID");
	if(iter != mSendMsg.end())
	{
		strUID = iter->second;
	}

	string sLatnId;
	iter = mSendMsg.find("LatnId");
	if(iter != mSendMsg.end())
	{
		sLatnId = iter->second;
	}

	string sRet;
	iter = mSendMsg.find("ResultCode");
	if(iter != mSendMsg.end())
	{
		sRet = iter->second;
	}
	int nResultCode = atoi(sRet.c_str());
	int nLatnId = atoi(sLatnId.c_str());
	char monGroup[50] = {0};
	DCKpiSender::instance()->GetFullGroup("TraceKpi",nLatnId, monGroup);

	std::multimap<string,string>::iterator itBeg;
	std::multimap<string,string>::iterator itEnd;

	itBeg = mSendMsg.lower_bound("LOGRATING");
	itEnd = mSendMsg.upper_bound("LOGRATING");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		tydic::storm::Tuple tuple2Stabolt;
		tuple2Stabolt.SetValues(msginfo);
		tuple2Stabolt.SetValues(iter->second);
		tuple2Stabolt.SetValues(strUID);
        tydic::storm::EmitDirect(-1,tuple2Stabolt,"StaBoltStream");
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to StaBolt LOGRATING sseq[%s]	msgsize[%d].",msginfo.c_str(),  iter->second.size());
	}


	itBeg = mSendMsg.lower_bound("CDRMSG");
	itEnd = mSendMsg.upper_bound("CDRMSG");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		tydic::storm::Tuple tuple2Stabolt;
		tuple2Stabolt.SetValues(msginfo);
		tuple2Stabolt.SetValues(iter->second);
		tuple2Stabolt.SetValues(strUID);
        tydic::storm::EmitDirect(-1,tuple2Stabolt,"StaBoltStream");
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to StaBolt CDRMSG sseq[%s]	msgsize[%d].",msginfo.c_str(),  iter->second.size());
		DCEvtCheck::instance()->Push_O("SMSta",strUID);
	}

	itBeg = mSendMsg.lower_bound("OFFLIN_MODE_CDR");
	itEnd = mSendMsg.upper_bound("OFFLIN_MODE_CDR");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		tydic::storm::Tuple tuple2Stabolt;
		tuple2Stabolt.SetValues(msginfo);
		tuple2Stabolt.SetValues(iter->second);
		tuple2Stabolt.SetValues(strUID);
        tydic::storm::EmitDirect(-1,tuple2Stabolt,"StaBoltStream");
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to StaBolt OFFLIN_MODE_CDR sseq[%s]	msgsize[%d].",msginfo.c_str(),  iter->second.size());
	}

	//头部加上固定16位时间戳
	struct timeval tmv;
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	sendmsg.insert(0, servAddr,25);
    sendmsg.insert(25, buf,16);
	DCDATLOG();
	DCDATLOG("MR00001:recv ccr, uid[%s], timestamp[%ld]", strUID.c_str(),tmv.tv_sec);

	if (!DCServEvtCheck::instance()->GetCacheValue("5G").empty())
	{
		DCServEvtCheck::instance()->ImportCtgInfo("ACC_NBR", DCServEvtCheck::instance()->GetCacheValue("ACC_NBR"));
		DCServEvtCheck::instance()->ImportCtgInfo("TICKET_ID", strUID);
		if (DCServEvtCheck::instance()->GetCacheValue("DUP_RESULT").empty())
		{
			DCServEvtCheck::instance()->SetCacheKey("DUP_RESULT", "1");
			DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_BEGIN_TIME", DCServEvtCheck::instance()->GetCurrentTime());
			DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_END_TIME", DCServEvtCheck::instance()->GetCurrentTime());
		}
		DCServEvtCheck::instance()->ImportCtgInfo("RESULT", DCServEvtCheck::instance()->GetCacheValue("DUP_RESULT"));
		DCServEvtCheck::instance()->ImportCtgInfo("REMOVE_BEGIN_TIME", DCServEvtCheck::instance()->GetCacheValue("DUP_REMOVE_BEGIN_TIME"));
		DCServEvtCheck::instance()->ImportCtgInfo("REMOVE_END_TIME", DCServEvtCheck::instance()->GetCacheValue("DUP_REMOVE_END_TIME"));
		DCServEvtCheck::instance()->SetAuditId("SM_REMOVE");
		SendToCheckBolt(msginfo, "SM_REMOVE");
		DCServEvtCheck::instance()->ClearImp();
		DCServEvtCheck::instance()->ImportCtgInfo("ACC_NBR", DCServEvtCheck::instance()->GetCacheValue("ACC_NBR"));
		DCServEvtCheck::instance()->ImportCtgInfo("SESSION_ID", DCServEvtCheck::instance()->GetCacheValue("SESSION_ID"));
		DCServEvtCheck::instance()->ImportCtgInfo("SESSION_SEQ", DCServEvtCheck::instance()->GetCacheValue("SESSION_SEQ"));
		DCServEvtCheck::instance()->SetAuditId("SM_PREP");
	}

	if(RET_SUCCESS != ret && RET_OVER != ret && OFF_LINE != ret && RET_NOT_NEED_ANS != ret)
	{
		if (!DCServEvtCheck::instance()->GetCacheValue("5G").empty())
		{
			DCServEvtCheck::instance()->ImportCtgInfo("TICKET_ID", strUID);
			DCServEvtCheck::instance()->ImportCtgInfo("RESULT", "2");
			DCServEvtCheck::instance()->ImportCtgInfo("END_TIME", DCServEvtCheck::instance()->GetCacheValue("END_TIME"));
			DCServEvtCheck::instance()->ImportCtgInfo("PREP_BEGIN_TIME", DCServEvtCheck::instance()->GetCacheValue("PREP_BEGIN_TIME"));
			DCServEvtCheck::instance()->ImportDealTime("PREP_END_TIME");
			SendToCheckBolt(msginfo, "SM_PREP");
		}	

		tydic::storm::Tuple tRnd;
		tRnd.SetValues(msginfo);
		tRnd.SetValues(sendmsg);
		tRnd.SetValues(strUID);
		tydic::storm::EmitDirect(-1,tRnd,"EptBoltStream");
		DCEvtCheck::instance()->Push_O("SMEpt",strUID);
		tydic::storm::exeAck(tuple.GetID());
		collet.stop();

		//DCEvtTrk::instance()->SetErrCode(nLatnId,nResultCode);
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","result code:%d,send to EptBolt\n",ret);

		char szResultCode[10]={0};
		sprintf(szResultCode,"%d",nResultCode);
		if(nResultCode != 0 && nResultCode != 2001)
		{
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon,monGroup, "", "SMEc", szResultCode, 1);
		}
	}
	else if(RET_OVER == ret || OFF_LINE == ret || RET_NOT_NEED_ANS == ret)
	{
		if (!DCServEvtCheck::instance()->GetCacheValue("5G").empty())
		{
			DCServEvtCheck::instance()->ImportCtgInfo("TICKET_ID", strUID);
			if (!DCServEvtCheck::instance()->GetCacheValue("FILTER_FLAG").empty())
			{
				DCServEvtCheck::instance()->ImportCtgInfo("RESULT", "2");
			}
			else
			{
				DCServEvtCheck::instance()->ImportCtgInfo("RESULT", "1");
			}
			DCServEvtCheck::instance()->ImportCtgInfo("END_TIME", DCServEvtCheck::instance()->GetCacheValue("END_TIME"));
			DCServEvtCheck::instance()->ImportCtgInfo("PREP_BEGIN_TIME", DCServEvtCheck::instance()->GetCacheValue("PREP_BEGIN_TIME"));
			DCServEvtCheck::instance()->ImportDealTime("PREP_END_TIME");
			SendToCheckBolt(msginfo, "SM_PREP");
		}

		if (ret != RET_NOT_NEED_ANS)
		{
			tydic::storm::Tuple tRnd;
			tRnd.SetValues(msginfo);
			tRnd.SetValues(sendmsg);
			tydic::storm::EmitDirect(taskid,tRnd,"EndBoltStream");
			tydic::storm::exeAck(tuple.GetID());
			DCEvtCheck::instance()->Push_O("ChfpA",strUID);
			collet.stop();
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","send to EndBolt");
		}
	}
	else
	{
		itBeg = mSendMsg.lower_bound("SENDMSG");
		itEnd = mSendMsg.upper_bound("SENDMSG");
		int i = 0;
		for(iter=itBeg;iter!=itEnd;++iter)
		{
			sendmsg = string(buf) + iter->second;
			tydic::storm::Tuple tRnd;

			if (!DCServEvtCheck::instance()->GetCacheValue("5G").empty())
			{
				DCServEvtCheck::instance()->ImportCtgInfo("ACC_NBR", DCServEvtCheck::instance()->GetCacheValue("ACC_NBR"));
				DCServEvtCheck::instance()->ImportCtgInfo("SESSION_ID", DCServEvtCheck::instance()->GetCacheValue("SESSION_ID"));
				DCServEvtCheck::instance()->ImportCtgInfo("SESSION_SEQ", DCServEvtCheck::instance()->GetCacheValue("SESSION_SEQ"));
				DCServEvtCheck::instance()->SetAuditId("SM_PREP");
				DCServEvtCheck::instance()->ImportCtgInfo("RESULT", "1");
				string lastUseOfTime = DCServEvtCheck::instance()->GetCacheVecValue("END_TIME", i);
				if (lastUseOfTime.empty())
				{
					DCServEvtCheck::instance()->ImportCtgInfo("END_TIME", DCServEvtCheck::instance()->GetCacheValue("END_TIME"));
				}
				else
				{
					long long llEndTime = atoll(lastUseOfTime.c_str());
					char szDateTime[25] = {0};
					TimestampToDate(llEndTime, szDateTime);
					DCServEvtCheck::instance()->ImportCtgInfo("END_TIME", szDateTime);
				}
				DCServEvtCheck::instance()->ImportCtgInfo("PREP_BEGIN_TIME", DCServEvtCheck::instance()->GetCacheValue("PREP_BEGIN_TIME"));
				DCServEvtCheck::instance()->ImportDealTime("PREP_END_TIME");
			}

			if(0==i)
			{
				tRnd.SetValues(msginfo);
				tRnd.SetValues(sendmsg);
				tRnd.SetValues(sBillingNbr);
				DCEvtCheck::instance()->Push_O("RER", strUID);
				DCServEvtCheck::instance()->SetCacheKey("UUID", strUID);
				//tydic::storm::EmitDirect(-1,0,tRnd,"javaCStream");
				tydic::storm::EmitDirect(-1,tRnd,"javaCStream");
			}
			else
			{
				string tmpmsg = ReSetUid(msginfo, i);
				tRnd.SetValues(tmpmsg);
				tRnd.SetValues(sendmsg);
				tRnd.SetValues(sBillingNbr);
				//tydic::storm::EmitDirect(-1,-1,tRnd,"javaCStream");
				tydic::storm::EmitDirect(-1,tRnd,"javaCStream");
			}

			if (!DCServEvtCheck::instance()->GetCacheValue("5G").empty())
			{
				DCServEvtCheck::instance()->ImportCtgInfo("TICKET_ID", DCServEvtCheck::instance()->GetCacheValue("UUID"));
				SendToCheckBolt(msginfo, "SM_PREP");
			}

			i++;
			
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon,monGroup, "", "Sm2Re", NULL, 1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","taskid[%d] send to ratebolt",taskid);
		}
		tydic::storm::exeAck(tuple.GetID());
		collet.stop();
		//DCEvtTrk::instance()->IncNormal(atoi(sLatnId.c_str()),(int)collet.m_usec);

		string bOffline;
		iter = mSendMsg.find("CHECKOFFLINE");
		if(iter != mSendMsg.end())
		{
			bOffline = iter->second;
		}

		if("1" == bOffline)
		{
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, "", "SMOfA", NULL, 1);
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","CHECKOFFLINE[%s]",bOffline.c_str());
	}
	SendToKpiBolt(msginfo);

	DCDATLOG();

	m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
	if (m_ptrBPMon)
	{
		m_perf.PerfEnd(m_ptrBPMon,sLatnId.c_str());
	}

	// 输出统计信息
	if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
	{
		bool bSample = true;
		string strStat = "";
		m_pm.get_statistic("APP")->to_json_string(strStat, false, bSample);
		m_pm.get_dbm()->get_statistic()->to_json_string(strStat, false, bSample);
		DCPERFLOG(0, "%s", strStat.c_str());
	}
	//重置统计信息
	m_pm.get_statistic("APP")->reset();
	m_pm.get_dbm()->get_statistic()->reset();
	return 0;
}

void DCReqBolt::Compress(std::string& buf)
{
	char szSize[10] = {0};
	string strSendMsg="";
	if(buf.size() > 1024*64)
	{
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","message.size[%d] > 1024*64 must compress first. ",buf.size());
		int nRet = Compressor::Instance()->Compress(buf.c_str(),buf.size());
		char * text =  Compressor::Instance()->GetCompress();

		buf.clear();
		buf.resize(nRet+1,0);
		memcpy((void*)buf.c_str(),(void*)text,nRet);

		m_en->clear();
	    m_en->encode(buf);
		strSendMsg = HexEncode(m_en->data(),m_en->size());

		buf.clear();
		sprintf(szSize,"%.10d",nRet);
		buf = "$$$$$$";
		buf += szSize;
		buf += strSendMsg;
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","After compress the message is[%s]. ",buf.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","Don't need compress the message.");
	}
}

//获取主机IP,多个网卡的情况也只取第一个
void DCReqBolt::GetHostIp(string &IP)
{
	struct ifaddrs * ifAddrStruct = NULL,*ifAddrStruct1=NULL;
    void * tmpAddrPtr = NULL;
    getifaddrs(&ifAddrStruct);
	ifAddrStruct1 = ifAddrStruct;

    while (ifAddrStruct != NULL)
    {
        if (ifAddrStruct->ifa_addr->sa_family == AF_INET)
        {
            // check it is IPv4
            // is a valid IPv4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];

            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            if(strcmp(addressBuffer, "127.0.0.1") == 0)
            {
                ;
            }
            else
            {
                IP = addressBuffer;
				break;
            }

        }

        ifAddrStruct = ifAddrStruct->ifa_next;
    }
	freeifaddrs(ifAddrStruct1);
    return;
}

std::string DCReqBolt::ReSetUid(string strMsgInfo, int c)
{
	//strMsgInfo消息体形如[{"isRealtime":true,"uuid":"A0001361248900051082018011221491515769542","endBoltTaskId":"334","info":0}]
	//或者{"extend":"{\"hashkey\":\"266093\"}","isRealtime":true,"serviceName":"rentservTest551","uuid":"R060982990000001516676023","endBoltTaskId":"390","info":0}
	char buf[10] = {0};
    size_t p = 0, s;
	size_t p5 = 0, p6=0;
	int i = 0;
	if(string::npos != (s = strMsgInfo.find("uuid\":\"",0)))
	{
		p = s+7;
		p5 = p;
		if(string::npos != (s = strMsgInfo.find_first_of("\"", p)))
		{
			p6 = s;
		}
	}
	if(p6 > p5)
	{
		sprintf(buf, "A%d", c);
		strMsgInfo.insert(p6, buf);
		p6 = strMsgInfo.find_first_of("\"", p);
		string strUUID = strMsgInfo.substr(p5,p6-p5);
		DCEvtCheck::instance()->Push_O("RER", strUUID);
		DCServEvtCheck::instance()->SetCacheKey("UUID", strUUID);
		struct timeval tmv;
		gettimeofday(&tmv, NULL);
		DCDATLOG("MR00003:source uid[%s], to RE uid[%s], timestamp[%lld]", strMsgInfo.substr(p5,p6-p5-strlen(buf)).c_str(), strUUID.c_str(), tmv.tv_sec);
	}
	return strMsgInfo;

}

void DCReqBolt::svc()
{
	time_t check = time(NULL);
	std::vector<string> v;
	while(1)
	{
		if(check + 5 < time(NULL))
		{
			//DCEvtTrk::instance()->GetJson(v);
			for(unsigned int i = 0; i < v.size();i++)
			{
				tydic::storm::sendEventTrking(v[i]);
			}
			v.clear();
			check = time(NULL);
		}
		sleep(1);
	}
}

//发送稽核消息给指标bolt
int DCReqBolt::SendToKpiBolt(std::string msginfo) 
{
	std::string strJsInfo;
	if (0 == DCKpiSender::instance()->GetKpiData(m_ptrBPMon,strJsInfo))
	{
		tydic::storm::Tuple tRndKpi;
		tRndKpi.SetValues(msginfo);
		tRndKpi.SetValues(strJsInfo); 
	    tydic::storm::EmitDirect(-1,tRndKpi,"DCKpiStream");	
	    DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "DCReqBolt::SendToKpiBolt end");
	}
	
	if(DCEvtCheck::instance()->GetCheckKey().size() > 0)
	{
		tydic::storm::Tuple tRndCheck;
		DCEvtCheck::instance()->GetJson(strJsInfo);
		tRndCheck.SetValues(msginfo);
		tRndCheck.SetValues(strJsInfo);
		tydic::storm::EmitDirect(-1,tRndCheck,"DCCheckStream");
		DCEvtCheck::instance()->ClearImp();
	}

	return 0;
}

//发送信息点稽核消息给信息点bolt
int DCReqBolt::SendToCheckBolt(std::string& msginfo, std::string sAuditId) 
{
	if (!DCServEvtCheck::instance()->GetCacheValue("ROLL_FLAG").empty())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","get roll flag, not send ctg check msg");
		return 0;
	}

	ocs::CtgCheckInfo check;
	check.CtgFileType = DCServEvtCheck::instance()->GetFileType();
	check.AuditId = sAuditId;
	DCServEvtCheck::instance()->GetFileJson(check.FileJson);
	m_en->clear();
	m_en->encode(check);
	string strMsg = HexEncode(m_en->data(), m_en->size());
	
	tydic::storm::Tuple tuple;
	tuple.SetValues(msginfo);
	tuple.SetValues(strMsg);
	tydic::storm::EmitDirect(-1, tuple, "CtgCheckStream"); 

	return 0;
}

void DCReqBolt::TimestampToDate(time_t timestamp, char *pszDate)
{
	//使用线程安全函数localtime_r
	timestamp -= 2208988800L;
	struct tm tmTime ;
	localtime_r(&timestamp, &tmTime);
	sprintf(pszDate, "%04d%02d%02d%02d%02d%02d000", (1900 + tmTime.tm_year), (1 + tmTime.tm_mon), tmTime.tm_mday, tmTime.tm_hour, tmTime.tm_min, tmTime.tm_sec);

	return ;
}



