#include "DCReqSMSTEL.h"
#include "TSMPara.h"
#include "ErrorCode.h"
#include "TConfig.h"
#include "BizCdrDefTEL.h"
#include "DCRbMsgDef.h"
#include "DCLogMacro.h"
#include "REMsgTypeDef.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCUDB.h"
#include "DCDBManer.h"
#include "UHead.h"
#include "DCCommonIF.h"

DCReqSMSTEL::DCReqSMSTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, "", "", "");
}

DCReqSMSTEL::~DCReqSMSTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, "", "", "");
}

int DCReqSMSTEL::SwitchReqType(STBizMsg* bizMsg)
{

	int ret 	   = RET_SUCCESS;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	SCCRSMS* data  =(SCCRSMS*)bizMsg->m_extend;

	switch (base->requestAction)
	{
		case SM_REQUESTED_ACTION_DEBIT:
			{
				//先增加xdr m_requestType 为5 此处不能写死
				//bizMsg->m_requestType = SM_SESSION_EVENT_CODE;
				ret = Debit(base, data, bizMsg);
			}
			break;
		case SM_REQUESTED_ACTION_REFUND:
			{
				//bizMsg->m_requestType = SM_SESSION_EVENT_REFUND_CODE;
				ret = Refund(base, data, bizMsg);
			}
			break;
		default:
			{
				ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "", "invalid Requested-Action[%u]", base->requestAction);
				return ret;
			}
			break;
	}

	return ret;
}


int DCReqSMSTEL::Debit(SCCRBase* base, SCCRSMS* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int iPayFlag 							= 1;
	int traceNumOnff						= 0;
	long nextCCTime							= 0;

	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]				= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	int roamType                            = bizMsg->m_roamtype;
	AREA_INFO *subVisit						= bizMsg->m_visit;
	TSMPara* m_smpara						= bizMsg->m_smpara;
	SUserInfo *userInfo						= (SUserInfo*)bizMsg->m_userinfo;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;
	string iCreatFlag;

	int negat = 0;
	negat = m_smpara->GetCommonPara()->EventSMSSwitch;


	//计算计费方类型
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID,"subscription[%s], calling[%s]", base->subscription.phone.c_str(), data->calling.phone.c_str());
	if(!strcasecmp(base->subscription.phone.c_str(), data->calling.phone.c_str()))
	{
		iPayFlag = 1;
		data->calling.area=base->subscription.area;
	}
	else if(!strcasecmp(base->subscription.phone.c_str(), data->called.phone.c_str()))
	{
		iPayFlag = 2;
		data->called.area=base->subscription.area;
	}
	else
	{
		iPayFlag = 3;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "nextCCTime[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	//组装RE消息
	/*
	100   MsgType     70         P2PSMS
	101   EventTimeStamp  YYYYMMDDHHmmSSsss
	000   会话ID
	001   请求序列号
	R01   付费号码
	R02   主叫号码
	R03   被叫号码
	R85   用户付费属性标识 1

	R20    短信业务
	R201  话单类型
	R202  短信消息ID

	R50    增强的计费信息
	R504  主叫号码归属费率区
	R506  主叫号码归属运营商
	R507  被叫号码归属费率区
	R509  被叫号码归属运营商
	R5012 付费号码归属费率区

	R60    可变计费信息
	R601  重发标记                0 -  正常 1 -  重发
	R602  计费类型
	R603  会话上次实际扣费开始时间
	R604  本次计费请求开始时间
	R605  是否进行使用量累计标识

	B03   实扣命令（在带Session的业务中表示通话/Session结束。）
	B036 请求实扣信用单位
	B037  申请实扣信用数量
	B038  是否允许扣费为负数

	B04 返还命令
	B046  请求返还信用单位
	B047   申请返还信用数量

	B06    累计量信息
	B07    资费信息
	B08    余额帐本改变的详细信息
	B21    费率信息查询命令
	B20    余额查询命令
	*/

	ocs::UHead uhd;
	ocs::rbsms rbr;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbext ext;
	head.topology = base->topology;
	uhd.uid = bizMsg->m_uid;
	uhd.car = "1";
	uhd.checkKey = bizMsg->m_strCheckKey;
	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	head.creditCtlFlag = atoi(iCreatFlag.c_str());
	//100   MsgType     70         P2PSMS
	head.type = RE_SERVICE_TYPE_INT_SMS_REQ;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose MsgType[%s]", RE_SERVICE_TYPE_STR_SMS_REQ);

	if(bizMsg->m_requestType == 5)
	{
		head.version = 1;
	}
	else
	{
		head.version = 2;
	}
	//000   会话ID
	head.session = bizMsg->m_sessionID;
       if(head.version == 1) head.session.erase(0, 3);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SessionID[%s]", head.session.c_str());

	//101   EventTimeStamp  YYYYMMDDHHmmSSsss
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose EventTimeStamp[%ld]", head.stamp);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Serial[%ld]", bizMsg->m_serial);

	// B14 设置信令跟踪标志，通知RB需要信令跟踪
	head.trace = bizMsg->m_trace_flag;
	uhd.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Trace[%d]", bizMsg->m_trace_flag);

	//R602 计费类型
	head.sreq = SM_SESSION_EVENT_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	//R01 付费号码
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Charged_nbr[%s]", base->subscription.phone.c_str());

	//R02 主叫号码
	rbr.calling_nbr = data->calling.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Calling[%s]", data->calling.phone.c_str());

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "latnid[%d]", rbr.latn_id);
	bizMsg->m_ilatnId = userInfo->ilatnid;

	//R03 被叫号码
	rbr.called_nbr = data->called.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Called[%s]", data->called.phone.c_str());

	//R201  话单类型
	rbr.charge_type = iPayFlag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose PayFlag[%d]", iPayFlag);

	//R202	短信消息ID
	rbr.sm_id = data->SMID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SMID[%s]", data->SMID.c_str());

	//R204	短信消息中心地址
	if(roamType>0)
	{
		rbr.sms_addr = data->SMSCAddress;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SMSCAddress[%s]", data->SMSCAddress.c_str());
	}

	//R504 主叫号码归属费率区
	if (86 != data->calling.country)
	{
		sprintf(value,"00%d",data->calling.country);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Calling_harea[%d]", data->calling.country);
	}
	else
	{
		sprintf(value,"0%d",data->calling.area);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Calling_harea[%d]", data->calling.area);
	}

	if(roamType>0)
	{
		if(0 == subVisit->area)
		{
			subVisit->area =  data->calling.area;
		}
		sprintf(value,"00%d",subVisit->area);
		rbr.calling_varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Calling_varea[%s]" ,rbr.calling_varea.c_str());
	}

	//R506  主叫号码归属运营商
	rbr.calling_hcarrier = data->calling.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CallingCarriers[%d]", data->calling.carriers);

	//R507  被叫号码归属费率区
	if (86 != data->called.country)
	{
		sprintf(value,"00%d",data->called.country);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CalledCountry[%s]", rbr.called_harea.c_str());
	}
	else
	{
		sprintf(value,"0%d",data->called.area);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CalledArea[%s]", rbr.called_harea.c_str());
	}

	if(roamType>0)
	{
		//国际漫游
		//R1012 MCS地址
		rbr.msc = data->mscAddr;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose MSCAddr[%s]",  data->mscAddr.c_str());

		//R5011 漫游类型
		rbr.roam_type = roamType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose RoamType[%d]", roamType);

		//R5016 漫游类型
		rbr.charged_vcarrier = subVisit->szcarriers;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ChargedCarriers[%s]", subVisit->szcarriers);
	}

	//R509 被叫号码归属运营商
	rbr.called_hcarrier = data->called.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Calledhcarrier[%d]", data->called.carriers);

	//R5012 付费号码归属费率区
	if (86 != base->subscription.country)
	{
		sprintf(value, "00%d", base->subscription.country);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SubCountry[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", base->subscription.area);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SubArea[%s]", value);
	}

	//R71
	sprintf(value,"%ld",base->timestamp);
	rbr.sess_start_time = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose sess_start_time[%s]", value);

	//R601 重发标记
	// pREMsg->set(RB_CODE_R_REPEAT_FLAG,  "0");
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose RepeatFlag[%s]", "0");

	//R602 计费类型
	rbr.sreq = SM_SESSION_EVENT_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	//R603  会话上次扣费开始时间
	rbr.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose DebitTime[%s]", "0");

	//R604  本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
    DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CurTime[%ld]",  bizMsg->timestampCCR);

	//R605  是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Ratable[%s]", "0");

	//R606  激活用户
	if(userInfo->isActive)
	{
		rbr.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ActiveFlag[%s]", "1");
	}
	else
	{
		rbr.active_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ActiveFlag[%s]", "1");
	}

	debit dbv;
	//B03	实扣命令
	//B036 请求实扣信用单位
	dbv.unit = RB_UNIT_CODE_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose DebitUnit[%s]", RB_UNIT_STR_TIME);

	//B037  申请实扣信用数量
	dbv.amount = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose DebitAmount[1]");


	//R07离线批价扣费标识
	string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
	int ratingflag= m_smpara->GetBillAttr(servAttr);
	sprintf(value,"%d",ratingflag);
	if(ratingflag>=0)
	{
		bizMsg->m_requestType = 10;
		ext.kv["ratingflag"] = value;
	}

	//B038	事件是否允许扣负
	if( SM_SESSION_XDR_CODE == bizMsg->m_requestType )
	{
            //xdr 取离线话单扣负标识配置值
			//B038
		servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectArrear");
		rbr.neg_debit= m_smpara->GetBillAttr(servAttr);
		if(rbr.neg_debit == -1)
			rbr.neg_debit = 1;
	}
	else
    {
        rbr.neg_debit = negat;
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose DebitResult[%d]", rbr.neg_debit);

	domain.dbv.push_back(dbv);
	//CEE被叫 CER主叫
	ext.kv["CER"] = bizMsg->m_callingNumber;
	ext.kv["CEE"] = bizMsg->m_calledNumber;

	rbr.accumlator_info = 1;		//B06	累积量信息
	rbr.tariff_info 	= 1;		//B07	资费信息
	rbr.balance_info 	= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 	= 1;		//B20	费率信息查询命令
	rbr.balance_query	= 1;		//B21	余额查询命令

	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	ext.kv["RTI"] = subVisit->sector_id;
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 2);
	}
	else
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 1);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
	ext.kv["batchId"] = bizMsg->m_sBatchId;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		bizMsg->data = (char *)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	UDBSQL* pExec = dbm->GetSQL(SMS_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		/*
			"OCP_STR_SESSION_ID,"			1
			"OCP_INT_REQ_NBR,"				2
			"OCP_STR_ORIGIN_HOST,"			3
			"OCP_STR_MSG_ID,"				4
			"OCP_STR_SMSC_ADDRESS,"			5
			"OCP_INT_MSG_LENGTH,"			6
			"OCP_STR_ORIGIN_CALLING_NBR,"	7
			"OCP_STR_ORIGIN_CALLED_NBR,"	8
			"SM_LNG_SERIAL,"					9
			"SM_INT_REQ_TYPE,"				10
		*/
		pExec->BindParam(1, bizMsg->m_sessionID );
		pExec->BindParam(2, (int)bizMsg->m_requestNumber);
		pExec->BindParam(3, base->originHost);
		pExec->BindParam(4, data->SMID);
		pExec->BindParam(5, data->SMSCAddress);
		pExec->BindParam(6, data->SMLength );
		pExec->BindParam(7, data->callingNumber);
		pExec->BindParam(8, data->calledNumber);
		pExec->BindParam(9, (long)bizMsg->m_serial);
	    pExec->BindParam(10, (int)bizMsg->m_requestType);


		/*
			"SM_INT_SESSION_STATUS,"			11
			"SM_LNG_TIME_TO_NEXT_CCR,"		12
			"RE_LNG_CURRENT_CCR_TIME,"		13
			"RE_INT_PAY_FLAG,"				14
			"RE_STR_SUB_NBR,"				15
			"RE_STR_SUB_AREA,"				16
			"RE_INT_SUB_OPERATOR,"			17
			"RE_INT_SUB_COUNTRY,"			18
			"RE_STR_OA_SUB_NBR,"			19
			"RE_STR_OA_SUB_AREA,"			20
		*/
		pExec->BindParam(11, TORB_ACTION);
		pExec->BindParam(12, (long)nextCCTime);
		pExec->BindParam(13, (long)bizMsg->timestampCCR);
		pExec->BindParam(14, iPayFlag);
		pExec->BindParam(15, base->subscription.phone);
		value[0] = '\0';
		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
		}
		pExec->BindParam(16, value);
		pExec->BindParam(17, base->subscription.carriers);
		value[0] = '\0';
		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
		}
		pExec->BindParam(18, value);

		pExec->BindParam(19, data->calling.phone);
		value[0] = '\0';
		if (data->calling.area)
		{
			sprintf(value, "0%d", data->calling.area);
		}
		pExec->BindParam(20, value);
		/*
			"RE_INT_OA_SUB_OPERATOR,"		21
			"RE_INT_OA_SUB_COUNTRY,"		22
			"RE_STR_DA_SUB_NBR,"			23
			"RE_STR_DA_SUB_AREA,"			24
			"RE_INT_DA_SUB_OPERATOR,"		25
			"RE_INT_DA_SUB_COUNTRY"		26
			"CDR_PUB_INT_VERSION,"			27
			"CDR_PUB_INT_TICKETTYPE,"		28
			"CDR_PUB_STR_HOSTID,"			29
			"CDR_PUB_INT_CORRELATIONID,"	30
		*/

		pExec->BindParam(21, data->calling.carriers);
		value[0] = '\0';
		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		pExec->BindParam(22, value);

		pExec->BindParam(23, data->called.phone);
		value[0] = '\0';
		if (data->called.area)
		{
			sprintf(value, "0%d", data->called.area);
		}
		pExec->BindParam(24, value);
		pExec->BindParam(25, data->called.carriers);
		value[0] = '\0';
		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		pExec->BindParam(26, value);
		pExec->BindParam(27, SM_CDR_VERSION);
		pExec->BindParam(28, SM_CDR_TICKETTYPE);
		pExec->BindParam(29, base->topology.c_str());
		/*
		if(1 == bizMsg->m_version)
		{
			pExec->BindParam(29, base->smExt.kv["CollectId"].c_str());
		}
		else
		{
			pExec->BindParam(29, base->topology.c_str());
		}
		*/
		pExec->BindParam(30, 0);
		/*
			"CDR_PUB_INT_TICKETSEQUENCEID,"		31
			"CDR_PUB_INT_SERVICESCENARIOUS,"	32
			"CDR_PUB_LNG_SERVID,"				33
			"CDR_PUB_LNG_CUSTID,"				34
			"CDR_PUB_STR_MASTERPRODUCTID,"		35
			"CDR_PUB_INT_SEQ,"					36
			"CDR_PUB_STR_CHARGED_PARTY,"		37
			"CDR_PUB_STR_CALLING_PARTY,"		38
			"CDR_PUB_STR_CALLED_PARTY "			39
		*/
		pExec->BindParam(31, 0);
		pExec->BindParam(32, SERVICES_CENARIOUS_SMS);
		pExec->BindParam(33, userInfo->servID);
		pExec->BindParam(34, userInfo->custID);
		pExec->BindParam(35, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(36, -1);
		pExec->BindParam(37, base->subUnified);
		pExec->BindParam(38, data->callingUnified);
		pExec->BindParam(39, data->calledUnified);
		pExec->BindParam(40, userInfo->aocType);
		pExec->BindParam(41, base->routeRecord);
		traceNumOnff = bizMsg->m_trace_flag;
		pExec->BindParam(42, traceNumOnff);
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(43, bizMsg->billcycle);
		}
		else
		{
			pExec->BindParam(43, (long)base->timestamp);
		}
		pExec->BindParam(44, userInfo->userType);
		pExec->BindParam(45, userInfo->mvnoID);
		pExec->BindParam(46, data->mscAddr);
		pExec->BindParam(47, roamType);
		pExec->BindParam(48, userInfo->ilatnid);
		pExec->BindParam(49, userInfo->lnAcctID);
		pExec->BindParam(50, bizMsg->m_szServiceContextIDStr);
		if( 1 == bizMsg->m_version)
		{
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(51, switchId);
		}
		else
		{
			pExec->BindParam(51, (long)0);
		}
		if(data->called.access)
		{
			sprintf(value, "%d", data->called.access);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(52, value);
		pExec->BindParam(53, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(54, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "insert ok ");
	}
	catch(UDBException& e)
	{
		std::string sql;
		pExec->GetSqlString(sql);
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,	"", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}



	return RET_SUCCESS;
}

int DCReqSMSTEL::Refund(SCCRBase* base, SCCRSMS* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int iPayFlag 							= 1;
	int traceNumOnff						= 0;
	long nextCCTime							= 0;
	string	iCreatFlag;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]				= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	char subStr[BIZ_TEMP_LEN_32]			= {0};

	int roamType							= bizMsg->m_roamtype;
	AREA_INFO *subVisit						= bizMsg->m_visit;
	TSMPara* m_smpara						= (TSMPara*)bizMsg->m_smpara;
	SUserInfo *userInfo						= (SUserInfo*)bizMsg->m_userinfo;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "nextCCTime[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	int debitflag = m_smpara->GetP2PSMSPara()->debitflag;
	if(1 == debitflag)
	{
		UDBSQL* pQuery = dbm->GetSQL(SMS_GetOcsSmDebitInfo);
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, data->SMID);
			if(NULL == pQuery)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "query SQL[SMS_GetOcsSmDebitInfo] failed!" );
			 	return SM_OCP_UNABLE_TO_COMPLY;
			}

			if(pQuery->Next())
			{
				pQuery->GetValue(1, subStr);
				base->subUnified = subStr;						//CDR消息
				DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "subscriptionNumber[%s]", subStr);

				pQuery->GetValue(2, subStr);
				data->callingUnified = subStr;	//CDR消息
				DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "callingNumber[%s]", subStr);

				pQuery->GetValue(3,	subStr);
				data->calledUnified = subStr;		//CDR消息
				DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "calledNumber[%s]", subStr);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "default info ", "");
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}





	//计算计费方类型
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID,"subscription[%s], calling[%s]", base->subscription.phone.c_str(), data->calling.phone.c_str());
	if(!strcasecmp(base->subscription.phone.c_str(), data->calling.phone.c_str()))
	{
		iPayFlag = 1;
		data->calling.area=base->subscription.area;
	}
	else if(!strcasecmp(base->subscription.phone.c_str(), data->called.phone.c_str()))
	{
		iPayFlag = 2;
		data->called.area=base->subscription.area;
	}
	else
	{
		iPayFlag = 3;
	}


	if((!data->called.area) && data->called.country == 86)
	{
		data->called.area = m_smpara->GetCommonPara()->currentProvCode;
		data->called.area = m_smpara->GetCommonPara()->currentAreaCode;
	}
	//组装RE消息
/*
	100   MsgType     70         P2PSMS
	101   EventTimeStamp  YYYYMMDDHHmmSSsss
	000   会话ID
	001   请求序列号
	R01   付费号码
	R02   主叫号码
	R03   被叫号码
	R85   用户付费属性标识 1

	R20    短信业务
	R201  话单类型
	R202  短信消息ID

	R50    增强的计费信息
	R504  主叫号码归属费率区
	R506  主叫号码归属运营商
	R507  被叫号码归属费率区
	R509  被叫号码归属运营商
	R5012 付费号码归属费率区

	R60    可变计费信息
	R601  重发标记                0 -  正常 1 -  重发
	R602  计费类型
	R603  会话上次实际扣费开始时间
	R604  本次计费请求开始时间
	R605  是否进行使用量累计标识

	B03   实扣命令（在带Session的业务中表示通话/Session结束。）
	B036 请求实扣信用单位
	B037  申请实扣信用数量
	B038  是否允许扣费为负数

	B04 返还命令
	B046  请求返还信用单位
	B047   申请返还信用数量

	B06    累计量信息
	B07    资费信息
	B08    余额帐本改变的详细信息
	B21    费率信息查询命令
	B20    余额查询命令
*/
	ocs::UHead uhd;
	ocs::rbsms rbr;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbext ext;
	head.topology = base->topology;
	uhd.uid = bizMsg->m_uid;
	uhd.car = "1";
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	head.creditCtlFlag = atoi(iCreatFlag.c_str());

	//100   MsgType     70         P2PSMS
	head.type = RE_SERVICE_TYPE_INT_SMS_REQ;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose MsgType[%s]", RE_SERVICE_TYPE_STR_SMS_REQ);

	if(bizMsg->m_requestType == 5)
	{
		head.version = 1;
	}
	else
	{
		head.version = 2;
	}
	//000   会话ID
	head.session = bizMsg->m_sessionID;
       if(head.version == 1) head.session.erase(0,3);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SessionID[%s]", head.session.c_str());

	//101   EventTimeStamp  YYYYMMDDHHmmSSsss
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose EventTimeStamp[%ld]", head.stamp);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Serial[%ld]", bizMsg->m_serial);

	// B14 设置信令跟踪标志，通知RB需要信令跟踪
	head.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose TracFlag[%d]", bizMsg->m_trace_flag);

	//R602 计费类型
	head.sreq = SM_SESSION_EVENT_REFUND_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SessionState[%d]", SM_SESSION_EVENT_REFUND_CODE);

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Latn ID[%d]", userInfo->ilatnid);
	bizMsg->m_ilatnId = userInfo->ilatnid;

	//R01 付费号码
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Charge[%s]", base->subscription.phone.c_str());

	//R02 主叫号码
	rbr.calling_nbr = data->calling.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Calling[%s]", data->calling.phone.c_str());

	//R03 被叫号码
	rbr.called_nbr = data->called.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose Called[%s]", data->called.phone.c_str());

	//R201  话单类型
	rbr.charge_type = iPayFlag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ChargeType[%d]", iPayFlag);

	//R202	短信消息ID
	rbr.sm_id = data->SMID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SMID[%s]", data->SMID.c_str());

	//R204	短信消息中心地址
	if(roamType>0)
	{
		rbr.sms_addr = data->SMSCAddress;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SMSCAddress[%s]", data->SMSCAddress.c_str());
	}
	//R504 主叫号码归属费率区
	if (86 != data->calling.country)
	{
		sprintf(value,"00%d",data->calling.country);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CallingArea[%d]", data->calling.country);
	}
	else
	{
		sprintf(value,"0%d",data->calling.area);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CallingArea[%d]", data->calling.area);
	}
	if(roamType>0)
	{
		if(0 == subVisit->area)
		{
			subVisit->area =  data->calling.area;
		}
		sprintf(value,"0%d",subVisit->area);
		rbr.calling_varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CallingVisitArea[%s]", rbr.calling_varea.c_str());
	}

	//R506  主叫号码归属运营商
	rbr.calling_hcarrier = data->calling.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CallingOperator[%d]", data->calling.carriers);

	//R507  被叫号码归属费率区
	if (86 != data->called.country)
	{
		sprintf(value,"00%d",data->calling.country);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CalledArea[%s]", rbr.called_harea.c_str());
	}
	else
	{
		sprintf(value,"0%d",data->called.area);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CalledArea[%s]", rbr.called_harea.c_str());
	}

	//R509 被叫号码归属运营商
	rbr.called_hcarrier = data->called.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CalledOperator[%d]", data->called.carriers);

	//R5012 付费号码归属费率区
	if (86 != base->subscription.country)
	{
		sprintf(value, "00%d", base->subscription.country);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ChargeArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", base->subscription.area);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ChargeArea[%s]", value);
	}

	if(roamType>0)
	{
		//国际漫游
		//R1012 MCS地址
		rbr.msc = data->mscAddr;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CallingMSC[%s]", data->mscAddr.c_str());

		//R5011 漫游类型
		rbr.roam_type = roamType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose RoamType[%d]", roamType);

		//R5016 漫游类型
		rbr.charged_vcarrier = subVisit->szcarriers;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose VisitOperator[%s]", subVisit->szcarriers);
	}

	//R601 重发标记
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose RepeatFlag[%s]", "0");

	//R602 计费类型
	rbr.sreq = SM_SESSION_EVENT_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	//R603  会话上次扣费开始时间
	rbr.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose DebitTime[%s]", "0");

	//R604  本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
    DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose CurDebitTime[%ld]", bizMsg->timestampCCR);

	//R605  是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose RatableTime[%s]", "0");

	//R606  激活用户
	if(userInfo->isActive)
	{
		rbr.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ActiveFlag[%s]", "1");
	}
	else
	{
		//pREMsg->del(RB_CODE_R_ACTIVE_FLAG);
		rbr.active_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose ActiveFlag[%s]", "1");
	}

	refund rfv;
	//B04	返还命令
	//B046 请求实扣信用单位
	rfv.unit = RB_UNIT_CODE_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose BackUnit[%d]", RB_UNIT_CODE_TIME);

	//B047  申请实扣信用数量
	rfv.amount = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "compose BaceMount[1]");

	//R07离线批价扣费标识
	string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
	int ratingflag= m_smpara->GetBillAttr(servAttr);
	sprintf(value,"%d",ratingflag);
	if(ratingflag>=0)
	{
		ext.kv["ratingflag"] = value;
	}
	domain.rfv.push_back(rfv);
	//CEE被叫 CER主叫
	ext.kv["CER"] = bizMsg->m_callingNumber;
	ext.kv["CEE"] = bizMsg->m_calledNumber;

	rbr.accumlator_info = 1;		//B06	累积量信息
	rbr.tariff_info 	= 1;		//B07	资费信息
	rbr.balance_info 	= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 	= 1;		//B20	费率信息查询命令
	rbr.balance_query 	= 1;		//B21	余额查询命令

	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["RTI"] = subVisit->sector_id;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	// 获取批次号
	if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 2);
	}
	else
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 1);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
	ext.kv["batchId"] = bizMsg->m_sBatchId;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		bizMsg->data = (char*)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	traceNumOnff = bizMsg->m_trace_flag;	//IsTraceNum(atol(base->subscription.phone));
	UDBSQL* pExec = dbm->GetSQL(SMS_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		/*
			"OCP_STR_SESSION_ID,"			1
			"OCP_INT_REQ_NBR,"				2
			"OCP_STR_ORIGIN_HOST,"			3
			"OCP_STR_MSG_ID,"				4
			"OCP_STR_SMSC_ADDRESS,"			5
			"OCP_INT_MSG_LENGTH,"			6
			"OCP_STR_ORIGIN_CALLING_NBR,"	7
			"OCP_STR_ORIGIN_CALLED_NBR,"	8
			"SM_LNG_SERIAL,"					9
			"SM_INT_REQ_TYPE,"				10
		*/
		pExec->BindParam(1, bizMsg->m_sessionID );
		pExec->BindParam(2, (int)bizMsg->m_requestNumber);
		pExec->BindParam(3, base->originHost);
		pExec->BindParam(4, data->SMID);
		pExec->BindParam(5, data->SMSCAddress);
		pExec->BindParam(6, data->SMLength );
		pExec->BindParam(7, data->callingNumber);
		pExec->BindParam(8, data->calledNumber);
		pExec->BindParam(9, (long)bizMsg->m_serial);
		pExec->BindParam(10, (int)bizMsg->m_requestType);

		/*
			"SM_INT_SESSION_STATUS,"		11
			"SM_LNG_TIME_TO_NEXT_CCR,"	12
			"RE_LNG_CURRENT_CCR_TIME,"	13
			"RE_INT_PAY_FLAG,"			14
			"RE_STR_SUB_NBR,"			15
			"RE_STR_SUB_AREA,"			16
			"RE_INT_SUB_OPERATOR,"		17
			"RE_INT_SUB_COUNTRY,"		18
			"RE_STR_OA_SUB_NBR,"		19
			"RE_STR_OA_SUB_AREA,"		20
		*/
		pExec->BindParam(11, TORB_ACTION);
		pExec->BindParam(12, (long)nextCCTime);
		pExec->BindParam(13, (long)bizMsg->timestampCCR);
		pExec->BindParam(14, iPayFlag);
		pExec->BindParam(15, base->subscription.phone);
		value[0] = '\0';
		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
		}
		pExec->BindParam(16, value);
		pExec->BindParam(17, base->subscription.carriers);
		value[0] = '\0';
		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
		}
		pExec->BindParam(18, value);

		pExec->BindParam(19, data->calling.phone);
		value[0] = '\0';
		if (data->calling.area)
		{
			sprintf(value, "0%d", data->calling.area);
		}
		pExec->BindParam(20, value);
		/*
			"RE_INT_OA_SUB_OPERATOR,"		21
			"RE_INT_OA_SUB_COUNTRY,"		22
			"RE_STR_DA_SUB_NBR,"			23
			"RE_STR_DA_SUB_AREA,"			24
			"RE_INT_DA_SUB_OPERATOR,"		25
			"RE_INT_DA_SUB_COUNTRY"		26
			"CDR_PUB_INT_VERSION,"			27
			"CDR_PUB_INT_TICKETTYPE,"		28
			"CDR_PUB_STR_HOSTID,"			29
			"CDR_PUB_INT_CORRELATIONID,"	30
		*/

		pExec->BindParam(21, data->calling.carriers);
		value[0] = '\0';
		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		pExec->BindParam(22, value);

		pExec->BindParam(23, data->called.phone);
		value[0] = '\0';
		if (data->called.area)
		{
			sprintf(value, "0%d", data->called.area);
		}
		pExec->BindParam(24, value);
		pExec->BindParam(25, data->called.carriers);
		value[0] = '\0';
		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		pExec->BindParam(26, value);
		pExec->BindParam(27, SM_CDR_VERSION);
		pExec->BindParam(28, SM_CDR_TICKETTYPE);
		pExec->BindParam(29, base->topology.c_str());
		/*
		if(1 == bizMsg->m_version)
		{
			pExec->BindParam(29, base->smExt.kv["CollectId"].c_str());
		}
		else
		{
			pExec->BindParam(29, base->topology.c_str());
		}
		*/
		pExec->BindParam(30, 0);
		/*
			"CDR_PUB_INT_TICKETSEQUENCEID,"		31
			"CDR_PUB_INT_SERVICESCENARIOUS,"	32
			"CDR_PUB_LNG_SERVID,"				33
			"CDR_PUB_LNG_CUSTID,"				34
			"CDR_PUB_STR_MASTERPRODUCTID,"		35
			"CDR_PUB_INT_SEQ,"					36
			"CDR_PUB_STR_CHARGED_PARTY,"		37
			"CDR_PUB_STR_CALLING_PARTY,"		38
			"CDR_PUB_STR_CALLED_PARTY "			39
		*/
		pExec->BindParam(31, 0);
		//if(SR_SMS == bizMsg->m_serviceContextID)
		{
		//	pExec->BindParam(32, SERVICES_CENARIOUS_SMS_SR);
		}
		//else
		{
			pExec->BindParam(32, SERVICES_CENARIOUS_SMS);
		}
		//pExec->BindParam(32, SERVICES_CENARIOUS_SMS);
		pExec->BindParam(33, userInfo->servID);
		pExec->BindParam(34, userInfo->custID);
		pExec->BindParam(35, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(36, -1);
		pExec->BindParam(37, base->subUnified);
		pExec->BindParam(38, data->callingUnified);
		pExec->BindParam(39, data->calledUnified);
		pExec->BindParam(40, userInfo->aocType);
		pExec->BindParam(41, base->routeRecord);
		pExec->BindParam(42, traceNumOnff);
		if(bizMsg->m_version== 1)
		{
			pExec->BindParam(43, bizMsg->billcycle);
		}
		else
			pExec->BindParam(43, (long)base->timestamp);
		pExec->BindParam(44, userInfo->userType);
		pExec->BindParam(45, userInfo->mvnoID);
		pExec->BindParam(46, data->mscAddr);
		pExec->BindParam(47, roamType);
		pExec->BindParam(48, userInfo->ilatnid);
		pExec->BindParam(49, userInfo->lnAcctID);
		pExec->BindParam(50, bizMsg->m_szServiceContextIDStr);
		if( 1 == bizMsg->m_version)
		{
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(51, switchId);
		}
		else
		{
			pExec->BindParam(51, (long)0);
		}
		if(data->called.access)
		{
			sprintf(value, "%d", data->called.access);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(52, value);
		pExec->BindParam(53, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(54, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE, bizMsg->m_sessionID, "insert ok ");
	}
	catch(UDBException& e)
	{
		std::string sql;
		pExec->GetSqlString(sql);
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,	"", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}

