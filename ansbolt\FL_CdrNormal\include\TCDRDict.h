/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                   SM项目组
*All rights reserved.
*
*Filename：
*       TCDRDict.h
*Indentifier：
*
*Description：
*       话单消息组装字段配置信息
*Version：
*       V1.0
*Author:
*      
*Finished：
*       
*History:
*     
********************************************/
#ifndef _TCDR_DICT_H_
#define _TCDR_DICT_H_

#include <vector>
#include "BizDataDef.h"
using namespace std;


class TCDRDict
{
	public:

		static TCDRDict* instance();
		int Destroy();
		
		int Load(const char* path);

		vector<SCDRField*>* GetINField();
		vector<SCDRField*>* GetP2PSMSField();
		vector<SCDRField*>* GetDATAField();
		vector<SCDRField*>* GetISMPField();
		vector<SCDRField*>* GetISMPFieldHB();
		vector<SCDRField*>* GetDSLField();
        vector<SCDRField*>* GetDATA5GField();

	private:

		TCDRDict();
		~TCDRDict();

	private:

		static TCDRDict* m_instance;

		vector<SCDRField*> m_INField;
		vector<SCDRField*> m_P2PSMSField;
		vector<SCDRField*> m_DATAField;
		vector<SCDRField*> m_ISMPField;
		vector<SCDRField*> m_ISMPFieldHB;
		vector<SCDRField*> m_DSLField;
        vector<SCDRField*> m_DATA_5GField;

};


#endif
