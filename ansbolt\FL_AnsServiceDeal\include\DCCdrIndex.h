#ifndef _DCCDR_INDEX_H_
#define _DCCDR_INDEX_H_
#include "DCBizMsgDef.h"
#include "DCSeriaOp.h"
#include "CdrIndexDef.h"
#include <string.h>
#include <stdio.h>
struct STIndRecord
{
	int  latn;					//本地网
	long lnCID;					//话单唯一标识 TicketID
    long lnSourceId;			//记录标识(source_id)
	int  iClockInterval;		//交换机时钟差
	int  iFlag;					//0 不判时间交叉，1 判时间交叉 ，2 进行剔重回退
	char sAccNbr[24];			//计费号码
	char sRecodeKey[80];		//话单key  	（回退时可不填）
	char sBeginTime[16];		//话单开始时间	（回退时可不填）
	char sEndTime[16];			//话单结束时间	（回退时可不填）

	STIndRecord()
	{
		latn = 0;
		lnCID = 0;
		lnSourceId = 0;
		iClockInterval = 0;
		iFlag = 0;
		memset(sAccNbr,0,24);
		memset(sRecodeKey,0,80);
		memset(sBeginTime,0,16);
		memset(sEndTime,0,16);
	}

	STIndRecord& operator=(const STIndRecord&value)
	{
		latn = value.latn;
		lnCID = value.lnCID;
		lnSourceId = value.lnSourceId;
		iClockInterval = value.iClockInterval;
		iFlag = value.iFlag;

		strcpy(sAccNbr,value.sAccNbr);
		strcpy(sRecodeKey,value.sRecodeKey);
		strcpy(sBeginTime,value.sBeginTime);
		strcpy(sEndTime,value.sEndTime);
		return *this;
	}

	void ToString(std::string & str)
	{
		char buf[1024] = {0};
		sprintf(buf,"latn:%d|cid:%ld|nbr:%s|key:%s|begin:%s|end:%s",latn,lnCID,sAccNbr,sRecodeKey,sBeginTime,sEndTime);
		str += buf;
		return;
	}
};
class DCCdrIndex
{
public:
	DCCdrIndex();
	~DCCdrIndex();
	void SetIndexInfo(STBizMsg *bizMsg,const char *startTime,const char *endTime,const char *append = "\0");
	void ToString(std::string & str);
private:
	void SendFileManer(STBizMsg *bizMsg,string suff);
	void EmitFilManer(STBizMsg *bizMsg,ocs::CdrIndexDef &cdridx);
	STIndRecord idxRecord;
	DCSeriaEncoder * _en;
};
#endif