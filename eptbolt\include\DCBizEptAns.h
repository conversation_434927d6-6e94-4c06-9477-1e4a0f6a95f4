/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizEptAns.h
*Indentifier：
*		
*Description：
*		应答异常处理
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DCBIZ_EPT_REA_H__
#define __DCBIZ_EPT_REA_H__
#include "DCBasePlugin.h"
#include "DCBizEpt.h"
#include "DCAnsPara.h"
//
class DCBizEptAns : public DCBizEpt,public DCBasePlugin
{
	public:

		DCBizEptAns(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */) 	   :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCBizEptAns()
		{
		
		}

		virtual  int Work(void * data);
		
		
	protected:
		virtual int init();
		virtual int process(void* input, void* output);

		

		virtual int SwitchCommon(STBizMsg* bizMsg);
		virtual int SwitchDATA(STBizMsg* bizMsg);
		int SwitchRATA(STBizMsg* bizMsg);
		int SwitchCCG(STBizMsg* bizMsg);
		int InitAns(STBizMsg* bizMsg);
		int Update(STBizMsg* bizMsg);
		int Term(STBizMsg* bizMsg);
		int Event(STBizMsg* bizMsg);

		//int DataAnsError(STBizMsg* bizMsg);
		int InitData(STBizMsg* bizMsg);
		int UpdateData(STBizMsg* bizMsg);
		int TermData(STBizMsg* bizMsg);

		int Switch5G(STBizMsg* bizMsg);
		int Init5G(STBizMsg* bizMsg);
		int Update5G(STBizMsg* bizMsg);
		int Term5G(STBizMsg* bizMsg);

		//PGW
		int SwitchPGW(STBizMsg* bizMsg);
		int InitPGW(STBizMsg* bizMsg);
		int UpdatePGW(STBizMsg* bizMsg);
		int TermPGW(STBizMsg* bizMsg);
		int UpdateRecvRGNum(STBizMsg* bizMsg);
	private:
		DCAnsPara* m_anspara;
		
};

#endif

