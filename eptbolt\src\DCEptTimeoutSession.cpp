#include "DCEptTimeoutSession.h"
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "DCOcpMsgDef.h"
#include "DCMqProduceServer.h"
#include "DCRFData.h"
#include "DCOBJSet.h"
#include "DCEptMsgDef.h"
#include "UHead.h"
#include <sys/time.h>
#include <stdio.h>

using namespace ocs;

int DCEptTimeoutSession::Work(void *data)
{
	STBizMsg* bizMsg = (STBizMsg*)data;
	bizMsg->m_freeFlag = bizMsg->m_RARFlag/10;
	if(bizMsg->m_RARFlag%10 == 3)
		bizMsg->m_RARFlag = 1;
	else if(bizMsg->m_RARFlag%10 == 4)
	{
		bizMsg->m_sendASRFlag = 0;
		bizMsg->m_RARFlag = 0;
	}
	else if(bizMsg->m_RARFlag%10 == 5)
	{
		bizMsg->m_sendASRFlag = 1;
		bizMsg->m_RARFlag = 0;
	}
	//PERF LOG
	//bizMsg->m_type = MSG_TYPE_TIMEOUT_SESSION;
	//bizMsg->m_perf.GetTimeT3_B();
	
	int ret = 0;
	if(bizMsg->m_freeFlag == 1)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID,  "free session flag 1 sevice[%u]", bizMsg->m_serviceContextID);
		DelSession(bizMsg);
	}
	if(bizMsg->m_freeFlag == 2)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID,  "free session flag 2 sevice[%u]", bizMsg->m_serviceContextID);

		//需要出清单的免费号码
		ret = RET_CDR;
		//DelSession(bizMsg);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID,  "service type[%u]", bizMsg->m_serviceContextID);
		switch(bizMsg->m_serviceContextID)
		{
			case VOICE:
				{
					ret = SwitchVOICE(bizMsg);
				}
				break;
			case SMS:
				{
					ret =  SwitchSMS(bizMsg);
				}
				break;
			case DATA:
			case CCG:
				{
					ret =  SwitchDATA(bizMsg);
				}
				break;
			case PGW:
				{
					ret =  SwitchPGW(bizMsg);
				}
				break;
            case DATA_5G:
                {
                    ret =  Switch5G(bizMsg);
                }
                break;
			case ISMP:
			case HRS:
				{
					ret =  SwitchISMP(bizMsg);
				}
				break;
			case DSL:
				{
					ret =  SwitchDSL(bizMsg);
				}
				break;
			default:
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "unknow business service type[%d]", bizMsg->m_serviceContextID);
				}
				break;
		}
	}
	
	//PERF LOG
	//bizMsg->m_perf.GetTimeT3_E();
	
	return ret;
}


int DCEptTimeoutSession::SwitchVOICE(STBizMsg* bizMsg)
{	
	int ret = 0;
	//是否组装RBR消息开关控制
	int onOff = bizMsg->m_anspara->GetCommonPara()->iSendEPTRBROnOff;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, bizMsg->m_sessionID, "session status:[%d], rbr onoff[%d]", bizMsg->m_requestType, onOff);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				SendASR(bizMsg);
				if(1 == onOff)
				{
					ret = SendTermRER(bizMsg);
				}
				DelSession(bizMsg);
				return ret;
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				SendASR(bizMsg);
				if(1 == onOff)
				{
					SendTermRER(bizMsg);
				}
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return RET_CDR;
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "unknow request type[%d]", bizMsg->m_serviceContextID);
				DelSession(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCEptTimeoutSession::SwitchSMS(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "request type[%d]", bizMsg->m_requestType);
	DelSession(bizMsg);
	return RET_SUCCESS;
}

int DCEptTimeoutSession::SwitchDATA(STBizMsg* bizMsg)
{
	//是否组装RBR消息开关控制
	int onOff = bizMsg->m_anspara->GetCommonPara()->iSendEPTRBROnOff;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "session status:[%d] rating_group[%ld], rbr onoff[%d]", bizMsg->m_requestType, bizMsg->m_ratingGroup, onOff);
	if(bizMsg->m_sendASRFlag == 1)
	{
		DelSession(bizMsg);
		return RET_SUCCESS;
	}
	//发送RAR消息
	if(bizMsg->m_RARFlag)
	{
		SendRAR(bizMsg);
		return 	RET_SUCCESS;
	}

	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				SendASR(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				SendASR(bizMsg);
				if(1 == onOff)
				{
					SendTermRER(bizMsg);
				}
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return RET_CDR;
			}
			break;
            case SM_SESSION_XDR_CODE:
			{
				DelSession(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "unknow business session status", "");
				DelSession(bizMsg);
			}
			break;
	}
    
	return RET_SUCCESS;
	
}

int DCEptTimeoutSession::SwitchPGW(STBizMsg* bizMsg)
{
	//是否组装RBR消息开关控制
	int onOff = bizMsg->m_anspara->GetCommonPara()->iSendEPTRBROnOffPGW;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,bizMsg->m_sessionID, "session status:[%d] rating_group[%ld], rbr onoff[%d]", bizMsg->m_requestType, bizMsg->m_ratingGroup, onOff);

	if(bizMsg->m_sendASRFlag == 1)
	{
		DelSession(bizMsg);
		return RET_SUCCESS;
	}
	//发送RAR消息
	if(bizMsg->m_RARFlag)
	{
		SendRAR(bizMsg);
		return 	RET_SUCCESS;
	}
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				SendASR(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				SendASR(bizMsg);
				if(1 == onOff)
				{
					SendTermRER(bizMsg);
				}
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return RET_CDR;
			}
			break;
            case SM_SESSION_XDR_CODE:
			{
				DelSession(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "unknow business session status", "");
				DelSession(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCEptTimeoutSession::Switch5G(STBizMsg* bizMsg)
{
	//是否组装RBR消息开关控制
	int onOff = bizMsg->m_anspara->GetCommonPara()->iSendEPTRBROnOff5G;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,bizMsg->m_sessionID, "session status:[%d] rating_group[%ld], rbr onoff[%d]", bizMsg->m_requestType, bizMsg->m_ratingGroup, onOff);
	if (1 == onOff)
	{
		SendTermRER(bizMsg);
	}
	DelSession(bizMsg);
	return RET_SUCCESS;
}


int DCEptTimeoutSession::SwitchISMP(STBizMsg* bizMsg)
{
	int ret = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,bizMsg->m_sessionID, "session status:[%d]", bizMsg->m_requestType);

	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				SendASR(bizMsg);
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
				return ret;
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				SendASR(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_EVENT_REFUND_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE, "", "unknow business session status", "");
				DelSession(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCEptTimeoutSession::SwitchDSL(STBizMsg* bizMsg)
{
	//是否组装RBR消息开关控制
	int onOff = bizMsg->m_anspara->GetCommonPara()->iSendEPTRBROnOff;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "session status:[%d], rbr onoff[%d]", bizMsg->m_requestType, onOff);
	int ret = 0;
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				SendASR(bizMsg);
				if(1 == onOff)
				{
					ret = SendTermRER(bizMsg);
				}
				DelSession(bizMsg);
				return ret;
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				SendASR(bizMsg);
				if(1 == onOff)
				{
					SendTermRER(bizMsg);
				}
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				//SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE, "", "unknow business session status", "");
				DelSession(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCEptTimeoutSession::SendASR(STBizMsg* bizMsg)
{
	int ret = RET_SUCCESS;
//
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.msgType = 97;
	cca.sessionID = bizMsg->m_sessionID;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.RouteRecord = bizMsg->m_requestNumber;
	cca.trace = bizMsg->m_trace_flag;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
		
		m_print.clear();
		m_print.print(uhd);	
		m_print.print(cca);	
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ASR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	string str = HexEncode(m_en.data(),m_en.size());
	
	// 写入MQ
	/*DCMqProduceServer* producer = bizMsg->m_producer;
	int topicnum=0;
	if(CCG == bizMsg->m_serviceContextID || DATA == bizMsg->m_serviceContextID || PGW == bizMsg->m_serviceContextID)
	{
		topicnum = 3;
	}
	else
	{
		topicnum = bizMsg->m_serviceContextID;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID, "TopicNum[%d]", topicnum);*/


  	if(bizMsg->m_testFlag == 0)
	{
		m_pMsendMsg->insert(pair<string,string>("CCAMSG",str));
		//ret = producer->Produce(str.c_str(), str.length(), bizMsg->m_anstopic);
		/*bizMsg->m_topictype = 1;
		ret = bizMsg->m_plugin->call((void *)(&str), (void *)bizMsg);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, bizMsg->m_sessionID, "Produce failed\n");	
			return RET_ERROR;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID, "send ASR to MQ,msglen:%d, topic[%s]",str.length(), bizMsg->m_anstopic);
		}*/
  	}
	else
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "test message\n");	
	return RET_SUCCESS;
}

int DCEptTimeoutSession::SendRAR(STBizMsg * bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "session status:[%d] rating_group[%ld]", bizMsg->m_requestType, bizMsg->m_ratingGroup);
	int ret = RET_SUCCESS;
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.msgType = 98;
	cca.sessionID = bizMsg->m_sessionID;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
       cca.requestNumber = bizMsg->m_requestNumber;
	cca.RouteRecord = bizMsg->m_requestNumber;
       cca.trace = bizMsg->m_trace_flag;
	
	if(cca.ServiceContextID == PGW || cca.ServiceContextID == DATA)
	{
		DATAUSU mscc;
		char value[256] = {0};
		char *prg;
		char *ppid;
		strcpy(value,bizMsg->m_topology);
		strtok(value,";");
		prg = strtok(NULL,";");
		ppid = strtok(NULL,";");
		if(prg != NULL)
		mscc.ratinggroup = atol(prg);
		if(ppid != NULL)
		mscc.ProductOfferId = ppid;
		cca.MSCC.push_back(mscc);
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
		
		m_print.clear();
		m_print.print(uhd);
		m_print.print(cca);	
		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RAR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	string str = HexEncode(m_en.data(),m_en.size());
	
	// 写入MQ
	/*DCMqProduceServer* producer = bizMsg->m_producer;
	int topicnum=0;
	if(CCG == bizMsg->m_serviceContextID || DATA == bizMsg->m_serviceContextID || PGW == bizMsg->m_serviceContextID)
	{
		topicnum = 3;
	}
	else
	{
		topicnum = bizMsg->m_serviceContextID;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID, "TopicNum[%d]", topicnum);*/


        
	if(bizMsg->m_testFlag == 0)
	{
		m_pMsendMsg->insert(pair<string,string>("CCAMSG",str));
		//ret = producer->Produce(str.c_str(), str.length(), bizMsg->m_anstopic);
		/*bizMsg->m_topictype = 1;
		ret = bizMsg->m_plugin->call((void *)(&str), (void *)bizMsg);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, bizMsg->m_sessionID, "Produce failed\n");	
			return RET_ERROR;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID, "send RAR to MQ,msglen:%d, topic[%s]",str.length(), bizMsg->m_anstopic);
		}*/
  	}
	else
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "test message\n");	
	
	return RET_SUCCESS;
}

int DCEptTimeoutSession::init()
{
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	return 0;
}

int DCEptTimeoutSession::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	SEPTMsg* base = pset->get<SEPTMsg>();
	bizMsg->m_base = base;
	bizMsg->m_anspara = m_anspara;	
	m_pMsendMsg =(std::multimap<string,string>*)bizMsg->m_pSendMsg;
	int ret = Work(bizMsg);
	return ret;
	
}

DYN_PLUGIN_CREATE(DCEptTimeoutSession, "FC_EPTTIMEOUTSESSION", "FC_EptTimeoutSession", "1.0.0")
