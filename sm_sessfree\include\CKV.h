#ifndef __CKV_H__
#define __CKV_H__
#include <vector>
#include <string>
#include <stdint.h>

class CKVVal
{
public:
	CKVVal();

	CKVVal(const char* key);

	virtual ~CKVVal();

	void key(const char* key);

	const char* key() const {return m_key;}

	int type() const {return m_type;}

	void value(const char* val);

	void value(int8_t val);

	void value(uint8_t val);

	void value(int16_t val);

	void value(uint16_t val);

	void value(int32_t val);

	void value(uint32_t val);

	void value(int64_t val);

	void value(uint64_t val);

	void value(float val);

	void value(double val);

	void value(void* ptr);

    void strto(int type);

	std::string toString() const;

	const char* str() const;

	int8_t getInt8() const;

    uint8_t getUInt8() const;

    int16_t getInt16() const;

	uint16_t getUInt16() const;

    int32_t getInt32() const;

	uint32_t getUInt32() const;

    int64_t getInt64() const;

    uint64_t getUInt64() const;

	float getFloat() const;

	double getDouble() const;

	void* getPtr() const;

	virtual CKVVal* clone() const;

	virtual void clear();

	virtual int print(FILE* fp, int depth = 0) const;

    bool val_equal(const CKVVal& rh) const;

    bool val_less(const CKVVal& rh) const;

    void val_plus(const CKVVal& rh);

    void val_assign(const CKVVal& rh);

private:
	CKVVal(const CKVVal&);
	CKVVal& operator=(const CKVVal&);

	friend int sparse_compose_ckv(char*, unsigned int, unsigned int &, const CKVVal*);
	friend int sparse_decompose_ckv(const char*, unsigned int, CKVVal*);

	friend int compact_compose_ckv(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVVal* val);
	friend int compact_decompose_ckv(const char* psrc, unsigned int max_size, unsigned int &size,  CKVVal* pval);

protected:
	char m_key[12];
	char m_val[8];
	char m_type;
};

class CKVNode : public CKVVal
{
public:
	CKVNode();

	CKVNode(const char* key);

	virtual ~CKVNode();

	void add(CKVVal* kv);

	CKVVal* get(const char* key) const;

	void erase(const char* key);

	CKVVal* remove(const char* key);

	void add(std::vector<CKVVal*>& child);

	std::vector<CKVVal*>& getChild(){return m_child; }

	std::vector<CKVVal*>& body(){return m_child;}

	virtual CKVVal* clone() const;

	virtual void clear();

	virtual int print(FILE* fp, int depth = 0) const;

private:
	friend int sparse_compose(char*, unsigned int, unsigned int &, const CKVNode*);
	friend int sparse_decompose(const char*, unsigned int, CKVNode*);

	friend int compact_compose(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVNode* node);
	friend int compact_decompose(const char* psrc, unsigned int size,  CKVNode* node);

private:
	std::vector<CKVVal*>  m_child;
};

CKVVal* make_ckv(const char* key, const char* val);
CKVVal* make_ckv(const char* key, int8_t val);
CKVVal* make_ckv(const char* key, uint8_t val);
CKVVal* make_ckv(const char* key, int16_t val);
CKVVal* make_ckv(const char* key, uint16_t val);
CKVVal* make_ckv(const char* key, int32_t val);
CKVVal* make_ckv(const char* key, uint32_t val);
CKVVal* make_ckv(const char* key, int64_t val);
CKVVal* make_ckv(const char* key, uint64_t val);
CKVVal* make_ckv(const char* key, float val);
CKVVal* make_ckv(const char* key, double val);
CKVVal* make_ckv(const char* key, void* val);

/*** 稀疏型 ***/
int sparse_compose_ckv(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVVal* val);
int sparse_decompose_ckv(const char* psrc, unsigned int size,  CKVVal* pval);

int sparse_compose(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVNode* node);
int sparse_decompose(const char* psrc, unsigned int size,  CKVNode* node);

/*** 紧凑型 ***/
int compact_compose_ckv(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVVal* val);
int compact_decompose_ckv(const char* psrc, unsigned int max_size, unsigned int &size,  CKVVal* pval);

int compact_compose(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVNode* node);
int compact_decompose(const char* psrc, unsigned int size,  CKVNode* node);

/*** 针对整个消息包 ***/
/*** 仅将root的子节点进行封包***/
/*** flag=0:稀疏型，flag=1:紧凑型***/
int compose_body(int flag, char* body, unsigned int max_size, unsigned int &body_size, const CKVNode* root);
int decompose_body(int flag, const char* body, unsigned int body_size, CKVNode* root);

/*** 针对单个组进行封包 **/
/*** flag=0:稀疏型，flag=1:紧凑型***/
int compose_group(int flag, char* body, unsigned int max_size, unsigned int &body_size, const CKVNode* group);
int decompose_group(int flag, const char* body, unsigned int body_size, CKVNode* group);

#endif // __CKV_H__
