#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCPluginManer.h"
#include "DCSeriaOp.h"
#include "DCRbMsgDef.h"
#include "REMsgTypeDef.h"
#include "UHead.h"

using namespace std;
using namespace ocs;

DCPluginManer m_pm; 

int Initialize()
{
	int ret = 0;
	char *buf =getenv("OCS_CONFIG");
	if(NULL==buf)
	{
	
	}

	
	//日志初始化
	ret = DCLOGINIT("ansbolt","ansbolt",6,"/public/ocs_ah/log/reqlog");
	if(ret)
	{
	  return -1;
	}

	
	//插件初始化
	ret = m_pm.init(buf, "reqbolt",DFM_USE_DBM|DFM_USE_REFRESH|DFM_USE_NOFLOW);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin failed");
		return 1;
	}

	ret = m_pm.load_flow("ansbolt");
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init flow ansbolt failed");
		return 1;
	}

	return 0;
}

int main(int argc,char **argv) 
{

	int ret = Initialize();
	if(ret)
	{
		return ret;
	}


	// 组装RBA
	UHead uhd;
	rbhead rbahead;
	rbresult rba;
	rbext ext;
	gsu_item_t gsv;

	uhd.uid="143807011476877049";
	rbahead.type = RE_SERVICE_TYPE_INT_PGW_ANS;
	rbahead.sreq = atoi(argv[1]);
	rbahead.stamp = 0;
	rbahead.session = "scp002.chinatelecom.com;1207666037;191744830;3681;65637;160030";
	rbahead.serial = 2;
	rbahead.trace = 1;
	rbahead.result = 0;

	rba.fnf_flag = 0;
	rba.tariff_change_time =0;
    rba.final_flag =0;
    rba.test_info = 0;
    rba.fav_flag =0;
    rba.evt_id = 45600;
    rba.plan_id = 26001701;
    rba.dist_fee = 100;


	reserve_item_t res;
	res.rating_group = 160030;
	res.acct_item_id = 30500;
	res.unit = 2;
	res.amount = 100;
	rba.rsv.push_back(res);

	gsu_item_t gsu;
	gsu.rating_group = 1001;
	gsu.acct_item_id = 30500;
	gsu.unit = 3; 
	gsu.amount=20480000;
	rba.gsv.push_back(gsu);

	debit_item_t debit;
	debit.rating_group = 160030;
	debit.acct_item_id = 30500;
	debit.unit = 3; 
	debit.amount=10485761;
	rba.dbv.push_back(debit);

	accum_item_t accum;
	accum.ratable_balance_id = 6020;
	accum.unit = 3;
	accum.amount = 1048576; 
	accum.total=2048576;
	rba.accv.push_back(accum);

	accum.ratable_balance_id = 6021;
	accum.unit = 2;
	accum.amount = 100; 
	accum.total=900;
	rba.accv.push_back(accum);

	tariff_t tariff;
	tariff.ofr_id =70100;
	tariff.amount =100;
	rba.trv.push_back(tariff);

	acctchangeitem_t acct;		
	acct.acct_item_id = 80100;
	acct.unit = 2;	
	acct.amount =80;
	acct.balance =800;
	acct.acct_code_id=1;
	acct.fee_item_id=20;
	rba.actv.push_back(acct);

	
	acctitem_t accti;
	accti.acct_item_id =21200;
	accti.unit =2;
	accti.amount=100;
	rba.bal.push_back(accti);


	// 序列化
	DCSeriaEncoder en(ESeriaBinString);
	en.encode(uhd);
	en.encode(rbahead);
	en.encode(rba);
	en.encode(ext);

	std::string recvCCRMsg = HexEncode(en.data(), en.size());
	std::string sendmsg;

	DCBaseFlow* flow = m_pm.get_flow("ansbolt");
	if(!flow)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find flow[ansbolt]");
		return 1;
	}

    ret = flow->call(&recvCCRMsg, &sendmsg);
	
	return 0;
}
