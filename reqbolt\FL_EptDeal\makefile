include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include

EPT_INC=$(PWD)/include
EPT_SRC=$(PWD)/src
EPT_OBJ=$(PWD)/obj

EPT_CPP=DCEptDeal.cpp desc_EptDeal.cpp
           
EPT_SRCS=$(addprefix $(EPT_SRC)/, $(EPT_CPP))
EPT_OBJS=$(patsubst $(EPT_SRC)/%.cpp, $(EPT_OBJ)/%.o, $(EPT_SRCS))

TLIB= $(RELEASE_PATH)/plugin/libEptDeal.so

INCLUDE =-I$(EPT_INC) \
				 -I$(COMMON_INC) \
				 -I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
         -I$(DFM_INC_PATH) 

LIBPATH= -L$(RELEASE_PATH)/lib
LIBSLIST= -lCommonIF

libtarget=$(TLIB)

tmpvar:=$(call CreateDir, $(EPT_OBJ))
.PHONY:all clean dup

all:$(TLIB)	
$(TLIB): $(EPT_OBJS)
	@echo "build libEptDeal.so----"
	$(CC)  $(DFLAGS)  -o $(TLIB) $(EPT_OBJS) $(LIBPATH) $(LIBSLIST)
$(EPT_OBJS):$(EPT_OBJ)/%.o:$(EPT_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	
$(EPT_SRC)/desc_EptDeal.cpp:$(EPT_SRC)/desc_EptDeal.clog
	$(TOOL)/clogtool -i $< -o $@

clean:
	@rm -rf $(EPT_OBJS) $(TLIB)

dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
	
       