/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCReqBaseFlow.h
*Indentifier：
*
*Description：
*      reqbolt主类
*Version：
*       V1.0
*Author:
*       
*Finished：
*       
*History:
*     
********************************************/
#ifndef DC_REQ_BASE_FLOW_H_
#define DC_REQ_BASE_FLOW_H_

#include "DCBaseFlow.h"
#include "DCOBJSet.h"
#include "DCSeriaOp.h"
#include "DCBizMsgDef.h"
#include "DCOcpMsgDef.h"
#include "UStaMsg.h"

//#include "DCMqProduceServer.h"

using namespace std;
using namespace ocs;

class DCReqBaseFlow: public DCBaseFlow 
{
public:
	DCReqBaseFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBaseFlow(category,func,version),m_de(ESeriaBinString),m_en(ESeriaBinString)
	{
	
	}
	
	virtual ~DCReqBaseFlow()
	{
	
	}

protected:	
	virtual int init();
	virtual int process(void* input, void* output);
	virtual int call_all(void* input, void* output);

private:

	int composeStaMsg(STBizMsg* bizMsg, int nRet);
    int composeOfflineCdrMsg(STBizMsg* bizMsg, SCCRBase* base, SCCR5GInfo* data);
    int composeCdrMsg(SComHead &pSComhead, STBizMsg* bizMsg, SCCRBase* base, SCCR5GInfo* data, SUSU *MSCC);
	bool checkSumFile(ocs::SComHead& pSComhead,STBizMsg* bizMsg,ocs::RatingMessageInfo_t& bodySM);

	DCOBJSetPool* m_pool;
	DCSeriaDecoder m_de;
	DCSeriaEncoder m_en;
	
	DCSeriaPrinter m_print;
	//DCMqProduceServer* m_producer;
	const char* m_Topic[5];
	int m_cdrFlag;
	int m_offline_cdr;  //离线计费模式落文件开关
	int m_combinaSwitch;
};

#endif 

