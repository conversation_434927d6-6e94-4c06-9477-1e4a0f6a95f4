﻿#include "DCReqVOICETEL.h"
#include "DCCommonIF.h"
#include "BizCdrDef.h"
#include "BizCdrDefTEL.h"
#include "UHead.h"

DCReqVOICETEL::DCReqVOICETEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

DCReqVOICETEL::~DCReqVOICETEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

int DCReqVOICETEL::SwitchReqType(STBizMsg* bizMsg)
{
	if(!bizMsg->m_base)
	{
		return RET_ERROR;
	}
	int ret = RET_SUCCESS;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	SCCRVOICE* data =(SCCRVOICE*)bizMsg->m_extend;


	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				ret = Init(base, data, bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				ret = Update(base, data, bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{

				ret = Term(base, data, bizMsg);
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				ret = Event(base, data, bizMsg);
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				ret = XdrEvent(base, data, bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "invalid request type[%d]", bizMsg->m_requestType);
				ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
			}
			break;
	}

	return ret;
}

int DCReqVOICETEL::Init(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;

	unsigned int roamType				= 0;
	unsigned int longType				= 0;
	unsigned int RERcallType			= 0;
	unsigned int CDRcallType			= 0;
	unsigned int earea					= 0;

	long nextCCTime						= 0;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO *subVisit					= NULL;
	SUserInfo * userInfo				= NULL;
	string	iCreatFlag;
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbvoice rbr;
	ocs::rsu_t rsu;
	ocs::usu   u;
	ocs::debit totalusu;
	ocs::rbext ext;

	longType = bizMsg->m_longtype;
	roamType = bizMsg->m_roamtype;
	subVisit = bizMsg->m_visit;
	userInfo = bizMsg->m_userinfo;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;

	if(bizMsg->m_calledNumberIsDefaultArea)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "called area use calling visit area[%d]",subVisit->area);
		data->called.area = subVisit->area;
		data->called.province = subVisit->province;
	}
	CDRcallType = bizMsg->m_cdrCallType;
	if(3==data->redirectionInfo && 3==CDRcallType)
	{
		RERcallType =3;
	}
	else if(3==CDRcallType)
	{
		RERcallType = 4;
	}
	else
	{
		RERcallType = bizMsg->m_cdrCallType;
	}

	if(CDRcallType == 1)
	{
		data->calling.area=base->subscription.area;
	}
	else if(CDRcallType == 2)
	{
		data->called.area = base->subscription.area;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CDRcallType is 2,called area base->subscription.area[%d]");
	}

	//获取配额信息
	if((conf =smpara->GetServiceQuotaConf(VOICE_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find service quota config[%d]", VOICE_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}


	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 2*conf->TOKEN;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	uhd.uid = bizMsg->m_uid;
	uhd.car = "1";
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	bizMsg->m_sessionID[255] = '\0';
	head.type = RE_SERVICE_TYPE_INT_VOICE_REQ;
	head.version = 2;
	head.sreq  = bizMsg->m_requestType;
	head.stamp = bizMsg->timestampCCR;
	head.session = string(bizMsg->m_sessionID);
	head.serial = bizMsg->m_serial;
	head.trace =  bizMsg->m_trace_flag;
	head.result = 0;
	head.topology = base->topology;
	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	head.creditCtlFlag = atoi(iCreatFlag.c_str());

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	//101   EventTimeStamp
    sprintf(value, "%ld", bizMsg->timestampCCR);

	//R65   服务代码
	if(!data->bearerCapability.empty())
	{
    	if(strcmp(data->bearerCapability.c_str(), "8") == 0)
    	{
    		strcpy(value, "7");
    	}
    	else
    	{
    		strcpy(value, data->bearerCapability.c_str());
    	}
        rbr.service_code = atoi(value);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SERVICE_CODE[%d]", rbr.service_code);

	//R71   会话开始时间
	rbr.sess_start_time = "0";
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SESSION_START_TIME[%s]",  "0");

	//R75     被叫接入号码
	if (0 == data->called.access)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "delete RB_CODE_R_ACCESS_NUMBER", "");
	}
	else
	{
		sprintf(value, "%d", data->called.access);
		rbr.access_nbr = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ACCESS_NUMBER[%s]" , value);
	}

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose latnid[%d]" , rbr.latn_id);
	bizMsg->m_ilatnId = userInfo->ilatnid;

	//R85   用户付费属性标识
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_PAY_TYPE[1]", "");

	//R01   付费号码
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CHARGE[%s]",  base->subscription.phone.c_str());

	//R03   被叫号码
	rbr.called_nbr = data->called.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED[%s]", data->called.phone.c_str());

	//呼转流程
	if((3 == RERcallType) || (4 == RERcallType))
	{
		//R02   主叫号码
		rbr.calling_nbr = base->subscription.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]", base->subscription.phone.c_str());

		//R105  连接号码
		rbr.connect_nbr = data->calling.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT[%s]",data->calling.phone.c_str());

		//R1012  主叫交换机
		rbr.calling.msc = data->callingVLR;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_MSC[%s]",data->MSC.c_str());

		//R504  主叫号码归属费率区
		sprintf(value, "0%d", base->subscription.area);
		rbr.calling.harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]", value);

		//R505  主叫号码拜访费率区
		if(6==roamType || 9==roamType)
		{
			sprintf(value, "00%d", subVisit->area);
		}
		else sprintf(value, "0%d", subVisit->area);
		rbr.calling.varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

		//R506  主叫号码归属运营商
		sprintf(value, "%d", base->subscription.carriers);
		rbr.calling.hcarrier = atoi(value);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

		//R508  被叫号码拜访费率区
		if (86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		else
		{
			sprintf(value, "0%d", data->called.area);
		}
		rbr.called.varea= value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

		//R5013 连接号码归属区
		if (86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else
		{
			sprintf(value, "0%d", data->calling.area);
		}
		rbr.connect_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_AREA[%s]", value);

		//R5014  连接号码拜访区
		rbr.connect_varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_VISIT_AREA[%s]",value);


	}
	//被叫流程
	else if(2 == RERcallType)
	{
		//R02   主叫号码
		rbr.calling_nbr = data->calling.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING [%s]",data->calling.phone.c_str());

		//R504  主叫号码归属费率区
		//R505  主叫号码拜访区号
		if (86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
			rbr.calling.harea = value;
			rbr.calling.varea= value;
		}
		else
		{
			sprintf(value, "0%d", data->calling.area);
			rbr.calling.harea = value;
			rbr.calling.varea= value;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]",value);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

		//R506  主叫号码归属运营商
		sprintf(value, "%d", data->calling.carriers);
		rbr.calling.hcarrier = atoi(value);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

		//R508  被叫号码拜访费率区
		if(6==roamType || 9==roamType)
		{
			sprintf(value, "00%d", subVisit->area);
		}
		else sprintf(value, "0%d", subVisit->area);
		rbr.called.varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

		//R1013  被叫交换机
		rbr.called.msc = data->calledVLR;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_MSC[%s]", data->calledVLR.c_str());


	}
	//主叫流程
	else
	{
		//R02   主叫号码
		rbr.calling_nbr = data->calling.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]", data->calling.phone.c_str());

		//R504  主叫号码归属费率区
		if (86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
			rbr.calling.harea = value;
		}
		else
		{
			sprintf(value, "0%d", data->calling.area);
			rbr.calling.harea = value;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]", value);

		//R505  主叫号码拜访区号
		if(6==roamType || 9==roamType)
		{
			sprintf(value, "00%d", subVisit->area);
		}
		else sprintf(value, "0%d", subVisit->area);
		rbr.calling.varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

		//R506  主叫号码归属运营商
		sprintf(value, "%d", data->calling.carriers);
		rbr.calling.hcarrier = atoi(value);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

		//R508  被叫号码拜访费率区
		if (86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
			rbr.called.varea= value;
		}
		else
		{
			sprintf(value, "0%d", data->called.area);
			rbr.called.varea= value;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

		//R1012  主叫交换机
		rbr.calling.msc = data->callingVLR;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_MSC[%s]", data->MSC.c_str());


	}

	//R507  被叫号码归属费率区
	if (86 != data->called.country)
	{
		sprintf(value, "00%d", data->called.country);
		rbr.called.harea= value;
	}
	else
	{
		sprintf(value, "0%d", data->called.area);
		rbr.called.harea= value;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_AREA[%s]", value);

	//R509  被叫号码归属运营商
	sprintf(value, "%d", data->called.carriers);
	rbr.called.hcarrier = data->called.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_OPERATOR[%s]",value);

	//R5010 长途类型
	sprintf(value, "%d", longType);
	rbr.long_type = longType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_LONG_TYPE[%s]", value);

	//R5011 漫游类型
	sprintf(value, "%d", roamType);
	rbr.roam_type = roamType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ROAM_TYPE[%s]", value);

	//R5012 付费号码归属费率区
	sprintf(value, "0%d", base->subscription.area);
	rbr.charged_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CHARGING_AREA[%s]", value);

	//R5015  拜访地运营商
	rbr.charged_vcarrier = subVisit->szcarriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_VISIT_CARRIER[%s]",  subVisit->szcarriers);

       //RTI 国际漫游资费区
       DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RTI[%s]",  subVisit->sector_id);

	//R601 重发标记
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_REPEAT_FLAG[%s]", "0");

	//R602 计费类型
	rbr.sreq = SM_SESSION_INITIAL_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SESSION_STATE[%s]",  SM_SESSION_INITIAL_STRING);

	//R603  会话上次扣费开始时间
	rbr.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_PRE_DEBIT_TIME[%s]", "0");

	//R604  本次计费请求开始时间
	sprintf(value,"%ld",bizMsg->timestampCCR);
	rbr.cur_dtime = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CUR_DEBIT_TIME[%s]", value);

	//R605  是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_RATABLE_TIME[%s]",  "0");

	//R606  激活用户(init激活可配置)
	if(userInfo->isActive && 1== smpara->GetINPara()->iInitActiveSwitch)
	{
		rbr.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ACTIVE_FLAG[%s]",  "1");
	}


	//R615 小区优惠标识
	if(smpara->GetINPara()->favCellIDSwitch)
	{
		rbr.fav_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_FAV_FLAG[%s]", "1");
	}


	//R103  话单类型
	sprintf(value, "%d", RERcallType);
	rbr.call_type = RERcallType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CDR_TYPE[%s]",  value);

	//R106  主叫小区(或基站)
	rbr.calling.cell = data->callingCellID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_CELLID_OR_SAI[%s]",  data->callingCellID.c_str());

	//R107  被叫小区(或基站)
	rbr.called.cell = data->calledCellID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_CELLID_OR_SAI[%s]",  data->calledCellID.c_str());

	//R108  主叫用户位置区
	rbr.calling.lai = data->callingVLR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_LAI[%s]",  data->callingVLR.c_str());

	//R109  被叫用户位置区
	rbr.called.lai = data->calledVLR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_LAI[%s]", data->calledVLR.c_str());

	//R1116 短号处理
	if(!data->calledShortNumber.empty())
	{
		rbr.called_short_nbr = data->calledShortNumber;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_AB_CALLED[%s]", data->calledShortNumber.c_str());
	}


	//R1117 短号处理
	if(!data->calledGropNum.empty())
	{
		rbr.called_group_nbr = data->calledGropNum;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_AB_GP_NUMVBER[%s]", data->calledGropNum.c_str());
	}


	//R1118 短号处理
	if(!data->calledVpnCallType.empty())
	{
		rbr.called_vpn_calltype = atoi(data->calledVpnCallType.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_VPN_CALLTYPE[%s]", data->calledVpnCallType.c_str());
	}


	//B016   请求信用单位
	rsu.unit = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_REQUESTED_UNIT[1]","" );

	//B017   申请信用数量
	sprintf(value, "%d", conf->TOKEN);
	rsu.amount = conf->TOKEN;
	domain.rsv.push_back(rsu);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_REQUESTED_AMOUNT[%s]",value);

	rbr.latn_id = userInfo->ilatnid;
	rbr.accumlator_info = 1;
	rbr.tariff_info = 1;
	rbr.tariff_info = 1;
	rbr.rating_info = 1;
	rbr.balance_query = 1;
	//CEE被叫 CER主叫
	ext.kv["CER"] = bizMsg->m_callingNumber;
	ext.kv["CEE"] = bizMsg->m_calledNumber;
	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.test_info = 1;
		rbr.acct_info = 1;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["RTI"] = subVisit->sector_id;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	// VoLTE 新增字段
	if (1 == data->VoLTEFlag)
	{
		ext.kv["R501"] = "3";
	}
	if (data->callaccesstype.length())
	{
		ext.kv["R623"] = data->callaccesstype;
	}

	// R3019
	if (1 == data->VoLTEFlag)
	{
		if(2==RERcallType)//被叫
		{
			ext.kv["R3019"] = rbr.called.msc;//R1013
		}
		else
		{
			ext.kv["R3019"] = rbr.calling.msc;//R1012
		}
	}
	else
	{
		if(2==RERcallType)//被叫
		{
			ext.kv["R3019"] = rbr.called.cell;//R107
		}
		else
		{
			ext.kv["R3019"] = rbr.calling.cell;//R106
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "volte[%d] callType[%d] compose R3019[%s]", data->VoLTEFlag, RERcallType, ext.kv["R3019"].c_str());


	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE,"","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char *)m_en.data();

	int traceNumOnff = bizMsg->m_trace_flag;

	UDBSQL *pExec  = NULL;

	pExec =  pdbm->GetSQL(Voice_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID);
		pExec->BindParam(2, (int)bizMsg->m_requestNumber);
		pExec->BindParam(3, base->subscriptionData);
		pExec->BindParam(4, base->originHost);
		pExec->BindParam(5, (long)conf->TOKEN);
		pExec->BindParam(6, SM_SESSION_INITIAL_CODE);
		pExec->BindParam(7, TORB_ACTION);
		pExec->BindParam(8, nextCCTime);
		pExec->BindParam(9, base->subscription.phone);
		pExec->BindParam(10, data->calling.phone);

		if(CDRcallType > 2)
		{
			pExec->BindParam(11, base->subscription.phone);
		}
		else
		{
			pExec->BindParam(11, data->called.phone);
		}

		pExec->BindParam(12, (int)RERcallType);
		pExec->BindParam(13, data->callingLAI);
		pExec->BindParam(14, data->calledLAI);

		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(15, value);

		if(86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else if (data->calling.province)
		{
			sprintf(value, "0%d", data->calling.province);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(16, value);//RE_STR_CALLING_PROVINCE

		//把值绑定到RE_STR_CALLING_AREA字段，记话单用
		if(86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else if (data->calling.area)
		{
			sprintf(value, "0%d", data->calling.area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(17, value); //RE_STR_CALLING_AREA
		pExec->BindParam(18, data->calling.carriers);

		if(CDRcallType > 2)
		{
			if (base->subscription.country)
			{
				sprintf(value, "00%d", base->subscription.country);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(19, value);

			if(86 != base->subscription.country)
			{
				sprintf(value, "00%d", base->subscription.country);
			}
			else if (base->subscription.province)
			{
				sprintf(value, "0%d", base->subscription.province);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(20, value);

			if(86 != base->subscription.country)
			{
				sprintf(value, "00%d", base->subscription.country);
			}
			else if (base->subscription.area)
			{
				sprintf(value, "0%d", base->subscription.area);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(21, value); //RE_STR_CALLED_AREA

			//if (base->subscription.carriers)
			{

				pExec->BindParam(22, base->subscription.carriers);
			}

		}
		else
		{
			if (data->called.country)
			{
				sprintf(value, "00%d", data->called.country);
			}
			else
			{
				value[0] = '\0';

			}
			pExec->BindParam(19, value);

			if(86 != data->called.country)
			{
				sprintf(value, "00%d", data->called.country);
			}
			else if (data->called.province)
			{
				sprintf(value, "0%d", data->called.province);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(20, value);//RE_STR_CALLED_PROVINCE

			if(86 != data->called.country)
			{
				sprintf(value, "00%d", data->called.country);
			}
			else if (data->called.area)
			{
				sprintf(value, "0%d", data->called.area);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(21, value);//RE_STR_CALLED_AREA

			//if (data->called.carriers)
			{
				pExec->BindParam(22, data->called.carriers);
			}
		}

		if(data->called.access)
		{
			sprintf(value, "%d", data->called.access);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(23, value);
		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(24,  value);
		if (base->subscription.province)
		{
			if(86 != base->subscription.country)
			{
				sprintf(value, "00%d", base->subscription.country);
			}
			else
			{
				sprintf(value, "0%d", base->subscription.province);
			}
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(25, value);

		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(26, value);//RE_STR_SUB_AREA

		//if(base->subscription.carriers)
		{
			pExec->BindParam(27, base->subscription.carriers);
		}

		if (subVisit->area)
		{
			if(6==roamType || 9==roamType)
			{
				sprintf(value, "00%d", subVisit->area);
			}
			else sprintf(value, "0%d", subVisit->area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(28, value);//RE_STR_SUB_VISIT_AREA

		pExec->BindParam(29, data->callingVLR);//RE_STR_CALLING_VLR
		pExec->BindParam(30, data->calledVLR);//RE_STR_CALLED_VLR
		pExec->BindParam(31, (int)longType);
		pExec->BindParam(32, (int)roamType);
		pExec->BindParam(33, (long)bizMsg->timestampCCR);//RE_LNG_CALL_START_TIME
		pExec->BindParam(34, (long)bizMsg->timestampCCR);//RE_LNG_CURRENT_CCR_TIME
		pExec->BindParam(35, (long)bizMsg->m_serial);
		pExec->BindParam(36, SM_CDR_VERSION);
		pExec->BindParam(37, SM_CDR_TICKETTYPE);
		pExec->BindParam(38, base->topology);
		pExec->BindParam(39, 0);
		pExec->BindParam(40, 0);

		if(CDRcallType == 1)
		{
			pExec->BindParam(41, SERVICES_CENARIOUS_VOICE_MOC);
		}
		else if(CDRcallType == 2)
		{
			pExec->BindParam(41, SERVICES_CENARIOUS_VOICE_MTC);
		}
		else
		{
			pExec->BindParam(41, SERVICES_CENARIOUS_VOICE_CFW);
		}

		pExec->BindParam(42, base->subUnified);
		pExec->BindParam(43, data->callingUnified);
		pExec->BindParam(45, data->callingNumber);//OCP_STR_ORIGIN_CALLING_NBR

		if(CDRcallType > 2)
		{
			pExec->BindParam(44, base->subUnified);//CDR_PUB_STR_CALLED_PARTY
			pExec->BindParam(46, base->subscriptionData);//OCP_STR_ORIGIN_CALLED_NBR
		}
		else
		{
			pExec->BindParam(44, data->calledUnified);
			pExec->BindParam(46, data->calledNumber);
		}

		pExec->BindParam(47, userInfo->servID);
		pExec->BindParam(48, userInfo->custID);
		pExec->BindParam(49, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(50, SM_CDR_SEQ_NUM);//SM_CDR_SEQ_NUM
		if(0 == smpara->GetINPara()->iSMIfalg)//网元上报IMSI
		{
			if(!data->IMSI.empty())
			{
				sprintf(value,"%s",data->IMSI.c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "IMSI[%s]", data->IMSI.c_str());
			}
			else
			{
				value[0] = '\0';
			}
		}
		else//主产品实例表中IMSI
		{
		   if(0 != strcmp(userInfo->IMSI, ""))
		   	{
		   		sprintf(value,"%s",userInfo->IMSI);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "IMSI[%s]", userInfo->IMSI);
		   	}
			else
			{
				value[0] = '\0';
			}
		}
		pExec->BindParam(51, value);
		pExec->BindParam(52, (long)data->serviceKey);
		pExec->BindParam(53, data->bearerCapability);
		pExec->BindParam(54, data->MSC);
		pExec->BindParam(55, userInfo->aocType);
		pExec->BindParam(56, base->routeRecord);
		pExec->BindParam(57, traceNumOnff);
		pExec->BindParam(58, data->calledShortNumber);
		pExec->BindParam(59, 1);//SM_INT_BILLING_MODE

		if(data->redirectionInfo)
		{
			if(6 == data->redirectionInfo)
			{
				data->redirectionInfo = 4;
			}
			sprintf(value, "%u", data->redirectionInfo);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(60, value);//CDR_STR_REDIRECT_TYPE
		if(CDRcallType > 2)
		{
			if(data->called.phone.length() != 0)
			{	
				pExec->BindParam(61, data->called.phone);//CDR_STR_REDIRECT_NBR
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(61, value);
			}

			if (data->called.country)
			{
				sprintf(value, "00%d", data->called.country);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(62, value);
			if (data->called.province)
			{
				if(86 != data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
				}
				else
				{
					sprintf(value, "0%d", data->called.province);
				}
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(63, value);

			if (data->called.area)
			{
				sprintf(value, "0%d", data->called.area);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(64, value);
			//if (data->called.carriers)
			{
				pExec->BindParam(65, data->called.carriers);//RE_INT_IPMOC_FLAG
			}

		}
		else
		{
			value[0] = '\0';
			pExec->BindParam(61, value);
			pExec->BindParam(62, value);
			pExec->BindParam(63, value);
			pExec->BindParam(64, value);
			pExec->BindParam(65, 0);
		}


		if(data->called.access)
		{
			pExec->BindParam(66, 2);
		}
		else
		{
			pExec->BindParam(66, 1);
		}
		pExec->BindParam(67, (int)CDRcallType);

		if(2 == CDRcallType)
		{
			value[0]= '\0';
			pExec->BindParam(68, value);
			if(6==roamType||9==roamType)	sprintf(value, "00%d", subVisit->area);
			else sprintf(value, "0%d", subVisit->area);
			pExec->BindParam(69, value);//RE_STR_CALLED_VISIT_AREA
		}
		else
		{
			if(6==roamType||9==roamType)	sprintf(value, "00%d", subVisit->area);
			else sprintf(value, "0%d", subVisit->area);
			pExec->BindParam(68, value); //RE_STR_CALLING_VISIT_AREA
			value[0]= '\0';
			pExec->BindParam(69, value);
		}

		if(userInfo->isRemind)
		{
			pExec->BindParam(70, (int)2207);
		}
		else
		{
			pExec->BindParam(70, (int)0);
		}
		pExec->BindParam(71, data->callingCellID);//RE_STR_CALLING_CELL_ID
		pExec->BindParam(72, data->calledCellID);//RE_STR_CALLED_CELL_ID
		2==data->eventTypeBCSM?pExec->BindParam(73, data->callingCellID):pExec->BindParam(73, data->calledCellID);
		if(userInfo->isActive)
		{
			pExec->BindParam(74, 1);//OCP_INT_ACTIVE_FLAG
		}
		else
		{
			pExec->BindParam(74, 0);
		}
		pExec->BindParam(75, (long)base->timestamp);//RE_LNG_SYS_CCR_TIME
		pExec->BindParam(76,data->calledGropNum);//RE_STR_CALLED_GROP_NUM
		pExec->BindParam(77,data->calledVpnCallType);//RE_STR_CALLED_VPN_CALL_TYPE
		pExec->BindParam(78, userInfo->userType);
		pExec->BindParam(79, userInfo->mvnoID);
		pExec->BindParam(80, base->topology);
		pExec->BindParam(81, subVisit->szcarriers);
		pExec->BindParam(82, userInfo->ilatnid);
		pExec->BindParam(83, userInfo->lnAcctID);
		pExec->BindParam(84, bizMsg->m_szServiceContextIDStr);
		pExec->BindParam(85, data->IMSChargingIdentifier);
		pExec->BindParam(86, data->callaccesstype);
		pExec->BindParam(87, subVisit->sector_id);
		pExec->BindParam(88, iCreatFlag);
		char szTemp[2]={0};
		if(base->smExt.kv.count("CALLINGECGI"))
		{
			pExec->BindParam(89, base->smExt.kv["CALLINGECGI"].c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession calling_wifi_ecgi[%s]", base->smExt.kv["CALLINGECGI"].c_str());
		}
		else
		{
			pExec->BindParam(89, szTemp);
		}
		if(base->smExt.kv.count("CALLEDECGI"))
		{
			pExec->BindParam(90, base->smExt.kv["CALLEDECGI"].c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession called_wifi_ecgi[%s]", base->smExt.kv["CALLEDECGI"].c_str());
		}
		else
		{
			pExec->BindParam(90, szTemp);
		}
		if(base->smExt.kv.count("CALLINGWIFIACCT"))
		{
			pExec->BindParam(91, base->smExt.kv["CALLINGWIFIACCT"].c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession calling_wifi_account[%s]", base->smExt.kv["CALLINGWIFIACCT"].c_str());
		}
		else
		{
			pExec->BindParam(91, szTemp);
		}
		if(base->smExt.kv.count("CALLEDWIFIACCT"))
		{
			pExec->BindParam(92, base->smExt.kv["CALLEDWIFIACCT"].c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession called_wifi_account[%s]", base->smExt.kv["CALLEDWIFIACCT"].c_str());
		}
		else
		{
			pExec->BindParam(92, szTemp);
		}
		pExec->Execute();
		pExec->Connection()->Commit();
	}

	catch(UDBException& e)
	{
		std::string sql;
		pExec->GetSqlString(sql);
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "insert ok", "");

	return RET_SUCCESS;
}

int DCReqVOICETEL::Update(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	SSessionCache cacheData            ;

	ret=composeRER(bizMsg, base, data,cacheData);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "compose RER failed[%d]", ret);
		return ret;
	}

	//更新会话
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	//update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		UDBSQL *pUpdate = pdbm->GetSQL(Voice_UpdateSession_update);
		try
		{
			pUpdate->DivTable(bizMsg->m_sessionID);
			pUpdate->UnBindParam();
			pUpdate->BindParam(1, SM_SESSION_UPDATE_CODE);
			pUpdate->BindParam(2, (int)bizMsg->m_requestNumber);
			pUpdate->BindParam(3, TORB_ACTION);
			pUpdate->BindParam(4, (long)bizMsg->m_serial);
			pUpdate->BindParam(5, cacheData.nextCCTime);
			pUpdate->BindParam(6, (long)bizMsg->timestampCCR);
			pUpdate->BindParam(7, (long)cacheData.callStartTime); // 把所有使用量都存到数据库中
			pUpdate->BindParam(8, (long)cacheData.usu_duration);
			pUpdate->BindParam(9, cacheData.lastRSU);
			pUpdate->BindParam(10, cacheData.nCCAFlag);
			pUpdate->BindParam(11, cacheData.nActiveFlag);
			pUpdate->BindParam(12, cacheData.nAocType);
			pUpdate->BindParam(13, bizMsg->m_trace_flag);
			pUpdate->BindParam(14, (long)base->timestamp);
			pUpdate->BindParam(15, (int)cacheData.roamType);
			pUpdate->BindParam(16, (int)cacheData.RERcallType);
			pUpdate->BindParam(17, (int)cacheData.longType);
			pUpdate->BindParam(18, cacheData.szCalledVisitArea);
			pUpdate->BindParam(19, cacheData.szCalledVlr);
			pUpdate->BindParam(20, cacheData.szCalledCellid);
			pUpdate->BindParam(21, cacheData.szBearerCapability);
			pUpdate->BindParam(22, bizMsg->m_sBatchId.c_str());
			pUpdate->BindParam(23, bizMsg->m_sessionID);
			pUpdate->Execute();
			pUpdate->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pUpdate->GetSqlString(sql);
			pUpdate->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "update sucessful");

	return RET_SUCCESS;
}

int DCReqVOICETEL::Term(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg)
{
	int ret 			       = RET_SUCCESS;
	SSessionCache cacheData            ;

	ret = composeRER(bizMsg, base, data, cacheData);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "compose RER failed[%d]", ret);
		return ret;
	}

	//更新子会话
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		UDBSQL *pUpdate = pdbm->GetSQL(Voice_UpdateSession_update);
		try
		{
			pUpdate->DivTable(bizMsg->m_sessionID);
			pUpdate->UnBindParam();
			pUpdate->BindParam(1, SM_SESSION_TERMINATION_CODE);
			pUpdate->BindParam(2, (int)bizMsg->m_requestNumber);
			pUpdate->BindParam(3, TORB_ACTION);
			pUpdate->BindParam(4, (long)bizMsg->m_serial);
			pUpdate->BindParam(5, cacheData.nextCCTime);
			pUpdate->BindParam(6, (long)bizMsg->timestampCCR);
			pUpdate->BindParam(7, (long)cacheData.callStartTime); // 把所有使用量都存到数据库中
			pUpdate->BindParam(8, (long)cacheData.usu_duration);
			pUpdate->BindParam(9, cacheData.lastRSU);
			pUpdate->BindParam(10, cacheData.nCCAFlag);
			pUpdate->BindParam(11, cacheData.nActiveFlag);
			pUpdate->BindParam(12, cacheData.nAocType);
			pUpdate->BindParam(13, bizMsg->m_trace_flag);
			pUpdate->BindParam(14, (long)base->timestamp);
			pUpdate->BindParam(15, (int)cacheData.roamType);
			pUpdate->BindParam(16, (int)cacheData.RERcallType);
			pUpdate->BindParam(17, (int)cacheData.longType);
			pUpdate->BindParam(18, cacheData.szCalledVisitArea);
			pUpdate->BindParam(19, cacheData.szCalledVlr);
			pUpdate->BindParam(20, cacheData.szCalledCellid);
			pUpdate->BindParam(21, cacheData.szBearerCapability);
			pUpdate->BindParam(22, bizMsg->m_sBatchId.c_str());
			pUpdate->BindParam(23, bizMsg->m_sessionID);
			pUpdate->Execute();
			pUpdate->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pUpdate->GetSqlString(sql);
			pUpdate->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "update sucessful");

	return RET_SUCCESS;
}

int DCReqVOICETEL::Event(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg)
{
	int ret = RET_SUCCESS;
	switch(base->requestAction)
	{

		case SM_REQUESTED_ACTION_CHECK://余额查询消息
			{
				ret = Check(base, bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "invalid action type[%d]", base->requestAction);
				ret = SM_OCP_INVALID_AVP_VALUE;
			}
			break;
	}

	return ret;
}

int DCReqVOICETEL::XdrEvent(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;
	long nextCCTime						= 0;
	string iCreatFlag;
	unsigned int roamType				= bizMsg->m_roamtype;
	unsigned int longType				= bizMsg->m_longtype;
	unsigned int RERcallType			= 0;
	unsigned int CDRcallType			= 0;
	unsigned int earea					= 0;
	int traceNumOnff					= 0;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO *subVisit					= NULL;
	SUserInfo * userInfo				= NULL;

	subVisit = bizMsg->m_visit;
	userInfo = bizMsg->m_userinfo;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;

	ocs::SCCRDataUnit* USU					= NULL;
	USU = &(base->USU);
	long TUSU                           = 0;
       long lnsess_start                = 0;

    if(bizMsg->m_calledNumberIsDefaultArea)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "called area use calling visit area[%d]",subVisit->area);
		data->called.area = subVisit->area;
		data->called.province = subVisit->province;
	}
	CDRcallType = bizMsg->m_cdrCallType;
	if(3==data->redirectionInfo && 3==CDRcallType)
	{
		RERcallType =3;
	}
	else if(3==CDRcallType)
	{
		RERcallType = 4;
	}
	else
	{
		RERcallType = bizMsg->m_cdrCallType;
	}

	if(CDRcallType == 1)
	{
		data->calling.area=base->subscription.area;
	}
	else if(CDRcallType == 2)
	{
		data->called.area = base->subscription.area;
	}

	if(&base->USU == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "missing MSCC", "");
		return SM_OCP_MISSING_AVP;
	}
	else
	{
		USU = &base->USU;
	}

	//获取配额信息
	if((conf = smpara->GetServiceQuotaConf(VOICE_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find service quota config[%d]", VOICE_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 2*conf->TOKEN;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "next cc time[%ld]", nextCCTime);


	TUSU=USU->duration;
	if(longType >= 2)
	{
		if(TUSU <= smpara->GetINPara()->shortCDRTime )//不是超长话单截取后的超短
		{
			TUSU = 0;
			USU->duration = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "short long");
		}
	}
	//判断市话超短话单
	else if((0==roamType) && (0==longType))
	{
		if(TUSU <= smpara->GetINPara()->shortCDRTimeCity)
		{
			TUSU = 0;
			USU->duration = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "short city");
		}
	}
	//判断漫游超短话单
	else if(roamType >= 1)
	{
		if(TUSU <= smpara->GetINPara()->shortCDRTimeRoam)
		{
			TUSU = 0;
			USU->duration = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "short roam");
		}
	}

       /*  ifree 卡离线时长为0 也需要出单
	if(TUSU ==0)//不需要批价扣费直接回CCA
	{
		ret = sendCCA(bizMsg);
		return RET_OVER;
	}*/

	long minsec = 0;
	long lastsec = 0;
       lnsess_start = base->starttime;
       int day = 1;
       int spiltflag = 0;
       long lastAmount = 0;
       long LockTime = 0;
       long BeforeMonth =  0;

       //仅在跨月且封账前截单
       if((base->starttime/100000000 != bizMsg->timestampCCR/100000000) )
       {
            // 有跨天，重新计算封账账期
            std::string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("LockTime");
            LockTime = smpara->GetBillAttr(servAttr);
            if(LockTime < 0) LockTime = 0;
            sprintf(value, "%ld01%06ld", base->timestamp/100000000, LockTime);
            LockTime = atol(value);

            //封账前
            if(base->timestamp < LockTime)
            {
                DCCommonIF::ChangeMonth(value, -1);
                BeforeMonth = atoi(value);

                sprintf(value,"%lld",base->starttime);
                day = DCCommonIF::GetDateDiff(value,TUSU,minsec,lastsec)+1;
	         spiltflag = day > 1? 1: 0;
            }
       }

	for(int i =0;i<day;i++)
	{
		value[0]='\0';
		ocs::UHead uhd;
		ocs::rbhead head;
		ocs::rbdomain domain;
		ocs::rbvoice rbr;
		ocs::rsu_t rsu;
		ocs::rbext ext;
		ocs::usu   u;
		ocs::debit totalusu;

		uhd.uid = bizMsg->m_uid;
		uhd.car = "1";
		uhd.trace = bizMsg->m_trace_flag;
		uhd.checkKey = bizMsg->m_strCheckKey;
		bizMsg->m_sessionID[255] = '\0';
		if("2100" == bizMsg->m_payMentMode)
		{
			iCreatFlag = "3";
		}
		else
		{
			iCreatFlag = "2";
		}
		head.creditCtlFlag = atoi(iCreatFlag.c_str());
		head.type = RE_SERVICE_TYPE_INT_VOICE_REQ;
		head.version = 1;
		head.sreq  = 4;
		head.stamp = bizMsg->timestampCCR;
		head.session = bizMsg->m_sessionID;
		head.session.erase(0,3);
		head.serial = bizMsg->m_serial;
		head.trace =  bizMsg->m_trace_flag;
		head.result = 0;
		head.topology = base->topology;
		//prod_inst_id
		head.prodinstid = userInfo->servID;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

        // VoLTE 新增字段
        if (1 == data->VoLTEFlag)
        {
            ext.kv["R501"] = "3";
        }

        if (data->callaccesstype.length())
        {
            ext.kv["R623"] = data->callaccesstype;
        }

		//R65	服务代码
		if(!data->bearerCapability.empty())
		{
    		if(strcmp(data->bearerCapability.c_str(), "8") == 0)
    		{
    			strcpy(value, "7");
    		}
    		else
    		{
    			strcpy(value, data->bearerCapability.c_str());
    		}
    		rbr.service_code = atoi(value);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SERVICE_CODE[%d]", rbr.service_code);

		//R75     被叫接入号码
		if (0 == data->called.access)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "delete RB_CODE_R_ACCESS_NUMBER", "");
		}
		else
		{
			sprintf(value, "%d", data->called.access);
			rbr.access_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ACCESS_NUMBER[%s]" , value);
		}

		rbr.latn_id = userInfo->ilatnid;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose latnid[%d]" , rbr.latn_id);


               // R71 会话开始时间
        sprintf(value, "%ld", lnsess_start);
        rbr.sess_start_time =  value;
        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_R_SESSION_START_TIME[%s]", value);

		//R85   用户付费属性标识
		rbr.pay = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_PAY_TYPE[1]", "");

		//R01   付费号码
		rbr.charged_nbr = base->subscription.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CHARGE[%s]",  base->subscription.phone.c_str());

		//R03   被叫号码
		rbr.called_nbr = data->called.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED[%s]", data->called.phone.c_str());

		//呼转流程
		if((3 == RERcallType) || (4 == RERcallType))
		{
			//R02   主叫号码
			rbr.calling_nbr = base->subscription.phone;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]", base->subscription.phone.c_str());

			//R105  连接号码
			rbr.connect_nbr = data->calling.phone;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT[%s]",data->calling.phone.c_str());

			//R1012  主叫交换机
			rbr.calling.msc = data->callingVLR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_MSC[%s]",data->MSC.c_str());

			//R504  主叫号码归属费率区
			sprintf(value, "0%d", base->subscription.area);
			rbr.calling.harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]", value);

			//R505  主叫号码拜访费率区
			if(6==roamType || 9==roamType)
			{
				sprintf(value, "00%d", subVisit->area);
			}
			else sprintf(value, "0%d", subVisit->area);
			rbr.calling.varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

			//R506  主叫号码归属运营商
			sprintf(value, "%d", base->subscription.carriers);
			rbr.calling.hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

			//R508  被叫号码拜访费率区
			if (86 != data->called.country)
			{
				sprintf(value, "00%d", data->called.country);
			}
			else
			{
				sprintf(value, "0%d", data->called.area);
			}
			rbr.called.varea= value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

			//R5013 连接号码归属区
			if (86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else
			{
				sprintf(value, "0%d", data->calling.area);
			}
			rbr.connect_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_AREA[%s]", value);

			//R5014  连接号码拜访区
			rbr.connect_varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_VISIT_AREA[%s]",value);


		}
		//被叫流程
		else if(2 == RERcallType)
		{
			//R02   主叫号码
			rbr.calling_nbr = data->calling.phone;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING [%s]",data->calling.phone.c_str());

			//R504  主叫号码归属费率区
			//R505  主叫号码拜访区号
			if (86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
				rbr.calling.harea = value;
				rbr.calling.varea= value;
			}
			else
			{
				sprintf(value, "0%d", data->calling.area);
				rbr.calling.harea = value;
				rbr.calling.varea= value;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]",value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

			//R506  主叫号码归属运营商
			sprintf(value, "%d", data->calling.carriers);
			rbr.calling.hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

			//R508  被叫号码拜访费率区
			if(6==roamType || 9==roamType)
			{
				sprintf(value, "00%d", subVisit->area);
			}
			else sprintf(value, "0%d", subVisit->area);
			rbr.called.varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

			//R1013  被叫交换机
			rbr.called.msc = data->calledVLR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_MSC[%s]", data->calledVLR.c_str());


		}
		//主叫流程
		else
		{
			//R02   主叫号码
			rbr.calling_nbr = data->calling.phone;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]", data->calling.phone.c_str());

			//R504  主叫号码归属费率区
			if (86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
				rbr.calling.harea = value;
			}
			else
			{
				sprintf(value, "0%d", data->calling.area);
				rbr.calling.harea = value;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]", value);

			//R505  主叫号码拜访区号
			if(6==roamType || 9==roamType)
			{
				sprintf(value, "00%d", subVisit->area);
			}
			else sprintf(value, "0%d", subVisit->area);
			rbr.calling.varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

			//R506  主叫号码归属运营商
			sprintf(value, "%d", data->calling.carriers);
			rbr.calling.hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

			//R508  被叫号码拜访费率区
			if (86 != data->called.country)
			{
				sprintf(value, "00%d", data->called.country);
				rbr.called.varea= value;
			}
			else
			{
				sprintf(value, "0%d", data->called.area);
				rbr.called.varea= value;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

			//R1012  主叫交换机
			rbr.calling.msc = data->callingVLR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_MSC[%s]", data->MSC.c_str());


		}

		//R507  被叫号码归属费率区
		if (86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
			rbr.called.harea= value;
		}
		else
		{
			sprintf(value, "0%d", data->called.area);
			rbr.called.harea= value;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_AREA[%s]", value);

		//R509  被叫号码归属运营商
		rbr.called.hcarrier = data->called.carriers;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_OPERATOR[%d]",data->called.carriers);

		//R5010 长途类型
		rbr.long_type = longType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_LONG_TYPE[%d]", longType);

		//R5011 漫游类型
		rbr.roam_type = roamType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ROAM_TYPE[%d]", roamType);

		//R5012 付费号码归属费率区
		sprintf(value, "0%d", base->subscription.area);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CHARGING_AREA[%s]", value);

		//R5015  拜访地运营商
		rbr.charged_vcarrier = subVisit->szcarriers;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_VISIT_CARRIER[%s]",  subVisit->szcarriers);

              //RTI 国际漫游资费区
              DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RTI[%s]",  subVisit->sector_id);


		//R601 重发标记
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_REPEAT_FLAG[%s]", "0");

		//R602 计费类型
		rbr.sreq = SM_SESSION_UPDATE_CODE;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SESSION_STATE[%s]",  SM_SESSION_INITIAL_STRING);


		//R605  是否进行使用量累计标识
		rbr.ratable_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_RATABLE_TIME[%s]",  "0");


		//R615 小区优惠标识
		if(smpara->GetINPara()->favCellIDSwitch)
		{
			rbr.fav_flag = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_FAV_FLAG[%s]", "1");
		}


		//R103  话单类型
		sprintf(value, "%d", RERcallType);
		rbr.call_type = RERcallType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CDR_TYPE[%s]",  value);

		//R106  主叫小区(或基站)
		rbr.calling.cell = data->callingCellID;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_CELLID_OR_SAI[%s]",  data->callingCellID.c_str());

		//R107  被叫小区(或基站)
		rbr.called.cell = data->calledCellID;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_CELLID_OR_SAI[%s]",  data->calledCellID.c_str());

		//R108  主叫用户位置区
		rbr.calling.lai = data->callingVLR;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_LAI[%s]",  data->callingVLR.c_str());

		//R109  被叫用户位置区
		rbr.called.lai = data->calledVLR;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_LAI[%s]", data->calledVLR.c_str());

		//R1116 短号处理
		if(!data->calledShortNumber.empty())
		{
			rbr.called_short_nbr = data->calledShortNumber;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_AB_CALLED[%s]", data->calledShortNumber.c_str());
		}


		//R1117 短号处理
		if(!data->calledGropNum.empty())
		{
			rbr.called_group_nbr = data->calledGropNum;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_AB_GP_NUMVBER[%s]", data->calledGropNum.c_str());
		}


		//R1118 短号处理
		if(!data->calledVpnCallType.empty())
		{
			rbr.called_vpn_calltype = atoi(data->calledVpnCallType.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_VPN_CALLTYPE[%s]", data->calledVpnCallType.c_str());
		}
		//B038
		string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectArrear");
		rbr.neg_debit= smpara->GetBillAttr(servAttr);
		if(rbr.neg_debit == -1)
		rbr.neg_debit = 1;

		//话单是否剔重
	       servAttr=string(bizMsg->m_szServiceContextIDStr)+string("OffTime");
		int  eliminateflag = smpara->GetBillAttr(servAttr);
		sprintf(value,"%d",eliminateflag);
		if(eliminateflag>=0)
		{
			ext.kv["eliminateflag"] = value;
		}
		//R07离线批价扣费标识
		servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
		int ratingflag= smpara->GetBillAttr(servAttr);
		sprintf(value,"%d",ratingflag);
		if(ratingflag>=0)
		{
			ext.kv["ratingflag"] = value;
		}


               //R606  激活用户
		if( TUSU > 0  && (RERcallType == 1 || RERcallType == 3|| RERcallType == 4))
		{
                    rbr.active_flag = userInfo->isActive;
		      DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ACTIVE_FLAG[%s]",  "1");
		}

		if(1==day)
		{
                     // 不跨天情况
			//R603   本次计费请求开始时间
			rbr.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbr.pre_dtime);

			//R604  本次计费请求结束时间
			rbr.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%ld]" , rbr.cur_dtime);

                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			sprintf(value, "%d", TUSU);
			totalusu.amount = TUSU;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);
		}
		else if(0==i)
		{
		       // 跨天第一条单
			//R603   本次计费请求开始时间
			rbr.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbr.pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			rbr.cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%ld]" , rbr.cur_dtime);

                     //重新计算账期
                    if(base->timestamp >= LockTime)
                    {
                            // 封账之后取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else if(rbr.cur_dtime/100000000 <  base->timestamp/100000000)
                    {
                            //封账前取上月账期
                            bizMsg->billcycle = BeforeMonth;
                    }
                    else if(rbr.cur_dtime/100000000 ==  base->timestamp/100000000)
                    {
                            //封账前取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else
                    {
                            bizMsg->billcycle = rbr.cur_dtime/100000000;
                    }
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			sprintf(value, "%d", minsec);
			totalusu.amount = minsec;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);
		}
		else if(i==day-1)
		{
		       // 跨天最后一条单
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			rbr.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbr.pre_dtime);

			//R604  本次计费请求结束时间
			rbr.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%ld]" , rbr.cur_dtime);

                      //重新计算账期
                    if(base->timestamp >= LockTime)
                    {
                            // 封账之后取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else if(rbr.cur_dtime/100000000 <  base->timestamp/100000000)
                    {
                            //封账前取上月账期
                            bizMsg->billcycle = BeforeMonth;
                    }
                    else if(rbr.cur_dtime/100000000 ==  base->timestamp/100000000)
                    {
                            //封账前取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else
                    {
                            bizMsg->billcycle = rbr.cur_dtime/100000000;
                    }
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			sprintf(value, "%d", lastsec);
			totalusu.amount = lastsec;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);

		}
		else
		{
		       // 跨天中间单
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			rbr.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbr.pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			rbr.cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%ld]" , rbr.cur_dtime);

                      //重新计算账期
                    if(base->timestamp >= LockTime)
                    {
                            // 封账之后取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else if(rbr.cur_dtime/100000000 <  base->timestamp/100000000)
                    {
                            //封账前取上月账期
                            bizMsg->billcycle = BeforeMonth;
                    }
                    else if(rbr.cur_dtime/100000000 ==  base->timestamp/100000000)
                    {
                            //封账前取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else
                    {
                            bizMsg->billcycle = rbr.cur_dtime/100000000;
                    }
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			totalusu.amount = 24*3600;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%d]", totalusu.amount);

		}

		sprintf(value,"%ld",lastAmount);
		ext.kv["lastAmount"] = value;
		lastAmount = totalusu.amount;


		sprintf(value,"%d",spiltflag);
		ext.kv["spiltflag"] = value;
		spiltflag++;

		rbr.latn_id = userInfo->ilatnid;
		rbr.accumlator_info = 1;
		rbr.tariff_info = 1;
		rbr.rating_info = 1;
		rbr.balance_query = 1;

		//CEE被叫 CER主叫
		ext.kv["CER"] = bizMsg->m_callingNumber;
		ext.kv["CEE"] = bizMsg->m_calledNumber;

		//模拟测试消息组装B09=1给RE
		if(1 == bizMsg->m_testFlag)
		{
			rbr.test_info = 1;
			rbr.acct_info = 1;
		}
		ext.kv["RTI"] = subVisit->sector_id;
		ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
		ext.kv["anstopic"] = bizMsg->m_anstopic;
		ext.kv["taskId"] = bizMsg->m_taskId;
		ext.kv["sourceId"] = bizMsg->m_sourceId;
		ext.kv["operListId"] = bizMsg->m_operListId;

		// 获取批次号
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, smpara->GetCommonPara()->iBatchIdTime, 2);
		ext.kv["batchId"] = bizMsg->m_sBatchId;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]", bizMsg->m_sBatchId.c_str());

		// R3019
		if (1 == data->VoLTEFlag)
		{
			if (2 == RERcallType) // 被叫
			{
				ext.kv["R3019"] = rbr.called.msc; // R1013
			}
			else
			{
				ext.kv["R3019"] = rbr.calling.msc; // R1012
			}
		}
		else
		{
			if (2 == RERcallType) // 被叫
			{
				ext.kv["R3019"] = rbr.called.cell; // R107
			}
			else
			{
				ext.kv["R3019"] = rbr.calling.cell; // R106
			}
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "volte[%d] callType[%d] compose R3019[%s]", data->VoLTEFlag, RERcallType, ext.kv["R3019"].c_str());

		try
		{
			m_en.clear();
			m_en.encode(uhd);
			m_en.encode(head);
			m_en.encode(domain);
			m_en.encode(rbr);
			m_en.encode(ext);
			bizMsg->data = (char *)m_en.data();
			bizMsg->m_vectorMsg.push_back(bizMsg->data);
			// 打印head消息
			m_print.clear();
			m_print.print(uhd);
			m_print.print(head);
			m_print.print(domain);
			m_print.print(rbr);
			m_print.print(ext);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
		}
		catch(exception& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
			return ERR_ENCODE_CODE;
		}

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));

		UDBSQL *pExec  = NULL;
		pExec =  pdbm->GetSQL(Voice_InsertSession_OffLine);
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			if(0==i)
			{
				pExec->BindParam(1, bizMsg->m_sessionID);
			}
			else
			{
				value[0]='\0';
				sprintf(value,"%s;00%d",bizMsg->m_sessionID,i+1);
				pExec->BindParam(1, value);
			}
			pExec->BindParam(2, (int)bizMsg->m_requestNumber);
			pExec->BindParam(3, base->subscriptionData);
			pExec->BindParam(4, base->originHost);
			pExec->BindParam(5, (long)conf->TOKEN);
			pExec->BindParam(6, SM_SESSION_XDR_CODE);
			pExec->BindParam(7, TORB_ACTION);
			pExec->BindParam(8, nextCCTime);
			pExec->BindParam(9, base->subscription.phone);
			pExec->BindParam(10, data->calling.phone);

			if(CDRcallType > 2)
			{
				pExec->BindParam(11, base->subscription.phone);
			}
			else
			{
				pExec->BindParam(11, data->called.phone);
			}

			pExec->BindParam(12, (int)RERcallType);
			pExec->BindParam(13, data->callingLAI);
			pExec->BindParam(14, data->calledLAI);

			if (data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(15, value);

			if(86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else if (data->calling.province)
			{
				sprintf(value, "0%d", data->calling.province);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(16, value);//RE_STR_CALLING_PROVINCE

			//把值绑定到RE_STR_CALLING_AREA字段，记话单用
			if(86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else if (data->calling.area)
			{
				sprintf(value, "0%d", data->calling.area);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(17, value); //RE_STR_CALLING_AREA
			pExec->BindParam(18, data->calling.carriers);

			if(CDRcallType > 2)
			{
				if (base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
					pExec->BindParam(19, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(19, value);
				}

				if(86 != base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
				}
				else if (base->subscription.province)
				{
					sprintf(value, "0%d", base->subscription.province);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(20, value);

				if(86 != base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
				}
				else if (base->subscription.area)
				{
					sprintf(value, "0%d", base->subscription.area);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(21, value); //RE_STR_CALLED_AREA

				if (base->subscription.carriers)
				{
					pExec->BindParam(22, base->subscription.carriers);
				}
				else
				{
					pExec->BindParam(22, 0);
				}
			}
			else
			{
				if (data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
					pExec->BindParam(19, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(19, value);
				}

				if(86 != data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
				}
				else if (data->called.province)
				{
					sprintf(value, "0%d", data->called.province);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(20, value);//RE_STR_CALLED_PROVINCE

				if(86 != data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
				}
				else if (data->called.area)
				{
					sprintf(value, "0%d", data->called.area);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(21, value);//RE_STR_CALLED_AREA

				if (data->called.carriers)
				{
					pExec->BindParam(22, data->called.carriers);
				}
				else
				{
					pExec->BindParam(22, 0);
				}
			}

			if(data->called.access)
			{
				sprintf(value, "%d", data->called.access);
				pExec->BindParam(23, value);
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(23, value);
			}

			if (base->subscription.country)
			{
				sprintf(value, "00%d", base->subscription.country);
				pExec->BindParam(24,  value);
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(24, value);
			}

			if (base->subscription.province)
			{
				if(86 != base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
				}
				else
				{
					sprintf(value, "0%d", base->subscription.province);
				}
				pExec->BindParam(25, value);
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(25, value);
			}

			if (base->subscription.area)
			{
				sprintf(value, "0%d", base->subscription.area);
				pExec->BindParam(26, value);//RE_STR_SUB_AREA
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(26, value);
			}

			if(base->subscription.carriers)
			{
				pExec->BindParam(27, base->subscription.carriers);
			}
			else
			{
				pExec->BindParam(27, 0);
			}

			if (subVisit->area)
			{
				if(6==roamType || 9==roamType)
				{
					sprintf(value, "00%d", subVisit->area);
				}
				else sprintf(value, "0%d", subVisit->area);
				pExec->BindParam(28, value);//RE_STR_SUB_VISIT_AREA
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(28, value);
			}

			pExec->BindParam(29, data->callingVLR);//RE_STR_CALLING_VLR
			pExec->BindParam(30, data->calledVLR);//RE_STR_CALLED_VLR
			pExec->BindParam(31, (int)longType);
			pExec->BindParam(32, (int)roamType);
			pExec->BindParam(33, rbr.pre_dtime);//RE_LNG_CALL_START_TIME
			pExec->BindParam(34, rbr.cur_dtime);//RE_LNG_CURRENT_CCR_TIME
			pExec->BindParam(35, bizMsg->m_serial);
			pExec->BindParam(36, SM_CDR_VERSION);
			pExec->BindParam(37, SM_CDR_TICKETTYPE);
			pExec->BindParam(38, base->topology);
			pExec->BindParam(39, 0);
			pExec->BindParam(40, 0);

			if(CDRcallType == 1)
			{
				pExec->BindParam(41, SERVICES_CENARIOUS_VOICE_MOC);
			}
			else if(CDRcallType == 2)
			{
				pExec->BindParam(41, SERVICES_CENARIOUS_VOICE_MTC);
			}
			else
			{
				pExec->BindParam(41, SERVICES_CENARIOUS_VOICE_CFW);
			}

			pExec->BindParam(42, base->subUnified);
			pExec->BindParam(43, data->callingUnified);
			pExec->BindParam(45, data->callingNumber);//OCP_STR_ORIGIN_CALLING_NBR

			if(CDRcallType > 2)
			{
				pExec->BindParam(44, base->subUnified);//CDR_PUB_STR_CALLED_PARTY
				pExec->BindParam(46, base->subscriptionData);//OCP_STR_ORIGIN_CALLED_NBR
			}
			else
			{
				pExec->BindParam(44, data->calledUnified);
				pExec->BindParam(46, data->calledNumber);
			}

			pExec->BindParam(47, userInfo->servID);
			pExec->BindParam(48, userInfo->custID);
			pExec->BindParam(49, MASTER_PRODUCTID_CDMA);
			pExec->BindParam(50, SM_CDR_SEQ_NUM);//SM_CDR_SEQ_NUM
			if(0 == smpara->GetINPara()->iSMIfalg)//网元上报IMSI
			{
				if(!data->IMSI.empty())
				{
					pExec->BindParam(51, data->IMSI);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "IMSI[%s]", data->IMSI.c_str());
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(51, value);
				}
			}
			else//主产品实例表中IMSI
			{
				if(0 != strcmp(userInfo->IMSI, ""))
				{
					pExec->BindParam(51, userInfo->IMSI);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "IMSI[%s]", userInfo->IMSI);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(51, value);
				}

			}
			pExec->BindParam(52, long(data->serviceKey));
			pExec->BindParam(53, data->bearerCapability);
			pExec->BindParam(54, data->MSC);
			pExec->BindParam(55, 0);
			pExec->BindParam(56, base->routeRecord);

			pExec->BindParam(57, traceNumOnff);
			pExec->BindParam(58, data->calledShortNumber);
			pExec->BindParam(59, 1);//SM_INT_BILLING_MODE

			if(data->redirectionInfo)
			{
				if(6 == data->redirectionInfo)
				{
					data->redirectionInfo = 4;
				}
				sprintf(value, "%u", data->redirectionInfo);
				pExec->BindParam(60, value);//CDR_STR_REDIRECT_TYPE
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(60, value);
			}

			if(CDRcallType > 2)
			{
				if(data->called.phone.length() != 0)
				{
					pExec->BindParam(61, data->called.phone);//CDR_STR_REDIRECT_NBR
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(61, value);
				}

				if (data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
					pExec->BindParam(62, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(62, value);
				}

				if (data->called.province)
				{
					if(86 != data->called.country)
					{
						sprintf(value, "00%d", data->called.country);
					}
					else
					{
						sprintf(value, "0%d", data->called.province);
					}
					pExec->BindParam(63, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(63, value);
				}

				if (data->called.area)
				{
					sprintf(value, "0%d", data->called.area);
					pExec->BindParam(64, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(64, value);
				}

				if (data->called.carriers)
				{
					pExec->BindParam(65, data->called.carriers);//RE_INT_IPMOC_FLAG
				}
				else
				{
					pExec->BindParam(65, 0);
				}
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(61, value);
				pExec->BindParam(62, value);
				pExec->BindParam(63, value);
				pExec->BindParam(64, value);
				pExec->BindParam(65, 0);
			}

			if(data->called.access)
			{
				pExec->BindParam(66, 2);
			}
			else
			{
				pExec->BindParam(66, 1);
			}
			pExec->BindParam(67, (int)CDRcallType);

			if(2 == CDRcallType)
			{
				value[0] = '\0';
				pExec->BindParam(68, value); //RE_STR_CALLING_VISIT_AREA
				if(6==roamType||9==roamType)	sprintf(value, "00%d", subVisit->area);
				else sprintf(value, "0%d", subVisit->area);
				pExec->BindParam(69, value);//RE_STR_CALLED_VISIT_AREA
			}
			else
			{
				if(6==roamType||9==roamType)	sprintf(value, "00%d", subVisit->area);
				else sprintf(value, "0%d", subVisit->area);
				pExec->BindParam(68, value); //RE_STR_CALLING_VISIT_AREA
				value[0] = '\0';
				pExec->BindParam(69, value); //RE_STR_CALLED_VISIT_AREA
			}

			if(userInfo->isRemind)
			{
				pExec->BindParam(70, (int)2207);
			}
			else
			{
				pExec->BindParam(70, (int)0);
			}
			pExec->BindParam(71, data->callingCellID);//RE_STR_CALLING_CELL_ID
			pExec->BindParam(72, data->calledCellID);//RE_STR_CALLED_CELL_ID
			2==data->eventTypeBCSM?pExec->BindParam(73, data->callingCellID):pExec->BindParam(73, data->calledCellID);
			if(userInfo->isActive)
			{
				pExec->BindParam(74, 1);//OCP_INT_ACTIVE_FLAG
			}
			else
			{
				pExec->BindParam(74, 0);
			}
			pExec->BindParam(75, bizMsg->billcycle);//RE_LNG_SYS_CCR_TIME
			pExec->BindParam(76, totalusu.amount);
			pExec->BindParam(77, userInfo->lnAcctID);
			pExec->BindParam(78, userInfo->userType);
			pExec->BindParam(79, userInfo->ilatnid);
			pExec->BindParam(80, userInfo->mvnoID);
			pExec->BindParam(81, bizMsg->m_szServiceContextIDStr);
			pExec->BindParam(82, 1);
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(83, switchId);
	        pExec->BindParam(84, data->IMSChargingIdentifier);
	        pExec->BindParam(85, data->callaccesstype);
			pExec->BindParam(86, bizMsg->m_sBatchId.c_str());
			pExec->BindParam(87, iCreatFlag);
			char szTemp[2]={0};
			if(base->smExt.kv.count("CALLINGECGI"))
			{
				pExec->BindParam(88, base->smExt.kv["CALLINGECGI"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession_offline calling_wifi_ecgi[%s]", base->smExt.kv["CALLINGECGI"].c_str());
			}
			else
			{
				pExec->BindParam(88, szTemp);
			}
			if(base->smExt.kv.count("CALLEDECGI"))
			{
				pExec->BindParam(89, base->smExt.kv["CALLEDECGI"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession_offline called_wifi_ecgi[%s]", base->smExt.kv["CALLEDECGI"].c_str());
			}
			else
			{
				pExec->BindParam(89, szTemp);
			}
			if(base->smExt.kv.count("CALLINGWIFIACCT"))
			{
				pExec->BindParam(90, base->smExt.kv["CALLINGWIFIACCT"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession_offline calling_wifi_account[%s]", base->smExt.kv["CALLINGWIFIACCT"].c_str());
			}
			else
			{
				pExec->BindParam(90, szTemp);
			}
			if(base->smExt.kv.count("CALLEDWIFIACCT"))
			{
				pExec->BindParam(91, base->smExt.kv["CALLEDWIFIACCT"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "i_voice_insertsession_offline called_wifi_account[%s]", base->smExt.kv["CALLEDWIFIACCT"].c_str());
			}
			else
			{
				pExec->BindParam(91, szTemp);
			}
			pExec->Execute();
			pExec->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			pExec->GetSqlString(sql);
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,	"", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "insert execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "insert ok", "");
	}
	return RET_SUCCESS;
}


int DCReqVOICETEL::composeRER(STBizMsg* bizMsg,SCCRBase* base,SCCRVOICE* data,SSessionCache &cacheData)

{
    char value[BIZ_TEMP_LEN_256] 		= {0};
	SPhone called;
	char szCalledVisitArea[16]			= {0};
	char szCalledVlr[20]				= {0};
	int TUSU                            = 0;
	long lnRelastGsuTime                = 0;
	int nActiveFlag                     = 0;
	int nLongCdrTime 	                = 0;
	int lastRSU                         = 0;
	char szBatchId[32]					= {0};

	AREA_INFO *subVisit				= bizMsg->m_visit;
	int roamType           = 0;
	int longType           = 0;
	int RERcallType        = 0;
	long lnBalance = 0;


	TSERVICE_QUOTA_CONF *conf			= NULL;
	SUserInfo *userInfo					= NULL;
	userInfo = (SUserInfo * )bizMsg->m_userinfo;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	SCCRDataUnit* USU					= NULL;
	USU = &(base->USU);
	unsigned int requestType = bizMsg->m_requestType;


	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbvoice rbr;
	ocs::rsu_t rsu;
	ocs::usu   u;
	ocs::debit totalusu;
	ocs::rbext ext;

	UDBSQL *pQuery	= NULL;
	pQuery =  pdbm->GetSQL(Voice_GetSessionInfo);

	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();

		if(pQuery->Next())
		{
			pQuery->GetValue(43, value);//RE_LNG_LAST_GSU_TIME
			lnRelastGsuTime= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "last gsu time[%s]", value);

            /* 西藏不做语音tern消息init授权量为0的限制
			if(0 == lnRelastGsuTime &&requestType == SM_SESSION_TERMINATION_CODE)
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "last gsu time is zero, set usu[%u] to zero", USU->duration);
				USU->duration = 0;
			}*/

	        //判断网元上报的USU是否超大
			if((USU->duration >2*lnRelastGsuTime) &&(1 == smpara->GetPSPara()->nUsuOverloadRefuse))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "object:usu[%u]>2 times gsu[%ld],refuse", USU->duration,lnRelastGsuTime);
				return SM_OCP_USU_OVERLOAD;
			}

			pQuery->GetValue(67, value);//SM_LNG_BALANCE_INFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get balance[%s]", value);
	    	//获取配额信息
	    	if((conf = smpara->GetServiceQuotaConf(VOICE_RATING_GROUP,lnBalance)) == 0)
	    	{
	    		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find service quota config[%d]", VOICE_RATING_GROUP);
	    		return SM_OCP_UNABLE_TO_COMPLY;
	    	}

			//当前时间用于会话超时
			if(requestType != SM_SESSION_TERMINATION_CODE)
			{
				time_t et;
				time(&et);
				long nextCCTime = et + 2*conf->TOKEN;
				cacheData.nextCCTime = nextCCTime;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "next cc time[%ld]", nextCCTime);
			}

			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.trace = bizMsg->m_trace_flag;
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_VOICE_REQ;
			head.version = 2;
			head.sreq  = bizMsg->m_requestType;
			head.stamp = bizMsg->timestampCCR;
			head.session = bizMsg->m_sessionID;
			head.serial = bizMsg->m_serial;
			head.trace =  bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = base->topology;

			pQuery->GetValue(74, value);
			head.creditCtlFlag = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose creditCtlFlag[%d]",head.creditCtlFlag);

			//R85   用户付费属性标识
			rbr.pay = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_PAY_TYPE[1]","" );

			pQuery->GetValue(68, value);//LATN_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get LATNID[%s]", value);
			rbr.latn_id = atoi(value);
			bizMsg->m_ilatnId = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose latnid[%d]" , rbr.latn_id);

                     pQuery->GetValue(69, value);//CDR_PUB_LNG_SERVID
                     userInfo->servID = atol(value);
                     head.prodinstid = userInfo->servID;
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

			//会话状态
			pQuery->GetValue(1, value);
			int lastReqType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "last req type [%s]", value);

			pQuery->GetValue(3, value);
			lastRSU= atoi(value);
			cacheData.lastRSU = lastRSU;


	        if(requestType == SM_SESSION_TERMINATION_CODE)
	        {
				//总使用时长
				pQuery->GetValue(4, value);
				TUSU = atoi(value);//SM_LNG_ALL_USU_TIME
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "no used totol USU[%d]", TUSU);

				pQuery->GetValue(65, value);
				int usedTUSU=atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "used total USU [%s]", value);

				TUSU+=usedTUSU;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "billing total USU[%d]", TUSU);

	        }
	        else
	        {
				//总使用时长
				pQuery->GetValue(4, value);
				TUSU = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "total USU [%d]", TUSU);
	        }
			//R65   服务代码
			pQuery->GetValue(31, value);
			if (1 == data->VoLTEFlag)
			{   
			    bool IsFirstCdr = false;  //首单判断
			    bool IsCutCdr = true;  //bearerCapability枚举值 SM: 0-语音, 7-视频;         网元: 0-语音, 8-视频
				if(data->bearerCapability.size() == 0)
				{
					if (strcmp(value, "7") == 0 || strcmp(value, "8") == 0)
					{
					    data->bearerCapability = "8";
					}
					else
					{
                        data->bearerCapability = "0";
					}
					IsCutCdr = false;
				}
				else
				{
				    if (strcmp(data->bearerCapability.c_str(), "0") == 0 && strcmp(value, "0") == 0)
					{
	                    IsCutCdr = false;
					}
					else if((strcmp(value, "8") == 0 || strcmp(value, "7") == 0) && strcmp(data->bearerCapability.c_str(), "8") == 0)
					{
                        IsCutCdr = false;
					}
					else
					{
                        //首单判断:第一次是init，第二次是update
						if (lastReqType == SM_SESSION_INITIAL_CODE && requestType == SM_SESSION_UPDATE_CODE)
						{
						    IsFirstCdr = true;
							IsCutCdr = false;
						}
					}
				}
				if(smpara->GetINPara()->inVolteCdrSwitch == 1 && bizMsg->m_inVolteSwitchCdr == 0 && IsCutCdr)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "volte in switch cut cdr");
					bizMsg->m_inVolteSwitchCdr = 1;
					bizMsg->m_volteFlag = 1;//volte 单独出单
					bizMsg->m_longCDR = 1;
					//直接出单, 出单完成重新走 req 流程
					return RET_CDR;
				}
				if (IsFirstCdr) //首单用上一次类型
				{
					if(strcmp(value, "8") == 0 || strcmp(value, "7") == 0)
				    {
						data->bearerCapability = "8";
					}
					else
					{
					    data->bearerCapability = "0";
					}
				    
				}
				if(strcmp(data->bearerCapability.c_str(), "8") == 0)
				{
					strcpy(value, "7");
				}
				else
				{
					strcpy(value, data->bearerCapability.c_str());
				}
			}
			else
			{
				if(strcmp(value, "8") == 0)
				{
					strcpy(value, "7");
				}
            }
            strcpy(cacheData.szBearerCapability,value);



			rbr.service_code = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SERVICE_CODE[%s]",  value);

			if(SM_SESSION_INITIAL_CODE == lastReqType)
			{
				GetCallStartTime((long)bizMsg->timestampCCR, USU->duration, value);
	            cacheData.callStartTime =  atol(value);

				//R71   会话开始时间
				rbr.sess_start_time = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SESSION_START_TIME[%s]",  value);

				//R603  会话上次扣费开始时间
				rbr.pre_dtime = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_PRE_DEBIT_TIME[%s]",  value);
			}
			else
			{
				//R71   会话开始时间
				pQuery->GetValue(6, value);//RE_LNG_CALL_START_TIME
				cacheData.callStartTime =  atol(value);
				rbr.sess_start_time = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SESSION_START_TIME[%s]", value);

				//R603  会话上次扣费开始时间
				pQuery->GetValue(7, value);
				rbr.pre_dtime = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_PRE_DEBIT_TIME[%s]", value);
			}

			//R75     被叫接入号码
			pQuery->GetValue(22, value);
			if (strlen(value) == 0)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "delete RB_CODE_R_ACCESS_NUMBER", "");
			}
			else
			{
				rbr.access_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ACCESS_NUMBER[%s]", value);
			}

			//R01   付费号码
			pQuery->GetValue(9, value);
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CHARGE[%s]",  value);

			//R103  话单类型
			pQuery->GetValue(8, value);
			RERcallType = atoi(value);
			rbr.call_type = RERcallType;
			cacheData.RERcallType = RERcallType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CDR_TYPE[%s]",  value);

			//R5011 漫游类型
			int nSwitchUpdateTermRoam = smpara->GetINPara()->initRoma;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "call type[%d],INIT_ROMAN_CALLED[%d]", RERcallType, nSwitchUpdateTermRoam);
			if(2 == RERcallType && data && (1 == nSwitchUpdateTermRoam))//被叫流程做一下漫游判断
			{
				pQuery->GetValue(25, value);
				roamType=atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get roam type[%d]",roamType);
				if(6 == roamType||9 ==roamType )//被叫流程并且是国际漫游的，VLR信息部准确，不需要判断漫游
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "called international roam[%d],no need judge roam",roamType);
				}
				else
				{
					roamType = bizMsg->m_roamtype;
				}
			}
			//R5011 漫游类型
			else
			{
				pQuery->GetValue(25, roamType);
			}
			cacheData.roamType = roamType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "roamType[%d]", roamType);

	 		// RE_STR_CALLED_VISIT_AREA
			pQuery->GetValue(60, value);
			strcpy(szCalledVisitArea, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]",   value);
			//呼转流程
			if((3 == RERcallType) || (4 == RERcallType))
			{
				//R02   主叫号码
				pQuery->GetValue(9, value);
				rbr.calling_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]",  value);

				//R03   被叫号码
				pQuery->GetValue(33, value);
				rbr.called_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED[%s]", value);
				called.phone = value;

				//R105  连接号码
				pQuery->GetValue(13, value);
				rbr.connect_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT[%s]", value);

				//R1012  主叫交换机
				pQuery->GetValue(37, value);
				rbr.calling.msc = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_MSC[%s]", value);

				//R504  主叫号码归属费率区
				pQuery->GetValue(11, value);
				rbr.calling.harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]",  value);

				//R505  主叫号码拜访费率区
				pQuery->GetValue(23, value);
				rbr.calling.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]",  value);

				//R506  主叫号码归属运营商
				pQuery->GetValue(12, value);
				rbr.calling.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]",  value);

				//R507  被叫号码归属费率区
				//R508  被叫号码拜访费率区
				pQuery->GetValue(34, value);//RE_STR_REDIRECT_COUNTRY
				if (86 != atoi(value))
				{
					rbr.called.harea = value;
					rbr.called.varea= value;
				}
				else
				{
					pQuery->GetValue(35, value);
					rbr.called.harea = value;
					rbr.called.varea= value;
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_AREA[%s]",  value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]",  value);
				called.area = atoi(value);

				//R509  被叫号码归属运营商
				pQuery->GetValue(36, value);
				rbr.called.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_OPERATOR[%s]",  value);

				//term
	            if(requestType == SM_SESSION_TERMINATION_CODE)
				{
					pQuery->GetValue(40, value);
					nActiveFlag = atoi(value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "active flag [%d]", nActiveFlag);
				}

				//R5013 连接号码归属区
				pQuery->GetValue(14, value);
				if (86 != atoi(value))
				{
					rbr.connect_harea = value;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_AREA[%s]",  value);
				}
				else
				{
					pQuery->GetValue(15, value);
					rbr.connect_harea = value;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_AREA[%s]",  value);
				}

				//R5014  连接号码拜访区
				rbr.connect_varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CONNECT_VISIT_AREA[%s]", value);

				//R5015  拜访地运营商
				pQuery->GetValue(64, value);//RE_SUB_VISIT_CARRIER
				rbr.charged_vcarrier = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_VISIT_CARRIER[%s]", value);

			}
			else if(2 == RERcallType)//被叫流程
			{
				//R02   主叫号码
				pQuery->GetValue(13, value);
				rbr.calling_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]",  value);

				//R03   被叫号码
				pQuery->GetValue(17, value);
				rbr.called_nbr= value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED[%s]",  value);
				called.phone = value;

				//R504  主叫号码归属费率区
				//R505  主叫号码拜访区号
				pQuery->GetValue(15, value);
				rbr.calling.harea = value;
				rbr.calling.varea= value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]", value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

				//R506  主叫号码归属运营商
				pQuery->GetValue(16, value);
				rbr.calling.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]",  value);

				//R507  被叫号码归属费率区
				pQuery->GetValue(20, value);
				rbr.called.harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_AREA[%s]",  value);
				called.area = atoi(value);

				if((1 == smpara->GetINPara()->initRoma) &&(6 != roamType)&& (9 != roamType))//需要判断漫游，并且是非国际漫游的，取最新的信息给RB
				{

					//R508 被叫号码拜访费率区
					sprintf(value, "0%d", subVisit->area);
					rbr.called.varea = value;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]", value);

					//R5015  拜访地运营商
					rbr.charged_vcarrier = subVisit->szcarriers;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_VISIT_CARRIER[%s]",  subVisit->szcarriers);

					//R1013 R3013
					rbr.called.msc = data->calledVLR;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_MSC[%s]",  data->calledVLR.c_str());

				}
				else//从会话表中取拜访地区号:开关关闭或者国际漫游的,取init信息给RB
				{
					//R508
					pQuery->GetValue(19, value);//RE_STR_CALLED_COUNTRY
					if (86 != atoi(value))
					{
						sprintf(value, "00%d", value);
						rbr.called.varea = value;
					}
					else
					{
						pQuery->GetValue(23, value);//RE_STR_SUB_VISIT_AREA
						rbr.called.varea = value;
					}
	  				strcpy(szCalledVisitArea, value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]",  value);

					//R5015  拜访地运营商
					pQuery->GetValue(64, value);//RE_SUB_VISIT_CARRIER
					rbr.charged_vcarrier = value;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_VISIT_CARRIER[%s]",  value);


				}


				//R509  被叫号码归属运营商
				pQuery->GetValue(21, value);
				rbr.called.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_OPERATOR[%s]",  value);

			}
			else//主叫流程
			{
				//R02   主叫号码
				pQuery->GetValue(13, value);
				rbr.calling_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING[%s]",  value);

				//R03   被叫号码
				pQuery->GetValue(17, value);
				rbr.called_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED[%s]",  value);
				called.phone = value;

				//R504  主叫号码归属费率区
				pQuery->GetValue(15, value);
				rbr.calling.harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_AREA[%s]",  value);

				//R505  主叫号码拜访区号
				pQuery->GetValue(23, value);
				rbr.calling.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_VISIT_AREA[%s]", value);

				//R506  主叫号码归属运营商
				pQuery->GetValue(16, value);
				rbr.calling.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_OPERATOR[%s]", value);

	                    if(requestType == SM_SESSION_TERMINATION_CODE)
	                    {
					pQuery->GetValue(40, value);
					nActiveFlag = atoi(value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "active flag [%d]", nActiveFlag);
				}

				//R507  被叫号码归属费率区
				//R508  被叫号码拜访费率区
				pQuery->GetValue(19, value);
				if (86 != atoi(value))
				{
					rbr.called.varea = value;
					rbr.called.harea = value;
				}
				else
				{
					pQuery->GetValue(20, value);
					rbr.called.varea = value;
					rbr.called.harea = value;
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_AREA[%s]", value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_VISIT_AREA[%s]",  value);
				called.area = atoi(value);

				//R509  被叫号码归属运营商
				pQuery->GetValue(21, value);
				rbr.called.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_OPERATOR[%s]",  value);

				//R1012  主叫交换机
				pQuery->GetValue(37, value);
				rbr.calling.msc = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_MSC[%s]",  value);


				//R5015  拜访地运营商
				pQuery->GetValue(64, value);//RE_SUB_VISIT_CARRIER
				rbr.charged_vcarrier = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_VISIT_CARRIER[%s]", value);


			}

                     // RTI
                     pQuery->GetValue(75, value);
                     strcpy(subVisit->sector_id, value);
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RTI[%s]",  value);

			//
			pQuery->GetValue(39, value);
			nLongCdrTime = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "nLongCdrTime[%s]",  value);

			sprintf(value, "%d", roamType);
			rbr.roam_type = roamType ;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ROAM_TYPE[%s]",  value);

			//R5010 长途类型
			pQuery->GetValue(24, value);
			longType = atoi(value);
			rbr.long_type = longType;
			cacheData.longType = longType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_LONG_TYPE[%s]", value);

			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);
			rbr.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CHARGING_AREA[%s]",  value);

			//R602 计费类型
			rbr.sreq = requestType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_SESSION_STATE[%u]",  requestType);

			//R604  本次计费请求开始时间
			sprintf(value,"%ld",bizMsg->timestampCCR);
			rbr.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CUR_DEBIT_TIME[%s]",  value);

			//R605  是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_RATABLE_TIME[%s]",  "0");

	              //R608判断是否亲情号码
			if(requestType == SM_SESSION_TERMINATION_CODE)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_RELATIVE_FLAG[%s]",  "1");
			}

			//R615 小区优惠标识
			if(smpara->GetINPara()->favCellIDSwitch)
			{
				rbr.fav_flag = 1;
			}


			//R106  主叫小区(或基站)
			pQuery->GetValue(26, value);
			rbr.calling.cell = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_CELLID_OR_SAI[%s]", value);

			//R107  被叫小区(或基站)
			if(2==RERcallType)
			{
				rbr.called.cell = data->calledCellID;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_CELLID_OR_SAI[%s]",  data->calledCellID.c_str());
			}
			else
			{
				pQuery->GetValue(27, value);
				rbr.called.cell = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_CELLID_OR_SAI[%s]",  value);
			}
			strcpy(cacheData.szCalledCellid, rbr.called.cell.c_str());
			//R108  主叫用户位置区
			pQuery->GetValue(37, value);
			rbr.calling.lai = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLING_LAI[%s]",  value);

			//R109  被叫用户位置区
			pQuery->GetValue(38, value);
			rbr.called.lai = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_CALLED_LAI[%s]", value);


			//R1116 短号处理
			pQuery->GetValue(18, value);
			if(strlen(value) > 0)
			{
				rbr.called_short_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_AB_CALLED[%s]",   value);
			}

			pQuery->GetValue(40, value);
			nActiveFlag = atoi(value);

			//R1117 短号处理
			pQuery->GetValue(44, value);
			if(strlen(value) > 0)
			{
				rbr.called_group_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_AB_GP_NUMVBER[%s]",  value);
			}

			//R1118 短号处理
			pQuery->GetValue(45, value);
			if(strlen(value) > 0)
			{
				rbr.called_vpn_calltype = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_VPN_CALLTYPE[%s]",   value);
			}

			//B016   请求信用单位
			if(requestType == SM_SESSION_UPDATE_CODE)
			{
			    rsu.unit = 1;
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_REQUESTED_UNIT[1]", "");

				//B017   申请信用数量
			    sprintf(value, "%d", lastRSU);//动态预占
			    rsu.amount = lastRSU;
			    domain.rsv.push_back(rsu);
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_REQUESTED_AMOUNT[%s]",  value);

				// OCP_INT_ACTIVE_FLAG
			    pQuery->GetValue(40, value);
			    nActiveFlag = atoi(value);
			    cacheData.nActiveFlag = nActiveFlag;
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "nActiveFlag [%s]", value);
			}

			// RE_STR_CALLED_VLR
			pQuery->GetValue(38, value);
			strcpy(szCalledVlr, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "szCalledVlr [%s]", value);



			// SM_INT_AOC_TYPE
			pQuery->GetValue(46, value);
			cacheData.nAocType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "nAocType [%s]", value);


			// SM_INT_RESULT_CODE
			pQuery->GetValue(48, value);
			int nResultCode = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "nResultCode [%s]", value);

			// OCP_INT_CCA_FLAG
			pQuery->GetValue(49, value);
			cacheData.nCCAFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "nCCAFlag [%s]", value);

			// CDR_STR_BATCH_ID
			pQuery->GetValue(73, szBatchId);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "last BatchID [%s]", szBatchId);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "not find session[%s]", bizMsg->m_sessionID);
			return  SM_OCP_UNKNOWN_SESSION_ID;
		}
	}
	catch(UDBException& e)
	{
		string  sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_UNABLE_TO_COMPLY, "", "query execption[%s],SQL[%s],SQLCODE[%d]", e.ToString(),e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//累计总使用量
	TUSU += USU->duration;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "TUSU druation[%d]", TUSU);

    if(requestType == SM_SESSION_UPDATE_CODE)
    {
	    cacheData.duration= TUSU;
    }

	//term
    if(requestType == SM_SESSION_TERMINATION_CODE)
    {
        if(longType >= 2)
	    {
		    if(TUSU <= smpara->GetINPara()->shortCDRTime && (nLongCdrTime == 0))//不是超长话单截取后的超短
		    {
			    TUSU = 0;
			    USU->duration = 0;
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "short long", "");
		    }
	    }
		//判断市话超短话单
 	    else if((0==roamType) && (0==longType))
	    {
		    if(TUSU <= smpara->GetINPara()->shortCDRTimeCity && (nLongCdrTime == 0))
		    {
			    TUSU = 0;
			    USU->duration = 0;
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "short city", "");
            }
        }
		//判断漫游超短话单
        else if(roamType >= 1)
        {
            if(TUSU <= smpara->GetINPara()->shortCDRTimeRoam && (nLongCdrTime == 0))
            {
                TUSU = 0;
                USU->duration = 0;
                DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "short roam", "");
            }
        }

		//安徽陕西:主叫流程使用量不为0 激活用户(用户在首次使用未接通时不允许激活用户)
		//四川:init时就激活
        if((TUSU != 0) && (nActiveFlag == 1) && ((1 == RERcallType) ||(3 == RERcallType)||(4 == RERcallType)))
        {
		//R606
            rbr.active_flag = 1;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_R_ACTIVE_FLAG[%s]",  "1");
        }
    }

	//更新使用量
	u.unit = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_USED_UNIT[%s]",   RB_UNIT_STR_SECOND);
	sprintf(value, "%u", USU->duration);
	u.amount = USU->duration;

	cacheData.usu_duration = USU->duration;


	//info 日志
	DCDATLOG("SM00009:%d", USU->duration);

	domain.usv.push_back(u);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_USED_AMOUNT[%s]", value);

   //B03总使用量
    if(requestType == SM_SESSION_TERMINATION_CODE)
    {
        totalusu.unit = 1;
        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_DEBIT_UNIT[%s]",   RB_UNIT_STR_SECOND);
        sprintf(value, "%d", TUSU);
        totalusu.amount = TUSU;

        domain.dbv.push_back(totalusu);
        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);
    }

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "cached ok", "");

	rbr.accumlator_info = 1;
	rbr.tariff_info = 1;
	rbr.rating_info = 1;
	rbr.balance_query = 1;

    if(requestType == SM_SESSION_TERMINATION_CODE)
    {
        rbr.balance_info = 1;
    }

	//当前时间用于会话超时，语音业务update时，超时时间根据预占步长计算
    if(requestType == SM_SESSION_UPDATE_CODE)
    {
        time_t et;
        time(&et);
        long nextCCTime = et + 2*lastRSU;
	 	cacheData.nextCCTime = nextCCTime;
        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "next cc time[%ld] lastRsu[%d]", nextCCTime, lastRSU);
    }

	//被叫流程时才更新该字段信息
	if(2==RERcallType && 1 == smpara->GetINPara()->initRoma)
	{
		if(6==roamType || 9==roamType)//被叫国际漫游的不需要更新拜访地区号和被叫的msc，取init的
		{
 			strcpy(cacheData.szCalledVisitArea, szCalledVisitArea);
			strcpy(cacheData.szCalledVlr, szCalledVlr);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "ready update visit.area[%s],called.vlr[%s]", szCalledVisitArea,szCalledVlr);
		}
		else
		{
			sprintf(cacheData.szCalledVisitArea, "0%d", subVisit->area);
			strcpy(cacheData.szCalledVlr, data->calledVLR.c_str());
		}
		//strcpy(cacheData.szCalledCellId, data->calledCellID);
	}
	else
	{
		strcpy(cacheData.szCalledVisitArea, szCalledVisitArea);
		strcpy(cacheData.szCalledVlr, szCalledVlr);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "cached ok", "");
	if(bizMsg->m_requestType == SM_SESSION_TERMINATION_CODE)
	{
		rbr.acct_info = 1;
		ext.kv["B12"] = "1";
	}
	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.test_info = 1;
		rbr.acct_info = 1;
	}
	//CEE被叫 CER主叫
	ext.kv["CER"] = bizMsg->m_callingNumber;
	ext.kv["CEE"] = bizMsg->m_calledNumber;
	ext.kv["RTI"] = subVisit->sector_id;

	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	// VoLTE 新增字段
	if (1 == data->VoLTEFlag)
	{
		ext.kv["R501"] = "3";
	}
	if (data->callaccesstype.length())
	{
		ext.kv["R623"] = data->callaccesstype;
	}
	char sVoLTEFlag[10] = {0};
	sprintf(sVoLTEFlag,"%d",data->VoLTEFlag);
	ext.kv["VolteFlag"] = sVoLTEFlag;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;

	// 获取批次号
	if(0 == strlen(szBatchId))
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, smpara->GetCommonPara()->iBatchIdTime, 1);
	}
	else
	{
		bizMsg->m_sBatchId = szBatchId;
	}
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());

	// R3019
	if (1 == data->VoLTEFlag)
	{
		if (2 == RERcallType) // 被叫
		{
			ext.kv["R3019"] = rbr.called.msc; // R1013
		}
		else
		{
			ext.kv["R3019"] = rbr.calling.msc; // R1012
		}
	}
	else
	{
		if (2 == RERcallType) // 被叫
		{
			ext.kv["R3019"] = rbr.called.cell; // R107
		}
		else
		{
			ext.kv["R3019"] = rbr.calling.cell; // R106
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "volte[%d] callType[%d] compose R3019[%s]", data->VoLTEFlag, RERcallType, ext.kv["R3019"].c_str());

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);


		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char *)m_en.data();

	return 0;
}

