﻿/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqPGW.h
*Indentifier：
*
*Description：
*		4G业务处理类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_PGW_TEL_H__
#define __DC_REQ_PGW_TEL_H__
#include "DCReqDATA.h"

class DCReqPGW : public DCReqDATA
{
	public:

		DCReqPGW();
		virtual ~DCReqPGW();

	private:

		virtual int SwitchReqType(STBizMsg* bizMsg);

		int composeRER(STBizMsg* bizMsg,ocs::SUSU *MSCC,SSessionCacheData &cacheData, std::vector<long> &v_rg);
		
		int Init(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);
		
		int Update(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);

		int FirstUpdate(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg, ocs::SUSU *MSCC);
		
		int Term(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);

		int XdrEvent(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);

};

#endif

