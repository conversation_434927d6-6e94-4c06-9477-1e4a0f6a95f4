include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)
COMMON_INC=$(LBSPUBROOT)/SM/common/include

CDR_INC=$(PWD)/include
CDR_SRC=$(PWD)/src
CDR_OBJ=$(PWD)/obj

CDR_CPP=DCBizCdr.cpp DCBizCdrNormal.cpp  DCBizCdrNormalVoice.cpp \
            DCBizCdrNormalDSL.cpp DCBizCdrNormalIsmp.cpp DCBizCdrNormalSMS.cpp  DCBizCdrNormalDATA.cpp DCBizCdrNormalPGW.cpp DCBizCdrNormal5G.cpp TCDRDict.cpp DCUUIDUtil.cpp\
            desc_cdrnormal.cpp
           
CDR_SRCS=$(addprefix $(CDR_SRC)/, $(CDR_CPP))
CDR_OBJS=$(patsubst $(CDR_SRC)/%.cpp, $(CDR_OBJ)/%.o, $(CDR_SRCS))

CFLAGS += -std=c++11

TLIB= $(RELEASE_PATH)/plugin/libcdrnormal.so

INCLUDE =-I$(CDR_INC) \
				 -I$(COMMON_INC) \
				 -I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
         -I$(DFM_INC_PATH) \
         -I$(TXML)/include \
         -I$(MQ)/include\
         -I$(EVT_CHECK_INC)

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(MQ)/lib -L$(LBSPUBROOT)/release/realbilling/lib -L$(EVT_CHECK_LIB)
LIBSLIST= -lCommonIF -lrocketmq64 -lCheckSDK

tmpvar:=$(call CreateDir, $(CDR_OBJ))
.PHONY: all clean dup
all:$(TLIB)	
$(TLIB): $(CDR_OBJS)
	@echo "build libcdrnormal.so----"
	$(CC) $(DFLAGS)  -o $(TLIB) $(CDR_OBJS) $(LIBPATH) $(LIBSLIST)
$(CDR_OBJS):$(CDR_OBJ)/%.o:$(CDR_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	
$(CDR_SRC)/desc_cdrnormal.cpp:$(CDR_SRC)/desc_cdrnormal.clog
	$(TOOL)/clogtool -i $< -o $@
clean:
	@rm -rf $(CDR_OBJS) $(TLIB)

dup:
	@cp -pf $(TLIB) $(PROJECT_RPATH)/plugin/SM && echo "dup $(TLIB) to $(PROJECT_RPATH)/plugin/SM"