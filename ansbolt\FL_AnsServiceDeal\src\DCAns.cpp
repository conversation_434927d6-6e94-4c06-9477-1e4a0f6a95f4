#include "DCAns.h"
typedef vector<long> veci;
DCAns::DCAns():m_en(ESeriaBinString)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCAns::~DCAns()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

int DCAns::Work(void *data)
{
	return 0;
}

int DCAns::ModifyREAMsg(STBizMsg* bizMsg,SREAInfo &REAMsg, int seq)
{
	int ret =0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID, "ModifyREAMsg, seq[%d].", seq);
    EraseZeroItem(bizMsg);

	ret = ModifyBalanceInfo(bizMsg,REAMsg.sz_balanceInfo, seq);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[balanceInfo]");
		return ret;
	}

	ret = ModifyBalanceInfo2(bizMsg, REAMsg.sz_balanceInfo2, seq);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[balanceInfo2]");
		return ret;
	}

	ret = ModifyAccumlatorInfo(bizMsg, REAMsg.sz_accumlatorInfo, sizeof(REAMsg.sz_accumlatorInfo) - 1, seq);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[accumlatorInfo]");
		return ret;
	}

	ret = ModifyTariffIdInfo(bizMsg, REAMsg.sz_tariffIdInfo, sizeof(REAMsg.sz_tariffIdInfo) - 1, seq);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[tariffIdInfo]");
		return ret;
	}
	if (bizMsg->m_requestType == SM_SESSION_EVENT_REFUND_CODE && (bizMsg->m_serviceContextID == SMS || bizMsg->m_serviceContextID == ISMP))
	{
		ret = ModifyChargeInfoRefund(bizMsg, REAMsg.sz_chargeInfo, &REAMsg.sz_gAmount);
	}
	else
	{
		ret = ModifyChargeInfo(bizMsg, REAMsg.sz_chargeInfo, &REAMsg.sz_gAmount, seq);
	}
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[chargeInfo]");
		return ret;
	}


	ret = ModifyPricinglanID(bizMsg,REAMsg.szPricingPlanID);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[PricingPlanID]");
		return ret;
	}

	ret = ModifyOriChargeInfo(bizMsg, REAMsg.sz_oriChargeInfo);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[oriChargeInfo]");
		return ret;
	}

	return ret;
}

int DCAns::ModifyBalanceInfo(STBizMsg* bizMsg, char* pBuf, int seq) //多组usu的情况seq代表usu序号
{
	int ret					= RET_SUCCESS;
	SUseInfo cur			;

	SUseInfo all[6]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	//获取原先信息
	ret = DCCommonIF::ParseUseInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//账本信息B08
	int size = base->actv.size();
	for (int i = 0; i < size; i++)
	{
		acctchangeitem_t acctitem = base->actv[i];
		if(acctitem.seq != seq)
		{
			continue;
		}
		cur.id = acctitem.acct_item_id;
		cur.unit = acctitem.unit;
		cur.change = acctitem.amount;
		cur.total = acctitem.balance;

		for(int j=0; j<6; j++)
		{
			if ((0 == all[j].id) && (0 == all[j].unit) && (0 == all[j].change) && (0 == all[j].total))
			{
				memcpy(&all[j], &cur, sizeof(SUseInfo));
				break;
			}
			else
			{
				if((all[j].id == cur.id)&&(all[j].unit == cur.unit))
				{
					all[j].change += cur.change;
					all[j].total = cur.total;
					break;
				}
			}
		}

	}

	for(int k=0; k<6; k++)
	{
		if((all[k].id==0) && (all[k].unit==0) && (all[k].change==0) && (all[k].total==0))
		{
			break;
		}
		sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld%c%ld;", all[k].id, all[k].unit, fence, all[k].change, fence, all[k].total);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "balanceInfo[%d][%s]", k, pBuf);
	}
	if(pBuf[0])
	{
		pBuf[strlen(pBuf)-1] = endfence;
	}
	return RET_SUCCESS;
}

//账户信息
//B081:B082,B086,B083,B084
int DCAns::ModifyBalanceInfo2(STBizMsg* bizMsg, char* pBuf, int seq)
{
	string str;
	char tmp[256]				= {0};
	char CHARINFO[2]			= {0};//存放话单间隔字符
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;



	int len=strlen(pBuf);
	if(len&&(';'!=pBuf[len-1]))   //第一次调用不作处理
	{
		pBuf[len]=';';//增加长度
		pBuf[len+1]='\0';
	}

	str=pBuf;
	int pos=0;
	long usedcount = 0;
	int endpos=0;
	//累积量信息B08
	int size = base->actv.size();
	for (int k = 0; k < size; k++)
	{

		acctchangeitem_t acctitem = base->actv[k];
		if(acctitem.seq != seq)
		{
			continue;
		}

		sprintf(tmp, "%ld:%d%c%ld%c",acctitem.acct_item_id, acctitem.unit, fence, acctitem.fee_item_id,fence);

		if(-1!=(pos=str.find(tmp, 0)))
		{
			CHARINFO[0]=fence;
			pos+=strlen(tmp);
			if(-1==(endpos=str.find(CHARINFO, pos+1)))return RET_SUCCESS;
			usedcount = acctitem.amount + atol(str.substr(pos, endpos-pos).c_str());
			memset(tmp, 0 ,sizeof(tmp));
			sprintf(tmp,"%ld%c%ld", usedcount, fence, acctitem.balance);
			if(-1==(endpos=str.find(";",endpos)))return RET_SUCCESS;
			str.replace(pos, endpos-pos,tmp);
		}
		else
		{
			sprintf(tmp, "%ld:%d%c%ld%c%ld%c%ld;",acctitem.acct_item_id, acctitem.unit, fence, acctitem.fee_item_id, fence, acctitem.amount, fence, acctitem.balance);
			str.append(tmp);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "current balanceinfo2[%ld:%d,%ld,%ld,%ld]",acctitem.acct_item_id, acctitem.unit, acctitem.fee_item_id, acctitem.amount, acctitem.balance);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "balanceinfo2[%s]",str.c_str());
	}

	strcpy(pBuf, str.c_str());
       if(pBuf[0])
       {
            pBuf[strlen(pBuf)-1] = endfence;
       }

	return RET_SUCCESS;
}

int DCAns::ModifyTariffIdInfo(STBizMsg* bizMsg, char* pBuf, int iBufCapacity, int seq)
{
	int ret					= RET_SUCCESS;
	STariffInfo cur			;
	STariffInfo all[100]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

       bool bfee = false;
       for(int i = 0; i < (int)base->dbv.size(); i++)
       {
            if(base->dbv[i].unit == 2)
            {
                    bfee = true;
            }
       }
       if(base->dbv.empty())
       {
            bfee = true;
       }

	//获取原先信息
	ret = DCCommonIF::ParseTariffInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//资费信息B07
	int size = base->trv.size();
	for (int i = 0;  i < size; i++)
	{
		tariff_t tariff = base->trv[i];
		if(tariff.seq != 0)
		{
			continue;
		}
		cur.offerId = tariff.ofr_id;
		cur.acctItemId = tariff.acct_item_id;
		cur.measure = tariff.Measure;
		cur.refacctItemId = tariff.Refacct_item_id;
		if(bizMsg->m_requestType == SM_SESSION_EVENT_REFUND_CODE && (bizMsg->m_serviceContextID == SMS || bizMsg->m_serviceContextID == ISMP))
		{
		        cur.amount = -tariff.amount;
		}
		else
		{
		    cur.amount= tariff.amount;
		}
		
		if(tariff.Measure == 3  && !bfee)
		{
		    cur.amount = 0;
		}
		cur.billingDuration = tariff.billing_duration;
		cur.distFee = tariff.dist_fee;
		cur.groupid= tariff.GroupId;
		cur.ofr_inst_id= tariff.ofr_inst_id;
		cur.counts = tariff.counts;
		cur.ori_tariff = tariff.OriTariff;
		cur.disct_tariff = tariff.DisctTariff;

		for(int j=0; j<100; j++)
		{
			if ((0 == all[j].offerId)
			&& (0 == all[j].amount)
			&& (0 == all[j].acctItemId)
			&& (0 == all[j].measure)
			&& (0 == all[j].billingDuration)
			&& (0 == all[j].distFee))
			{
				memcpy(&all[j], &cur, sizeof(STariffInfo));
				break;
			}
			else
			{
				if(all[j].offerId == cur.offerId && all[j].acctItemId == cur.acctItemId && all[j].ofr_inst_id == cur.ofr_inst_id) //ofrId 与 acctitemid都一样时，费用叠加
				{
					all[j].amount+= cur.amount;
					all[j].distFee+= cur.distFee;
					all[j].billingDuration+= cur.billingDuration;
					all[j].counts+= cur.counts;
					all[j].ori_tariff += cur.ori_tariff;
					all[j].disct_tariff += cur.disct_tariff;
					break;
				}
			}
		}
	}

	int len = 0;
	for(int k=0; k<100; k++)
	{
		if((all[k].offerId==0)
		&& (all[k].amount==0)
		&& (all[k].acctItemId==0)
		&& (all[k].measure==0)
		&& (all[k].billingDuration==0)
		&& (all[k].distFee==0))
		{
			break;
		}
		
		if((all[k].measure==0) && (all[k].billingDuration==0) && (all[k].amount==0))
		{
		    continue;
		}
		len = strlen(pBuf);
		sprintf(pBuf + len, "%ld:%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld;", all[k].offerId, all[k].acctItemId,fence,all[k].measure,fence,all[k].amount,fence,all[k].billingDuration,fence,all[k].distFee,fence,all[k].groupid,fence,all[k].ofr_inst_id,fence,all[k].counts,fence,all[k].refacctItemId,fence,all[k].ori_tariff,fence,all[k].disct_tariff);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "tariffid[%d][%s]", k, pBuf+len);
	}

	if(pBuf[0])
	{
		pBuf[strlen(pBuf)-1] = endfence;
	}
	DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID),  "", "tariffid[%s]", pBuf);

	return RET_SUCCESS;

}

int DCAns::ModifyAccumlatorInfo(STBizMsg* bizMsg, char* pBuf, int iBufCapacity, int seq)
{
	int ret 				= RET_SUCCESS;
	SAccumInfo cur 		;
	SAccumInfo all[100]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "CdrEndchar[%c]", endfence);

	//获取原先信息
	ret = DCCommonIF::ParseAccumlatorInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	int size = base->accv.size();
	for (int i = 0; i < size; i++)
	{
		accum_item_t accum = base->accv[i];
		if(accum.seq != 0)
		{
			continue;
		}
		cur.accu_id = accum.accu_id;
		cur.amount = accum.amount;
		cur.offerId = accum.OfrId;
		cur.unit = accum.unit;
		cur.total = accum.TotalValue;
		cur.ofrinstid = accum.OfrInstId;
		cur.ratableBalanceId = accum.ratable_balance_id;
		cur.accu_type_id = accum.accu_type_id;
		cur.billingLatn = accum.billingLatn;
		cur.iLatnid = accum.iLatnid;
		cur.updateflag = accum.updateflag;
		cur.usage_voice = accum.usage_voice;
		cur.usage_sms = accum.usage_sms;
		cur.accu_use_id = accum.accu_use_id;
		cur.billingCycle = atol(accum.BillingCycle.c_str());

		for(int j=0; j<100; j++)
		{
			if ((0 == all[j].accu_id) && (0 == all[j].amount))
			{
				memcpy(&all[j], &cur, sizeof(SAccumInfo));
				break;
			}
			else
			{
				if(all[j].accu_id == cur.accu_id && all[j].offerId == cur.offerId && all[j].ofrinstid == cur.ofrinstid)
				{
					all[j].amount+= cur.amount;
					all[j].total=all[j].total>cur.total?all[j].total:cur.total;
					break;
				}
				//all[j].offerId = cur.offerId;
				//all[j].unit = cur.unit;
				//all[j].total = cur.total;
				//all[j].ofrinstid = cur.ofrinstid;
			}
		}
	}

	int len = 0;
	for(int k=0; k<100; k++)
	{
		if((all[k].accu_id==0) && (all[k].amount==0))
		{
			break;
		}
		len = strlen(pBuf);
		//sprintf(pBuf + len, "%ld:%ld:%ld:%ld:%ld:%ld:%ld:%ld:%ld;", all[k].accu_type_id, all[k].offerId, all[k].unit, all[k].amount, all[k].total, all[k].ofrinstid, all[k].accu_id,all[k].billingLatn,all[k].iLatnid);
		// modify by zhuhuan, 改成江西HB输出格式,
		sprintf(pBuf + len, "%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%d%c%ld%c%ld%c%ld%c%ld;",
		all[k].accu_type_id, fence,
		all[k].offerId, fence,
		all[k].unit, fence,
		all[k].amount, fence,
		all[k].total, fence,
		all[k].ofrinstid, fence,
		all[k].accu_id, fence,
		all[k].ratableBalanceId, fence,
		all[k].billingLatn, fence,
		all[k].iLatnid, fence,
		all[k].updateflag, fence,
		all[k].usage_voice, fence,
		all[k].usage_sms, fence,
		all[k].accu_use_id, fence,
		all[k].billingCycle);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "accumInfo[%d][%s]", k, pBuf + len);
	}
	
	if(strlen(pBuf) > 0)
		pBuf[strlen(pBuf)-1] = endfence;
		
	DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID),  "", "accumInfo[%s]", pBuf);
	return RET_SUCCESS;

}

int DCAns::ModifyChargeInfo(STBizMsg* bizMsg, char* pBuf,  int *pInt, int seq)
{
	int ret					= RET_SUCCESS;
	SUseInfo cur			;
	SUseInfo all[6]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	//获取原先信息
	ret = DCCommonIF::ParseUseInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//账本信息B03
	int size = base->dbv.size();
	for (int i = 0; i < size; i++)
	{
		debit_item_t debit = base->dbv[i];
		if(debit.seq != 0)
		{
			continue;
		}
		cur.id = debit.acct_item_id;
		cur.unit = debit.unit;
		cur.change = debit.amount;

		if(pInt&&(cur.unit==2))*pInt=cur.change;

		if(2 != cur.unit)
		{
			cur.id = 0;
			cur.unit = 2;
			cur.change = 0;
		}

		for(int j=0; j<6; j++)
		{
			if ((0 == all[j].id) && (0 == all[j].unit) && (0 == all[j].change) && (0 == all[j].total))
			{
				memcpy(&all[j], &cur, sizeof(SUseInfo));
				break;
			}
			else
			{
				if(all[j].id == cur.id)
				{
					all[j].change += cur.change;
					break;
				}
			}
		}
	}

	int len = 0;
	for(int k=0; k<6; k++)
	{
		if((all[k].id==0) && (all[k].unit==0) && (all[k].change==0) && (all[k].total==0))
		{
			break;
		}
		len = strlen(pBuf);
		sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].id, all[k].unit, fence, all[k].change);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "chargeInfo[%d][%s]", k, pBuf+len);
	}

	if(pBuf[0])
	{
		pBuf[strlen(pBuf)-1] = endfence;
	}
	DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID),  "", "chargeInfo[%s]", pBuf);

	return RET_SUCCESS;

}

int DCAns::ModifyChargeInfoRefund(STBizMsg* bizMsg, char* pBuf,int *pInt)
{
	int ret					= RET_SUCCESS;
	SUseInfo cur			;
	SUseInfo all[6]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;


	//获取原先信息
	ret = DCCommonIF::ParseUseInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//账本信息B04
	int size = base->rfv.size();
	for (int i = 0; i < size; i++)
	{

		refund_item_t refund_item =  base->rfv[i];
		cur.id = refund_item.acct_item_id;
		cur.unit = refund_item.unit;
		cur.change -= refund_item.amount;

		if(pInt&&(cur.unit==2))*pInt=cur.change;

		if(2 != cur.unit)
		{
			cur.id = 0;
			cur.unit = 2;
			cur.change = 0;
		}

		for(int j=0; j<6; j++)
		{
			if ((0 == all[j].id) && (0 == all[j].unit) && (0 == all[j].change) && (0 == all[j].total))
			{
				memcpy(&all[j], &cur, sizeof(SUseInfo));
				break;
			}
			else
			{
				if(all[j].id == cur.id)
				{
					all[j].change += cur.change;
					break;
				}
			}
		}
	}

	for(int k=0; k<6; k++)
	{
		if((all[k].id==0) && (all[k].unit==0) && (all[k].change==0) && (all[k].total==0))
		{
			break;
		}
		sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].id, all[k].unit, fence, all[k].change);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "chargeInfo[%d][%s]", k, pBuf);
	}
	
	if(pBuf[0])
	{
		pBuf[strlen(pBuf)-1] = endfence;
	}
	return RET_SUCCESS;
}

//保存定价计划ID
int DCAns::ModifyPricinglanID(STBizMsg* bizMsg, char* pBuf)
{
	char value[256]			= {0};
	long lValue = 0;
	char tmp[256]			= {0};

	bool bFindFlag = false;             //定价计划是否存在标识
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();


	if(1 == commonPara->iPricePlanID)
	{
		for (int i = 0; i < 4; i++)
		{
			if(base->plan_id.length()>0)
			{
				string str = base->plan_id;
				int pos = 0, prev_pos = 0;

				int len = str.length();
				while(true)//解析出每个定价计划ID
				{
					pos = str.find("#",pos);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "pos[%d], len[%d], prePos[%d]", pos, len, prev_pos);
					if(-1 != pos)
					{
			      		//strcpy(value, (str.substr(pos,len).c_str()));
			      		prev_pos = pos + 1;
						pos++;
					}
					else
					{
						strcpy(tmp, (str.substr(prev_pos,len-prev_pos).c_str()));
						break;
					}

				}

				if(strlen(tmp))
				{
					sprintf(pBuf, "%s", tmp);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "get plan id[%s]", tmp);
				}

			}

		}
	}
	else
	{
		for (int i = 0; i < 4; i++)
		{
			strncpy(value, base->plan_id.c_str(),sizeof(value));
			{
				if(0 == strlen(pBuf))
				{
					sprintf(pBuf, "%s;", value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "no find plan id[%s],add",  value);
				}
				else
				{
					string str = pBuf;
					int pos = 0, prev_pos = 0;
					while(true)//解析出每个定价计划ID
					{
						pos = str.find(";",pos);
						if(-1 != pos)
						{
					      lValue = atol(str.substr(prev_pos,pos).c_str());

					      prev_pos = pos + 1;

							if(atol(value) == lValue)
							{
								bFindFlag = true;//存在
								DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "find plan id[%d],skip", lValue);
								break;
							}
							pos++;
						}
						else
						{
							break;
						}

					}

					if(!bFindFlag)//不存在，加入
					{
						sprintf(pBuf, "%s%s;", pBuf,value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "no find plan id[%s],add", value);
					}
				}
			}

		}
	}
	//pBuf[strlen(pBuf)-1] = SM_CDR_END_CHAR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "pricing_plan_id[%s]", pBuf);

	return RET_SUCCESS;
}

int DCAns::ModifyOriChargeInfo(STBizMsg* bizMsg, char* pBuf, int seq)
{
	int ret					= RET_SUCCESS;
	SOriChargeInfo cur			;
	SOriChargeInfo all[12]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;


	//获取原先信息
	ret = DCCommonIF::ParseOriChargeInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//一批信息 B16
	int size = base->oric.size();
	for (int i = 0; i < size; i++)
	{
		oricharge_t& oricharge = base->oric[i];
		if (oricharge.seq != seq)
		{
			continue;
		}
		cur.acctItemId = oricharge.acct_item_id;
		cur.measure = oricharge.Measure;
		cur.oriAmount = oricharge.ori_amount;

		for (int j = 0; j < 12; j++)
		{
			if ((0 == all[j].acctItemId) && (0 == all[j].measure) && (0 == all[j].oriAmount))
			{
				memcpy(&all[j], &cur, sizeof(SOriChargeInfo));
				break;
			}
			else
			{
				if(all[j].acctItemId == cur.acctItemId) //acctitemid一样时，费用叠加
				{
					all[j].oriAmount+= cur.oriAmount;
					break;
				}
			}
		}
	}

	//如果有多组，只取最后一?
	for (int k = 0; k < 12; k++)
	{
		if (11 == k)
		{
			sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].acctItemId, all[k].measure, fence, all[k].oriAmount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "oriChargeInfo[%d][%s]", k, pBuf);
			break;
		}

		if ((all[k+1].acctItemId==0) && (all[k+1].measure==0) && (all[k+1].oriAmount==0))
		{
			sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].acctItemId, all[k].measure, fence, all[k].oriAmount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "oriChargeInfo[%d][%s]", k, pBuf);
			break;
		}
	}

	pBuf[strlen(pBuf)-1] = endfence;
	return RET_SUCCESS;
}

int DCAns::EraseZeroItem(STBizMsg* bizMsg)
{
        rbresult *base = (rbresult *)bizMsg->m_base;
        bool nTariffFind = false;
        bool nAccumFind = false;


        //累积量信息B06
	int size = base->accv.size();
	for (int k = 0; k < size; k++)
	{
            if(base->accv[k].amount == 0)
            {
                 nAccumFind = true;
                 break;
            }
       }

       if(!nAccumFind) return 0;

        //资费信息B07
	size = base->trv.size();
	for (int i = 0; i < size; i++)
	{
            if(base->trv[i].amount == 0 && base->trv[i].billing_duration == 0)
            {
                 nTariffFind = true;
                 break;
            }
	}

       if(!nTariffFind) return 0;

       //累积量与资费均存在为0的则删除值为0的组
       for (std::vector<accum_item_t >::iterator it = base->accv.begin(); it != base->accv.end(); )
	{
            if(it->amount == 0)
            {
                 DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "erase accum item, ratable_id[%ld], amount[%ld]", it->accu_id, it->amount);
                 it = base->accv.erase(it);
            }
            else
            {
                it++;
            }
       }

       for (std::vector<tariff_t >::iterator iv = base->trv.begin(); iv != base->trv.end(); )
	{
            if(iv->amount == 0 && iv->billing_duration == 0)
            {
                 DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "erase tariff item, ofr_id[%ld], amount[%ld]", iv->ofr_id, iv->amount);
                 iv = base->trv.erase(iv);
            }
            else
            {
                iv++;
            }
       }
       return 0;
}

int DCAns::PreChargeInfo(STBizMsg* bizMsg, int &amount)
{
	int k = 0;

	rbresult *base = (rbresult *)bizMsg->m_base;

	int size = base->rsv.size();
	for(k=0; k<size; k++)
	{
		reserve_item_t reserve_item = base->rsv[k];

		if(2!=reserve_item.unit)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "预占单位为 %d",reserve_item.unit);
			continue;
		}

		amount += reserve_item.amount;
 	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", " 预占量为[%d]", amount);

	return 0;
}

int DCAns::AccumlateBalance(STBizMsg* bizMsg)
{
	int totalBalance    = 0;
	int balance         = 0;
	int iGroup          = 0;
	char value[256] 	= {0};
	rbresult *base = (rbresult *)bizMsg->m_base;


	int size = base->bal.size();
	while(size)
	{
		acctitem_t acctitem =base->bal[iGroup];
		balance = acctitem.amount;
		if (2 == acctitem.unit) // 流量账本过滤
		{
			totalBalance += balance;
		}
		iGroup++;
		size--;
	}

	if(iGroup == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_OTHER_TYPE,  "", "RBA msg balance group error");
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", " balance[%d]", totalBalance);

	return totalBalance;
}

int DCAns::GetCostInfo(STBizMsg* bizMsg, int& cost_unit, int& cost_value)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "Get Cost Info");

	rbresult *base = (rbresult *)bizMsg->m_base;

	//账本信息B03
	int size = base->dbv.size();
	for (int i = 0; i < size; i++)
	{
		debit_item_t dbvitem = base->dbv[i];

		if(0==i)
		{
			cost_unit = dbvitem.unit;
		}
		cost_value += dbvitem.amount;
	}

	return 0;
}

int DCAns::DelSession(STBizMsg* bizMsg)
{
	char value[128] = {0};
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "delete session", "");

	if(bizMsg->m_serviceContextID == DATA || bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == PGW || bizMsg->m_serviceContextID == DATA_5G)
	{
		sprintf(value, "%s%%", bizMsg->m_sessionID);
	}

	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;

	UDBSQL *pDelete = NULL;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pDelete = dbm->GetSQL(Voice_DeleteSession);
			}
			break;
		case SMS:
			{
				pDelete =   dbm->GetSQL(SMS_DeleteSession);
			}
			break;
		case DATA:
		case CCG:
			{
				pDelete =  dbm->GetSQL(DATA_DeleteSessionStoreRG);
			}
			break;
		case PGW:
			{
				pDelete =  dbm->GetSQL(PGW_CDR_DeleteSession);
			}
			break;
		case ISMP:
		case HRS:
			{
				pDelete =  dbm->GetSQL(ISMP_DeleteSession);
			}
			break;
		case DSL:
			{
				pDelete = dbm->GetSQL(DSL_DeleteSession);
			}
			break;
		case DATA_5G:
			{
				pDelete = dbm->GetSQL(_5G_DeleteChildSession);
			}
			break;
		default:
			{
				return RET_ERROR;
			}
			break;
	}
	try
	{
		pDelete->DivTable(bizMsg->m_sessionID);
		pDelete->UnBindParam();
		pDelete->BindParam(1, bizMsg->m_sessionID);
		pDelete->Execute();
		pDelete->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "delete session successful");
	}
	catch (UDBException& e)
	{
		pDelete->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}


int DCAns::ComposeRMQMsg(STBizMsg* bizMsg,ocs::SCCAMsg &ccamsg,const char *topic,string &msg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "begin send CCA");

	ocs::UHead uhd;
	uhd.uid = bizMsg->m_uid;
	ccamsg.trace = DCLOG_GETCTL(DCLOG_MASK_TRACE);
	uhd.checkKey = bizMsg->m_strCheckKey;
  	//打印cca内容
	DCSeriaPrinter p2;
	p2.print(uhd);
	p2.print(ccamsg);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "cca[%s]", p2.data());

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(ccamsg);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE,"","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	return 0;
}

int DCAns::ComposeRMQMsg(STBizMsg* bizMsg,ocs::SCCA5G &cca5g,const char *topic,string &msg)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "begin send CCA");

	ocs::UHead uhd;
	uhd.uid = bizMsg->m_uid;
	cca5g.trace = DCLOG_GETCTL(DCLOG_MASK_TRACE);
	uhd.checkKey = bizMsg->m_strCheckKey;

  	//打印cca内容
	DCSeriaPrinter p2;
	p2.print(uhd);
	p2.print(cca5g);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "cca[%s]", p2.data());

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca5g);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE,"","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	return 0;
}
int DCAns::MergeReaMsg(STBizMsg* bizMsg,const char * split,char *merge)
{
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	if(strlen(merge) == 0)
	{
		strcpy(merge,split);
		return 0;
	}
	if(strlen(split) == 0)
		return 0;

	map<string,veci> m1;
	int flag = 0;
	int head = 0;
	const char *phead = split;
	string keyname;
	char temp[32] = {0};
	for(int i=0;i<strlen(split);i++)
	{
		if(split[i] == ';')
		{
			head = i+1;
			phead = &split[i+1];
			continue;
		}
		if(split[i] != ':')
			continue;
		else
		{
			for (;i < strlen(split);i++)
			{
				if (split[i] == fence)
					break;
			}
			memset(temp, 0, sizeof(temp));
			strncpy(temp, phead, i - head);
			keyname = temp;
			phead = &split[i + 1];
			head = i + 1;
			i++;
			for(;i<strlen(split);i++)
			{
				if(split[i] == ';' || i == strlen(split) - 1)
				{
					memset(temp, 0, sizeof(temp));
					strncpy(temp,phead,i+1-head);
					m1[keyname].push_back(atol(temp));
					head = i+1;
					phead = &split[i+1];
					break;
				}
				if(split[i] != fence)
					continue;
				memset(temp, 0, sizeof(temp));
				strncpy(temp,phead,i-head);
				m1[keyname].push_back(atol(temp));
				head = i+1;
				phead = &split[i+1];
			}
		}
	}

	map<string,veci> m2;
	flag = 0;
	head = 0;
	phead = merge;
	memset(temp,0,32);
	for(int i=0;i<strlen(merge);i++)
	{
		if(merge[i] == ';')
		{
			head = i+1;
			phead = &merge[i+1];
			continue;
		}
		if(merge[i] != ':')
			continue;
		else
		{
			for (;i < strlen(merge);i++)
			{
				if (merge[i] == fence)
					break;
			}
			strncpy(temp, phead, i - head);
			keyname = temp;
			phead = &merge[i + 1];
			head = i + 1;
			i++;
			for(;i < strlen(merge);i++)
			{
				if(merge[i] == ';' || i == strlen(merge) - 1)
				{
					memset(temp, 0, sizeof(temp));
					strncpy(temp,phead,i+1-head);
					m2[keyname].push_back(atol(temp));
					head = i+1;
					phead = &merge[i+1];
					break;
				}
				if(merge[i] != fence)
					continue;
				memset(temp, 0, sizeof(temp));
				strncpy(temp,phead,i-head);
				m2[keyname].push_back(atol(temp));
				head = i+1;
				phead = &merge[i+1];
			}
		}
	}

	map<string,veci>::iterator it = m1.begin();
	while(it != m1.end())
	{
		if(m2.find(it->first) != m2.end())
		{
			(m2[it->first])[0] += (it->second)[0];
			(m2[it->first])[1] += (it->second)[1];
			/*
			if(m2[it->first].size() > 1)
			{
				if((m2[it->first])[1] > (it->second)[1])
				(m2[it->first])[1] = (it->second)[1];
			}
			*/
		}
		else
			m2[it->first] = it->second;
		it++;
	}


	map<string,veci>::iterator it2 = m2.begin();
	merge[0] = 0;
	while(it2 != m2.end())
	{
		sprintf(merge + strlen(merge), "%s",it2->first.c_str());
		for(int i = 0;i<(it2->second).size();i++)
		{
			sprintf(merge + strlen(merge), "%c%ld",fence,(it2->second)[i]);
		}
		sprintf(merge + strlen(merge), ";");
		it2++;
	}
	sprintf(merge + strlen(merge)-1, "%c",endfence);
	return 0;
}

int DCAns::MergeReaMsgActv(STBizMsg* bizMsg,const char * split,char *merge)
{
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	if(strlen(merge) == 0)
	{
		strcpy(merge,split);
		return 0;
	}
	if(strlen(split) == 0)
		return 0;

	map<string,veci> m1;
	int flag = 0;
	int head = 0;
	const char *phead = split;
	string keyname;
	char temp[32] = {0};
	for(int i=0;i<strlen(split);i++)
	{
		if(split[i] == ';')
		{
			head = i+1;
			phead = &split[i+1];
			continue;
		}
		if(split[i] != ',')
			continue;
		else
		{
			i++;
			for (;i < strlen(split);i++)
			{
				if (split[i] == fence)
					break;
			}
			memset(temp, 0, sizeof(temp));
			strncpy(temp, phead, i - head);
			keyname = temp;
			phead = &split[i + 1];
			head = i + 1;
			i++;
			for(;i<strlen(split);i++)
			{
				if(split[i] == ';' || i == strlen(split) - 1)
				{
					memset(temp, 0, sizeof(temp));
					strncpy(temp,phead,i+1-head);
					m1[keyname].push_back(atol(temp));
					head = i+1;
					phead = &split[i+1];
					break;
				}
				if(split[i] != fence)
					continue;
				memset(temp, 0, sizeof(temp));
				strncpy(temp,phead,i-head);
				m1[keyname].push_back(atol(temp));
				head = i+1;
				phead = &split[i+1];
			}
		}
	}

	map<string,veci> m2;
	flag = 0;
	head = 0;
	phead = merge;
	memset(temp,0,32);
	for(int i=0;i<strlen(merge);i++)
	{
		if(merge[i] == ';')
		{
			head = i+1;
			phead = &merge[i+1];
			continue;
		}
		if(merge[i] != ',')
			continue;
		else
		{
			i++;
			for (;i < strlen(merge);i++)
			{
				if (merge[i] == fence)
					break;
			}
			memset(temp, 0, sizeof(temp));
			strncpy(temp, phead, i - head);
			keyname = temp;
			phead = &merge[i + 1];
			head = i + 1;
			i++;
			for(;i < strlen(merge);i++)
			{
				if(merge[i] == ';' || i == strlen(merge) - 1)
				{
					memset(temp, 0, sizeof(temp));
					strncpy(temp,phead,i+1-head);
					m2[keyname].push_back(atol(temp));
					head = i+1;
					phead = &merge[i+1];
					break;
				}
				if(merge[i] != fence)
					continue;
				memset(temp, 0, sizeof(temp));
				strncpy(temp,phead,i-head);
				m2[keyname].push_back(atol(temp));
				head = i+1;
				phead = &merge[i+1];
			}
		}
	}

	map<string,veci>::iterator it = m1.begin();
	while(it != m1.end())
	{
		if(m2.find(it->first) != m2.end())
		{
			(m2[it->first])[0] += (it->second)[0];
			if((m2[it->first])[1] > (it->second)[1])
				(m2[it->first])[1] = (it->second)[1];

		}
		else
			m2[it->first] = it->second;
		it++;
	}


	map<string,veci>::iterator it2 = m2.begin();
	merge[0] = 0;
	while(it2 != m2.end())
	{
		sprintf(merge + strlen(merge), "%s",it2->first.c_str());
		for(int i = 0;i<(it2->second).size();i++)
		{
			sprintf(merge + strlen(merge), "%c%ld",fence,(it2->second)[i]);
		}
		sprintf(merge + strlen(merge), ";");
		it2++;
	}
	sprintf(merge + strlen(merge)-1, "%c",endfence);
	return 0;
}

int DCAns::MergeReaMsgPri(STBizMsg* bizMsg,const char * split,char *merge)
{
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	if(1 == commonPara->iPricePlanID)
	{
		if(strlen(merge) == 0)
		{
			strcpy(merge,split);
			return 0;
		}
		if(strlen(split) == 0)
			return 0;
		merge[strlen(merge)-1] = '#';
		sprintf(merge + strlen(merge), "%s",split);
	}
	return 0;
}
int DCAns::MergeReaMsgTrv(STBizMsg* bizMsg, const char * split, char *merge)
{

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char endfence = commonPara->CdrEndchar;

	if (strlen(merge) == 0)
	{
		strcpy(merge, split);
		return 0;
	}
	if (strlen(split) == 0)
		return 0;

	map<string, veci> m1;
	int flag = 0;
	int head = 0;
	const char *phead = split;
	string keyname;
	char temp[32] = { 0 };
	for (int i = 0;i<strlen(split);i++)
	{
		if (split[i] == ';')
		{
			head = i + 1;
			phead = &split[i + 1];
			continue;
		}
		if (split[i] != ':')
			continue;
		else
		{
			memset(temp, 0, sizeof(temp));
			strncpy(temp, phead, i+1 - head);
			keyname = temp;
			phead = &split[i + 1];
			head = i + 1;
			i++;
			for (;i<strlen(split);i++)
			{
				if (split[i] == ';' || i == strlen(split) - 1)
				{
					memset(temp, 0, sizeof(temp));
					strncpy(temp, phead, i + 1 - head);
					m1[keyname].push_back(atol(temp));
					head = i + 1;
					phead = &split[i + 1];
					break;
				}
			}
		}
	}

	map<string, veci> m2;
	flag = 0;
	head = 0;
	phead = merge;
	memset(temp, 0, 32);
	for (int i = 0;i<strlen(merge);i++)
	{
		if (merge[i] == ';')
		{
			head = i + 1;
			phead = &merge[i + 1];
			continue;
		}
		if (merge[i] != ':')
			continue;
		else
		{
			memset(temp, 0, sizeof(temp));
			strncpy(temp, phead, i+1 - head);
			keyname = temp;
			phead = &merge[i + 1];
			head = i + 1;
			i++;
			for (;i < strlen(merge);i++)
			{
				if (merge[i] == ';' || i == strlen(merge)-1)
				{
					memset(temp, 0, sizeof(temp));
					strncpy(temp, phead, i + 1 - head);
					m2[keyname].push_back(atol(temp));
					head = i + 1;
					phead = &merge[i + 1];
					break;
				}
			}
		}
	}

	map<string, veci>::iterator it = m1.begin();
	while (it != m1.end())
	{
		if (m2.find(it->first) != m2.end())
		{
			for (int i = 0;i<(it->second).size();i++)
			{
				(m2[it->first])[i] += (it->second)[i];
			}
		}
		else
			m2[it->first] = it->second;
		it++;
	}


	map<string, veci>::iterator it2 = m2.begin();
	merge[0] = 0;
	while (it2 != m2.end())
	{
		sprintf(merge + strlen(merge), "%s", it2->first.c_str());
		for (int i = 0;i<(it2->second).size();i++)
		{
			sprintf(merge + strlen(merge), "%ld", (it2->second)[i]);
		}
		sprintf(merge + strlen(merge), ";");
		it2++;
	}
	sprintf(merge + strlen(merge) - 1, "%c", endfence);
	return 0;
}
int DCAns::UpsetRatingMsg(STBizMsg* bizMsg)
{
	char szSessionId[256]={0};
	UDBSQL *pInsert = NULL;
	UDBSQL *pQuery  = NULL;
	UDBSQL *pUpdate = NULL;

	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pInsert = bizMsg->m_dbm->GetSQL(Voice_Insert_RatingMsg);
				pQuery = bizMsg->m_dbm->GetSQL(Voice_Query_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(Voice_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_sessionID);
			}
			break;
		case DATA:
		case CCG:
			{
				pInsert = bizMsg->m_dbm->GetSQL(DATA_Insert_RatingMsg);
				pQuery = bizMsg->m_dbm->GetSQL(DATA_Query_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(DATA_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_childsessionID);
			}
			break;
		case PGW:
			{
				pInsert = bizMsg->m_dbm->GetSQL(PGW_Insert_RatingMsg);
				pQuery = bizMsg->m_dbm->GetSQL(PGW_Query_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(PGW_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_childsessionID);
			}
			break;
		case DSL:
			{
				pInsert = bizMsg->m_dbm->GetSQL(DSL_Insert_RatingMsg);
				pQuery = bizMsg->m_dbm->GetSQL(DSL_Query_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(DSL_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_sessionID);
			}
			break;

		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "serviceType ERROR[%d]", bizMsg->m_serviceContextID);
				return -1;
			}
	}
	vector<string> vAccum;
	vector<string> vRata;
	vector<string> vSum;
	vector<long> v_key;
	v_key.push_back(0L);
	int type = 0;
	long key = 0;
	int i = 0;
	char buf[32] = {0};
	try
	{
		string str;
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, szSessionId);
		pQuery->Execute();
		while(pQuery->Next())
		{
			pQuery->GetValue(1,type);
			pQuery->GetValue(2,str);
			pQuery->GetValue(3,key);
			v_key.push_back(key);
			sprintf(buf,"%d:",++i);
			str = buf + str;
			if(type == 0)
				vAccum.push_back(str);
			else if(type == 1)
				vRata.push_back(str);
			else if(type == 2)
				vSum.push_back(str);
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "select execption[%s]", e.ToString());
		return -1;
	}
	DealAccunumlation(vAccum,bizMsg,v_key);
	DealRatableInfo(vRata,bizMsg,v_key);
	DealSumInfo(vSum,bizMsg,v_key);
	try
	{
		pInsert->Connection()->Commit();
		pUpdate->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		pInsert->Connection()->Rollback();
		pUpdate->Connection()->Rollback();
		return -1;
	}
	return 0;


}
void DCAns::DealAccunumlation(vector<string> &vAccum,STBizMsg* bizMsg,vector<long> &v_key)
{
	vector<std::string>::iterator iter;
	vector<string> vTmp;
	vector<vector<string> > v_OriAccum;
	const int accumSize = 32;
	ocs::RatingMessageInfo_t* ratingMsg = (ocs::RatingMessageInfo_t*)bizMsg->m_ratingMsg;
	int oriAccumSize = 0;
	for(iter = vAccum.begin() ; iter != vAccum.end(); iter++)
	{
		v_OriAccum.push_back(vTmp);
		ParserString(*iter,v_OriAccum[oriAccumSize],'^');
		v_OriAccum[oriAccumSize].resize(accumSize, "0");
		oriAccumSize++;
	}

	for(iter = ratingMsg->vAccunumlationInfo.begin(); iter != ratingMsg->vAccunumlationInfo.end(); iter++)
	{
		bool updateFlag = false;
		ParserString(*iter,vTmp,'^');
		int accumRealitySize = vTmp.size();
		vTmp.resize(accumSize, "0");
		
		long key = 0;
		for(vector<vector<string> >::iterator itv = v_OriAccum.begin(); itv != v_OriAccum.end(); itv++)
		{

			size_t keyPos = (*itv)[0].find(":");
			if(keyPos != string::npos)
			{
				key = atoi(itv->at(0).substr(0,keyPos).c_str());
				itv->at(0) = itv->at(0).substr(keyPos+1);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "error accum data[%s]", itv->at(0).c_str());
				continue;
			}
			//accu_id,UpateFlag,LatnId
			if(itv->at(0) == vTmp.at(0) && itv->at(2) == vTmp.at(2) && itv->at(26) == vTmp.at(26))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "merge accum data[%s]", itv->at(0).c_str());
				char buf[32] = {0};
				updateFlag = true;
				long value,value1;
				//lvalue,lUsageVoice,lUsageSms,lFluxUsedValue
				int addPos[] = {14,17,18,19};
				for(int i = 0; i < 4; i++)
				{
					value = atol(itv->at(addPos[i]).c_str());
					value += atol(vTmp.at(addPos[i]).c_str());
					sprintf(buf,"%ld",value);
					vTmp.at(addPos[i]) = buf;
				}

				//lTransValue
				value = atol(itv->at(15).c_str());
				value1 = atol(vTmp.at(15).c_str());
				if(vTmp.at(2) == "2")
					sprintf(buf,"%ld",value<value1?value:value1);//取最小值
				else
					sprintf(buf,"%ld",value1);//取新数据
				vTmp.at(15) = buf;

				//lTransUseValue
				value = atol(itv->at(16).c_str());
				value1 = atol(vTmp.at(16).c_str());
				if(vTmp.at(2) == "1")
					sprintf(buf,"%ld",value>value1?value:value1);//取最大值
				else if(vTmp.at(2) == "2")
					sprintf(buf,"%ld",value1);//取新数据
				vTmp.at(16) = buf; //其它取值时,取lTransValue
				break;
			}
		}

		string accumValue;
		accumValue = vTmp[0];
		for(int i = 1; i < accumRealitySize;i++)
		{
			accumValue += "^";
			accumValue += vTmp[i];
		}
			UpsetMsg(bizMsg,accumValue,v_key[key],updateFlag,0);
	}
	return;
}
void DCAns::DealRatableInfo(vector<string> &vRata,STBizMsg* bizMsg,vector<long> &v_key)
{
	vector<std::string>::iterator iter;
	vector<string> vTmp;
	vector<vector<string> > v_OriRata;
	const int rataSize = 32;
	ocs::RatingMessageInfo_t* ratingMsg = (ocs::RatingMessageInfo_t*)bizMsg->m_ratingMsg;
	int oriRataSize = 0;
	for(iter = vRata.begin();iter != vRata.end(); iter++)
	{
		v_OriRata.push_back(vTmp);
		ParserString(*iter,v_OriRata[oriRataSize],'^');
		v_OriRata[oriRataSize].resize(rataSize, "0");
		oriRataSize++;
	}

	for(iter = ratingMsg->vRatableInfo.begin(); iter != ratingMsg->vRatableInfo.end(); iter++)
	{
		bool updateFlag = false;
		ParserString(*iter,vTmp,'^');
		int rataRealitySize = vTmp.size();
		vTmp.resize(rataSize, "0");
		long key = 0;
		for(vector<vector<string> >::iterator itv = v_OriRata.begin(); itv != v_OriRata.end(); itv++)
		{

			size_t keyPos = (*itv)[0].find(":");
			if(keyPos != string::npos)
			{
				key = atoi(itv->at(0).substr(0,keyPos).c_str());
				itv->at(0) = itv->at(0).substr(keyPos+1);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "error rata data[%s]", itv->at(0).c_str());
				continue;
			}
			//accu_id,LatnId
			if(itv->at(0) == vTmp.at(0) && itv->at(29) == vTmp.at(29))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "merge rata data[%s]", itv->at(0).c_str());
				char buf[32] = {0};
				updateFlag = true;
				long value,value1;
				//lTransUseValue
				value = atol(itv->at(12).c_str());
				value1 = atol(vTmp.at(12).c_str());
				sprintf(buf,"%ld",value>value1?value:value1);//取最大值
				vTmp.at(12) = buf; //其它取值时,取lTransValue
				//lCurValue,lAfterValue,lUsageVoice,lUsageSms
				int addPos[] = {13,23,24,25};
				for(int i = 0; i < 4; i++)
				{
					value = atol(itv->at(addPos[i]).c_str());
					value += atol(vTmp.at(addPos[i]).c_str());
					sprintf(buf,"%ld",value);
					vTmp.at(addPos[i]) = buf;
				}
				break;
			}
		}

		string rataValue;
		rataValue = vTmp[0];
		for(int i = 1; i < rataRealitySize; i++)
		{
			rataValue += "^";
			rataValue += vTmp[i];
		}
			UpsetMsg(bizMsg,rataValue,v_key[key],updateFlag,1);
	}
	return;
}
void DCAns::DealSumInfo(vector<string> &vSum,STBizMsg* bizMsg,vector<long> &v_key)
{
	vector<std::string>::iterator iter;
	vector<string> vTmp;
	vector<vector<string> > v_OriSum;
    const int sumSize = 64;
	ocs::RatingMessageInfo_t* ratingMsg = (ocs::RatingMessageInfo_t*)bizMsg->m_ratingMsg;
	int oriSumSize = 0;
	for(iter = vSum.begin();iter != vSum.end(); iter++)
	{
		v_OriSum.push_back(vTmp);
		ParserString(*iter,v_OriSum[oriSumSize],'|');
		v_OriSum[oriSumSize].resize(sumSize, "0");
		oriSumSize++;
	}

	for(iter = ratingMsg->vSumInfo.begin(); iter != ratingMsg->vSumInfo.end(); iter++)
	{
		bool updateFlag = false;
		ParserString(*iter,vTmp,'|');
		int sumRealitySize = vTmp.size();
		vTmp.resize(sumSize, "0");
		long key = 0;
		for(vector<vector<string> >::iterator itv = v_OriSum.begin(); itv != v_OriSum.end(); itv++)
		{

			size_t keyPos = (*itv)[0].find(":");
			if(keyPos != string::npos)
			{
				key = atoi(itv->at(0).substr(0,keyPos).c_str());
				itv->at(0) = itv->at(0).substr(keyPos+1);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "error sum data[%s]", itv->at(0).c_str());
				continue;
			}
            //nServId,nAcctID,nAcctItemTypeId,nOfferId,nDay,nBelongDay,nBillingMode,nBelongBillingCycleId,cFeeType,cPayFlag,nModGroupId,nIfRealtiomeDisct,nSourceInstId,nPlatform
            int commPos[] = {0,1,2,3,9,10,11,12,13,14,20,21,23,24,26,28};
            bool commFlag = true;
            for(int i = 0; i < 16; i++)
            {
                if(itv->at(commPos[i]) != vTmp.at(commPos[i]))
                {
                    commFlag = false;
                    break;
                }
            }
            if(commFlag)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "merge sum data[%s]", itv->at(0).c_str());
                char buf[32] = {0};
                updateFlag = true;
                long value,value1;

                //lnDuration,lnBillingDuration,lnCounts,lnFlux,DisctCharge,OriCharge
                int addPos[] = {4,5,6,8,15,16};
                for(int i = 0; i < 6; i++)
                {
                    value = atol(itv->at(addPos[i]).c_str());
                    value += atol(vTmp.at(addPos[i]).c_str());
                    sprintf(buf,"%ld",value);
                    vTmp.at(addPos[i]) = buf;
                }
                break;
            }
		}

		string sumValue;
		sumValue = vTmp[0];
		for(int i = 1; i < sumRealitySize; i++)
		{
			sumValue += "|";
			sumValue += vTmp[i];
		}
        UpsetMsg(bizMsg,sumValue,v_key[key],updateFlag,2);

	}
	return;
}
void DCAns::UpsetMsg(STBizMsg*bizMsg,string &value,long key,bool updateFlag,int type)
{
	UDBSQL *pInsert = NULL;
	UDBSQL *pUpdate = NULL;
	char szSessionId[256]={0};
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pInsert = bizMsg->m_dbm->GetSQL(Voice_Insert_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(Voice_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_sessionID);
			}
			break;
		case DATA:
		case CCG:
			{
				pInsert = bizMsg->m_dbm->GetSQL(DATA_Insert_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(DATA_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_childsessionID);
			}
			break;
		case PGW:
			{
				pInsert = bizMsg->m_dbm->GetSQL(PGW_Insert_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(PGW_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_childsessionID);
			}
			break;
		case DSL:
			{
				pInsert = bizMsg->m_dbm->GetSQL(DSL_Insert_RatingMsg);
				pUpdate = bizMsg->m_dbm->GetSQL(DSL_Update_RatingMsg);
				strcpy(szSessionId, bizMsg->m_sessionID);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "serviceType ERROR[%d]", bizMsg->m_serviceContextID);
				return ;
			}
	}
	if(updateFlag)
	{
		try
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "update data[%s]", value.c_str());
			pUpdate->DivTable(bizMsg->m_sessionID);
			pUpdate->UnBindParam();
			pUpdate->BindParam(1, value);
			pUpdate->BindParam(2, key);
			pUpdate->BindParam(3, szSessionId);
			pUpdate->Execute();
		}
		catch(UDBException& e)
		{
			std::string sql;
			pUpdate->Connection()->Rollback();
			pUpdate->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "update execption[%s]", e.ToString());
			return;
		}
	}
	else
	{
		try
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "new data[%s]", value.c_str());
			pInsert->DivTable(bizMsg->m_sessionID);
			pInsert->UnBindParam();
			pInsert->BindParam(1, szSessionId);
			pInsert->BindParam(2, type);
			pInsert->BindParam(3, value);
			pInsert->Execute();
		}
		catch(UDBException& e)
		{
			std::string sql;
			pInsert->Connection()->Rollback();
			pInsert->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "insert execption[%s]", e.ToString());
			return;
		}
	}
	return;
}

void DCAns::ParserString(string str, vector<string> &vKey, char key)
{
	vKey.clear();
	size_t npos = str.find(key);
	size_t nposLast = 0;
	while(npos != string::npos)
	{
		vKey.push_back(str.substr(nposLast,npos-nposLast));
		nposLast = npos+1;
		npos = str.find(key,nposLast);
	}
	vKey.push_back(str.substr(nposLast));
}

int DCAns::SplitStr(const char *pszStr,const char cSplit,std::vector<int>& ltStr)
{
	long nValue;
	char *pch;
	char szBuf[1024];
	char szTmp[20];

	strncpy(szBuf,pszStr,sizeof(szBuf)-1);
      szBuf[sizeof(szBuf)-1] = 0;

	while(1)
	{
		pch = strchr(szBuf,cSplit);
		if( NULL == pch )
		{
			break;
		}

		memset(szTmp,0,sizeof(szTmp));
		strncpy(szTmp,szBuf,pch - szBuf);
		nValue = atoi(szTmp);
		ltStr.push_back(nValue);
		strcpy(szBuf,pch + 1);
	}

	nValue = atoi(szBuf);
	ltStr.push_back(nValue);	

	return 0;
}

int DCAns::SplitString(const char* pszStr, const char cSeparator, std::vector<std::string>& vecStr)
{
    if (!pszStr)
    {
        return 0;
    }

    string strField;
    strField.clear();
    for (const char* p = pszStr; *p; p++)
    {
        if ((*p) != cSeparator)
        {
            strField.push_back(*p);
            continue;
        }

        vecStr.push_back(strField);
        strField.clear();
    }

    vecStr.push_back(strField);

    return 0;
}
