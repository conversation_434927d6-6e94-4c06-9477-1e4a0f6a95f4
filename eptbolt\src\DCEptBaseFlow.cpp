#include "DCEptBaseFlow.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCRFData.h"
#include "DCAnsPara.h"
#include "DCParseXml.h"
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "DCOcpMsgDef.h"
#include "REMsgTypeDef.h"
#include "UHead.h"
#include "UStaMsg.h"

using namespace std;
using namespace ocs;

int DCEptBaseFlow::init()
{
	int ret  =0 ;
	BData* base = new DCAnsPara();
	ret= DCRFData::instance()->regist("DCAnsPara",base);
	if(ret)
	{
		return -1;
	}
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	//mq初始�?
    /*const char *brokers = DCParseXml::Instance()->GetParam("mqservAddr","Common/mq");

	m_producer = new DCMqProduceServer(MQ_ROCKET);
	ret = m_producer->Init(brokers,"eptBoltGroup");
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init producer failed!");
		return -1;
	}
	m_producer->Start();*/

	char sz_combinaSwitch[8] = {0};
	strncpy(sz_combinaSwitch, DCParseXml::Instance()->GetParam("combinaSwitch","SM"), sizeof(sz_combinaSwitch));
	m_combinaSwitch = atoi(sz_combinaSwitch);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get combinaSwitch [%d]", m_combinaSwitch);

  	m_pool = DCOBJSetPool::instance();
	m_pool->reg<STBizMsg>();
	m_pool->reg<SEPTMsg>();
	m_pool->reg<UHead>();

	return 0;
}

int DCEptBaseFlow::process(void* input, void* output)
{
	int ret = 0;
	int nCdr = 0;
	std::string &recvEptMsg =  *(std::string *)input;
	DCOBJSet* pset = m_pool->get();


	UHead* uhd = pset->get<UHead>();
	SEPTMsg* base = pset->get<SEPTMsg>();
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	bizMsg->m_dbm=(DCDBManer*)dbm();
	bizMsg->m_pSendMsg = (std::multimap<string,string>*)output ;
	bizMsg->m_anspara = m_anspara;
	bizMsg->m_base = base;

	vector<uint8_t> vectorMsg;
	vectorMsg = HexDecode(recvEptMsg.c_str(),recvEptMsg.size());

	try
    {
		m_de.set(vectorMsg);
		m_de.decode(*uhd);
		m_de.decode(*base);
		std::string skey = uhd->uid;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, base->trace);	// 设置号码跟踪
		m_print.clear();
		m_print.print(*uhd);
		m_print.print(*base);
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "sessionID[%s], uid[%s]", base->sessionID.c_str(), uhd->uid.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BASE[%s]", m_print.data());
    }
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_DECODE_CORE, "","decode failed");
              m_pool->put(pset);
		return ERR_DECODE_CORE;
	}

	bizMsg->m_uid = uhd->uid.c_str();
	bizMsg->m_iRollFlag = uhd->flag;
	if (uhd->checkKey.size() > 0)
	{
		DCEvtCheck::instance()->SetByCheckKey(uhd->checkKey);
		DCEvtCheck::instance()->Set_ID(DCEvtCheck::instance()->CreateUUID());
		DCEvtCheck::instance()->Set_P("SMEpt",uhd->uid);
		DCEvtCheck::instance()->Set_ID_T(DCEvtCheck::instance()->CreateTimeStamps());
		bizMsg->m_strCheckKey = DCEvtCheck::instance()->GetCheckKey();
	}

	ret = fmtEptMsg(base,bizMsg);
	if (bizMsg->m_operListId.empty())
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID), "", "operListId empty");
		DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID), "", "sessionId[%s], uid[%s]", base->sessionID.c_str(), uhd->uid.c_str());
		DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID), "", "BASE[%s]", m_print.data());
	}
	
	if(bizMsg->m_sBatchId.length() == 0)
	{
		if (bizMsg->m_requestType == SM_SESSION_XDR_CODE)
		{
			bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID,bizMsg->m_anspara->GetCommonPara()->iBatchIdTime, 2);
		}
		else
		{
			bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID,bizMsg->m_anspara->GetCommonPara()->iBatchIdTime, 1);
		}
	}
	
	string sEptType;
	switch(bizMsg->m_eptType)
	{
		case 1:
			ret = call_one("FC_EPTREQ", pset, pset);
			sEptType = "REQ";
			break;
		case 2:
			ret = call_one("FC_EPTANS", pset, pset);
			sEptType = "ANS";
			break;
		case 3:
			ret = call_one("FC_EPTTIMEOUTSESSION", pset, pset);
			sEptType = "SESIONFREE";
			break;
		case 4:
			ret = call_one("FC_EPTTIMEOUTREA", pset, pset);
			sEptType = "TIMEOUTREA";
			break;
		case 5:
			ret = RET_CDR;
			break;
		default:
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","unknow eptType");
			m_pool->put(pset);
			return -1;
	}

	if (RET_CDR == ret)
	{
		nCdr = RET_CDR;
		ret = call_one("FC_CDRNORMAL", pset, pset);
	}

	composeStaMsg(bizMsg, nCdr);

	if(bizMsg->m_eptType == 1 || bizMsg->m_eptType == 2)
	{
		bizMsg->m_pSendMsg->insert(pair<string,string>("TaskId",bizMsg->m_taskId));
		bizMsg->m_pSendMsg->insert(pair<string,string>("AnsTopic",bizMsg->m_anstopic));
	}
	bizMsg->m_pSendMsg->insert(pair<string,string>("UID",bizMsg->m_uid));
	for(int i = 0; i < bizMsg->m_vectorMsg.size(); i++)
	{
		bizMsg->m_pSendMsg->insert(pair<string,string>("RBRMSG",bizMsg->m_vectorMsg[i]));
	}

   //info 日志
	DCDATLOG("SM00015:%s%d", sEptType.c_str(), base->result);
	DCDATLOG();
	m_pool->put(pset);
	return ret;
}
//
int DCEptBaseFlow::fmtEptMsg(SEPTMsg *base,STBizMsg *bizMsg)
{
	bizMsg->m_requestNumber = base->requestnumber;
	bizMsg->m_resultcode = base->result;
	bizMsg->m_serial = base->serial;
	bizMsg->m_requestType = base->sreq;
	bizMsg->timestamps = base->stamp;
	bizMsg->m_trace_flag = base->trace;
	bizMsg->m_eptType = base->type%10;
	bizMsg->m_serviceContextID = base->servicecontextid;
	DCAnsPara* anspara= (DCAnsPara *)bizMsg->m_anspara;
	if (base->version == 1)
	{
		sprintf(bizMsg->m_sessionID,"XDR%s",base->sessionID.c_str());
	}
	else
	{
		strcpy(bizMsg->m_sessionID,base->sessionID.c_str());
	}
	bizMsg->m_testFlag = base->type/10;
	bizMsg->m_RARFlag = base->ASRFlag;//此处仅仅保存base中的ASRFlag，真正的值在timeoutsession中解�?
	bizMsg->m_topology = base->topology.c_str();
	bizMsg->m_version = base->version;
	bizMsg->m_spiltflag = base->spiltflag;
	bizMsg->m_offline = atoi(base->EptExt["OnlineMod5g"].c_str());
	strcpy(bizMsg->m_szServiceContextIDStr,base->szServiceContextIDStr.c_str());
	if (strncmp(bizMsg->m_szServiceContextIDStr, "test", 4) == 0)
	{
		bizMsg->m_testFlag = 1;
	}
	strcpy(bizMsg->m_childsessionID, bizMsg->m_sessionID);
	bizMsg->m_sBatchId = base->EptExt["batchId"];
	bizMsg->m_anstopic = base->anstopic.c_str();

	std::map<string,string>::iterator iter;
	iter = base->EptExt.find("operListId");
	if (iter != base->EptExt.end())
	{
		bizMsg->m_operListId = iter->second;
	}
	
	iter = base->EptExt.find("batchId");
	if (iter != base->EptExt.end())
	{
		bizMsg->m_sBatchId = iter->second;
	}
	
	iter = base->EptExt.find("sourceId");
	if(iter != base->EptExt.end())
	{
		bizMsg->m_sourceId = iter->second;
	}

	iter = base->EptExt.find("switchId");
	if (iter != base->EptExt.end())
	{
		bizMsg->m_switchId = iter->second;
	}

	iter = base->EptExt.find("LatnId");
	if(iter != base->EptExt.end())
	{
		bizMsg->m_ilatnId = atoi(iter->second.c_str());
	}

	char stmp[6] = {0};
	sprintf(stmp, "%d", bizMsg->m_ilatnId);
	bizMsg->m_pSendMsg->insert(pair<string,string>("LatnId",stmp));

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sourceId[%s], switchId[%s], latnId[%d]", 
		bizMsg->m_sourceId.c_str(), bizMsg->m_switchId.c_str(), bizMsg->m_ilatnId);

	if(bizMsg->m_eptType == 4)
	{
		std::multimap<string,string>::iterator iter = bizMsg->m_pSendMsg->find("TaskId");
		if(iter != bizMsg->m_pSendMsg->end())
		{
			strcpy(bizMsg->m_taskId,iter->second.c_str());
		}
	}
	else
	{
		strcpy(bizMsg->m_taskId,base->taskId.c_str());
	}

	if (20 == bizMsg->m_requestType)  // sessfree 超时长截单
	{
		for(int i = strlen(bizMsg->m_sessionID)-1; i>=0; i--)
		{
			if(bizMsg->m_sessionID[i] == ';')
			{
				bizMsg->m_sessionID[i] = '\0';
				break;
			}
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionID[%s]", bizMsg->m_sessionID);
		bizMsg->m_longCDR = 4;
		return RET_CDR;
	}

	if (2 == bizMsg->m_eptType)
	{
		switch(bizMsg->m_serviceContextID)
		{
			case RE_SERVICE_TYPE_INT_PGW_ANS:
				bizMsg->m_serviceContextID = PGW;
				break;
			case RE_SERVICE_TYPE_INT_DSL_ANS:
				bizMsg->m_serviceContextID = DSL;
				break;
			case RE_SERVICE_TYPE_INT_VOICE_ANS:
			case RE_SERVICE_TYPE_INT_BALANCE_ANS:
				bizMsg->m_serviceContextID = VOICE;
				break;
			case RE_SERVICE_TYPE_INT_DATA_ANS:
				bizMsg->m_serviceContextID = DATA;
				break;
			case RE_SERVICE_TYPE_INT_SMS_ANS:
				bizMsg->m_serviceContextID = SMS;
				break;
			case RE_SERVICE_TYPE_INT_ISMP_ANS:
			case RE_SERVICE_TYPE_INT_BALANCE_ANS_ISMP:
				bizMsg->m_serviceContextID = ISMP;
				break;
			case RE_SERVICE_TYPE_INT_5G_ANS:
				bizMsg->m_serviceContextID = DATA_5G;
				break;
		}
	}
	else
	{
		bizMsg->m_serviceContextID = base->servicecontextid;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, bizMsg->m_sessionID, "ServType[%d]", bizMsg->m_serviceContextID);
	if (2 == bizMsg->m_eptType && SM_SESSION_INITIAL_CODE == bizMsg->m_requestType 
	&& ((DATA==bizMsg->m_serviceContextID) || (CCG == bizMsg->m_serviceContextID) 
	 || (PGW == bizMsg->m_serviceContextID) || (DATA_5G == bizMsg->m_serviceContextID)))
	{
		bizMsg->m_requestType = SM_SESSION_UPDATE_FIRST_CODE;
	}

	if (bizMsg->m_eptType !=3 && bizMsg->m_eptType != 4  && bizMsg->m_requestType != SM_SESSION_INITIAL_CODE
	&& ((DATA==bizMsg->m_serviceContextID) || (CCG == bizMsg->m_serviceContextID) 
	 || (PGW == bizMsg->m_serviceContextID) || (DATA_5G == bizMsg->m_serviceContextID)))
	{
        char* p = strrchr(bizMsg->m_sessionID, ';');
        if (p)
        {
        	bizMsg->m_ratingGroup = atoll(p+1);
        	*p = 0x0;
        }
	}

    if (bizMsg->m_spiltflag >1 && ((VOICE==bizMsg->m_serviceContextID) || (DSL == bizMsg->m_serviceContextID)))
    {
        sprintf(bizMsg->m_childsessionID, "%s;00%d",bizMsg->m_sessionID, bizMsg->m_spiltflag);
    }

	return 0;
}

int DCEptBaseFlow::composeStaMsg(STBizMsg* bizMsg, int nRet)
{

	char value[256] = {0};
	ocs::SComHead pSComhead;
	ocs::StatRecord pStatRecord;
	ocs::RatingMessageInfo_t bodySM;
	bool checkSum = false;

	if(!bizMsg->m_anspara)
	{
		bizMsg->m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	}

	pSComhead.AckEndBoltFlag = 0;
	pSComhead.collectid = 0;
	pSComhead.latnid = bizMsg->m_ilatnId;
	pSComhead.operlistid = atoi(bizMsg->m_operListId.c_str());
	pSComhead.switchid = atoi(bizMsg->m_operListId.c_str());
	pSComhead.opertype = bizMsg->m_szServiceContextIDStr;
	pSComhead.sourceid = atol(bizMsg->m_sourceId.c_str());
	pSComhead.recordid = 0;
	pSComhead.sourcefile = "";
	pSComhead.m_sProcerName = "B";
	pSComhead.msgtype = "idx";	  // SM 正常清单
	pSComhead.epttype = 0;
	pSComhead.resultcode = bizMsg->m_resultcode;
	pSComhead.BatchNo = bizMsg->m_sBatchId;
	pSComhead.uid = bizMsg->m_uid;
	pSComhead.trace = bizMsg->m_trace_flag;
	pSComhead.modid = 0;
	pSComhead.ip = "";
	if(bizMsg->m_requestType == SM_SESSION_EVENT_CODE || bizMsg->m_requestType == SM_SESSION_XDR_CODE)
		pSComhead.ext["IMPORT_P"] = "File";
	else
		pSComhead.ext["IMPORT_P"] = "Msg";

	if(bizMsg->m_offline == 1 && bizMsg->m_serviceContextID == DATA_5G)
		pSComhead.ext["OnlineMod5g"] = "1";
	else if(bizMsg->m_serviceContextID == DATA_5G)
		pSComhead.ext["OnlineMod5g"] = "0";
	else
		pSComhead.ext["OnlineMod5g"] = "2";

	pSComhead.checkKey = bizMsg->m_strCheckKey;
	pStatRecord.m_iNormalRecords = 0;
	pStatRecord.m_iInvalidRecords = 0;
	pStatRecord.m_iAbNormalRecords = 0;
	pStatRecord.m_iNoUserRecords = 0;
	pStatRecord.m_iDualRecords = 0;
	pStatRecord.m_iTotalRecords = 0;

	if(bizMsg->m_serviceContextID == VOICE && bizMsg->m_anspara->GetINPara()->volteCdrFalg == 1 && bizMsg->m_volteFlag == 1)
	{
		pSComhead.opertype += ".volte";
	}

	m_print.clear();
	m_print.print(pSComhead);
	m_print.print(pStatRecord);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LOGRATING:%s", m_print.data());

	m_en.clear();
	m_en.encode(pSComhead);
	m_en.encode(pStatRecord);

	string strMsg = HexEncode(m_en.data(),m_en.size());
	//头部加上固定16位时间戳
	char buf[30]={0};
	struct timeval tmv;
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	strMsg.insert(0,buf,16);
	bizMsg->m_pSendMsg->insert(pair<string,string>("LOGRATING",strMsg));
	int chgnum = 0;

	if(nRet == RET_CDR)
	{
		std::multimap<string,string>::iterator itBeg;
		std::multimap<string,string>::iterator itEnd;
		std::multimap<string,string>::iterator iter;

		if(bizMsg->m_testFlag == 0)
		{
			pSComhead.msgtype = "SM";	  // SM 正常清单
			if(DATA_5G == bizMsg->m_serviceContextID)
			{
				pSComhead.msgtype = "SM_5G";
			}

            if(m_combinaSwitch == 1)
                checkSum = checkSumFile(pSComhead,bizMsg, bodySM);

            if(!checkSum)
            {
                itBeg = bizMsg->m_pSendMsg->lower_bound("SUMINFO");
                itEnd = bizMsg->m_pSendMsg->upper_bound("SUMINFO");
                for(iter = itBeg; iter != itEnd; ++iter)
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SUMINFO MSG:%s", iter->second.c_str());
                    bodySM.vSumInfo.push_back(iter->second);
                }

                itBeg = bizMsg->m_pSendMsg->lower_bound("PAYFLAGTOPIC");
                itEnd = bizMsg->m_pSendMsg->upper_bound("PAYFLAGTOPIC");
				if (bizMsg->m_iRollFlag == 1)  // 回退清单
				{
					for(iter = itBeg; iter != itEnd; ++iter)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC roll MSG:%s", iter->second.c_str());
						bodySM.vRollCHGMessage.push_back(iter->second);
					}
					chgnum = bodySM.vRollCHGMessage.size();
				}
				else
				{
					for(iter = itBeg; iter != itEnd; ++iter)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC MSG:%s", iter->second.c_str());
						bodySM.vCHGmessage.push_back(iter->second);
					}
					chgnum = bodySM.vCHGmessage.size();
				}

                itBeg = bizMsg->m_pSendMsg->lower_bound("ACCUMULATION");
                itEnd = bizMsg->m_pSendMsg->upper_bound("ACCUMULATION");
                for(iter = itBeg; iter != itEnd; ++iter)
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ACCUMULATION MSG:%s", iter->second.c_str());
                    bodySM.vAccunumlationInfo.push_back(iter->second);
                }

                itBeg = bizMsg->m_pSendMsg->lower_bound("RATABLEINFO");
                itEnd = bizMsg->m_pSendMsg->upper_bound("RATABLEINFO");
                for(iter = itBeg; iter != itEnd; ++iter)
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RATABLEINFO MSG:%s", iter->second.c_str());
                    bodySM.vRatableInfo.push_back(iter->second);
                }
                DCDATLOG("SM00016:%d!%d!%d!%d",chgnum,bodySM.vSumInfo.size(),bodySM.vAccunumlationInfo.size(),bodySM.vRatableInfo.size());
            }
		}
		else
		{
			pSComhead.msgtype = "SMTest";	  // SMTest 模拟拨测清单
			itBeg = bizMsg->m_pSendMsg->lower_bound("TESTCDRTOPIC");
			itEnd = bizMsg->m_pSendMsg->upper_bound("TESTCDRTOPIC");
			for(iter = itBeg; iter != itEnd; ++iter)
			{
				bodySM.vCHGmessage.push_back(iter->second);
			}
		}

		m_en.clear();
		m_en.encode(pSComhead);
		m_en.encode(bodySM);

		string cdrMsg = HexEncode(m_en.data(),m_en.size());
		cdrMsg.insert(0,buf,16);

		bizMsg->m_pSendMsg->insert(pair<string,string>("CDRMSG",cdrMsg));

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CDRMSG:%s", cdrMsg.c_str());
	}
	return 0;
}

bool DCEptBaseFlow::checkSumFile(ocs::SComHead& pSComhead,STBizMsg* bizMsg,ocs::RatingMessageInfo_t& bodySM)
{
    int realtime = 0;
    ocs::CombineRecord bodySMRecord;
    std::vector<std::string> vfileInfo;
    std::multimap<string,string>::iterator itBeg;
    std::multimap<string,string>::iterator itEnd;
    std::multimap<string,string>::iterator iter;
    iter = bizMsg->m_pSendMsg->find("SUMINFO");

    if(iter != bizMsg->m_pSendMsg->end())
    {
        //判断n_if_realtime_disct是否为1
        vfileInfo.clear();
        DCCommonIF::SplitString(iter->second, '|', vfileInfo);
        realtime = atoi(vfileInfo[21].c_str());
        if(realtime == 1)
        {
            itBeg = bizMsg->m_pSendMsg->lower_bound("SUMINFO");
            itEnd = bizMsg->m_pSendMsg->upper_bound("SUMINFO");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                //sum文件出两次,组合一次，单独一次
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SUMINFO MSG:%s", iter->second.c_str());
                bodySM.vSumInfo.push_back(iter->second);
                bodySMRecord.vSumInfo.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("PAYFLAGTOPIC");
            itEnd = bizMsg->m_pSendMsg->upper_bound("PAYFLAGTOPIC");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC MSG:%s", iter->second.c_str());
                bodySMRecord.vCHGmessage.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("ACCUMULATION");
            itEnd = bizMsg->m_pSendMsg->upper_bound("ACCUMULATION");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ACCUMULATION MSG:%s", iter->second.c_str());
                bodySMRecord.vAccunumlationInfo.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("RATABLEINFO");
            itEnd = bizMsg->m_pSendMsg->upper_bound("RATABLEINFO");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RATABLEINFO MSG:%s", iter->second.c_str());
                bodySMRecord.vRatableInfo.push_back(iter->second);
            }
            DCDATLOG("SM00016:%d!%d!%d!%d",bodySMRecord.vCHGmessage.size(),bodySMRecord.vSumInfo.size(),bodySMRecord.vAccunumlationInfo.size(),bodySMRecord.vRatableInfo.size());
            //编码
            m_en.clear();
			m_en.encode(pSComhead);
            m_en.encode(bodySMRecord);
            bodySM.m_sCombineMsg = HexEncode(m_en.data(),m_en.size());
        }
        else
            return false;
    }
    else
        return false;

    return true;
}


DYN_PLUGIN_CREATE(DCEptBaseFlow, "FL_EPTBASEFLOW", "FL_EptBaseFlow", "1.0.0")

