#include <stdio.h>
#include <string.h>

#include "tinyxml.h"
#include "TConfig.h"

TConfig* TConfig::m_instance = NULL;
TConfig* TConfig::instance()
{
	if (NULL == m_instance)
	{
		m_instance = new TConfig();
	}

	return m_instance;
}

int TConfig::load(const char* cfg, char* error)
{
	strcpy(cfgpath, cfg);
	cfgpath[strlen(cfg)]='\0';
	TiXmlDocument xml(cfg);
	if(!xml.LoadFile())
	{
		return -1;
	}

	TiXmlHandle handle(&xml);
	TiXmlElement* serv = NULL;
	TiXmlElement* cdr = NULL;
	TiXmlElement* elem = NULL;
	TiXmlElement* root = xml.FirstChildElement(SM_CFG_ROOT_ELEM);
	serv = root->FirstChildElement(SM_CFG_SERV_ELEM);
	if(serv)
	{
		//host
		elem = serv->FirstChildElement(SM_CFG_SERV_HOST);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_SERV_HOST_NAME))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_SERV_HOST_NAME);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->serv.host, elem->Attribute(SM_CFG_SERV_HOST_NAME));
			}

		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_SERV_HOST);
			return -1;
		}

		//mq
		elem = serv->FirstChildElement(SM_CFG_SERV_MQ);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_SERV_MQ_ADDRESS))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_SERV_MQ_ADDRESS);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->serv.mqaddr, elem->Attribute(SM_CFG_SERV_MQ_ADDRESS));
			}
			
		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_SERV_MQ);
			return -1;
		}

		//log
		elem = serv->FirstChildElement(SM_CFG_SERV_LOG);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_SERV_LOG_PATH))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_SERV_LOG_PATH);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->serv.log.path, elem->Attribute(SM_CFG_SERV_LOG_PATH));
				if ('/' == m_pSMConfig->serv.log.path[strlen(m_pSMConfig->serv.log.path)-1])
				{
					m_pSMConfig->serv.log.path[strlen(m_pSMConfig->serv.log.path)-1] = '\0';
				}
			}

			if(NULL == elem->Attribute(SM_CFG_SERV_LOG_LEVEL))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_SERV_LOG_LEVEL);
				return -1;
			}
			else
			{
				m_pSMConfig->serv.log.level = atoi(elem->Attribute(SM_CFG_SERV_LOG_LEVEL));
			}
		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_SERV_LOG);
			return -1;
		}

	}
    
	cdr = root->FirstChildElement(SM_CFG_CDR_ELEM);
	if (cdr)
	{
		//host
		elem = cdr->FirstChildElement(SM_CFG_CDR_HOST);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_CDR_HOST_NAME))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_HOST_NAME);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->cdr.host, elem->Attribute(SM_CFG_CDR_HOST_NAME));
			}
		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_CDR_HOST);
			return -1;
		}

		//path
		elem = cdr->FirstChildElement(SM_CFG_CDR_PATH);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_CDR_PATH_BASE))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_PATH_BASE);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->cdr.path.base, elem->Attribute(SM_CFG_CDR_PATH_BASE));
				if ('/' == m_pSMConfig->cdr.path.base[strlen(m_pSMConfig->cdr.path.base)-1])
				{
					m_pSMConfig->cdr.path.base[strlen(m_pSMConfig->cdr.path.base)-1] = '\0';
				}
			}

			if(NULL == elem->Attribute(SM_CFG_CDR_PATH_CURRENT))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_PATH_CURRENT);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->cdr.path.cur, elem->Attribute(SM_CFG_CDR_PATH_CURRENT));
			}

			if(NULL == elem->Attribute(SM_CFG_CDR_PATH_BACKUP))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_PATH_BACKUP);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->cdr.path.bak, elem->Attribute(SM_CFG_CDR_PATH_BACKUP));
			}

			if(NULL == elem->Attribute(SM_CFG_CDR_PATH_SAVE))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_PATH_SAVE);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->cdr.path.sav, elem->Attribute(SM_CFG_CDR_PATH_SAVE));
			}

			if(NULL == elem->Attribute(SM_CFG_CDR_PATH_2HB))
			{
				sprintf(error, "get [%s] failed, ignored\n", SM_CFG_CDR_PATH_2HB);
				//m_pSMConfig->cdr.path.toHB[0] = '\0';
			}
			else
			{
				//strcpy(m_pSMConfig->cdr.path.toHB, elem->Attribute(SM_CFG_CDR_PATH_2HB));
			}

		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_CDR_PATH);
			return -1;
		}

		//log
		elem = cdr->FirstChildElement(SM_CFG_SERV_LOG);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_CDR_LOG_PATH))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_LOG_PATH);
				return -1;
			}
			else
			{
				strcpy(m_pSMConfig->cdr.log.path, elem->Attribute(SM_CFG_CDR_LOG_PATH));
				if ('/' == m_pSMConfig->cdr.log.path[strlen(m_pSMConfig->cdr.log.path)-1])
				{
					m_pSMConfig->cdr.log.path[strlen(m_pSMConfig->cdr.log.path)-1] = '\0';
				}
			}

			if(NULL == elem->Attribute(SM_CFG_CDR_LOG_LEVEL))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_LOG_LEVEL);
				return -1;
			}
			else
			{
				m_pSMConfig->cdr.log.level = atoi(elem->Attribute(SM_CFG_CDR_LOG_LEVEL));
			}

		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_SERV_LOG);
			return -1;
		}

		//switch
		elem = cdr->FirstChildElement(SM_CFG_CDR_END);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_CDR_END_SIZE))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_END_SIZE);
				return -1;
			}
			else
			{
				m_pSMConfig->cdr.exchange.size = atoi(elem->Attribute(SM_CFG_CDR_END_SIZE));
			}

			if(NULL == elem->Attribute(SM_CFG_CDR_END_TIME))
			{
				sprintf(error, "get [%s] failed\n", SM_CFG_CDR_END_TIME);
				return -1;
			}
			else
			{
				m_pSMConfig->cdr.exchange.time = atoi(elem->Attribute(SM_CFG_CDR_END_TIME));
			}
		}
		else
		{
			sprintf(error, "get [%s] failed\n", SM_CFG_CDR_END);
			return -1;
		}
		
	}

	return 0;
}

char *TConfig::getCfgPath()
{
	return cfgpath;
}

int TConfig::loadLog()
{
	TiXmlDocument xml(cfgpath);
	if(!xml.LoadFile())
	{
		return -1;
	}

	TiXmlHandle handle(&xml);
	TiXmlElement* serv = NULL;
	TiXmlElement* elem = NULL;
	TiXmlElement* root = xml.FirstChildElement(SM_CFG_ROOT_ELEM);
	serv = root->FirstChildElement(SM_CFG_SERV_ELEM);
	if(serv)
	{	//log
		elem = serv->FirstChildElement(SM_CFG_SERV_LOG);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_SERV_LOG_LEVEL))
			{
				printf("get [%s] failed\n", SM_CFG_SERV_LOG_LEVEL);
			}
			else
			{
				m_pSMConfig->serv.log.level = atoi(elem->Attribute(SM_CFG_SERV_LOG_LEVEL));
			}

		}

	}

	return 0;
}



int TConfig::loadcdrlog()
{
	TiXmlDocument xml(cfgpath);
	if(!xml.LoadFile())
	{
		return -1;
	}

	TiXmlHandle handle(&xml);
	TiXmlElement* cdr = NULL;
	TiXmlElement* elem = NULL;
	TiXmlElement* root = xml.FirstChildElement(SM_CFG_ROOT_ELEM);
	cdr = root->FirstChildElement(SM_CFG_CDR_ELEM);
	if(cdr)
	{
		elem = cdr->FirstChildElement(SM_CFG_SERV_LOG);
		if (elem)
		{
			if(NULL == elem->Attribute(SM_CFG_CDR_LOG_LEVEL))
			{
				return -1;
			}
			else
			{
				m_pSMConfig->cdr.log.level = atoi(elem->Attribute(SM_CFG_CDR_LOG_LEVEL));
			}
		}
		else
		{
			return -1;
		}
	}
	return 0;
}

int TConfig::getPerfLog()
{
	//return m_pSMConfig->serv.log.perfLogOnOff;
	return 0;
}


int TConfig::GetPerfUsec()
{
	//return m_pSMConfig->serv.log.nPerfLogUsec;
	return 0;
}



int TConfig::destroy()
{
	delete this;
	return 0;
}

const SSMConfig*  TConfig::config()
{
	return m_pSMConfig;
}


TConfig::TConfig():m_pSMConfig(NULL)
{
	m_pSMConfig = new SSMConfig;
	memset(m_pSMConfig, 0, sizeof(SSMConfig));
}

TConfig::~TConfig()
{

}

