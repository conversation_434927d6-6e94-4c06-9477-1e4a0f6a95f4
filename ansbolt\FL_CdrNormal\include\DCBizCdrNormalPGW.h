/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdrNormalPGW.h
*Indentifier：
*		
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef _DCBIZ_CDR_NORMAL_PGW_H_
#define _DCBIZ_CDR_NORMAL_PGW_H_

#include "DCBizCdrNormal.h"
#include "TCDRDict.h"


class DCBizCdrNormalPGW:public DCBizCdrNormal
{
	public:

		DCBizCdrNormalPGW();
		~DCBizCdrNormalPGW();
		
	protected:
		
		
		virtual int ComposePGW(STBizMsg* bizMsg);

		int PretreatPGWRGCut(STBizMsg* bizMsg,int longtype);
		int PretreatPGWRGLeft(STBizMsg* bizMsg);

		int PutCdr_PGW(STBizMsg* bizMsg);

		int updateLocInfo(STBizMsg* bizMsg,char *sessinID,char *startTime, char* szNewLocInfo);
		int updateCdrInfo(STBizMsg* bizMsg,char *sessinID);

		int SelectPretreatColumn(STBizMsg* bizMsg);
		
		int SelectPretreatColumnAAA(STBizMsg* bizMsg);
		int SplitString(const char * str, const char del, std::vector<std::string> & vec);
		int ComposePGWCDR(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,char* rg,SCDRData &cdr);
	
};

#endif


