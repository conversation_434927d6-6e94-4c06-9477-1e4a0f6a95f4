/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		BizLenDef.h
*Indentifier：
*
*Description：
*		字符串长度定义
*Version：
*		V1.0
*Author:
*		YF.Du
		ST.J
*Finished：
*		2009年8月20日
*History:
********************************************/
#ifndef __BIZ_LEN_DEF_H__
#define __BIZ_LEN_DEF_H__

#define BIZ_TEMP_LEN_2048	2048
#define BIZ_TEMP_LEN_1024	1024
#define BIZ_TEMP_LEN_512	512
#define BIZ_TEMP_LEN_256	256
#define BIZ_TEMP_LEN_128 	128
#define BIZ_TEMP_LEN_64 	64
#define BIZ_TEMP_LEN_32 	32
#define BIZ_TEMP_LEN_16 	16
#define BIZ_TEMP_LEN_4 		4

#define BIZ_DATA_LEN_4096   4096
#define BIZ_DATA_LEN_1024	1024
#define BIZ_DATA_LEN_512	512
#define BIZ_DATA_LEN_256	256
#define BIZ_DATA_LEN_128 	128
#define BIZ_DATA_LEN_32 	32
#define BIZ_DATA_LEN_24		24
#define BIZ_DATA_LEN_8 		8
#define BIZ_DATA_LEN_4 		4
#endif
