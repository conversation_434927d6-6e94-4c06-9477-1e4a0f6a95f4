#include "DCSMPara.h"
#include "DCDBManer.h"
#include "DCLogMacro.h"

#include <stdlib.h>
#include <string.h>

//系统参数项
struct SSystemPara
{
	char 	PARA_GROUP[64];
	char 	PARA_KEY[64];
	char 	PARA_VALUE[256];
};


DCSMPara::DCSMPara()
{
	m_dbm = NULL;
}

DCSMPara::~DCSMPara()
{
}


int DCSMPara::init(DCDBManer* dbm)
{
	m_dbm = dbm;
	if(m_dbm->GetSQL("q_sm_system_parameter") == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_sm_system_parameter sql");
		return -1;
	}
	return 0;
}

int DCSMPara::work(EBDIDX idx)
{
	return LoadComPara(idx);
}

int DCSMPara::LoadComPara(EBDIDX idx)
{
	DCParaCom* para = &m_para[idx];
	SSystemPara systemPara;

    if(LoadSystemPara("CM.COMMON", "FREE_ADD_CDR", &systemPara) != 0){
		para->nFreeAddCdr = 0;
	}
    else{
		para->nFreeAddCdr = atoi(systemPara.PARA_VALUE);
    }

	if(LoadSystemPara("SM.PS.CONFIG", "PS_LONG_CDR_TIME", &systemPara) != 0){
		para->longCDRTime = 0;
	}
    else{
		para->longCDRTime = atoi(systemPara.PARA_VALUE);
    }

    if(LoadSystemPara("CM.COMMON", "RELEASE_SESSION_FLAG", &systemPara) != 0){
		para->nReleaseFlag = 0;
	}
    else{
		para->nReleaseFlag = atoi(systemPara.PARA_VALUE);
    }
	
	if(LoadSystemPara("CM.COMMON", "ASR_MAX_SECOND", &systemPara) != 0){
		para->nAmountLimit = 0;
	}
    else{
		para->nAmountLimit = atoi(systemPara.PARA_VALUE);
    }

	if(LoadSystemPara("CM.COMMON", "NOTIFY_SWITCH", &systemPara) != 0){
		para->iNotifyFlag = 0;
	}
	else{
		para->iNotifyFlag = atoi(systemPara.PARA_VALUE);
	}

	if(LoadSystemPara("CM.COMMON", "FAST_RELEASE_FLAG", &systemPara) != 0){
		para->iFastReleaseFlag = 0;
	}
	else{
		para->iFastReleaseFlag = atoi(systemPara.PARA_VALUE);
	}

	if(LoadSystemPara("CM.COMMON", "CHECK_LATNID_FLAG", &systemPara) != 0){
		para->iCheckLatnId = 0;
	}
	else{
		para->iCheckLatnId = atoi(systemPara.PARA_VALUE);
	}

	return 0;
}

int DCSMPara::LoadSystemPara(const char *paraGroup, const char *paraKey, SSystemPara *systemPara)
{
	UDBSQL* query = m_dbm->GetSQL("q_sm_system_parameter");
	int ret = 0;
	strcpy(systemPara->PARA_GROUP, paraGroup);
	strcpy(systemPara->PARA_KEY, paraKey);
	systemPara->PARA_VALUE[0] = 0x0;

	try
	{
		query->UnBindParam();
		query->BindParam(1, paraGroup);
		query->BindParam(2, paraKey);
		query->Execute();
      	if(query->Next())
      	{
			query->GetValue(1, systemPara->PARA_VALUE);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "load system parameter :group[%s], key[%s], value[%s]", paraGroup, paraKey, systemPara->PARA_VALUE);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "load system parameter nothing:group[%s], key[%s]", paraGroup, paraKey);
			ret = -2;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "dbexp: [%s]", e.ToString());
		ret = -1;
	}
	return ret;
}

void* DCSMPara::data(EBDIDX idx)
{
	return &m_para[idx];
}

void DCSMPara::clear(EBDIDX idx)
{
	m_para[idx].nFreeAddCdr = 0;
	m_para[idx].longCDRTime = 0;
	m_para[idx].nReleaseFlag = 0;
	m_para[idx].nAmountLimit = 0;
}

DCParaCom* DCSMPara::getPara()
{
    return reinterpret_cast<DCParaCom*>(BData::data());
}
