#include "DCBizCdrNormalSMS.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"

using namespace ocs;


DCBizCdrNormalSMS::DCBizCdrNormalSMS()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormalSMS::~DCBizCdrNormalSMS()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

int DCBizCdrNormalSMS::ComposeSMS(STBizMsg* bizMsg)
{
	int ret = 0;
	char value[BIZ_TEMP_LEN_256] = {0};
	vector<SCDRField*> *field;
	vector<SCDRField*>::iterator iter;
	
	SCDRData cdr = {0};
	DataCDRInfo stCdrInfo;
	int nFieldNum;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;
	long long iCdrVersionSerial = 1;

/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		return RET_SUCCESS;
	}
*/
	UDBSQL *pQuery = dbm->GetSQL(SMS_GetSessionInfo_Cdr);

	try
	{
		string  sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "SQL[%s]", sql.c_str());
		
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(2, stCdrInfo.planInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "planInfo[%s]", stCdrInfo.planInfo);

			pQuery->GetValue(3, stCdrInfo.tarifInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "tarifInfo[%s]", stCdrInfo.tarifInfo);

			pQuery->GetValue(4, stCdrInfo.chargeInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "chargeInfo[%s]", stCdrInfo.chargeInfo);


			if(bizMsg->m_version != 1 )
			{
                            // 在线
				//RE_LNG_CURRENT_CCR_TIME
				pQuery->GetValue(6, value);
				stCdrInfo.Day[0] = value[6];			
				stCdrInfo.Day[1] = value[7];			
				stCdrInfo.Day[2] = '\0';
				int day = atoi(stCdrInfo.Day);
				sprintf(stCdrInfo.Day,"%d",day);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "Day[%d]", day);
				strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "sessionCurrent[%s]", value);
			}
			else
			{
                            //离线
				//RE_LNG_CURRENT_CCR_TIME
				pQuery->GetValue(6, value);
                            strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "sessionCurrent[%s]", value);
                            
				stCdrInfo.Day[0] = value[6];			
				stCdrInfo.Day[1] = value[7];			
				stCdrInfo.Day[2] = '\0';
				
				//RE_LNG_SYS_CCR_TIME
				pQuery->GetValue(7, stCdrInfo.Payment);

                            value[6] = 0;
                            if(atoi(value) < stCdrInfo.Payment)
                            {
                                    // 封账后上个月话单使用本月账期DAY = 0
                                    stCdrInfo.Day[0] = '0';			
				        stCdrInfo.Day[1] = '0';	
                            }

                            int day = atoi(stCdrInfo.Day);
				sprintf(stCdrInfo.Day,"%d",day);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "Day[%d]", day);
			}
			pQuery->GetValue(15, value);
			stCdrInfo.Payment= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "Payment[%ld]", stCdrInfo.Payment);
			if(stCdrInfo.Payment == 0)
			{
				strcpy(value,stCdrInfo.sessionCurrent);
				value[6] = '\0';
				stCdrInfo.Payment= atol(value);
			}
			pQuery->GetValue(8, value);
			stCdrInfo.lCdrVersionSerial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "OCS_SM_CDR_VERSION_SEQ_ID.nextval[%ld]", stCdrInfo.lCdrVersionSerial);

			pQuery->GetValue(9, stCdrInfo.accumuInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "accumuInfo[%s]", stCdrInfo.accumuInfo);

			pQuery->GetValue(10, value);
			stCdrInfo.PayFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "payflag[%d]", stCdrInfo.PayFlag);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "invalid session id", "");
			return -2;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "select  execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	} 



	vector<STariffAccumCDRInfo> vecTaiAuCdr;
	vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

	//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
	ParaseTariffAccumCdrInfo(bizMsg,stCdrInfo,vecTaiAuCdr);	
	
	
	
	field = TCDRDict::instance()->GetP2PSMSField();


	pQuery = dbm->GetSQL(COM_CDR_SMS);	
	
	try
	{
		//string  sql;
		//pQuery->GetSqlString(sql);
		//DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "SQL[%s]", sql.c_str());
		
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();

		int i = 1;
		if(pQuery->Next())
		{
			int cdrSeqNum=0;
			long totalcdrduration = 0;
			long totalduration = stCdrInfo.duration;
			totalcdrduration = totalduration;
			if(vecTaiAuCdr.size()>0)
			{
				for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
				{
					cdrSeqNum++;
					SCDRData cdr = {0};	
					stCdrInfo.nCutnum = vecTaiAuCdr.size();					
					ComposeSmsCDR(bizMsg,stCdrInfo,pQuery,*TaiAuCdrIter,field,cdrSeqNum,cdr);
					
					int len = strlen(cdr.m_body);
					/*
					cdr.m_body[len - 1] = '\r';
					cdr.m_body[len] = '\n';
					cdr.m_body[len + 1] = '\0';
					*/
					cdr.m_body[len - 1] = '\0';
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "cdr field:\n[%s]", cdr.m_body);

					UCDRData scdr;
					scdr.body = cdr.m_body;
					//发送话单数据
					ret = ProduceCdr(bizMsg,scdr,stCdrInfo.PayFlag);
				}

				if(ret != -2)
				{
					DeleteSession(bizMsg);
				}
			}
			else
			{
				cdrSeqNum++;
				SCDRData cdr = {0};	
				stCdrInfo.nCutnum = 1;
				STariffAccumCDRInfo TaiAuCdrInfo;
				
				ComposeSmsCDR(bizMsg,stCdrInfo,pQuery,TaiAuCdrInfo,field,cdrSeqNum,cdr);
				
				int len = strlen(cdr.m_body);
				cdr.m_body[len - 1] = '\0';
				
				
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "cdr field:\n[%s]", cdr.m_body);

				UCDRData scdr;
				scdr.body = cdr.m_body;
				//发送话单数据
				ret = ProduceCdr(bizMsg,scdr,stCdrInfo.PayFlag);

				if(ret != -2)
				{
					DeleteSession(bizMsg);
				}
			}
						
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "invalid session id", "");
			return -2;
		}
	}
	catch(UDBException& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "select [%s]  execption[%s]", e.GetSqlInfo(), e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	} 

	
	//info 日志
	DCDATLOG("SM00014:%s%s%s%s%s%s%s", stCdrInfo.sessionStart, stCdrInfo.sessionCurrent,\
							stCdrInfo.planInfo, stCdrInfo.tarifInfo, stCdrInfo.chargeInfo, stCdrInfo.accumuInfo, stCdrInfo.orichargeInfo);

	return RET_SUCCESS;
}

int DCBizCdrNormalSMS::ComposeSmsCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr)
{
	char value[BIZ_TEMP_LEN_256] = {0};
	int nFieldNum;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;
	vector<STFeeItem> vecFeeItem;	
	vector<SCDRField*>::iterator iter;
	int i = 1;
	//账目类型和费用字段
	ParaseFeeItem(bizMsg,stCdrInfo,vecFeeItem);

	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");
	
	for(iter=field->begin(); iter!=field->end(); iter++)
	{
		if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)) && iCdrVersionType>0)
		{
			char szVersion[32] = {0};
			int iDay = timestampf();
			sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//组装version信息
			strcpy(value, szVersion);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_BILL_CYCLE", (*iter)->value))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%ld", stCdrInfo.Payment);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",bizMsg->m_xdrsource);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}	
		if((0 == strcmp("DAY", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s", stCdrInfo.Day);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d",cdrSeqNum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s]", value);
			//i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			//sprintf(value,"%s",TaiAuCdrInfo.sz_accumInfo);
			sprintf(value, "%s", stCdrInfo.accumuInfo);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "", "add item CDR_PUB_STR_ACCUMLATORINFO[%s]", value);
			//i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);			
			continue;						
		}
		if(0 == strcmp("CDR_ROAMINT_TYPE", (*iter)->value))
		{
			pQuery->GetValue(i, value);
	 		if(0 == strcmp(value, "0") )
	 		{
	 			memset(value, 0x00, sizeof(value));
	 			strcpy(value, "0");
				
	 		}
			else
			{
				memset(value, 0x00, sizeof(value));
	 			strcpy(value, "4");
			}			
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);			
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}

		if(0 == strcmp("CDR_STR_USER_TYPE", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			DealwithField(temp);
	 		strcat(cdr.m_body, temp);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_STR_MVNO_ID", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			DealwithField(temp);
	 		strcat(cdr.m_body, temp);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());			
			continue;			
		}
		
		if((*iter)->flag)
		{	
			nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
			if(nFieldNum)//话单特殊字段处理
			{
				if(nFieldNum < 9)
				//清单中加入Fee字段或者ACCT_Item_Type_ID字段
					AddFeeItem(vecFeeItem,&cdr,nFieldNum);
				else
					AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
				continue;
			}
			else
			{
				pQuery->GetValue(i, value);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
			}
		}
		else
		{
			strcpy(value, (*iter)->value);
			DealwithField(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "cdr fliedname[%s],value[%s]", (*iter)->value,value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
		}
	}

}


