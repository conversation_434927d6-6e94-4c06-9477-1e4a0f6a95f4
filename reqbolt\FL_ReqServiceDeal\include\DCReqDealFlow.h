/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqDealFlow.h
*Indentifier：
*
*Description：
*		用户鉴权类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/

#ifndef __DC_REQ_DEAL_FLOW_H__
#define __DC_REQ_DEAL_FLOW_H__
#include "DCBasePlugin.h"
#include "DCReq.h"

class DCReqDealFlow :  public DCBasePlugin
{
	public:	
		DCReqDealFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCReqDealFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
	private:
		DCReq *m_req[7];

};

#endif
