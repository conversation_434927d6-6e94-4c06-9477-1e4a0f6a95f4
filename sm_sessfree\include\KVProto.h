#ifndef __KVPROTODEF_H__
#define __KVPROTODEF_H__
#include <string.h>
#include <stdint.h>
/**
* message header define
*/
#pragma pack(1)
struct KVHead
{
    unsigned int version:8;			// 版本号为1
    unsigned int flag:8;			// 8位标志位，flag0=R, flag1=P,flag2=B
    unsigned int proto:16;			// 协议类型，特定的载荷数据格式
    unsigned int msg;				// 消息id
    unsigned int size;				// 包大小，包含包头
}; /* 12 BYTE */

#define KV_FR   0x80				// flag0 R标志掩码, 请求消息掩码
#define KV_FP	0x40				// flag1 P标志掩码, 分包消息掩码
#define KV_FB	0x20				// flag2 B标志掩码, 备份消息掩码

#if 0
/*** 稀疏型键值对 ***/
struct KVPair
{
    /*unsigned int size;*/			// KVPair 的实际大小
    /*unsigned char type;*/			// 高4bit: key值类型; 低4bit: value值类型
    /*key*/							// key值
    /*value*/						// value值
};

struct KVGroup
{
	unsigned int size;				// KVGroup 的实际大小
	unsigned char type;				// 高4bit: key值类型; 低4bit: 0xE组类型
	/*key*/							// 组标识
	/* KVPair kv[N]; */				// N个连续存放的 KVPair
};
#endif // 0

#if 0
/*** 紧凑型键值对 ***/
struct KVPair
{
    /*unsigned char type;*/			// 高4bit: key值类型; 低4bit: value值类型
    /*key*/							// key值
    /*value*/						// value值
};

struct KVGroup
{
	unsigned char type;				// 高4bit: key值类型; 低4bit: 0xE组类型
	unsigned int size;				// KVGroup 的实际大小
	/*key*/							// 组标识
	/* KVPair kv[N]; */				// N个连续存放的 KVPair
	/* KVGroup kg;   */				// 子组
};

#endif // 0

#pragma pack()

/**
* kv type define
*/
enum KVType
{
	t_none		= 0x0,		//	0B
	t_int8 		= 0x1,		//	1B
	t_uint8 	= 0x2,		//	1B
	t_int16   	= 0x3,		//	2B
	t_uint16	= 0x4,		//	2B
	t_int32		= 0x5,		//	4B
	t_uint32	= 0x6,		//	4B
	t_float		= 0x7,		//	4B
	t_string	= 0x8,		//	N+1B  包含0结束符
	t_int64		= 0x9,		//	8B
	t_uint64	= 0xa,		//	8B
	t_double	= 0xb,		//	8B
	t_addr		= 0xc,		//	8B
	t_group		= 0xe		//	组标志
};

inline unsigned int KVSize(KVType type, const void*data)
{
	unsigned int size = 0;
    switch(type)
    {
		case t_int8:
		case t_uint8:
			size = 1;
			break;
		case t_int16:
		case t_uint16:
			size = 2;
			break;
		case t_int32:
		case t_uint32:
		case t_float:
			size = 4;
			break;
		case t_string:
			size = strlen((char*)data)+1;
			break;
		case t_int64:
		case t_uint64:
		case t_double:
		case t_addr:
			size = 8;
			break;
		default:
			break;
    }
    return size;
}

#endif // __KVPROTODEF_H__
