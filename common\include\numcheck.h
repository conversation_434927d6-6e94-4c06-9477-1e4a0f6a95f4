  /*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*                   技术平台项目组
*All rights reserved.
*
*Filename：
*       numcheck.h
*Indentifier：
*
*Description：
*       封装NumCheck 类的头文件，用于数据检查和号码归整
*Version：
*       V1.0
*Author:
*
*Finished：
*
*History:
*
********************************************/
#ifndef NUMCHECK_H
#define NUMCHECK_H

#include <regex.h>
#include <math.h>
#include <map>

#include "SMParaStruct.h"
#include "DCLogMacro.h"
#include "TSMPara.h"
#include "DCOcpMsgDef.h"
#include "DCParseXml.h"


using namespace std;
using namespace ocs;



#define MAX_PHONE_LEN 20
class NumCheck
{
	public:

		Num<PERSON>he<PERSON>();
		~NumCheck();

	public:

		/****************************************************************************************
		*@input
				accessMap : 接入号表
				countryMap : 国家号表
				areaMap : 区号表
				mobileMap : 手机号段表

		*@output

		*@return

		*@description		初始化电话信息
		******************************************************************************************/
		static void Init(TSMPara *smpara);


		/****************************************************************************************
		*@input
				str : 需归整的号码字符串
		*@output
				phoneNum : 归整后号码

		*@return
				0 :	归整成功
				-1 : 	格式错误
				-2 :	无匹配国家号
				-3 :	号码长度超过
				-4 :	无匹配手机号段

		*@description		号码归整
		******************************************************************************************/
		static int SortNum(const char * str, SPhone *phoneNum, unsigned long time, int flag = 1);

		/****************************************************************************************
		*@input
				access : 接入号
		*@output
		*@return
				接入号结构
		*@description		获取接入号详细
		******************************************************************************************/
		static const ACCESS_INFO 	* GetAccessInfo(int access);


		/****************************************************************************************
		*@input
				str : 需归整的号码字符串
		*@output

		*@return

		*@description		宽带业务号码规整
		******************************************************************************************/
		static int SortNumDSL(const char *str, SPhone* phoneNum);


		/****************************************************************************************
		*@input
				str : 需归整的号码字符串
		*@output

		*@return

		*@description		WLAN业务号码规整
		******************************************************************************************/
		static int SortNumWLAN(const char *str, SPhone* phoneNum);



		/****************************************************************************************
		*@input
				str : 需检查的字符串

		*@output

		*@return
				0 :	是
				-1 : 不是

		*@description		检查是否全为数字
		******************************************************************************************/
		static int IsDigital(const char *str);


		/****************************************************************************************
		*@input
				str : 需检查的字符串

		*@output

		*@return
				0 :	是
				-1 : 不是

		*@description		检查是否时间格式[yyyy-mm-dd hh:mm:ss]
		******************************************************************************************/
		static int IsTimeFormat(const char *str);


		/****************************************************************************************
		*@input
				str : 数值字符串(不能超过18 位)

		*@output

		*@return
				0 :	超过范围
				other : 数值

		*@description		字符串转换为long long 类型
		******************************************************************************************/
		static long long Atoll(const char *str);
		static int InitMsisdn(map<long long, MSISDN_MAP_INFO*> *msisdn);

		static void lltoa(const long long num, char *str);

		static int IsPrefixCode(const char *str);//是否为手机号码头前缀

		static bool GetDefaultFlag();
	private:
		static int IsAccessCode(const char *str);
		static int IsCountryCode(const char *str);
		static AREA_INFO * IsAreaCode(const char *str);
		static int IsFixedPhoneCode(const char *str, SPhone* phoneNum);
		static int IsMobileCode(const char *str, SPhone* phoneNum);

		static void countMaxMinLength();

		static void UnifiedCallNum(SPhone &phone, char* unified);
		
		static int  getNetWorkType(const char *str, SPhone* phoneNum);
		
	private:
		static regmatch_t 		m_pm;
		static size_t 			m_nmatch;
		static regex_t			m_reg[10];

		static int				m_accessMapMax;
		static int				m_accessMapMin;
		static int				m_countryMapMax;
		static int				m_countryMapMin;
		static int				m_areaMapMax;
		static int				m_areaMapMin;

		static int				m_defaultArea;
		static int				m_defaultProvince;
		static int				m_defaultCarriers;

		static map<int, ACCESS_INFO *>		*m_accessMap;
		static map<int, COUNTRY_INFO *>		*m_countryMap;
		static map<int, AREA_INFO *>		*m_areaMap;
		static map<long long, MSISDN_MAP_INFO*> *m_msisdn;
		static map<int,int>				*m_prefixMap;
		static TSMPara *m_smpara;
		static bool 			m_isDefault;

		static string           m_PARTNERIDMap;

		
};
#endif
