#include "DCReqDATA.h"
#include "ErrorCode.h"
#include <sys/time.h>
#include "DCSeriaOp.h"
#include "DCLogMacro.h"
#include "BizDataDef.h"
#include "TSMPara.h"
#include "func_sqlindex.h"
#include "DCCommonIF.h"
#include "DCMqProduceServer.h"
#include "UHead.h"
#include <stdio.h>
#include <sys/time.h>

DCReqDATA::DCReqDATA()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

DCReqDATA::~DCReqDATA()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

int DCReqDATA::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG,  "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}
	int nMsgType = 0;
	
	STBizMsg *bizMsg = (STBizMsg*)data;

	// cvar->m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));

	
	//PERF LOG
	// bizMsg->m_perf.GetTimeT2_B();
	
	int ret = SwitchReqType(bizMsg);
	
	bizMsg->m_resultcode = ret;	

	//PERF LOG
	// bizMsg->m_perf.GetTimeT2_E();

	return ret;
}

int DCReqDATA::SwitchReqType(STBizMsg* bizMsg)
{
	return 0;
}

int DCReqDATA::GetServerid(SUserInfo* userInfo, STBizMsg* bizMsg)
{
	DCDBManer* dbm 	= (DCDBManer*)bizMsg->m_dbm;
	UDBSQL* pQuery;
	if(bizMsg->m_serviceContextID == PGW)
	{
		pQuery = dbm->GetSQL(PGW_SELECT_SESSION_STORE_SERVERID);
	}
	else
	{
		pQuery = dbm->GetSQL(DATA_SELECT_SESSION_STORE_SERVERID);
	}
	try
	{	
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		int ret =0;
		if(pQuery->Next())
		{
			char serverid[20]={0};
			pQuery->GetValue(1,serverid);
			userInfo->servID=atol(serverid);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "servID[%ld]", userInfo->servID);
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}
	return  0;
}

int DCReqDATA::QueryOss(STBizMsg* bizMsg, long servid,char *PLCA_LOC,char *PLCA_CELLID,char *PLCA_MSC,char *PLCA_RAT_TYPE)
{

		time_t LocalTime;
		time_t updatetime=0;
		char value[20]={0};
		char Plca_cellidTemp[20]={0};
		TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
		DCDBManer* dbm 	= (DCDBManer*)bizMsg->m_dbm;
		UDBSQL* pQuery = dbm->GetSQL(DATA_Select_PLCA_USER_POSITION);
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, servid);
			if(pQuery->Next())
			{
				pQuery->GetValue(1, Plca_cellidTemp);//SM_STR_PLCA_LOC
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","PLCA_CELLID[%s]", Plca_cellidTemp);

				pQuery->GetValue(2, value);//UPDATE_DATE
				updatetime=DCCommonIF::GetTimeFromLongStr(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","updatetime[%d]", updatetime);
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
			return RB_SM_UNABLE_TO_COMPLY;
		}

		LocalTime=time(NULL);
		if(LocalTime-updatetime<=m_smpara->GetCommonPara()->lPlcavalidtime)
		{
			strcpy(PLCA_CELLID,Plca_cellidTemp);
		}
		sprintf(PLCA_LOC,"%s|%s|%s",PLCA_CELLID,PLCA_MSC,PLCA_RAT_TYPE);

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","PLCA_LOC[%s]", PLCA_LOC);
		return  0;
}

int DCReqDATA::sendInitCCA(STBizMsg* bizMsg)
{
	char serviceFlowId[48] = {0};   //Service-Flow-Id
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;

	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = bizMsg->m_resultcode;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.serial = bizMsg->m_serial;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial        = bizMsg->m_serial;
       cca.trace   = bizMsg->m_trace_flag;
       
	DCCommonIF::GetServiceFlowID(serviceFlowId);
       cca.ServiceFlowID = serviceFlowId;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	bizMsg->m_RARFlag = RET_OVER;
	//DCMqProduceServer* producer = bizMsg->m_producer;

/*	string sendmsg = HexEncode(m_en.data(),m_en.size());

       struct timeval tmv;
       char buf[20];
 
       // 头部加固定16位微妙时间戳
       gettimeofday(&tmv, NULL);
       sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
       sendmsg.insert(0, buf);
    
	//int ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), bizMsg->m_anstopic);
	bizMsg->m_topictype = 1;
	int ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Produce CCADATA failed, ret=%d\n", ret);	
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA to MQ,topic[%s], msglen:%d", bizMsg->m_anstopic, m_en.size());
	}	*/	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID, "send CCA data,uid[%s]",uhd.uid.c_str());

	return 	RET_OVER;
}

int DCReqDATA::sendTermCCAWithOutRG(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret = 0;
	int i = 0;
	int n = 0;
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;

	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = bizMsg->m_resultcode;
	cca.requestType = 3;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial        = bizMsg->m_serial;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
       cca.trace   = bizMsg->m_trace_flag;
	int MSCCCount = base->MSCC.size() - 1;

	ocs::DATAUSU usu; 
	for(int k = 0; k < MSCCCount; k++)
	{	
	    usu.ratinggroup = base->MSCC[k].ratingGroup;
		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "set rating group[%lld]", base->MSCC[k].ratingGroup);

		usu.ResultCode = bizMsg->m_resultcode;
		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "MSCC result code [%d]",bizMsg->m_resultcode);

		if(base->MSCC[k].ProductOfferId.length())
		{
			usu.ProductOfferId=base->MSCC[k].ProductOfferId;
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "set ofr id[%s] ", base->MSCC[n].ProductOfferId.c_str());
			continue;
		}

		cca.MSCC.push_back(usu);
	}
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	bizMsg->m_RARFlag = RET_OVER;
/*	string sendmsg = HexEncode(m_en.data(),m_en.size());
	DCMqProduceServer* producer = bizMsg->m_producer;

        struct timeval tmv;
       char buf[20];
 
       // 头部加固定16位微妙时间戳
       gettimeofday(&tmv, NULL);
       sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
       sendmsg.insert(0, buf);
           
	//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), bizMsg->m_anstopic);
	bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Produce %s failed, ret=%d\n", bizMsg->m_anstopic, ret);	
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA to MQ,topic[%s]\n msg:%s", bizMsg->m_anstopic, sendmsg.c_str());
	}	*/	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA data[%s]",bizMsg->data.c_str());
	return 	RET_OVER;
}

int DCReqDATA::sendTermCCAWithOutRG(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg)
{
	int ret = 0;
	int i = 0;
	int n = 0;
	ocs::UHead uhd;
	ocs::SCCA5G cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;

	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = bizMsg->m_resultcode;
	cca.requestType = 3;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial        = bizMsg->m_serial;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
       cca.trace   = bizMsg->m_trace_flag;
	int MSCCCount = base->MSCC.size() - 1;

	ocs::AUSU usu;
	for(int k = 0; k < MSCCCount; k++)
	{
	    usu.ratinggroup = base->MSCC[k].ratingGroup;
		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "set rating group[%lld]", base->MSCC[k].ratingGroup);

		usu.ResultCode = bizMsg->m_resultcode;
		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "MSCC result code [%d]",bizMsg->m_resultcode);

		if(base->MSCC[k].ProductOfferId.length())
		{
			usu.ProductOfferId=base->MSCC[k].ProductOfferId;
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "set ofr id[%s] ", base->MSCC[n].ProductOfferId.c_str());
			continue;
		}

		cca.MUU.push_back(usu);
	}
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
/*	string sendmsg = HexEncode(m_en.data(),m_en.size());
	DCMqProduceServer* producer = bizMsg->m_producer;

        struct timeval tmv;
       char buf[20];

       // 头部加固定16位微妙时间戳
       gettimeofday(&tmv, NULL);
       sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
       sendmsg.insert(0, buf);

	//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), bizMsg->m_anstopic);
	bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Produce %s failed, ret=%d\n", bizMsg->m_anstopic, ret);
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA to MQ,topic[%s]\n msg:%s", bizMsg->m_anstopic, sendmsg.c_str());
	}	*/
	//DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA data[%s]",bizMsg->data.c_str());
	return 	RET_OVER;
}
