#include "DCAnsVOICETEL.h"
#include "UStaMsg.h"
#include "DCCdrIndex.h"
DCAnsVOICETEL::DCAnsVOICETEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "DCAnsVOICETEL", "");
}

DCAnsVOICETEL::~DCAnsVOICETEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "~DCAnsVOICETEL", "");
}

int DCAnsVOICETEL::ComposeCCA(STBizMsg* bizMsg)
{
	int ret 									= 0;
	int aocType 								= 0;
	int callType								= 0;
	int cost_unit 								= 0;
	int cost_amount 							= 0;
	char value[BIZ_TEMP_LEN_2048 + 1] 				= {0};
	char szEndTime[20]							= {0};
	int amountInfo 								= 0;
	char subStr[BIZ_TEMP_LEN_32] 				= {0};
	int balance                            	 	= 0;
	int lastRSU 								= 0;
	int preChargeUnit 							= 0;
	int preChargeAmount 						= 0;
	int balanceFlag                         	= 0;
	long lEventTypeID 							= 0;//事件类型ID
	int nActiveFlag 							= 0;
	long lnSerial 								= 0;
	int nTraceNbrOnOff 							= 0;
	char szChildSessionID[256]              	= {0};
	int nCdrLongFlag					= 0;
	int nResultCode						= 0;
	int nCCAFlag						= 0;
	long lnLastALLUsu					= 0;
	long TUSU							= 0;
	long USU							= 0;

	SREAInfo REAMsg ;
	long lnCdrDiscountFee				= 0;

	map< long long, STCodeOfr > MSCCResult;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	int iaocflag = 	smpara->GetCommonPara()->iAocType;

	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	int PayFlag = atoi(ext->kv["PayFlag"].c_str());
	std::string mocType = ext->kv["R619"];
	bizMsg->m_volteFlag = atoi(ext->kv["VolteFlag"].c_str());
	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend StragegyId[%ld]", StragegyId);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend PayFlag[%d]", PayFlag);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend R619[%s]", mocType.c_str());


	long discount_fee                   = 0;
	long discount_totalfee              = 0;

	balance = AccumlateBalance(bizMsg);
	PreChargeInfo(bizMsg, preChargeAmount);

	//授权量
	int gUnit 		= 0;
	long long gNum 	= 0;
	if(base->gsv.size()>0)
	{
		gUnit                         = base->gsv[0].unit;
		gNum                          = base->gsv[0].amount;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "gunit[%d],gnum[%d]", gUnit, gNum);
	}

	//info 日志
	DCDATLOG("SM00011:%d%ld", gUnit, gNum);

	UDBSQL *pQuery = pdbm->GetSQL(Voice_GetSessionInfo);
	try
	{
		long long serial = 0;
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{

			pQuery->GetValue(1,value);
			bizMsg->m_requestType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "request type[%s]", value);

                     pQuery->GetValue(71,value);
			bizMsg->m_requestNumber= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "request number[%s]", value);

			pQuery->GetValue(2, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "ccr-time[%s]", value);

			//会话状态
			pQuery->GetValue(3, value);
			lastRSU= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "last gsu[%s]", value);

			pQuery->GetValue(5, value);
			nTraceNbrOnOff = atol(value);

			//R103  话单类型
			pQuery->GetValue(8, value);
			callType = atoi(value);
			bizMsg->m_cdrCallType = callType;

			pQuery->GetValue(9, bizMsg->m_subNumber);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "subNumber[%s]", bizMsg->m_subNumber);

			pQuery->GetValue(40, value);
			nActiveFlag =  atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "active flag[%s]", value);

			pQuery->GetValue(41, value);
			USU = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "current USU[%ld]", USU);

			pQuery->GetValue(4, value);
			TUSU=USU+atol(value);
			bizMsg->m_nTUSU = TUSU;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "TOTOL USU[%ld]", TUSU);

			pQuery->GetValue(24, value);
			bizMsg->m_longtype= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "longtype[%s]", value);

			pQuery->GetValue(25, value);
			bizMsg->m_roamtype= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "rometype[%s]", value);

			// SM_INT_AOC_TYPE
			pQuery->GetValue(46, value);
			aocType = atoi(value);
			bizMsg->m_naoc_type=aocType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "aoc type[%d]", aocType);


			//转售提醒
			pQuery->GetValue(51, value);
			bizMsg->m_userType= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "usertype [%s]", value);

			//转售提醒
			pQuery->GetValue(52, bizMsg->m_mvnoId);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "mvnoid [%s]", bizMsg->m_mvnoId);

			// CDR_DISCOUNT_FEE
			pQuery->GetValue(53, value);
			lnCdrDiscountFee = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "lnCdrDiscountFee [%s]", value);

			// CDR_PUB_STR_BALANCEINFO
			pQuery->GetValue(54, value);
			strcpy(REAMsg.sz_balanceInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "balanceInfo [%s]", value);

			// CDR_PUB_STR_BALANCEINFO2
			pQuery->GetValue(55, value);
			strcpy(REAMsg.sz_balanceInfo2, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "BalanceInfo2 [%s]", value);

			// CDR_PUB_STR_ACCUMLATORINFO
			pQuery->GetValue(56, value);
			strcpy(REAMsg.sz_accumlatorInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "szCdrAccumInfo [%s]", value);

			// CDR_PUB_STR_TARIFFID
			pQuery->GetValue(57, value);
			strcpy(REAMsg.sz_tariffIdInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "szCdrTariffnfo [%s]", value);

			// CDR_PUB_STR_CHARGEINFO
			pQuery->GetValue(58, value);
			strcpy(REAMsg.sz_chargeInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "szCdrChargeInfo [%s]", value);

			// CDR_PUB_STR_PRICING_PLAN_ID
			pQuery->GetValue(59, value);
			strcpy(REAMsg.szPricingPlanID, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "szCdrPlanId [%s]", value);

			// OCP_INT_CCA_FLAG
			pQuery->GetValue(49, value);
			nCCAFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "nCCAFlag [%s]", value);

			// CDR_LNG_LAST_ALL_USU
			pQuery->GetValue(50, value);
			lnLastALLUsu = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "lnLastALLUsu [%s]", value);

			// CDR_PUB_STR_ORICHARGE_INFO
			pQuery->GetValue(72, value);
			strcpy(REAMsg.sz_oriChargeInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "sz_oriChargeInfo [%s]", value);

			//pQuery->GetValue(66, bizMsg->m_serial);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "serial [%lld]", bizMsg->m_serial);

			pQuery->GetValue(75, value);
			//bizMsg->m_serial = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "billcycle[%s]", value);


			if(!bizMsg->m_ReDealAns && strlen(value) > 0 && ext->kv["BILLCYCLE"].size() > 0 && ext->kv["BILLCYCLE"] != value)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "BILLCYCLE change[%s],cut cdr", ext->kv["BILLCYCLE"].c_str());
				bizMsg->m_ReDealAns = true;
				bizMsg->m_longCDR = 2;//与流量超长截单方式一样
				return RET_CDR;
			}

  		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	if((SM_SESSION_UPDATE_CODE == bizMsg->m_requestType) || (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType))
	{
		ret = ModifyREAMsg(bizMsg,REAMsg);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_RATING_FAILED,  bizMsg->m_sessionID, "parse rating info failed");
			return SM_OCP_RATING_FAILED;
		}

		//获取B036,B037
		ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
		if(ret != RET_SUCCESS)
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "GetCostInfo error ", "");
		}

	}

	if(SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType  || SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
	{
		//获取B213
		balance = AccumlateBalance(bizMsg);
		if(2202 ==bizMsg->m_resultcode)
		{
			balanceFlag = 1;
		}

		if(1==iaocflag)
		{
			if((1==aocType)||(8==aocType)||(9==aocType))
			{
				balanceFlag = 1;
			}
		}
		else
		{
			if((aocType == 1) ||(aocType == 5))
			{
				balanceFlag = 1;
			}
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID,  "accumlate B213[%d]", balance);
	}

	if(SM_SESSION_EVENT_CODE !=  bizMsg->m_requestType)//余额查询的不需要返回事件ID
	{
		lEventTypeID = base->evt_id;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "eventtypeid[%ld]",lEventTypeID);
	}

	discount_fee= lnCdrDiscountFee;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID,  "discount_fee[%ld],get B0371[%ld]",discount_fee, base->dist_fee);

	discount_totalfee = discount_fee+base->dist_fee;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "total discount fee[%ld]", discount_totalfee);

	//亲情号码处理
	char szFNFFlag[2]={0};
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "family code flag R608[%d]", base->fnf_flag);
	if(1 == base->fnf_flag)
	{
		szFNFFlag[0]='0';
	}
	else
	{
		szFNFFlag[0]='1';
	}


	//info 日志
	DCDATLOG("SM00012:%s%s%s%s%s%s%ld", REAMsg.sz_balanceInfo, REAMsg.sz_accumlatorInfo,\
										REAMsg.sz_tariffIdInfo, REAMsg.sz_chargeInfo,\
										REAMsg.sz_balanceInfo2, REAMsg.szPricingPlanID, lEventTypeID);

	UDBSQL *pExec  = NULL;
	if (SM_SESSION_EVENT_CODE != bizMsg->m_requestType)
	{
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			pExec = pdbm->GetSQL(Voice_UpdateSession);
			try
			{
				pExec->DivTable(bizMsg->m_sessionID);
				pExec->UnBindParam();
				pExec->BindParam(1, REAMsg.sz_balanceInfo);
				pExec->BindParam(2, REAMsg.sz_accumlatorInfo);
				pExec->BindParam(3, REAMsg.sz_tariffIdInfo);
				pExec->BindParam(4, REAMsg.sz_chargeInfo);
				pExec->BindParam(5, bizMsg->m_resultcode);
				pExec->BindParam(6, szFNFFlag);
				pExec->BindParam(7, STATUS_IDLE);

				pExec->BindParam(8, REAMsg.sz_balanceInfo2);
				pExec->BindParam(9, (long)balance);
				pExec->BindParam(10, REAMsg.szPricingPlanID); // CDR_LNG_PRICING_PLAN_ID
				pExec->BindParam(11, lEventTypeID);			  // CDR_LNG_EVENT_TYPE_ID
				pExec->BindParam(12, (long)gNum);			  // RE_LNG_LAST_GSU_TIME

				pExec->BindParam(13, discount_totalfee);
				pExec->BindParam(14, TUSU);
				pExec->BindParam(15, StragegyId);
				pExec->BindParam(16, PayFlag);
				pExec->BindParam(17, mocType.c_str());
				pExec->BindParam(18, REAMsg.sz_oriChargeInfo);
				pExec->BindParam(19, OfrInstId.c_str());
				// pExec->BindParam(20, bizMsg->m_CreditCtlFlag);
				pExec->BindParam(20, ext->kv["BILLCYCLE"].c_str());
				pExec->BindParam(21, bizMsg->m_sessionID);

				pExec->Execute();
				pExec->Connection()->Commit();
				success = true;
			}
			catch (UDBException &e)
			{
				std::string sql;
				pExec->Connection()->Rollback();
				pExec->GetSqlString(sql);
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			}
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "update session ok\n", "");
	}
	else  // 余额查询删除会话
	{
		try
		{
			pExec =  pdbm->GetSQL(Voice_DeleteSession);
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_sessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "delete execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "delete session ok\n", "");
	}

	//保存量本信息，累积量信息
	ocs::RatingMessageInfo_t* ratingMsg = (ocs::RatingMessageInfo_t*)bizMsg->m_ratingMsg;

	if(bizMsg->m_requestType != SM_SESSION_INITIAL_CODE)
	{
		int ret = UpsetRatingMsg(bizMsg);
		if(RET_ERROR == ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  bizMsg->m_sessionID,	"UpsetRatingMsg ERROR!","");
		}
	}

	if(1 == nCCAFlag)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID,  "ret cdr","");
		return RET_CDR;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "final_flag[%d]", base->final_flag);

	//费率信息
	int tariff = 0;
	int tariffTime = 0;
	int tariffStep = 0;
	int tariffPrice = 0;
	//组装CCA消息
	ocs::SCCAMsg cca;
	ocs::DATAUSU gsu;
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				if(1==nActiveFlag)
				{
					cca.resultCode = 2201;
				}
				else
				{
					cca.resultCode = bizMsg->m_resultcode;
				}
				if((aocType == 2) || (aocType == 5))
				{
					tariffTime = base->rgv[0].tariff_start_time;

					tariffTime = base->rgv[0].tariff_start_time;
					tariffStep = base->rgv[0].step;
					tariffPrice = base->rgv[0].price;

					tariff = 1;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "RB_CODE_R_TARIFF_TIME_CHARGE[%d]", tariff);
					gsu.TariffTimeChange = tariffTime;

				}

				cca.GSUAns.duration= gNum;
				cca.GSUAns.gUnit = gUnit;
				//小区优惠信息
				//cca.fav_flag = base->fav_flag;



				if(balanceFlag)
				{
					cca.AOC.balance = balance;
				}

				if(tariff)
				{
					cca.AOC.startTime = tariffTime;
					cca.AOC.unit = tariffStep;
					cca.AOC.price = tariffPrice;
				}
				char str[4] = {0};
				sprintf(str, "%d", balanceFlag);
				cca.smExt.kv["balanceFlag"] = str;
				str[0] = '\0';
				sprintf(str, "%d", tariff);
				cca.smExt.kv["tariffFlag"] = str;

				if(base->final_flag)
				{
					cca.FinalFlag = 1;
					cca.finalUnitAction = 0;
				}
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				cca.resultCode = bizMsg->m_resultcode;
				cca.GSUAns.duration= gNum;
				cca.GSUAns.gUnit = gUnit;
				cca.Cost.costUnit = cost_unit;
				cca.Cost.valueDigits = cost_amount;
				cca.Cost.exponent = 0;

				if(base->final_flag)
				{
					cca.FinalFlag = 1;
					cca.finalUnitAction = 0;
				}
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{

				cca.resultCode = bizMsg->m_resultcode;
				cca.AOC.balance = balance;
				cca.Cost.costUnit = cost_unit;
				cca.Cost.valueDigits = cost_amount;
				cca.Cost.exponent = 0;
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				cca.resultCode = bizMsg->m_resultcode;
				cca.Account.valueDigits = balance;
			}
			break;
		default:
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "unknow session state[%d]", bizMsg->m_requestType);
			return RB_SM_UNABLE_TO_COMPLY;
		}
		break;
	}
	cca.sessionID = bizMsg->m_sessionID;
	cca.requestType = bizMsg->m_requestType;

	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 1;

	if(1 == bizMsg->m_testFlag)
	{
		cca.BalaInfo =  REAMsg.sz_balanceInfo;
		cca.AccuInfo = REAMsg.sz_accumlatorInfo;
		cca.TariInfo = REAMsg.sz_tariffIdInfo;
		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		cca.AcctInfo = flag;
	}

	// 写队列
	//ret = ProduceCCA(bizMsg, cca, bizMsg->m_anstopic);

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce VOICE failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}

	/*bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce VOICE failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, bizMsg->m_sessionID,  "Produce VOICE topic[%s] Successful ", bizMsg->m_anstopic);*/

/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		if (SM_SESSION_TERMINATION_CODE ==  bizMsg->m_requestType)
		{
			DelSession(bizMsg);
		}
		return RET_SUCCESS;
	}
*/
	//判断是否超长话单
	if (SM_SESSION_UPDATE_CODE ==  bizMsg->m_requestType)
	{
	   if(TUSU >= smpara->GetINPara()->longCDRTime)
	   {
		   	bizMsg->m_longCDR = 1;//正常流程为1
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "long voice cdr,tusu[%ld],config[%d]", TUSU,smpara->GetINPara()->longCDRTime);
			return RET_CDR;//update 时直接返回到话单程序，term时需继续处理，term不处理超长
	    }
	}

	if (SM_SESSION_TERMINATION_CODE ==  bizMsg->m_requestType)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "ret cdr");
		return RET_CDR;
	}
	return RET_SUCCESS;
}

int DCAnsVOICETEL::XdrEvent(STBizMsg* bizMsg)
{
	long lnCurrCCRTime = 0;
	long lnCallStartTime = 0;
	long lnSysCCRTime = 0;
	long sessionStartTime = 0;
	int  ret              = 0;
	SREAInfo REAMsg ;
	int cost_unit 								= 0;
	int cost_amount 							= 0;
	char value[BIZ_TEMP_LEN_256] 				= {0};
	char szSessionSTime[BIZ_TEMP_LEN_32]        ={0};
	long TUSU = 0;
	long USU = 0;
	long discount_fee                       = 0;
	int balance                             = 0;
	long lEventTypeID = 0;//事件类型ID
	balance = AccumlateBalance(bizMsg);
	long lnCdrDiscountFee = 0;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	ocs::rbext *ext = (rbext*)bizMsg->m_extend;

	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	int PayFlag = atoi(ext->kv["PayFlag"].c_str());
	std::string mocType = ext->kv["R619"];

	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend StragegyId[%ld]", StragegyId);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend PayFlag[%d]", PayFlag);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend mocType R619[%s]", mocType.c_str());


	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	int OffLine = 0;

	int gUnit = 0;
	long long gNum = 0;
	if(base->gsv.size()>0)
	{
		gUnit                         = base->gsv[0].unit;
		gNum                          = base->gsv[0].amount;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "gunit[%d],gnum[%d]", gUnit, gNum);
	}

	UDBSQL *pQuery = pdbm->GetSQL(Voice_GetSessionInfo);
	try
	{
		long long serial = 0;
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_childsessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
		       pQuery->GetValue(1,value);
			bizMsg->m_requestType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "request type[%s]", value);

                     pQuery->GetValue(71,value);
			bizMsg->m_requestNumber= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "request number[%s]", value);

			pQuery->GetValue(7, szSessionSTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "szSessionSTime[%s]", szSessionSTime);

                     pQuery->GetValue(9, bizMsg->m_subNumber);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "subNumber[%s]", bizMsg->m_subNumber);

			pQuery->GetValue(4, value);
			TUSU = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "TUSU [%s]", value);

			pQuery->GetValue(41, value);
			USU = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU [%s]", value);

			// CDR_DISCOUNT_FEE
			pQuery->GetValue(53, value);
			lnCdrDiscountFee = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "lnCdrDiscountFee [%s]", value);

			// CDR_PUB_STR_ORICHARGE_INFO
			pQuery->GetValue(72, value);
			strcpy(REAMsg.sz_oriChargeInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "sz_oriChargeInfo [%s]", value);

			//pQuery->GetValue(66, bizMsg->m_serial);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "serial [%lld]", bizMsg->m_serial);

  		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	ret = ModifyREAMsg(bizMsg,REAMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_RATING_FAILED,  bizMsg->m_sessionID, "parse rating info failed");
		return SM_OCP_RATING_FAILED;
	}

	//获取B036,B037
	ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "GetCostInfo error ", "");
	}


	lEventTypeID = base->evt_id;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "eventtypeid[%ld]",lEventTypeID);

	discount_fee= lnCdrDiscountFee + base->dist_fee;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID,  "discount_fee[%ld],get B0371[%ld]",discount_fee, base->dist_fee);

       bizMsg->m_ifree = atoi(ext->kv["IFREE"].c_str());
       DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "ifree[%d]", bizMsg->m_ifree);

	char szCurrentTime[16]={0};
	char szEndTime[16] ={0};
	int nAmount = 0;
	//求开始时间结束时间
	if(ext->kv["eliminateflag"] == "1")
	{
		nAmount = atoi(ext->kv["nAmount"].c_str());
		strcpy(szEndTime,szSessionSTime);
		DCCommonIF::SetSecTimeToDate(DCCommonIF::dateToSec(szEndTime) - nAmount+1, szCurrentTime);
	}
	else
	{
		nAmount = USU;
		strcpy(szEndTime,szSessionSTime);
		DCCommonIF::SetSecTimeToDate(DCCommonIF::dateToSec(szEndTime) - nAmount, szCurrentTime);
	}

	//亲情号码处理
	int fnf_flag = base->fnf_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "family code flag R608[%d]", fnf_flag);


	UDBSQL *pExec  = NULL;
	pExec = pdbm->GetSQL(Voice_UpdateSessionOffLine);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, REAMsg.sz_balanceInfo);
			pExec->BindParam(2, REAMsg.sz_accumlatorInfo);
			pExec->BindParam(3, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(4, REAMsg.sz_chargeInfo);
			pExec->BindParam(5, bizMsg->m_resultcode);
			pExec->BindParam(6, fnf_flag);
			pExec->BindParam(7, STATUS_IDLE);

			pExec->BindParam(8, REAMsg.sz_balanceInfo2);
			pExec->BindParam(9, (long)balance);
			pExec->BindParam(10, REAMsg.szPricingPlanID); // CDR_LNG_PRICING_PLAN_ID
			pExec->BindParam(11, lEventTypeID);			  // CDR_LNG_EVENT_TYPE_ID
			pExec->BindParam(12, (long)gNum);			  // RE_LNG_LAST_GSU_TIME

			pExec->BindParam(13, (int)discount_fee);
			pExec->BindParam(14, (long)nAmount);
			pExec->BindParam(15, atol(szCurrentTime));
			pExec->BindParam(16, atol(szEndTime));
			pExec->BindParam(17, 0);
			pExec->BindParam(18, (long)cost_amount);
			pExec->BindParam(19, StragegyId);
			pExec->BindParam(20, PayFlag);
			pExec->BindParam(21, mocType.c_str());
			pExec->BindParam(22, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(23, OfrInstId.c_str());
			pExec->BindParam(24, ext->kv["BILLCYCLE"].c_str());
			pExec->BindParam(25, bizMsg->m_childsessionID);

			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "update session ok\n", "");

       // 拆分出的子会话直接出单
	if(bizMsg->m_spiltflag > 1)
	{
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "to cdr");
            return RET_CDR;
	}

	ocs::SCCAMsg cca;
	cca.sessionID = bizMsg->m_sessionID;
       cca.sessionID.erase(0, 3);
	cca.resultCode = bizMsg->m_resultcode;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 1;
	cca.Account.valueDigits = balance;

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "",  "Produce VOICE failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}

	DCCdrIndex cdrIndex;
	cdrIndex.SetIndexInfo(bizMsg,szCurrentTime,szEndTime);
	string strIndex;
	cdrIndex.ToString(strIndex);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "cdrIndex[%s]", strIndex.c_str());
/*	bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, bizMsg->m_sessionID,  "Produce VOICE failed topic[%s]", bizMsg->m_anstopic);
	}
       else
       {
	    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, bizMsg->m_sessionID,  "Produce VOICE topic[%s] Successful ", bizMsg->m_anstopic);
       }*/
	return RET_CDR;
}
