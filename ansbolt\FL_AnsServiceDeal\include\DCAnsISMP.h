/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsISMP.h
*Indentifier：
*
*Description：
*		增值业务处理子类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_ISMP_H__
#define __DC_ANS_ISMP_H__
#include "DCAns.h"
#include "DCBizMsgDef.h"

class DCAnsISMP : public DCAns
{
	public:

		DCAnsISMP();
		virtual ~DCAnsISMP();

	public:

		int Work(void *data);

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);
};

#endif

