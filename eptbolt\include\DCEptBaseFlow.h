/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCEptBaseFlow.h
*Indentifier：
*
*Description：
*      ept流程主类
*Version：
*       V1.0
*Author:
*       
*Finished：
*       
*History:
*     
********************************************/
#ifndef DC_EPT_BASE_FLOW_H_
#define DC_EPT_BASE_FLOW_H_

#include <stdio.h>
#include "DCBaseFlow.h"
#include "DCOBJSet.h"
#include "DCEptMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCSeriaOp.h"
#include "UStaMsg.h"
#include "DCCommonIF.h"
#include "DCEvtCheck.h"
//#include "DCMqProduceServer.h"
using namespace ocs;
//
class DCEptBaseFlow: public DCBaseFlow 
{
public:
	DCEptBaseFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBaseFlow(category,func,version),m_de(ESeriaBinString),m_en(ESeriaBinString)
	{
	
	}
	
	virtual ~DCEptBaseFlow()
	{
	
	}

protected:	
	virtual int init();
	virtual int process(void* input, void* output);
private:
	int fmtEptMsg(SEPTMsg*base,STBizMsg *bizMsg);
	
	int composeStaMsg(STBizMsg* bizMsg, int nRet);

	bool checkSumFile(ocs::SComHead& pSComhead,STBizMsg* bizMsg,ocs::RatingMessageInfo_t& bodySM);

private:
	DCOBJSetPool* m_pool;
 	DCSeriaDecoder m_de;
	//DCMqProduceServer* m_producer;
	
	DCSeriaPrinter m_print;
	const char* m_Topic[5];
	DCSeriaEncoder m_en;
	int m_combinaSwitch;
	DCAnsPara* m_anspara;
};

#endif 

