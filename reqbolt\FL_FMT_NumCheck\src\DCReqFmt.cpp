﻿#include "DCReqFmt.h"
#include <stdio.h>
#include <vector>
#include <time.h>
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "numcheck.h"
#include "DCOBJSet.h"
#include "DCCommonIF.h"
#include "func_sqlindex.h"
#include "DCSeriaOp.h"
#include "DCMqProduceServer.h"
#include "UHead.h"
#include <sys/time.h>
#include <stdio.h>
#include "DCServEvtCheck.h"
#include <algorithm>

using namespace std;


DCReqFmt::DCReqFmt()
{

}
DCReqFmt::~DCReqFmt()
{

}

int DCReqFmt::FormatCCR(void *data)
{
	DCOBJSet* pset = (DCOBJSet*)data;
	SCCRBase* base = pset->get<SCCRBase>();
	STBizMsg* bizMsg = pset->get<STBizMsg>();

	int ret = 0;

	ret=FormatCommon(bizMsg,base);
	if(ret == RET_OVER || ret == OFF_LINE)
		return ret;
    if(bizMsg->m_serviceflag>0)//业务组存在
	{
	       ret = FormatServiceInfo(pset);
		if(ret != 0)
		{
			return ret;
		}
	}
	else
	{
		 SCCRISMP* ismp  = NULL;
		 switch(bizMsg->m_serviceContextID)
		 {

			case VOICE:
				{
					ocs::SCCRVOICE* voice = pset->get<SCCRVOICE>();
					bizMsg->m_extend = voice;
				}
				break;
			case DATA:
			case CCG:
			case PGW:
				{
					SCCRDATA* data = pset->get<SCCRDATA>();
					bizMsg->m_extend = data;
				}
				break;
             case DATA_5G:
                 {
                     SCCR5GInfo* data = pset->get<SCCR5GInfo>();
                     bizMsg->m_extend = data;
                 }
                 break;
			case SMS:
				{
					ocs::SCCRSMS* sms = pset->get<SCCRSMS>();
					bizMsg->m_extend = sms;
				}
				break;
			case ISMP:
			case HRS:
				{
					ismp = pset->get<SCCRISMP>();
					bizMsg->m_extend = ismp;
				}
				break;
			case DSL:
				{
					SCCRDSL* dsl = pset->get<SCCRDSL>();
					bizMsg->m_extend = dsl;
				}
				break;
			case RATA:
				{
					ismp = pset->get<SCCRISMP>();
					bizMsg->m_extend = ismp;
				}
				break;
			default:
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE,  "", "invalid service information,without avp IN_Information");
					ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				}
				break;
		}

	}
	// 跨月截单
	/*ret = judgeCutCdr(bizMsg);
	if(ret > 0)
	{
		return ret;
	}*/
	return 0;
}

int DCReqFmt::FormatCommon(STBizMsg* bizMsg,ocs::SCCRBase* base)
{
	char value[128]= {0};
	int ret = 0;
	bizMsg->m_base = base;
	TSMPara *smpara= (TSMPara *)bizMsg->m_smpara;

	strncpy(bizMsg->m_sessionID, base->sessionId.c_str(), sizeof(bizMsg->m_sessionID));
	bizMsg->m_requestType   = base->requestType;
	bizMsg->m_requestNumber = base->requestNumber;
	bizMsg->m_serial        = base->serial;
	bizMsg->m_serviceflag   = base->ServiceInformation;
	bizMsg->m_trace_flag   = base->trace;
	bizMsg->m_sourceId = base->source;
    bizMsg->m_switchId = base->smExt.kv["switchId"];
	bizMsg->m_iRollFlag = atoi(base->smExt.kv["rollFlag"].c_str());
	bizMsg->m_ilatnId = atoi(base->smExt.kv["latnId"].c_str());
	bizMsg->m_operListId = base->smExt.kv["operListId"];
	bizMsg->m_offline = atoi(base->smExt.kv["OFFLINE"].c_str());
	
	bizMsg->m_operType = base->smExt.kv["opertype"];
	if(base->smExt.kv["PDPAddressIpv6"].size() > 0)
	{
		bizMsg->addressIpv6 = base->smExt.kv["PDPAddressIpv6"];
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get PDPAddressIpv6[%s]", bizMsg->addressIpv6.c_str());
	}
	else
	{
		bizMsg->addressIpv6 = base->nFIPv6Address;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get PDPAddressIpv6[%s]", bizMsg->addressIpv6.c_str());
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get PDPAddressIpv6[%s]", bizMsg->addressIpv6.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "sourceID[%s]", bizMsg->m_sourceId.c_str());

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "serial[%ld]", bizMsg->m_serial);
	//业务类型
	strncpy(value, base->szContextOriginal.c_str(),sizeof(value));
	bizMsg->m_serviceContextID = str2type(value,smpara);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "msg service type[%d]",bizMsg->m_serviceContextID);
	if (bizMsg->m_requestType == SM_SESSION_XDR_CODE)
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID,smpara->GetCommonPara()->iBatchIdTime, 2);
	}
	else
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID,smpara->GetCommonPara()->iBatchIdTime, 1);
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]", bizMsg->m_sBatchId.c_str());

	//保存原始的service context id
	char* original = strtok(value, "@");
	strcpy(bizMsg->m_szServiceContextIDStr,original);
	//保存离线消息来源
	strcpy(bizMsg->m_xdrsource,base->source.c_str());

	if(0 == strncmp(value, "test", 4))
	{
		bizMsg->m_testFlag = 1;
		DCBIZLOG(DCLOG_LEVEL_TRACE, SERVTYPE(bizMsg->m_serviceContextID), bizMsg->m_sessionID, "testflag[%d]", bizMsg->m_testFlag);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "serviceContextID[%s],type[%d]", value,bizMsg->m_serviceContextID);

	bizMsg->m_bImsFilter = false;
	if (bizMsg->m_serviceContextID == DATA_5G)
	{
		bizMsg->m_bImsFilter = (atoi(base->smExt.kv["IMS_CHG"].c_str()) == 1) ? true : false;
	}

	ret = CheckMustAVP(base,bizMsg);
	if(ret)
	{
		return ret;
	}

	//时间戳
	time_t ne_et = base->timestamp - OCP_TIME_BASE;			//网元发送消息时间戳
	time_t cursec= time(NULL);
	bizMsg->timestamps = cursec+DCC_MSG_TIMEOUT_SEC;
	time_t sm_et = cursec;		//SM收到消息的时间戳
	timestamp(value, sm_et);
	base->timestamp = atol(value);  // 系统时间

	timestamp(value, ne_et);//新增
	bizMsg->timestampCCR = atol(value);  //新增
	
    if (bizMsg->m_serviceContextID == DATA_5G)
    {
        char szReqNumber[20] = {0};
        char szEndTime[16] = {0};
        DCServEvtCheck::instance()->SetCacheKey("SESSION_ID", bizMsg->m_sessionID);
        sprintf(szReqNumber, "%u", bizMsg->m_requestNumber);
        DCServEvtCheck::instance()->SetCacheKey("SESSION_SEQ", szReqNumber);
        DCServEvtCheck::instance()->SetCacheKey("5G", "1");
        DCServEvtCheck::instance()->SetCacheKey("PREP_BEGIN_TIME", DCServEvtCheck::instance()->GetCurrentTime());
        sprintf(szEndTime, "%ld000", bizMsg->timestampCCR);
        DCServEvtCheck::instance()->SetCacheKey("END_TIME", szEndTime);
        DCServEvtCheck::instance()->SetFileType((SM_SESSION_XDR_CODE == bizMsg->m_requestType) ? CTG_FILE_OFFLINE : CTG_FILE_ONLINE);
    }
    bizMsg->m_version = 2;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "NE[%lld], SM[%ld], system timeStamp[%lld]", bizMsg->timestampCCR, sm_et, base->timestamp);

	if(bizMsg->m_requestType == SM_SESSION_XDR_CODE)
	{
        sprintf(bizMsg->m_sessionID,"XDR%s",base->sessionId.c_str());
		bizMsg->m_version = 1;
		time_t ne_et2 = base->starttime - OCP_TIME_BASE;
		timestamp(value, ne_et2);//新增
		base->starttime = atol(value);  //新增

		string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("LockTime");
		long   LockTime = smpara->GetBillAttr(servAttr);
        if (LockTime < 0)
		{
			LockTime = 0;
        }
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),	bizMsg->m_sessionID, "LockTime[%ld]", LockTime);

		servAttr=string(bizMsg->m_szServiceContextIDStr)+string("nCompareFlag");
		int  nCompareFlag = smpara->GetBillAttr(servAttr);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),	bizMsg->m_sessionID, "nCompareFlag[%d]", nCompareFlag);
		if(nCompareFlag == 1)
		{
			struct tm cur;
			struct tm net2;
			localtime_r(&cursec,&cur);
			localtime_r(&ne_et2,&net2);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "month1[%d] month2[%d]", net2.tm_mon+1,cur.tm_mon+1);
			if(cur.tm_mday > 1 && net2.tm_mon < cur.tm_mon)
			{
				sendCCA(bizMsg);
				return RET_OVER;
			}
			else if(cur.tm_mday == 1)
			{
				char time1[8] = {0};
				sprintf(time1,"%02d%02d%02d",cur.tm_hour,cur.tm_min,cur.tm_sec);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "time1[%s] time2[%ld]", time1,LockTime);
				if(atol(time1) > LockTime)
				{
					sendCCA(bizMsg);
					return RET_OVER;
				}
			}
		}
		//按结束时间判断封账
		sprintf(value, "%ld01%06ld", base->timestamp/100000000, LockTime);
		LockTime = atol(value);
		DCCommonIF::ChangeMonth(value, -1);
		long BeforeMonth = atoi(value);

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),	bizMsg->m_sessionID, "System[%ld], LockTime[%ld], timestampCCR[%ld], BeforeMonth[%ld]", \
		base->timestamp, LockTime, bizMsg->timestampCCR, BeforeMonth);

		if(base->timestamp >= LockTime)
		{
			// 封账之后取本月账期
			bizMsg->billcycle = base->timestamp/100000000;
		}
		else if(bizMsg->timestampCCR/100000000 <  base->timestamp/100000000)
		{
			//封账前取上月账期
			bizMsg->billcycle = BeforeMonth;
		}
		else if(bizMsg->timestampCCR/100000000 ==  base->timestamp/100000000)
		{
			//封账前取本月账期
			bizMsg->billcycle = base->timestamp/100000000;
		}
		else
		{
			bizMsg->billcycle = bizMsg->timestampCCR/100000000;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "billcycle[%ld]", bizMsg->billcycle);
	}
	char tmpbuf[32] = {0};
	strncpy(tmpbuf, base->subscriptionData.c_str(), 32);

	if(0 == strncmp(tmpbuf, "4600", 4))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "IMSI code[%s]", tmpbuf);
		strncpy(bizMsg->m_subNumber, tmpbuf, sizeof(bizMsg->m_subNumber));
	}
	else
	{
		strncpy(bizMsg->m_subNumber, tmpbuf, sizeof(bizMsg->m_subNumber));
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "subscriptionData[%s]", tmpbuf);
	}

	//获取RSU组信息
	if(base->rsued>0)
	{
		FormatRSU(bizMsg);
	}

	if(base->subscriptionData.size()>0)
	{
		//归整计费号码
		if(10 == base->subscriptionType)
		{
			base->subUnified = tmpbuf;
			base->subscription.phone =  base->subscriptionData;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "card user[%s]", base->subscriptionData.c_str());
		}
		else
		{

			if(bizMsg->m_serviceContextID == DSL)
			{
				NumCheck::SortNumDSL(base->subscriptionData.c_str(), &(base->subscription));
			}
			else
			{
				ret = NumCheck::SortNum(base->subscriptionData.c_str(), &(base->subscription), bizMsg->timestampCCR);
				if(ret == 0)
				{
				     // 获取巢湖割接号码实际区号
				     GetAreaCode(base->subscription, bizMsg);
				}
			}


			if(bizMsg->m_serviceContextID == DSL)
			{
				//宽带业务不做号码规整
				base->subUnified = base->subscriptionData;
			}
			else
			{
				DCCommonIF::UnifiedCallNum(base->subscription, tmpbuf);
				base->subUnified = tmpbuf;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "subUnified[%s]", tmpbuf);
		}
	}
	else if(bizMsg->m_serviceContextID != RATA)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "missing Subscription-Id-Data", "");
		return SM_OCP_MISSING_AVP;
	}

	//PGW外省漫入返回网元切离线
	if(PGW == bizMsg->m_serviceContextID && IfOtherProvinces(bizMsg,base->subscription.phone))
	{
		sendCCAOffLine(bizMsg);
		return OFF_LINE;
	}
	char szBillingNbr[16] = {0};
	sprintf(szBillingNbr, "%s", base->subUnified.c_str());
	bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", szBillingNbr));

	//获取USU组信息
	if(base->usued)
	{
		ret = FormatUSU(bizMsg,&base->USU);
		if(ret != RET_SUCCESS)
		{
			return ret;
		}
	}

	// info日志输出

	DCDATLOG("SM00001:%s%s%d%d", bizMsg->m_sessionID, bizMsg->m_uid, bizMsg->m_serviceContextID, bizMsg->m_requestType);
	DCDATLOG(); //主动触发日志输出
	DCDATLOG("SM00002:%s0%d", base->subscription.phone.c_str(), base->subscription.area);
	return 0;
}

int DCReqFmt::FormatServiceInfo(void* data)
{
	int ret = 0;
	int bizType = 0;//校验业务消息完整性
	DCOBJSet* pset = (DCOBJSet*)data;
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	SCCRBase* base = pset->get<SCCRBase>();
	 if(__IN_Information==bizMsg->m_serviceflag)
	 {
		 bizType =VOICE;
	 }

	 if(__PS_Information == bizMsg->m_serviceflag)
	 {
		if(bizMsg->m_serviceContextID == PGW)
		{
			bizType = PGW;
		}
		else
		{
			bizType = DATA;
		}
	 }

     if(__5G_Information == bizMsg->m_serviceflag)
     {
        bizType = DATA_5G;
     }

	 if(__P2PSMS_Information ==  bizMsg->m_serviceflag)
	 {
		 bizType = SMS;
	 }

	 if(__ISMP_Information ==  bizMsg->m_serviceflag)
	 {
		 bizType = ISMP;
	 }

	 if(__DSL_Information == bizMsg->m_serviceflag)
	 {
		 bizType = DSL;
	 }

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "service information bizType[%d]",bizType);

	SCCRISMP* ismp  = NULL;
	 switch(bizMsg->m_serviceContextID)
	 {
		case VOICE:
			{
				 if(VOICE == bizType)
				 {
					ocs::SCCRVOICE* voice = pset->get<SCCRVOICE>();
					bizMsg->m_extend = voice;
					strncpy(bizMsg->m_callingNumber, voice->callingNumber.c_str(), sizeof(bizMsg->m_subNumber));
					strncpy(bizMsg->m_calledNumber, voice->calledNumber.c_str(), sizeof(bizMsg->m_subNumber));
					ret = FormatVOICE(voice, bizMsg);
				 }
				 else
				 {
					 DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "invalid service information,without avp IN_Information");
					 ret = SM_OCP_INVALID_AVP_VALUE;
				 }
			}
			break;
		case DATA:
		case CCG:
		case PGW:
			{
				if(DATA == bizType||PGW == bizType)
				{
					SCCRDATA* data = pset->get<SCCRDATA>();
					bizMsg->m_extend = data;
					ret = FormatDATA(data,bizMsg);
					bizMsg->visitAreaCode = base->smExt.kv["VISCODE"];
					if(bizMsg->visitAreaCode.size() > 0)
					{
						data->userMsc = base->smExt.kv["MSCID"];
						data->userCellid = base->smExt.kv["CELLID"];
						data->userLac = base->smExt.kv["LACID"];
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get visitAreaCode[%s],mscID[%s],cellid[%s],lacid[%s] from smExt",bizMsg->visitAreaCode.c_str(),data->userMsc.c_str(),data->userCellid.c_str(),data->userLac.c_str());
					}
				}
				else
				{

					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE, "", "invalid service information,without avp PS_Information");
					ret = SM_OCP_INVALID_AVP_VALUE;
				}
			}
			break;
        case DATA_5G:
            {
                SCCR5GInfo* data = pset->get<SCCR5GInfo>();
                bizMsg->m_extend = data;

                //将位置信息改为10进制
                FormatDATA_5G(base, bizMsg, data);

            }
            break;
		case SMS:
			{
				if(SMS == bizType)
				{
					ocs::SCCRSMS* sms = pset->get<SCCRSMS>();
					bizMsg->m_extend = sms;
					strncpy(bizMsg->m_callingNumber, sms->callingNumber.c_str(), sizeof(bizMsg->m_subNumber));
					strncpy(bizMsg->m_callingNumber, sms->calledNumber.c_str(), sizeof(bizMsg->m_subNumber));
					ret = FormatSMS(sms, bizMsg);
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE,  "", "invalid service information,without avp P2PSMS_Information");
					ret = SM_OCP_INVALID_AVP_VALUE;
				}
			}
			break;
		case ISMP:
		case HRS:
			{
				if(ISMP == bizType)
				{
					ismp = pset->get<SCCRISMP>();
					bizMsg->m_extend = ismp;
					ret = FormatISMP(ismp, bizMsg);
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE,  "", "invalid service information,without avp ISMP_Information");
					ret = SM_OCP_INVALID_AVP_VALUE;
				}
			}
			break;
		case DSL:
			{
				if(DSL == bizType)
				{
					SCCRDSL* dsl = pset->get<SCCRDSL>();
					bizMsg->m_extend = dsl;
					ret = FormatDSL(dsl, bizMsg);
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE,  "", "invalid service information,without avp DSL_Information");
					ret = SM_OCP_INVALID_AVP_VALUE;
				}
			}
			break;
		case RATA:
			{
				if(RATA == bizType)
				{
					ismp = pset->get<SCCRISMP>();
					bizMsg->m_extend = ismp;
					ret = FormatRATA(ismp, bizMsg);
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE,  "", "invalid service information,without avp ISMP_Information");
					ret = SM_OCP_INVALID_AVP_VALUE;
				}
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE,  "", "invalid service information");
				ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
			}
			break;
	}
	return ret;
}

int DCReqFmt::FormatUserLocation(string rATType, ocs::userLoc &userLocInfoMation)
{
	int lac = 0;
	int cellid = 0;
	int msc = 0;
	string mscTmp;
	string cellidTmp;
	char valueLac[128] = {0};
	char valueCell[128] = {0};
	char valueMsc[128] = {0};
	if (userLocInfoMation.nrCellId.length() >= 7)
	{
		cellidTmp = userLocInfoMation.nrCellId.substr(6, 3);
		cellid = DCCommonIF::OCTTODEC(cellidTmp.c_str());
		if(cellid != 0)
		{
			sprintf(valueCell, "%d", cellid);
			userLocInfoMation.cellid = valueCell;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get cellid[%s] from smExt[%s].", valueCell, userLocInfoMation.nrCellId.c_str());
		}
	}

	mscTmp = userLocInfoMation.nrCellId.substr(0, 6);
	msc = DCCommonIF::OCTTODEC(mscTmp.c_str());
	if (msc != 0)
	{
		sprintf(valueMsc, "%d", msc);
		userLocInfoMation.msc = valueMsc;
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "get msc[%s] from smExt[%s].", valueMsc, userLocInfoMation.nrCellId.c_str());
	}

	lac = DCCommonIF::OCTTODEC(userLocInfoMation.tac.c_str());
	if (lac != 0)
	{
		sprintf(valueLac, "%d", lac);
		userLocInfoMation.tac = valueLac;
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "tac OCTTODEC [%s].", valueLac);
	}

	if (rATType == "EUTRA")
	{
		if (userLocInfoMation.nrCellId.length() == 9)
		{
			userLocInfoMation.nrCellId.erase(6, 1);
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "format nrCellId[%s].",  userLocInfoMation.nrCellId.c_str());
	}
	transform(userLocInfoMation.nrCellId.begin(), userLocInfoMation.nrCellId.end(), userLocInfoMation.nrCellId.begin(), ::toupper);//存在网元发来的nrCellId是小写的，后面要匹配表里的（表里都是大写）

	return 0;
}
int DCReqFmt::FormatDATA_5G(SCCRBase* base, STBizMsg* bizMsg, SCCR5GInfo* data)
{
    ocs::SUSU* MSCC = NULL;
    int lac = 0;
    int cellid = 0;
    int msc = 0;
    string mscTmp;
    string cellidTmp;
    char valueLac[128] = {0};
    char valueCell[128] = {0};
    char valueMsc[128] = {0};

    //转换多RG
    for(int i=0; i<base->MSCC.size(); i++)
    {
		MSCC = &base->MSCC[i];
		bizMsg->m_hexLocInfoTac = MSCC->userLocInfoMation.tac;
		int iusuSize = atoi(base->smExt.kv["usuSize"].c_str());
		if (iusuSize > 0 && bizMsg->m_iRollFlag == 1)
		{
			MSCC->USU0.unitTotal = MSCC->USU0.unitInput + MSCC->USU0.unitOutput; // 总流量落单由byte转成KB，导致精度差，回退重新计算总流量
			continue;
		}

		FormatUserLocation(MSCC->rATType, MSCC->userLocInfoMation);

		if (iusuSize > 0)
		{
			if (MSCC->VecUSU.size() == 0)
			{
				FormatUserLocation(MSCC->USU0.rATType, MSCC->USU0.userLocInfoMation);
				FormatUserLocation(MSCC->USU1.rATType, MSCC->USU1.userLocInfoMation);
			}
			else
			{
				for(int i = 0; i < MSCC->VecUSU.size(); i++)
				{
					FormatUserLocation(MSCC->VecUSU[i].rATType, MSCC->VecUSU[i].userLocInfoMation);
				}
			}
		}
    }

    return 0;
}

int DCReqFmt::FormatVOICE(ocs::SCCRVOICE* voice,STBizMsg* bizMsg)
{
	int ret = 0;
	char szData[128] ={0};
	TSMPara *m_smpara = (TSMPara*)bizMsg->m_smpara;

	//关键AVP存在性检查
	//判断呼叫类型
	unsigned int RERcallType				= 0;
	unsigned int CDRcallType				= 0;
	switch(voice->eventTypeBCSM)
	{
		case 2://主叫、呼转
		case 3://主叫、呼转
			{
				switch(voice->redirectionInfo)
				{
					case 0://主叫
						RERcallType = 1;
						CDRcallType = 1;
						break;
					case 1://忙
						RERcallType = 4;
						CDRcallType = 3;
						break;
					case 2://无应答
						RERcallType = 4;
						CDRcallType = 3;
						break;
					case 3://无条件
						RERcallType = 3;
						CDRcallType = 3;
						break;
					case 6://不在服务区
						RERcallType = 4;
						CDRcallType = 3;
						break;
					default:
						{
							voice->redirectionInfo = 1;
							RERcallType = 4;
							CDRcallType = 3;
						}
				}
			}
			break;
		case 12:	//被叫
			{
				if((11 == strlen(voice->calledVLR.c_str())) && ('1' == voice->calledVLR[0]))
				{
					voice->calledVLR[8]=0;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "tsdl calledVLR[%s]", voice->calledVLR.c_str());
				}
				CDRcallType = 2;
			}
			break;
		default:
			{
				if (4 == bizMsg->m_requestType)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "balance query ignore");
					break;
				}
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid eventTypeBCSM[%u]", voice->eventTypeBCSM);
				return SM_OCP_INVALID_AVP_VALUE;
			}
	}
	bizMsg->m_cdrCallType = CDRcallType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "call type[%d]\n", CDRcallType);

       // 修正值为空的但是c_str() 取值有问题的字段
       if(!voice->callingLAI.size() && voice->callingLAI.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty callingLAI[%s]\n", voice->callingLAI.c_str());
            voice->callingLAI.clear();
            voice->callingLAI.assign("");
       }

       if(!voice->callingVLR.size() && voice->callingVLR.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty callingVLR[%s]\n", voice->callingVLR.c_str());
            voice->callingVLR.clear();
            voice->callingVLR.assign("");
       }

       if(!voice->callingCellID.size() && voice->callingCellID.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty callingCellID[%s]\n", voice->callingCellID.c_str());
            voice->callingCellID.clear();
            voice->callingCellID.assign("");
       }

       if(!voice->calledLAI.size() && voice->calledLAI.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty calledLAI[%s]\n", voice->calledLAI.c_str());
            voice->calledLAI.clear();
            voice->calledLAI.assign("");
       }

       if(!voice->calledVLR.size() && voice->calledVLR.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty calledVLR[%s]\n", voice->calledVLR.c_str());
            voice->calledVLR.clear();
            voice->calledVLR.assign("");
       }

       if(!voice->calledCellID.size() && voice->calledCellID.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty calledCellID[%s]\n", voice->calledCellID.c_str());
            voice->calledCellID.clear();
            voice->calledCellID.assign("");
       }

       if(!voice->MSC.size() && voice->MSC.c_str()[0])
       {
            DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid empty MSC[%s]\n", voice->MSC.c_str());
            voice->MSC.clear();
            voice->MSC.assign("");
       }


	//特殊avp解析
	//解析avp__Calling_CellID_Or_SAI
	if(SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
	{
		char tmpCellID[32] ={0};
		strncpy(tmpCellID, voice->callingCellID.c_str(), sizeof(tmpCellID));
		if((4 == strlen(tmpCellID)) && m_smpara->GetINPara()->nCellidConvert)//高2位和低2位顺序互换,例如5C7A转换后为C5A7
		{
			string str4 = tmpCellID+3;
			tmpCellID[3]='\0';
			string str3 = tmpCellID+2;
			tmpCellID[2]='\0';
			string str2 = tmpCellID+1;
			tmpCellID[1]='\0';
			string str1= tmpCellID;

			string strNew = str2;
			strNew += str1;
			strNew += str4;
			strNew += str3;
			DCBIZLOG(DCLOG_LEVEL_TRACE, 0,bizMsg->m_sessionID, "str1[%s],str2[%s],str3[%s],str4[%s],new cellid[%s]", str1.c_str(),str2.c_str(),str3.c_str(),str4.c_str(),strNew.c_str());

			voice->callingCellID = strNew.c_str();
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,bizMsg->m_sessionID, "calling cellid[%s]", voice->callingCellID.c_str());
	}

	if(SM_SESSION_UPDATE_CODE == bizMsg->m_requestType || SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType  )
	{
		char tmpCellID[32] ={0};
		strncpy(tmpCellID, voice->calledCellID.c_str(), sizeof(tmpCellID));
		if((4 == strlen(tmpCellID)) && m_smpara->GetINPara()->nCellidConvert)//高2位和低2位顺序互换,例如5C7A转换后为C5A7
		{
			string str4 = tmpCellID+3;
			tmpCellID[3]='\0';
			string str3 = tmpCellID+2;
			tmpCellID[2]='\0';
			string str2 = tmpCellID+1;
			tmpCellID[1]='\0';
			string str1= tmpCellID;

			string strNew = str2;
			strNew += str1;
			strNew += str4;
			strNew += str3;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "str1[%s],str2[%s],str3[%s],str4[%s],new cellid[%s]", str1.c_str(),str2.c_str(),str3.c_str(),str4.c_str(),strNew.c_str());

			voice->calledCellID = strNew.c_str();
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "called cellid[%s]", voice->calledCellID.c_str());

	}

	//检查主叫
	if(voice->callingNumber.length()>0)
	{
		if(12 == voice->eventTypeBCSM && bizMsg->m_requestType == 5)
		{
			ret = NumCheck::SortNum(voice->callingNumber.c_str(), &(voice->calling),bizMsg->timestampCCR, 2);
		}
		else
			ret = NumCheck::SortNum(voice->callingNumber.c_str(), &(voice->calling), bizMsg->timestampCCR);
		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid callingNumber[%s]", voice->callingNumber.c_str());
			return SM_OCP_INVALID_AVP_VALUE;
		}
		DCCommonIF::UnifiedCallNum(voice->calling, szData);
		voice->callingUnified = szData;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "callingUnified[%s]", szData);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "missing callingNumber", "");
		return SM_OCP_MISSING_AVP;
	}


	//检查被叫
	if(voice->calledNumber.size()>0)
	{
		//被叫号码超过20位截断
		char tmpCalled[24]={0};
		strncpy(tmpCalled,voice->calledNumber.c_str(),20);

		if(voice->calledNumber.size() > 20)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "called too long[%s],cut[%s]", voice->calledNumber.c_str(),tmpCalled);
		}

		ret = NumCheck::SortNum(tmpCalled, &(voice->called), bizMsg->timestampCCR);
		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid calledNumber[%s]", voice->calledNumber.c_str());
			bizMsg->m_calledNumberIsDefaultArea = true;
		}
		else
		{
			bizMsg->m_calledNumberIsDefaultArea = NumCheck::GetDefaultFlag();
		}

		szData[0]='\0';
		DCCommonIF::UnifiedCallNum(voice->called, szData);
		voice->calledUnified = szData;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "calledUnified[%s]", szData);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "missing calledNumber", "");
		return SM_OCP_MISSING_AVP;
	}

	//检查eventTypeBCSM
	if (bizMsg->m_requestType != 4)
	{
		if(voice->eventTypeBCSM >0)
		{
			if((2 != voice->eventTypeBCSM) && (3 != voice->eventTypeBCSM) && (12 != voice->eventTypeBCSM))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "invalid eventTypeBCSM[%d]", voice->eventTypeBCSM);
				return SM_OCP_INVALID_AVP_VALUE;
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "missing eventTypeBCSM", "");
			return SM_OCP_MISSING_AVP;
		}
	}


	//检查MSC
	if(voice->MSC.size() > 0)
	{
		if(voice->MSC.size() < 2)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "invalid MSC[%s]", voice->MSC.c_str());
			return SM_OCP_INVALID_AVP_VALUE;
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "missing MSC");
	}

	// info 日志
	DCDATLOG("SM00003:%s0%d%s0%d%d", voice->calling.phone.c_str(),voice->calling.area, voice->called.phone.c_str(), voice->called.area,voice->eventTypeBCSM);
	return 0;
}

int DCReqFmt::FormatDATA(SCCRDATA *data,STBizMsg* bizMsg)
{
	int is_3GPP_User_Location_Info_Has = 0;
	char bufin[128]={0};

	//关键avp存在性检查
	//特殊avp解析
	if (data->userLocationInfo.size() > 0)
	{
		//默认非4G标示
		bizMsg->m_if4Gpp = 0;
		if(PGW == bizMsg->m_serviceContextID)
		{

			bizMsg->m_if4Gpp = 1;//标识4G

			if(5==bizMsg->m_requestType)
			{
				int len = data->userLocationInfo.size();
				if(len>0)
				{
					is_3GPP_User_Location_Info_Has = 1;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "3GPP-USER-LOCATIONINFO is null");
				}
			    map<int,string> mapFileField;
				mapFileField.clear();
			    map<int,string>::iterator iter;
				char usertac[4]={0};
				DCCommonIF::SplitCharStr(data->userLocationInfo.c_str(),'#',mapFileField);
				int nSize=mapFileField.size();
				if(nSize>2)
				{
					iter =mapFileField.find(nSize);
					if(iter!=mapFileField.end())
					{
						data->userCellid = iter->second.c_str();
					}
					iter =mapFileField.find(nSize-1);
					if(iter!=mapFileField.end())
					{

						data->userMsc = iter->second.c_str();
					}
					iter =mapFileField.find(3);
					if(iter!=mapFileField.end())
					{
						strncpy(usertac,iter->second.c_str(),sizeof(usertac)-1);
						data->userLac = iter->second.c_str();
					}

				}
			}
			else
			{
				int len = data->userLocationInfo.size();
				if(len>0)
				{
					is_3GPP_User_Location_Info_Has = 1;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "3GPP-USER-LOCATIONINFO is null");
				}
				strncpy(bufin,data->userLocationInfo.c_str(),sizeof(bufin));

				char locationbuf[64]={0};
				int lac = 0;
				int msc = 0;
				char spare[8] = {0};
				int cellid=0;
				char locationtype[8]={0};

				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userinfo[%s],meslen[%d]", data->userLocationInfo.c_str(),len);

				memcpy(locationtype, bufin, 2);//TAC
				data->userLac.assign(bufin+8, 4);

				memcpy(spare, bufin+18, 1);
				data->userMsc.assign(bufin+19, 5);
				data->userCellid.assign(bufin+24, 2);


				lac = DCCommonIF::OCTTODEC(data->userLac.c_str());
				msc = DCCommonIF::OCTTODEC(data->userMsc.c_str());
				cellid = DCCommonIF::OCTTODEC(data->userCellid.c_str());
                bool isUserInfoNULL = false;

                if((lac==0)&&(msc==0)&&(cellid ==0))
                {
                    isUserInfoNULL ==true;
                }

				bufin[0]=0;
                if(false == isUserInfoNULL)
                {
				    sprintf(bufin, "%d", lac);
                }
                data->userLac = bufin;

				bufin[0]=0;
				memset(bufin,0x00,sizeof(bufin));
                if(false == isUserInfoNULL)
                {
				    sprintf(bufin, "%d", msc);
                }
                data->userMsc = bufin;

				bufin[0]=0;
                memset(bufin,0x00,sizeof(bufin));
                if(false == isUserInfoNULL)
                {
                    sprintf(bufin, "%d", cellid);
                }
				data->userCellid = bufin;

				sprintf(locationbuf,"46011%d46011%s%d%d",lac,spare,msc,cellid);
				data->userLocationInfo=locationbuf;

			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userinfo[%s],userLac[%s],userMsc[%s],usercellid[%s]",data->userLocationInfo.c_str(), data->userLac.c_str(),data->userMsc.c_str(),data->userCellid.c_str());

		}
		else
		{
			int cellid = 0;
			int SID = 0;
			int NId = 0;
			char mscbuf[10]={0};
			mscbuf[9]='\0';

			int len = data->userLocationInfo.length();
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userlocationinfo[%s],meslen[%d]", data->userLocationInfo.c_str(),len);

			if(len >= 12)
			{
				strncpy(mscbuf, data->userLocationInfo.c_str(), 4);
				mscbuf[4] = 0;
				SID = DCCommonIF::OCTTODEC(mscbuf);

                data->userLac.assign(data->userLocationInfo,6, 2);
				NId = DCCommonIF::OCTTODEC(data->userLac.c_str());

                data->userCellid.assign(data->userLocationInfo,8, 4);
				cellid = DCCommonIF::OCTTODEC(data->userCellid.c_str());


                bool isUserInfoNULL = false;
                if((SID==0)&&(NId==0)&&(cellid ==0))
                {
                    isUserInfoNULL = true;
                }

				bufin[0]=0;
                if(false == isUserInfoNULL)
                {
				    sprintf(bufin,"%05d",SID);
                }
				data->userMsc = bufin;

                if(false == isUserInfoNULL)
                {
				    sprintf(mscbuf+strlen(mscbuf),"%03d",NId);
                }
				data->userLac = mscbuf;

				bufin[0]=0;
                if(false == isUserInfoNULL)
                {
				    sprintf(bufin,"%d",cellid);
                }

                data->userCellid = bufin;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userLac[%s],usercellid[%s],msc[%s]", data->userLac.c_str(),data->userCellid.c_str(),data->userMsc.c_str());

			}

		}
	}

	if(data->BSID.size()>0)//PGW由4G切换到3G网络
	{
		if((PGW == bizMsg->m_serviceContextID) && (0 == is_3GPP_User_Location_Info_Has))
		{
			data->userLocationInfo = data->BSID;

			int cellid = 0;
			int SID = 0;
			int NId = 0;
			char mscbuf[10]={0};
			mscbuf[9]='\0';
			int len = data->userLocationInfo.size();
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userlocationinfo[%s],meslen[%d]", data->userLocationInfo.c_str(),len);

			if(len >= 12)
			{
				strncpy(mscbuf, data->userLocationInfo.c_str(), 4);
				mscbuf[4] = 0;
				SID = DCCommonIF::OCTTODEC(mscbuf);

				data->userLac.assign(data->userLocationInfo,6, 2);
				NId = DCCommonIF::OCTTODEC(data->userLac.c_str());

                data->userCellid.assign(data->userLocationInfo,8, 4);
				cellid = DCCommonIF::OCTTODEC(data->userCellid.c_str());

                bool isUserInfoNULL = false;
                if((SID==0)&&(NId==0)&&(cellid ==0))
                {
                    isUserInfoNULL ==true;
                }

				bufin[0] = 0;
                if(false == isUserInfoNULL)
                {
				    sprintf(bufin,"%05d",SID);
                }
                data->userMsc = bufin;

                bufin[0] = 0;
                if(false == isUserInfoNULL)
                {
				    sprintf(mscbuf+strlen(mscbuf),"%03d",NId);
                }
                data->userLac=mscbuf;

				bufin[0] = 0;
                if(false == isUserInfoNULL)
                {
				    sprintf(bufin,"%d",cellid);
                }
                data->userCellid = bufin;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userLac[%s],usercellid[%s],msc[%s]", data->userLac.c_str(),data->userCellid.c_str(),data->userMsc.c_str());

			}

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "3GPP2-BSID skip,service[%d]", bizMsg->m_serviceContextID);
		}


	}

	if (data->RATType.size()>0)
	{
		if(PGW == bizMsg->m_serviceContextID)
		{
			bufin[0]=0;
			memcpy(bufin, data->RATType.c_str(), 8);

			data->RATType=bufin;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "buf[%s],RATType[%s]", bufin, data->RATType.c_str());
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RATType[%s]", data->RATType.c_str());
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RATType[%s]", data->RATType.c_str());
	}

	if (data->gppChargingID.size()>0)
	{
		if(PGW == bizMsg->m_serviceContextID)
		{
			char buf[32] = {0};

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "buf[%s],chargingid[%s]", buf, data->gppChargingID.c_str());
			long chargeid = DCCommonIF::OCTTODEC(data->gppChargingID.c_str());

			sprintf(buf, "%ld", chargeid);
			data->gppChargingID = buf;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "buf[%s],3gppChargingID[%s]",buf, data->gppChargingID.c_str());

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "3gppChargingID[%s]", data->gppChargingID.c_str());

		}
	}

	return 0;
}

int DCReqFmt::FormatSMS(ocs::SCCRSMS *sms,STBizMsg* bizMsg)
{
	int ret = 0;
	char szData[64]={0};
	//检查主叫号码
	if(sms->callingNumber.size()>0)
	{
		ret = NumCheck::SortNum(sms->callingNumber.c_str(), &(sms->calling), bizMsg->timestampCCR);
		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "invalid callingNumber[%s]", sms->callingNumber.c_str());
			return SM_OCP_INVALID_AVP_VALUE;
		}

		DCCommonIF::UnifiedCallNum(sms->calling, szData);
		sms->callingUnified = szData;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, "", "callingUnified[%s]", sms->callingUnified.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "missing callingNumber", "");
		return SM_OCP_MISSING_AVP;
	}

	//检查被叫号码
	if(sms->calledNumber.size()>0)
	{
		ret = NumCheck::SortNum(sms->calledNumber.c_str(), &(sms->called), bizMsg->timestampCCR);
		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "invalid calledNumber[%s]", sms->calledNumber.c_str());
			return SM_OCP_INVALID_AVP_VALUE;
		}
		DCCommonIF::UnifiedCallNum(sms->called, szData);
		sms->calledUnified = szData;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, "", "calledUnified[%s]", sms->calledUnified.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "missing calledUnified");
		return SM_OCP_MISSING_AVP;
	}

	//检查短信地址
	if(sms->SMSCAddress.size()>0)
	{}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "missing SMSCAddress");
		return SM_OCP_MISSING_AVP;
	}

	//检查短信ID
	if(sms->SMID.size()>0)
	{}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "missing SMID");
		return SM_OCP_MISSING_AVP;
	}

	//检查短信长度
	/*if(sms->SMLength>0)
	{}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "missing SMLength");
		return SM_OCP_MISSING_AVP;
	}*/

	// info 日志
	DCDATLOG("SM00004:%s0%d%s0%d", sms->calling.phone.c_str(),sms->calling.area, sms->called.phone.c_str(), sms->called.area);
	return 0;
}

int DCReqFmt::FormatISMP(ocs::SCCRISMP *ismp,STBizMsg* bizMsg)
{
	ocs::SCCRBase *base = (SCCRBase *)bizMsg->m_base;
	char szData[32] ={0};
	int ret = 0;
	//检查主叫号码
	if(ismp->callingNumber.size()>0)
	{
		ret = NumCheck::SortNum(ismp->callingNumber.c_str(), &(ismp->calling), bizMsg->timestampCCR);

		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "invalid UnifiedCallNum[%s]", ismp->calledNumber.c_str());
			ismp->callingUnified = ismp->callingNumber;
			ismp->calling.phone = ismp->callingNumber;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "no Unified number[%s]", ismp->callingNumber.c_str());
		}
		else
		{
			DCCommonIF::UnifiedCallNum(ismp->calling, szData);
			ismp->callingUnified = szData;
		}
	}
	else
	{
		ismp->calling=base->subscription;
		if ("" != base->subUnified)
		{
			ismp->callingUnified = base->subUnified;
		}
		else
		{
			ismp->callingUnified=ismp->calling.phone;
		}
		ismp->callingNumber =  ismp->calling.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "callintNumber[%s]", ismp->calling.phone.c_str());
	}

	//检查被叫号码
	if(ismp->calledNumber.size()>0)
	{
		ret = NumCheck::SortNum(ismp->calledNumber.c_str(), &(ismp->called), bizMsg->timestampCCR);

		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "invalid UnifiedCallNum[%s]", ismp->calledNumber.c_str());
			ismp->calledUnified = ismp->calledNumber;
			ismp->called.phone = ismp->calledNumber;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "no Unified number[%s]", ismp->calledNumber.c_str());
		}
		else
		{
			DCCommonIF::UnifiedCallNum(ismp->called, szData);
			ismp->calledUnified =szData;
		}
	}
	else
	{
		ismp->called=base->subscription;

		if ("" != base->subUnified)
		{
			ismp->calledUnified = base->subUnified;
		}
		else
		{
			ismp->calledUnified = ismp->calling.phone;
		}

		ismp->calledNumber = ismp->called.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "calledNumber[%s]", ismp->called.phone.c_str());
	}

	//ismp modify timestampCCR
	// 针对事件扣费/返款, 且没有配置productofferid 时调整扣费时间为系统时间
	TSMPara* smpara = (TSMPara*)bizMsg->m_smpara;
	if((base->requestType==4||base->requestType==5)
	   &&( base->requestAction == 0 || base->requestAction == 1)
	   && smpara->JudgeISMPModTime(ismp->productOfferID.c_str()) )
	{
		bizMsg->timestampCCR = base->timestamp;//开始时间修改为当前系统交互时间
		sprintf(szData, "%ld", bizMsg->timestampCCR);
		szData[6] = 0x0;
		bizMsg->billcycle = atoi(szData);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "modify time for product_offerid[%s], new  timestampCCR[%ld], billcycle[%ld]", \
			ismp->productOfferID.c_str(), bizMsg->timestampCCR, bizMsg->billcycle);

	}
   	// info 日志
   	DCDATLOG("SM00004:%s0%d%s0%d", ismp->calling.phone.c_str(),ismp->calling.area, ismp->called.phone.c_str(), ismp->called.area);

	return 0;

}

int DCReqFmt::FormatDSL(SCCRDSL *dsl,STBizMsg* bizMsg)
{
	//检查ProductSpecID
	if(dsl->productSpecID.size() > 0)
	{
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "missing productSpecID");
		return SM_OCP_MISSING_AVP;
	}
	/*
	//检查NASIP
	if(dsl->NASIP.size() > 0)
	{
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "missing NASIP");
		return SM_OCP_MISSING_AVP;
	}

	//检查frameIP
	if(dsl->frameIP.size() > 0)
	{
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,   bizMsg->m_sessionID, "missing frameIP");
		return SM_OCP_MISSING_AVP;
	}

	//检查userNodeID
	if(dsl->userNodeID.size() > 0)
	{
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "missing userNodeID");
		return SM_OCP_MISSING_AVP;
	}
	*/
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", " Check DSL OK ");
	return 0;

}

int DCReqFmt::FormatRATA(SCCRISMP *ismp,STBizMsg* bizMsg)
{
	int ret =0;
	char szData[64] ={0};
	SCCRBase *base = (SCCRBase *)bizMsg->m_base;

	base->subscription.phone = ismp->callingNumber;
	//检查必选AVP
	if(ismp->callingNumber.length()>0)//Acc-Nbr
	{
		ret = NumCheck::SortNum(base->subscription.phone.c_str(), &(base->subscription), bizMsg->timestampCCR);
		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "invalid UnifiedCallNum[%s]", ismp->calledNumber.c_str());
			ismp->callingUnified = ismp->callingNumber;
			ismp->calling.phone = ismp->callingNumber;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "no Unified number[%s]", ismp->callingNumber.c_str());
		}
		else
		{
			DCCommonIF::UnifiedCallNum(base->subscription, szData);
			ismp->callingUnified = szData;
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "missing Acc-Nbr", "");
		return SM_OCP_MISSING_AVP;
	}

	if(""==ismp->billCycle)	//Billing-Cycle
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "missing Billing-Cycle", "");
		return SM_OCP_MISSING_AVP;
	}
	return 0;

}

int DCReqFmt::FormatRSU(STBizMsg* bizMsg)
{
	TSMPara *m_smpara = (TSMPara*)bizMsg->m_smpara;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	long digits = 0;
	int iExponet = 0;

	if(base->RSU.duration>0)
	{
		base->RSU.RSUflag.durationFlag = 1;
	}


	base->RSU.RSUflag.moneyFlag = 1;
	digits = base->RSU.valueDigits;
	iExponet = base->RSU.Exponent;
	int para_value = m_smpara->GetCommonPara()->nRsuUsuCCmoneyUnits;//0:元;1:分;2:Exponent为-2时认为是元,Exponent为0时认为是分
	if(0 ==  para_value)//网元默认计算后的Value-Digits 为元，要转换成分给RB
	{
		digits *= 100;
	}
	else if(2 == para_value)//Exponent为-2时认为是元,Exponent为0时认为是分
	{
		if(iExponet == -2)
		{
			digits *= 100;
		}
	}
	base->RSU.money = digits*pow((double)10, (double)(iExponet));
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "RSU money[%d],para_value[%d]", base->RSU.money,para_value);


	if(base->RSU.unitTotal>0)
	{
		base->RSU.RSUflag.totalunitFlag = 1;
	}

	if(base->RSU.unitInput>0)
	{
		base->RSU.RSUflag.inputFlag = 1;
	}

	if(base->RSU.unitOutput>0)
	{
		base->RSU.RSUflag.outputFlag = 1;
	}

	return 0;
}

int DCReqFmt::FormatUSU(STBizMsg* bizMsg,SCCRDataUnit *tmpUSU)
{

	TSMPara *m_smpara = (TSMPara*)bizMsg->m_smpara;
	long digits = 0;
	int iExponet = 0;

	if(tmpUSU->duration>0)
	{
		tmpUSU->RSUflag.durationFlag = 1;
	}

	tmpUSU->RSUflag.moneyFlag = 1;
	digits = tmpUSU->valueDigits;
	iExponet = tmpUSU->Exponent;
	int para_value = m_smpara->GetCommonPara()->nRsuUsuCCmoneyUnits;//0:元;1:分;2:Exponent为-2时认为是元,Exponent为0时认为是分
	if(0 ==  para_value)//网元默认计算后的Value-Digits 为元，要转换成分给RB
	{
		digits *= 100;
	}
	else if(2 == para_value)//Exponent为-2时认为是元,Exponent为0时认为是分
	{
		if(iExponet == -2)
		{
			digits *= 100;
		}
	}

	tmpUSU->money = digits*pow((double)10, (double)(iExponet));
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "USU money[%d],para_value[%d]", tmpUSU->money,para_value);


	if(tmpUSU->unitTotal>0)
	{
		tmpUSU->RSUflag.totalunitFlag = 1;
	}

	if(tmpUSU->unitInput>0)
	{
		tmpUSU->RSUflag.inputFlag = 1;
	}

	if(tmpUSU->unitOutput>0)
	{
		tmpUSU->RSUflag.outputFlag = 1;
	}

	return RET_SUCCESS;
}

int DCReqFmt::CheckMustAVP(SCCRBase* base,STBizMsg* bizMsg)
{
	//会话ID
	if(strlen(bizMsg->m_sessionID)<= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "invalid session id[%s]", bizMsg->m_sessionID);
		return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
	}

	//源主机
	if(base->originHost.size() <=0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "invalid originHost", "");
		return SM_OCP_INVALID_AVP_VALUE;
	}


	//请求类型
	if((bizMsg->m_requestType<SM_SESSION_INITIAL_CODE) || (bizMsg->m_requestType>SM_SESSION_XDR_CODE) )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "invalid requestType", "");
		return SM_OCP_INVALID_AVP_VALUE;
	}


	//事件类型
	if(bizMsg->m_requestType == SM_SESSION_EVENT_CODE)
	{
		if(bizMsg->m_serviceContextID == RATA)
		{
			if((base->requestAction != SM_REQUESTED_ACTION_QUERY_RATABLE_1) && (base->requestAction != SM_REQUESTED_ACTION_QUERY_RATABLE_2))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "invalid requestAction", "");
				return SM_OCP_INVALID_AVP_VALUE;
			}
		}
		else if(base->requestAction>SM_REQUESTED_ACTION_CHECK)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "invalid requestAction", "");
			return SM_OCP_INVALID_AVP_VALUE;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "Check  MustAVP  OK ");
	return 0;
}

int DCReqFmt::str2type(const char* str,TSMPara *smpara)
{
	if (NULL == str)
	{
		return RET_ERROR;
	}

	char tmp[258] = {0};
	strcpy(tmp, str);
	char* type = strtok(tmp, "@");

	return smpara->GetServiceType(type);
}

int DCReqFmt::timestamp(char* buf,  time_t et)
{
	//使用线程安全函数localtime_r
	struct tm tmtt ;
	localtime_r(&et, &tmtt);
	struct tm *p = &tmtt;
	sprintf(buf, "%04d%02d%02d%02d%02d%02d", (1900+p->tm_year), (1+p->tm_mon), p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);

	return 0;
}

time_t DCReqFmt::tamptotime(unsigned long time)
{
	char buf[BIZ_TEMP_LEN_64]={0};
	char value[BIZ_TEMP_LEN_16]={0};
	struct tm tmtt;


	sprintf(buf,"%ld",time);
	strncpy(value,buf,4);
	tmtt.tm_year=atoi(value)-1900;
	memset(value,0,sizeof(value));
	strncpy(value,buf+4,2);
	tmtt.tm_mon=atoi(value)-1;
	memset(value,0,sizeof(value));
	strncpy(value,buf+6,2);
	tmtt.tm_mday=atoi(value);
	memset(value,0,sizeof(value));
	strncpy(value,buf+8,2);
	tmtt.tm_hour=atoi(value);
	memset(value,0,sizeof(value));
	strncpy(value,buf+10,2);
	tmtt.tm_min=atoi(value);
	memset(value,0,sizeof(value));
	strncpy(value,buf+12,2);
	tmtt.tm_sec=atoi(value);

	time_t et=mktime(&tmtt);

	return et+OCP_TIME_BASE;
}

int DCReqFmt::sendCCA(STBizMsg* bizMsg)
{
	char serviceFlowId[48] = {0};   //Service-Flow-Id
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = 3999;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.serial = bizMsg->m_serial;
	cca.requestType = 5;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial        = bizMsg->m_serial;
	DCCommonIF::GetServiceFlowID(serviceFlowId);
	cca.ServiceFlowID = serviceFlowId;
    cca.trace = bizMsg->m_trace_flag;

    DCSeriaEncoder m_en(ESeriaBinString);
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();

	/*DCMqProduceServer* producer = bizMsg->m_producer;
	string sendmsg = HexEncode(m_en.data(),m_en.size());

        struct timeval tmv;
       char buf[20];

       // 头部加固定16位微妙时间戳
       gettimeofday(&tmv, NULL);
       sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
       sendmsg.insert(0, buf);

	int ret = 0;
	bizMsg->m_topictype = 1;
	switch(cca.ServiceContextID)
	{
		case VOICE:
			//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), "CCAVOICE");
			ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
			break;
		case SMS:
			//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), "CCASMS");
			ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
			break;
		case DATA:
		case PGW:
		case CCG:
			//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), "CCADATA");
			ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
			break;
		case DSL:
			//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), "CCADSL");
			ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
		case ISMP:
			//ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), "CCAISMP");
			ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
			break;
		default:
			break;
	}
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Produce LockTime CCA failed, ret=%d\n", ret);
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA to MQ,msglen:%d",m_en.size());
	}	*/
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "send CCA data[%s]",bizMsg->data.c_str());

	return 	RET_OVER;
}
int DCReqFmt::judgeCutCdr(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "judge CutCdr ");

	int iMonthCDRTime = 0;
	TSMPara *m_smpara = (TSMPara*)bizMsg->m_smpara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	//判断按月截单功能是否打开
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				iMonthCDRTime = m_smpara->GetINPara()->iMonthCDRTime;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "iMonthCDRSwitch[%d]", iMonthCDRTime);
			}
			break;
		case DSL:
			{
				iMonthCDRTime = m_smpara->GetDSLPara()->iMonthDSLCDRTime;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "iMonthDSLCDRSwitch[%d]", iMonthCDRTime);
			}
			break;
		case CCG:
		case DATA:
			{
				iMonthCDRTime = m_smpara->GetPSPara()->iMonthAAACDRTime;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "iMonthCDRControl[%d]", iMonthCDRTime);
			}
			break;
		case PGW:
            {
                iMonthCDRTime = m_smpara->GetPSPara()->iMonthPGWCDRTime;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "iMonthCDRPGWControl[%d]", iMonthCDRTime);
			}
			break;
        case DATA_5G:
            {
            	//5g跨天/跨月截单本次使用需批价并计入截单之前的话单,需在 ans 流程截单.
            	/*
                iMonthCDRTime = m_smpara->Get5GPara()->iMonth5GCDRTime;
                DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "iMonthCDR5GControl[%d]", iMonthCDRTime);
                */
            }
            break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "no need CutCdr");
				return -1;
			}
			break;
	}


	//struct tm tm2;
	//time_t timestamps = tamptotime(bizMsg->timestampCCR);
	//localtime_r(&timestamps,&tm2);

	int nCdr 								= 0;
	int mday;
	int mhour;
	mday = bizMsg->timestampCCR % 100000000;
	mday = mday / 1000000;
	mhour = bizMsg->timestampCCR % 1000000;
	//mhour = bizMsg->timestampCCR / 10000;
	mhour = mhour / 10000;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "timestampCCR[%ld],day[%d], hour[%d],threshold[%d]",bizMsg->timestampCCR, mday, mhour,iMonthCDRTime);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "day[%d], hour[%d],threshold[%d]", mday, mhour,iMonthCDRTime);
	if(mday != 1 || mhour >= iMonthCDRTime)	//跨月结束会话
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "no need to CutCdr");
		return -1;
	}

	//判断是否跨月截单
	if(SM_SESSION_UPDATE_CODE == bizMsg->m_requestType || SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		SUSU MSCC;
		int ret								= 0;

		SCCRBase* base = (SCCRBase*)bizMsg->m_base;
        if(base->MSCC.size() == 0 && (bizMsg->m_serviceContextID == DATA_5G || bizMsg->m_serviceContextID == PGW || bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == DATA))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "missing MSCC", "");
			return SM_OCP_MISSING_AVP;
		}

		char ChildSessionId[BIZ_TEMP_LEN_256] 	={0};
		char value[BIZ_TEMP_LEN_64]			  	={0};
		long sessionStartTime					=0;
		long timing								=0;
		//子会话ID
		memset(ChildSessionId,0,sizeof(ChildSessionId));
        if(bizMsg->m_serviceContextID == DATA_5G || bizMsg->m_serviceContextID == DATA || bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == PGW)
		{
			sprintf(ChildSessionId, "%s", bizMsg->m_sessionID);
		}
		else
		{
			sprintf(ChildSessionId, "%s", bizMsg->m_sessionID);
		}

		switch(bizMsg->m_serviceContextID)
		{
			case VOICE:
				{
					UDBSQL* pQuery = dbm->GetSQL(Voice_GetSessionInfo);
					try
					{
						pQuery->DivTable(bizMsg->m_sessionID);
						pQuery->UnBindParam();
						pQuery->BindParam(1, ChildSessionId);
						pQuery->Execute();
						if(pQuery->Next())
						{
							pQuery->GetValue(7, value);
							timing=atol(value);
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "timing[%s]", value);

							int i = timing/100000000;
							int j = bizMsg->timestampCCR/100000000;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "i[%d], j[%d]", i,j);

							if(i != j)
							{
								nCdr = 1;
							}
						}
						else
						{
							return -1;
						}
					}
					catch(UDBException& e)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
						return RB_SM_UNABLE_TO_COMPLY;
					}

				}
				break;
			case SMS:
			case ISMP:
			case HRS:
			case RATA:
				{
					return -1;
				}
				break;
			case DSL:
				{
					UDBSQL* pQuery = dbm->GetSQL(DSL_GetSessionInfo);
					try
					{
						pQuery->DivTable(bizMsg->m_sessionID);
						pQuery->UnBindParam();
						pQuery->BindParam(1, ChildSessionId);
						pQuery->Execute();
						if(pQuery->Next())
						{
							pQuery->GetValue(9, value);
							timing=atol(value);
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "timing[%s]", value);

							int i = timing/100000000;
							int j = bizMsg->timestampCCR/100000000;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "i[%d], j[%d]", i,j);
							if(i != j)
							{
								nCdr = 1;
							}

						}
						else
						{
							return -1;
						}
					}
					catch(UDBException& e)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
				break;
			case CCG:
			case DATA:
				{
					UDBSQL* pQuery = dbm->GetSQL(DATA_CUTCDR);
					try
					{
						pQuery->DivTable(bizMsg->m_sessionID);
						pQuery->UnBindParam();
						pQuery->BindParam(1, ChildSessionId);
						pQuery->Execute();
						while(pQuery->Next())
						{
							SCCRDataUnit TUSU;
							char sessionid[128] = {0};
							pQuery->GetValue(3, value);
							timing=atol(value);
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "timing[%s]", value);

							pQuery->GetValue(4, value);
							TUSU.duration=atoi(value);

							pQuery->GetValue(5, value);
							TUSU.unitTotal=atol(value);

							pQuery->GetValue(6, value);
							TUSU.unitInput=atol(value);

							pQuery->GetValue(7, value);
							TUSU.unitOutput=atol(value);

							pQuery->GetValue(8, sessionid);

							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "",
							"rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
							sessionid,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

							pQuery->GetValue(11, value);
							TUSU.unitTotal -= atol(value);

							pQuery->GetValue(12, value);
							TUSU.unitInput -= atol(value);

							pQuery->GetValue(13, value);
							TUSU.unitOutput -= atol(value);

							pQuery->GetValue(14, value);
							TUSU.duration -= atol(value);

							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "",
							"rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
							sessionid,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

							int i = timing/100000000;
							int j = bizMsg->timestampCCR/100000000;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "i[%d], j[%d]", i,j);
							if((TUSU.unitTotal || TUSU.unitInput || TUSU.unitOutput || TUSU.duration) && i != j)
							{
								nCdr = 1;
								break;
							}
						}
					}
					catch(UDBException& e)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
				break;
			case PGW:
				{
					UDBSQL* pQuery = dbm->GetSQL(PGW_CUTCDR);
					try
					{
						pQuery->DivTable(bizMsg->m_sessionID);
						pQuery->UnBindParam();
						pQuery->BindParam(1, ChildSessionId);
						pQuery->Execute();
						while(pQuery->Next())
						{
							SCCRDataUnit TUSU;
							char sessionid[128] = {0};

							pQuery->GetValue(3, value);
							timing=atol(value);
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "timing[%s]", value);


							pQuery->GetValue(4, value);
							TUSU.duration=atoi(value);

							pQuery->GetValue(5, value);
							TUSU.unitTotal=atol(value);

							pQuery->GetValue(6, value);
							TUSU.unitInput=atol(value);

							pQuery->GetValue(7, value);
							TUSU.unitOutput=atol(value);

							pQuery->GetValue(8, sessionid);

							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "",
							"rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
							sessionid,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

							pQuery->GetValue(11, value);
							TUSU.unitTotal -= atol(value);

							pQuery->GetValue(12, value);
							TUSU.unitInput -= atol(value);

							pQuery->GetValue(13, value);
							TUSU.unitOutput -= atol(value);

							pQuery->GetValue(14, value);
							TUSU.duration -= atol(value);

							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "",
							"rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
							sessionid,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

							int i = timing/100000000;
							int j = bizMsg->timestampCCR/100000000;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "i[%d], j[%d]", i,j);
							if((TUSU.unitTotal || TUSU.unitInput || TUSU.unitOutput || TUSU.duration) && i != j)
							{
								nCdr = 1;
								break;
							}
						}
					}
					catch(UDBException& e)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
				break;
            case DATA_5G:
            /*
                {

                    UDBSQL* pQuery = dbm->GetSQL(_5G_CUTCDR);
                    try
                    {
                        pQuery->DivTable(bizMsg->m_sessionID);
                        pQuery->UnBindParam();
                        pQuery->BindParam(1, ChildSessionId);
                        pQuery->Execute();
                        while(pQuery->Next())
                        {
                            SCCRDataUnit TUSU;
                            char sessionid[128] = {0};

                            pQuery->GetValue(3, value);
                            timing=atol(value);
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "timing[%s]", value);


                            pQuery->GetValue(4, value);
                            TUSU.duration=atoi(value);

                            pQuery->GetValue(5, value);
                            TUSU.unitTotal=atol(value);

                            pQuery->GetValue(6, value);
                            TUSU.unitInput=atol(value);

                            pQuery->GetValue(7, value);
                            TUSU.unitOutput=atol(value);

                            pQuery->GetValue(8, sessionid);

                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "",
                            "rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
                            sessionid,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

                            pQuery->GetValue(11, value);
                            TUSU.unitTotal -= atol(value);

                            pQuery->GetValue(12, value);
                            TUSU.unitInput -= atol(value);

                            pQuery->GetValue(13, value);
                            TUSU.unitOutput -= atol(value);

                            pQuery->GetValue(14, value);
                            TUSU.duration -= atol(value);

                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "",
                            "rg[%s]:out[%ld], in[%ld], total[%ld], duration[%d]",
                            sessionid,TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

                            int i = timing/100000000;
                            int j = bizMsg->timestampCCR/100000000;
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "i[%d], j[%d]", i,j);
                            if((TUSU.unitTotal || TUSU.unitInput || TUSU.unitOutput || TUSU.duration) && i != j)
                            {
                                nCdr = 1;
                                break;
                            }
                        }
                    }
                    catch(UDBException& e)
                    {
                        DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
                        return RB_SM_UNABLE_TO_COMPLY;
                    }
                }
                */
                break;
			default:
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "send to ept[%d]", ret);
					bizMsg->m_resultcode = SM_OCP_INVALID_AVP_VALUE;
					return SM_OCP_INVALID_AVP_VALUE;
				}

				break;
		}
		if(nCdr)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "close session", "");
			bizMsg->m_longCDR = 3;
			return RET_CDR;

		}
	}
	return RET_SUCCESS;
}

int DCReqFmt::GetAreaCode(SPhone & phone, STBizMsg * bizMsg)
{
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;
	if(!m_smpara->GetCutArea(phone.area) || !m_smpara->GetCommonPara()->iLatnRelQueryControl)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "NOAreaCode[%d]", phone.area);
		return 0;
	}
	char value[8] = {0};

	//根据用户号码在TB_NBR_LATN_REL中查询实际的归属区号
	UDBSQL *pQuery = dbm->GetSQL(COM_USER_SELECT_TB_NBR_LATN_REL);
	try
	{
		pQuery->UnBindParam();
		pQuery->BindParam(1, phone.phone);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			phone.area = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "real area[%d]", phone.area);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "not found area by phone[%s]", phone.phone.c_str());
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,	"", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return 0;
}

bool DCReqFmt::IfOtherProvinces(STBizMsg* bizMsg,string gsmCode)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "DCReqFmt::IfOtherProvinces begin GsmCode[%s]",gsmCode.c_str());
	if(gsmCode.length() > 16)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", " GsmCode[%s] is too long.",gsmCode.c_str());
	}
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;
	char tmpGsmCode [16] = {0};
	strcpy(tmpGsmCode,gsmCode.c_str());
	int i = strlen(tmpGsmCode);
	int latnid = 0;
	//PAR_GSM_CODE_ALL
	UDBSQL* query = dbm->GetSQL("q_par_gsm_code_all");
	try
	{
		while(i > 0)
		{
			query->UnBindParam();
			query->BindParam(1,atol(tmpGsmCode));
			query->Execute();
			if(query->Next())
			{
				query->GetValue(5, latnid);
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "find latnid[%d] by gsmcode[%s]",latnid,tmpGsmCode);
				query->Close();
				break;
			}
			else
			{
				i--;
				tmpGsmCode[i] = '\0';
			}
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load gsm_code failed: [%s]", e.ToString());
		return false;
	}
	if(888 == latnid)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","m_serviceContextID == PGW is other provinces phone seed cca.");
		return true;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "DCReqFmt::IfOtherProvinces end.");
	return false;
}

int DCReqFmt::sendCCAOffLine(STBizMsg* bizMsg)
{
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = 4011;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.serial = bizMsg->m_serial;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial        = bizMsg->m_serial;
    cca.trace = bizMsg->m_trace_flag;
	cca.msgType = 99;
    DCSeriaEncoder m_en(ESeriaBinString);
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","DCReqFmt::sendCCAOffLine encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA data offline[%s]",bizMsg->data.c_str());

	return 	OFF_LINE;
}


