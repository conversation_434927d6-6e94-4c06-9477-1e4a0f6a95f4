include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include

ROAM_INC=$(PWD)/include
ROAM_SRC=$(PWD)/src
ROAM_OBJ=$(PWD)/obj

ROAM_CPP=DCRoamDealFlow.cpp DCRoamJudge.cpp desc_RoamJudge.cpp
           
ROAM_SRCS=$(addprefix $(ROAM_SRC)/, $(ROAM_CPP))
ROAM_OBJS=$(patsubst $(ROAM_SRC)/%.cpp, $(ROAM_OBJ)/%.o, $(ROAM_SRCS))

CFLAGS += -std=c++11

TLIB= $(RELEASE_PATH)/plugin/libRoamJudge.so

INCLUDE =-I$(ROAM_INC) \
				-I$(COMMON_INC) \
				-I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
         -I$(DFM_INC_PATH) 

LIBPATH= -L$(RELEASE_PATH)/lib  
LIBSLIST=-lCommonIF 

libtarget=$(TLIB)

tmpvar:=$(call CreateDir, $(ROAM_OBJ))
.PHONY:all clean dup

all:$(TLIB)	
$(TLIB): $(ROAM_OBJS)
	@echo "build libRoamJudge.so----"
	$(CC) $(DFLAGS)  -o $(TLIB) $(ROAM_OBJS) $(LIBPATH) $(LIBSLIST)
$(ROAM_OBJS):$(ROAM_OBJ)/%.o:$(ROAM_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	
$(ROAM_SRC)/desc_RoamJudge.cpp:$(ROAM_SRC)/desc_RoamJudge.clog
	$(TOOL)/clogtool -i $< -o $@

clean:
	@rm -rf $(ROAM_OBJS) $(TLIB)
       
dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
	