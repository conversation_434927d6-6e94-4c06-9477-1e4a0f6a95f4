#ifndef _MD5_H_
#define _MD5_H_

#include <string>

/* Type define */
typedef unsigned char byte;
typedef unsigned int uint32;


/* MD5 declaration. */
class MD5 {
public:
	MD5();
	std::string MD5SUM(const void* input, size_t length);
	std::string MD5SUM(const std::string& str);
private:
	void update(const byte* input, size_t length);
	void final();
	void transform(const byte block[64]);
	void MD5Encode(const uint32* input, byte* output, size_t length);
	void MD5Decode(const byte* input, uint32* output, size_t length);
	std::string bytesToHexString(const byte* input, size_t length);
	const byte* digest();
	/* class uncopyable */
	MD5(const MD5&);
	std::string toString();
	void reset();
	MD5& operator=(const MD5&);

private:
	uint32 _state[4];	/* state (ABCD) */
	uint32 _count[2];	/* number of bits, modulo 2^64 (low-order word first) */
	byte _buffer[64];	/* input buffer */
	byte _digest[16];	/* message digest */
	bool _finished;		/* calculate finished ? */

	static const byte PADDING[64];	/* padding for calculate */
	static const char HEX[16];
	enum { BUFFER_SIZE = 1024 };
};

#endif /*MD5_H*/
