#include "DCAnsPara.h"
#include "DCLogMacro.h"


DCAnsPara::DCAnsPara()
{
	m_para[0] = new AnsSMPARA();
	m_para[1] = new AnsSMPARA();
	m_dbm = NULL;
}

DCAnsPara::~DCAnsPara()
{

}

int DCAnsPara::init(DCDBManer* dbm)
{
	m_dbm = dbm;
	m_sql.m_pSELECT_SM_SYSTEM_PARAMETER = dbm->GetSQL("q_sm_system_parameter");
	m_sql.m_pSELECT_SM_RESULTCODE_MAP = dbm->GetSQL("q_sm_resultcode_map");
	//m_sql.m_pSELECT_SM_AOC_GLOBAL_CFG = dbm->GetSQL("q_sm_aoc_global_cfg");
	return 0;
}


int DCAnsPara::work(EBDIDX idx)
{
	m_pSMPARA = m_para[idx] ;

	int ret = 0;

	ret = LoadCommonPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadCommonPara failed [%d]",ret);
		return ret;
	}

	ret = LoadINPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadINPara failed [%d]",ret);
		return ret;
	}

	ret = LoadPSPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadPSPara failed [%d]",ret);
		return ret;
	}

	ret = Load5GPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Load5GPara failed [%d]",ret);
		return ret;
	}
	ret = LoadISMPPara();
	if(ret != 0)
	{

		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadISMPPara failed [%d]",ret);
		return ret;
	}

	ret = LoadDSLPara();
	if(ret != 0)
	{

		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadDSLPara failed [%d]",ret);
		return ret;
	}

	ret = LoadResultCodeMap();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadResultCodeMap failed [%d]",ret);
		return -1;
	}
	/*
	ret = LoadAocPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadAccess failed [%d]",ret);
		return -1;
	}
	*/

	LodeSizeComp();

	return 0;
}

int DCAnsPara::LodeSizeComp()
{
	//SM_RESULTCODE_MAP
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SM_RESULTCODE_MAP size[%d]",m_pSMPARA->m_result_code_map.size());

	//SM_AOC_GLOBAL_CFG
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SM_AOC_GLOBAL_CFG size[%d]",m_pSMPARA->m_aoc_para_map.size());

	return 0;
}

void DCAnsPara::clear(EBDIDX idx)
{
	AnsSMPARA* m_pSMCLEAN = m_para[idx];

	map<int, SResultCode*>::iterator iter20 = m_pSMCLEAN->m_result_code_map.begin();
	while(iter20!=m_pSMCLEAN->m_result_code_map.end())
	{
		SResultCode* pInfo = iter20->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter20++;
	}
	m_pSMCLEAN->m_result_code_map.clear();

	map<int, SAocPara*>::iterator iter5 = m_pSMCLEAN->m_aoc_para_map.begin();
	while(iter5!=m_pSMCLEAN->m_aoc_para_map.end())
	{
		SAocPara* pInfo = iter5->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter5++;
	}
	m_pSMCLEAN->m_aoc_para_map.clear();

}

int DCAnsPara::LoadSystemPara(const char *paraGroup, const char *paraKey, SSystemPara *systemPara)
{
	m_sql.m_pSELECT_SM_SYSTEM_PARAMETER = m_dbm->GetSQL("q_sm_system_parameter");
	UDBSQL* query = m_sql.m_pSELECT_SM_SYSTEM_PARAMETER;
	try
	{
		query->UnBindParam();
		query->BindParam(1, paraGroup);
		query->BindParam(2, paraKey);
		query->Execute();

      	if(query->Next())
      	{
			query->GetValue(1, systemPara->PARA_VALUE);
			sprintf(systemPara->PARA_GROUP, "%s", paraGroup);
			sprintf(systemPara->PARA_KEY, "%s", paraKey);
			DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "load system parameter :group[%s], key[%s], value[%s]", paraGroup, paraKey, systemPara->PARA_VALUE);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-2, "", "load system parameter failed:group[%s], key[%s]", paraGroup, paraKey);
			return -2;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load system parameter failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}


int DCAnsPara::LoadCommonPara()
{
	SSystemPara systemPara;
	string strParaValue;
	int ret = 0;

	if((ret=LoadSystemPara("CM.COMMON", "ONLY_SUB_FLAG", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.onlySubFlag = atoi(systemPara.PARA_VALUE);



	if((ret=LoadSystemPara("CM.COMMON", "ONLINE_SESSION", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.onlineSession = atoi(systemPara.PARA_VALUE);



	if((ret=LoadSystemPara("CM.COMMON", "TRANSLATE_DATA_CARD", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.isTranslateDataCard = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "TRACE_NUM_ONFF", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.isTraceNumOnff = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("CM.COMMON", "EVENT_ISMP_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.EventISMPSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "EVENT_SMS_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.EventSMSSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "TIME_OUT_VALUE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.TIMEOUTVALUE = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_FENCE", &systemPara)) != 0)
	{
		return ret;
	}
	/*注意字符char 类型*/
	m_pSMPARA->m_commonPara.CdrFence = systemPara.PARA_VALUE[0];

	if((ret=LoadSystemPara("CM.COMMON", "SMS_SEND_NBR", &systemPara)) != 0)
	{
		return ret;
	}
	for(int i=0; i<strlen(systemPara.PARA_VALUE); i++)
	{
		if(!( systemPara.PARA_VALUE[i] >= '0' && systemPara.PARA_VALUE[i] <= '9' ))
		{
			return -1;
		}
	}
	strncpy(m_pSMPARA->m_commonPara.SmsSendNo, systemPara.PARA_VALUE, sizeof(m_pSMPARA->m_commonPara.SmsSendNo));

	if((ret=LoadSystemPara("CM.COMMON", "SMS_SEND_NBR_AOC", &systemPara)) != 0)
	{
		return ret;
	}
	for(int i=0; i<strlen(systemPara.PARA_VALUE); i++)
	{
		if(!( systemPara.PARA_VALUE[i] >= '0' && systemPara.PARA_VALUE[i] <= '9' ))
		{
			return -1;
		}
	}
	strncpy(m_pSMPARA->m_commonPara.SmsExperienceSendNo, systemPara.PARA_VALUE, sizeof(m_pSMPARA->m_commonPara.SmsExperienceSendNo));

	if((ret=LoadSystemPara("CM.COMMON", "CDR_INFO_END_CHAR", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.CdrEndchar = (1 == atoi(systemPara.PARA_VALUE)?';':0);

	if((ret=LoadSystemPara("CM.COMMON", "INVALID_MSISDN_DEFAULT_AREA", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.invalidMsisdnArea = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_TARIFFID", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.CdrTariffid = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "BALANCE_INFO_TYPE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.BalanceInfoType = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.BalanceInfoType = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "INVALID_MSISDN_DEAL", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.invalidMsisdnArea = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.invalidMsisdnDeal = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "AOC_TEMPLATE_MINIT", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nAocTemplateMinit = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.nAocTemplateMinit = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "AOC_TEMPLATE_TOTAL_BYTE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nAocTemplateTotalByte = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.nAocTemplateTotalByte = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "RSU_USU_CCMONEY_UNITS", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nRsuUsuCCmoneyUnits = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.nRsuUsuCCmoneyUnits = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "GSU_CCMONEY_EXPONENT", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nGsuCCmoneyExponent = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nGsuCCmoneyExponent = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "GSU_COST_INFORMATION_EXPONENT", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nGsuCostInformationExponent = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nGsuCostInformationExponent = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "ASR_MAX_SECOND", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.SessionTimeOutMaxMessageForEachSecond = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "RELEASE_SESSION_FLAG", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nReleaseFlag = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nReleaseFlag = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "TB_NBR_LATN_REL_QUERY_CONTROL", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iLatnRelQueryControl = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "SEND_EPT_RBR_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iSendEPTRBROnOff = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_VERSION_TYPE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iCdrVersionType = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "IN_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.INAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.INAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "P2PSMS_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.P2PSMSAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.P2PSMSAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "PS_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.PSAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.PSAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "5G_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara._5GAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara._5GAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "ISMP_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.ISMPAuthUserResultCode= 0;	//默认为0
	}
	else
	{
		m_pSMPARA->m_commonPara.ISMPAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "DSL_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.DSLAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.DSLAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "FREE_ADD_CDR", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nFreeAddCdr = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nFreeAddCdr = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "DSL_MONTH_CDR_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iDslMonthCDRSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "UPDATE_USER_STATE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iUpdateUserStateSwitch= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "SUB_AREA_CONTROL", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.isubAreaControl= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "PRICE_PLAN_ID_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iPricePlanID = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "COMMON_CONTEXTID", &systemPara)) != 0)
	{
		return ret;
	}
	std::string strTmpServContextId = systemPara.PARA_VALUE;
	ParseString(strTmpServContextId, m_pSMPARA->m_commonPara.vecServContextId, ",");

	if((ret=LoadSystemPara("CM.COMMON", "SEND_EPT_RBR_SWITCH_PGW", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iSendEPTRBROnOffPGW = atoi(systemPara.PARA_VALUE);

    if((ret=LoadSystemPara("CM.COMMON", "SEND_EPT_RBR_SWITCH_5G", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iSendEPTRBROnOff5G = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "COMMON_AOC_TYPE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iAocType = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "AOC_MSG_TYPE", &systemPara)) != 0)
	{
		return ret;
	}
	strParaValue = systemPara.PARA_VALUE;
	ParseString(strParaValue, m_pSMPARA->Vec_Aoc_Message_type, ",");

	if((ret=LoadSystemPara("CM.COMMON", "AOC_OFR_ID", &systemPara)) != 0)
	{
		return ret;
	}
	strParaValue = systemPara.PARA_VALUE;
	ParseString(strParaValue, m_pSMPARA->Vec_Aoc_Ofr_Id, ",");

	if((ret=LoadSystemPara("CM.COMMON", "AAA_MIN_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iAAAMinTime= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "BATCH_ID_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iBatchIdTime= atoi(systemPara.PARA_VALUE);

	return 0;
}


int DCAnsPara::LoadINPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_LONG_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.longCDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_IMSI_CODE", &systemPara)) != 0)
	{
	     return ret;
	}
	m_pSMPARA->m_INPara.iSMIfalg= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_TIME_CITY", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRTimeCity = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_TIME_ROAM", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRTimeRoam = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_FAV_CELLID_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.favCellIDSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "EXPIRE_REMIND_IN", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.remindDay = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROAM_MSC_VLR", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.roamMSCorVLR = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROAM_CELLID_LAC", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.roamCELLIDorLAC = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROAM_COLLIGATE_MSC", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_INPara.roamColligateMSC, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "DEFAULT_ROAM_PROVINCE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.defaultProv = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "DEFAULT_ROAM_AREA", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.defaultArea = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "EDGE_ROAM_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.edgeRoamSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_MAX_STEP_LTH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.INMaxStepLth = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "AOC_BALANCE_GSU", &systemPara)) != 0)
	{
		return ret;
	}
	else
	{
		m_pSMPARA->m_INPara.balanceGsu = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_COUNTRY", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRCountry = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_PREFERENTIAL_VILLAGE_RESULT_CODE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.resultCode = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "OUT_ORG_CELLID_OR_SAI", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.isOrgCellIdOrSai = 0;
	}
	else
	{
		m_pSMPARA->m_INPara.isOrgCellIdOrSai = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "INIT_ROMAN_CALLED", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.initRoma = 0;
	}
	else
	{
		m_pSMPARA->m_INPara.initRoma = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROMAN_LONG_RELATION", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.nRomaLongRelation = 1;
	}
	else
	{
		m_pSMPARA->m_INPara.nRomaLongRelation = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "INIT_ACTIVE_SWITCH", &systemPara)) != 0)
	{
		return -1;
	}
	m_pSMPARA->m_INPara.iInitActiveSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "CELLID_CONVERT", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.nCellidConvert = 0;;
	}
	else
	{
		m_pSMPARA->m_INPara.nCellidConvert = atoi(systemPara.PARA_VALUE);
	}
	if((ret=LoadSystemPara("SM.IN.CONFIG", "VOLTE_CDR_FLAG", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.volteCdrFalg = 0;;
	}
	else
	{
		m_pSMPARA->m_INPara.volteCdrFalg = atoi(systemPara.PARA_VALUE);
	}

	return 0;
}

int DCAnsPara::Load5GPara()
{
	SSystemPara systemPara;
	int ret = 0;

    
    if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_LONG_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_5GPara.longCDRTime5G = atoi(systemPara.PARA_VALUE);
    
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_LONG_CDR_VOLUMN", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_5GPara.longCDRVolumn5G = atoi(systemPara.PARA_VALUE);
	 if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SERVICE_CONTEX_ID", &systemPara)) != 0)
    {
        return ret;
    }
    strncpy(m_pSMPARA->m_5GPara.szServiceContextId5G,systemPara.PARA_VALUE,128);
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SHORT_CDR_OCTET", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.shortCDROctet5G = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.shortCDROctet5G = atoi(systemPara.PARA_VALUE);
	}


	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SHORT_CDR_TIME", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.shortCDRTime5G = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.shortCDRTime5G = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_DAY_CUT_CDR", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.iDay5GCDRTime = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.iDay5GCDRTime = atoi(systemPara.PARA_VALUE);
	}
	
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_MONTH_CUT_CDR", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.iMonth5GCDRTime = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.iMonth5GCDRTime = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_CNPLMN", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.str5GCNPLMN.clear();
	}
	else
	{
		m_pSMPARA->m_5GPara.str5GCNPLMN = systemPara.PARA_VALUE;
	}

	if(LoadSystemPara("SM.5G.CONFIG", "5G_filter_condition", &systemPara) != 0){
		m_pSMPARA->m_5GPara.iRgFilterSwitch = 0;
	}
    else{
		m_pSMPARA->m_5GPara.iRgFilterSwitch = atoi(systemPara.PARA_VALUE);
    }

	return 0;
}

int DCAnsPara::LoadPSPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_LONG_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.longCDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_LONG_CDR_VOLUMN", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.longCDRVolumn = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_SHORT_CDR_OCTET", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.shortCDROctet = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "FINAL_UNIT_ACTION", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.finalUnitAction = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_ADDRESS_TYPE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.redirectType = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_MAX_STEP_AMOUNT", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.PSMaxStepAmount = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_MAX_STEP_DURATION", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.PSMaxStepDuration = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_SERVER_ADDRESS", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.redirectServer, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "ACCUMULATE_INFO", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.accumulateinfo, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_SERVER_ADDRESS_RECHARGE", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szRedirectServerRecharge, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_SERVER_ADDRESS_LOCK", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szRedirectServerLock, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "RECHARGE_PAGE_RG", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.lRechargePageRG = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "BASIC_STATE_RECHARGE", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szBasicStateRecharge, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "BASIC_STATE_LOCK", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szBasicStateLock, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_RATING_GROUP", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.cdrRatingGroupSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "OFFLINE_ALL_USU", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.nOfflineAllUsu = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_RG_UNIT_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iRGUNITSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_ACCU_UNIT_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iAccuUNITSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "RAT_TYPE_SWITCH", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.nRatTypeSwitch = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.nRatTypeSwitch = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.PS.CONFIG", "FINAL_UNIT_RESULT_CODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.nFinalUintResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.nFinalUintResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.PS.CONFIG", "USU_OVERLOAD_REFUSE", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.nUsuOverloadRefuse = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.nUsuOverloadRefuse = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_VISIT_AREA", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iPsVisitArea= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "LTE_SERVICE_CONTEX_ID", &systemPara)) != 0)
	{
		return ret;
	}
	strncpy(m_pSMPARA->m_PSPara.szServiceContextId,systemPara.PARA_VALUE,128);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "LTE_SERVICE_CONTEX_ID", &systemPara)) != 0)
	{
		return ret;
	}
	strncpy(m_pSMPARA->m_PSPara.szServiceContextId,systemPara.PARA_VALUE,128);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_RATING_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
   m_pSMPARA->m_PSPara.iCdrRatingSwitch = atoi(systemPara.PARA_VALUE);

    if((ret=LoadSystemPara("SM.PS.CONFIG", "PGW_LOC_CHANGE_CELLID", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.iLocChCellid = 1;
	}
    else
   	{
   		m_pSMPARA->m_PSPara.iLocChCellid = atoi(systemPara.PARA_VALUE);
   	}

	return 0;
}

int DCAnsPara::LoadISMPPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.ISMP.CONFIG", "ISMP_DEBIT_CONTROL", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_ISMPPara.debitControl = atoi(systemPara.PARA_VALUE);
	if(m_pSMPARA->m_ISMPPara.debitControl<0 || m_pSMPARA->m_ISMPPara.debitControl>3 )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", , "error ISMP_DEBIT_CONTROL[%d]", m_pSMPARA->m_ISMPPara.debitControl);
		return ret;
	}


	//完成加载处理特殊spid的值
	if((ret=LoadSystemPara("SM.ISMP.CONFIG", "ISMP_DEBIT_SPC_SPID", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_ISMPPara.sp_id, systemPara.PARA_VALUE);

	return 0;
}

int DCAnsPara::LoadDSLPara()
{
	SSystemPara systemPara;
	int ret = 0;

	if((ret=LoadSystemPara("SM.DSL.CONFIG", "DSL_LONG_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	else
	{
		m_pSMPARA->m_DSLPara.iDslLongCdrTime = atoi(systemPara.PARA_VALUE);
	}
	return 0;
}


int DCAnsPara::LoadResultCodeMap()
{
	char tmp[256] = {0};
	long curData	= 0;
	SResultCode *resultCode = NULL;
	m_sql.m_pSELECT_SM_RESULTCODE_MAP = m_dbm->GetSQL("q_sm_resultcode_map");
	UDBSQL* query = m_sql.m_pSELECT_SM_RESULTCODE_MAP;

	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;

	try
	{
		query->UnBindParam();
		query->Execute();

  		while(query->Next())
  		{
			resultCode = new SResultCode;

			query->GetValue(1, tmp);
			resultCode->resultCode = atoi(tmp);

			query->GetValue(2, tmp);
			resultCode->OCPResultCode = atoi(tmp);

			query->GetValue(3, tmp);
			resultCode->codeType = atoi(tmp);

			pair<map<int, SResultCode*>::iterator, bool> iter;

			iter = m_pSMPARA->m_result_code_map.insert(pair<int, SResultCode*>(resultCode->resultCode, resultCode));
			if(!(iter.second))
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "same result code value: [%d]", resultCode->resultCode);
			}
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load result code map failed: [%s]", e.ToString());
		return -1;
	}

	if(m_pSMPARA->m_result_code_map.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "result code map size is :[0]");
		return -1;
	}

	return 0;
}
/*
int DCAnsPara::LoadAocPara()
{
	char tmp[256] = {0};

	UDBSQL* query = m_sql.m_pSELECT_SM_AOC_GLOBAL_CFG;

	SAocPara *aocPara = NULL;
	int index = 0;

	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			aocPara = new SAocPara;
			query->GetValue(1, tmp);
			aocPara->serviceType = atoi(tmp);

			query->GetValue(2, tmp);
			aocPara->networkType= atoi(tmp);

			query->GetValue(3, tmp);
			aocPara->reqType= atoi(tmp);

			query->GetValue(4, tmp);
			aocPara->callType= atoi(tmp);

			query->GetValue(5, tmp);
			aocPara->isAocLowBalance= atoi(tmp);

			query->GetValue(6, tmp);
			aocPara->aocLowBalance= atoi(tmp);

			index = aocPara->serviceType*100 + aocPara->reqType*10 + aocPara->callType;

			// DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "load aoc config:service[%d], reqype[%d], calltype[%d],isaoclowbalance[%d],index[%d]", aocPara->serviceType, aocPara->reqType, aocPara->callType,aocPara->isAocLowBalance,index);

			pair<map<int, SAocPara*>::iterator, bool> iter;
			iter = m_pSMPARA->m_aoc_para_map.insert(pair<int, SAocPara *>(index, aocPara));
			if(!(iter.second))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"", "same aoc service type value: [%d]", aocPara->serviceType);
				return -1;
			}
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "load aoc parameter failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}
*/

SCommonPara *DCAnsPara::GetCommonPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();

	return &pSMPARA->m_commonPara;
}


SINPara * DCAnsPara::GetINPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();

	return &pSMPARA->m_INPara;
}


SPSPara * DCAnsPara::GetPSPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
	return &pSMPARA->m_PSPara;
}

S5GPara * DCAnsPara::Get5GPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
	return &pSMPARA->m_5GPara;
}

SISMPPara* DCAnsPara::GetISMPPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
	return &pSMPARA->m_ISMPPara;
}

SP2PSMSPara * DCAnsPara::GetP2PSMSPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
	return &pSMPARA->m_P2PSMSPara;
}

SDSLPara * DCAnsPara::GetDSLPara()
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();

	return &pSMPARA->m_DSLPara;
}




int DCAnsPara::GetOCPResultCode(int resultCode)
{
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();

	map<int, SResultCode*>::iterator iter = pSMPARA->m_result_code_map.find(resultCode);

	if(iter != pSMPARA->m_result_code_map.end())
	{
		return (iter->second)->OCPResultCode;
	}
	else
	{
		return -1;
	}
}


 bool DCAnsPara::IsCfgServContexID(const char * pServiceContextID)
 {
	 AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
	 std::vector<std::string> vecTmpServContextID(pSMPARA->m_commonPara.vecServContextId);
	 if ( 0 == vecTmpServContextID.size() || 0 == strlen(pServiceContextID) )
	 {
		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "not config  ServiceContextID or ServiceContextID is  null");
		 return false;
	 }

	 for(int i=0; i<vecTmpServContextID.size(); ++i)
	 {
		 if( !strcmp(vecTmpServContextID[i].c_str(),pServiceContextID) )
		 {
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", \
			 "ServiceContextID[%s] is  Configed", pServiceContextID);
			 return true;
		 }
	 }
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", \
	 "ServiceContextID[%s] is not Config", pServiceContextID);
	 return false;
 }

 int DCAnsPara::IsAoc_Message_Type(int message_type)
 {

   AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
   vector<int> vecTmp(pSMPARA->Vec_Aoc_Message_type);
   if ( 0 == vecTmp.size())
   {
	  DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", \
	   "not config	Vec_Aoc_Message_type  is  null");
	   return -1;
   }
	  for(int i=0; i<vecTmp.size(); ++i)
   {
	   if( vecTmp[i] ==  message_type)
	   {
		   DCBIZLOG(DCLOG_LEVEL_DEBUG ,SM_OTHER_TYPE,"", \
		   "Aoc_Message_Type[%d] is  Configed", message_type);
		   return 0;
	   }
   }
   DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", \
   "Aoc_Message_Type[%d] is not Config", message_type);
   return -1;
 }

 int DCAnsPara::IsAoc_Ofr_ID(int ofr_id)
 {
	AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();
	if(0==pSMPARA->Vec_Aoc_Ofr_Id.size())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE,"", \
		"not config	AOC_OFR_ID");
		return -1;
	}
	vector<int> vecTmp(pSMPARA->Vec_Aoc_Ofr_Id);
	if ( 0 == vecTmp.size())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", \
		"not config	Vec_Aoc_Message_type or value is  null");
		return -1;
	}
	for(int i=0; i<vecTmp.size(); ++i)
	{
		if( vecTmp[i] == ofr_id )
		{
		   DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", \
		   "Aoc_Message_Type[%d] is  Configed", ofr_id);
		   return 0;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", \
	"Aoc_Message_Type[%d] is not Config", ofr_id);
	return -1;
 }
 /*
  SAocPara * DCAnsPara::GetAocPara(int serviceType, int reqType, int callType)
  {
	  AnsSMPARA *pSMPARA = (AnsSMPARA*)BData::data();

	  int index = serviceType*100 + reqType*10 + callType;
	  DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "service type:%d,calltype:%d,reqtype:%d,index [%d]",
	  serviceType,callType,reqType,index);

	  map<int, SAocPara *>::iterator iter = pSMPARA->m_aoc_para_map.find(index);
	  if(iter == pSMPARA->m_aoc_para_map.end())
	  {
		  DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no aoc config[%d]", index);
		  return NULL;
	  }
	  else
	  {
		  return iter->second;
	  }

	  return NULL;
  }
  */

 void DCAnsPara::ParseString(string& szSourString,vector<string> &vecDestString,const char* szSeparator)
 {
	 string::size_type pos = 0, prev_pos = 0;
	 int nCounter = 1;

	 vecDestString.clear();

	 while (( pos = szSourString.find(szSeparator , pos ))
		 != string::npos)
	 {
		 vecDestString.push_back( szSourString.substr( prev_pos, pos - prev_pos ) );

		 prev_pos = ++pos;
		 ++nCounter;
	 }

	 vecDestString.push_back( szSourString.substr( prev_pos, pos - prev_pos ) );

	 return ;
 }

 void DCAnsPara::ParseString(string& szSourString,vector<int> &vecDest,const char* szSeparator)
 {
	 string::size_type pos = 0, prev_pos = 0;
	 int nCounter = 1;

	 vecDest.clear();

	 while (( pos = szSourString.find(szSeparator , pos ))
		 != string::npos)
	 {
		 if(pos!=prev_pos)
		 {
			 vecDest.push_back( atoi(szSourString.substr( prev_pos, pos - prev_pos ).c_str()) );
		 }

		 prev_pos = ++pos;
		 ++nCounter;
	 }
	 if(pos != prev_pos)
	 {
		 vecDest.push_back( atoi(szSourString.substr( prev_pos, pos - prev_pos ).c_str()) );
	 }
 }



