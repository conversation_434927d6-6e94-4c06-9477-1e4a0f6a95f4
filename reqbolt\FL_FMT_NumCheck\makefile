include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include

PLUGINNUM_INC=$(PWD)/include
PLUGINNUM_SRC=$(PWD)/src
PLUGINNUM_OBJ=$(PWD)/obj

PLUGINNUM_CPP=DCNumCheckFlow.cpp DCReqFmt.cpp desc_NumCheck.cpp
           
PLUGINNUM_SRCS=$(addprefix $(PLUGINNUM_SRC)/, $(PLUGINNUM_CPP))
PLUGINNUM_OBJS=$(patsubst $(PLUGINNUM_SRC)/%.cpp, $(PLUGINNUM_OBJ)/%.o, $(PLUGINNUM_SRCS))

TLIB= $(RELEASE_PATH)/plugin/libPluginMsgFmt.so

INCLUDE =-I$(PLUGINNUM_INC) \
				-I$(COMMON_INC) \
				-I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
         -I$(DFM_INC_PATH) \
         -I$(MQ)/include \
		 -I$(TXML)/include \
		 -I$(CTG_CHECK)/inc

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(CTG_CHECK_LIB) -L$(TXML)/lib
LIBSLIST= -lCommonIF -lCtgCheckSDK -ltinyxml

CFLAGS += -std=c++11

libtarget=$(TLIB)

tmpvar:=$(call CreateDir, $(PLUGINNUM_OBJ))
.PHONY:all clean dup

all:$(TLIB)	
$(TLIB): $(PLUGINNUM_OBJS)
	@echo "build libPluginMsgFmt.so----"
	$(CC) $(DFLAGS)  -o $(TLIB) $(PLUGINNUM_OBJS) $(LIBPATH) $(LIBSLIST)
$(PLUGINNUM_OBJS):$(PLUGINNUM_OBJ)/%.o:$(PLUGINNUM_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	
$(PLUGINNUM_SRC)/desc_NumCheck.cpp:$(PLUGINNUM_SRC)/desc_NumCheck.clog
	$(TOOL)/clogtool -i $< -o $@
	
clean:
	@rm -rf $(PLUGINNUM_OBJS) $(TLIB)
       
dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
	
