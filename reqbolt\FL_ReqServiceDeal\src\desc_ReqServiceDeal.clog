@组件名称：libReqServiceDeal.so.x.x.x
@创建日期：2016/7/27
@修改日期：2016/12/5
@当前版本：1.0.0
@功能描述：接收SMSpout的发送的CCR消息，基于rbmessage.json接口组装RBR消息发送给UserBolt处理
@版本历史：
@1.0.0：
---2016/6/3：  原始代码提交
---2016/6/6：  业务接口实现
---2016/7/26:  数据业务4G位置变更截单
---2016/7/28:  号码拨测
---2016/8/1:   数据业务init消息写队列失败问题修改
---2016/8/20： 总使用量组装错误问题修改;DSL号码规整core问题修改
---2016/8/31： 增加离线扣负开关;动态预占功能实现
---2016/9/1：  短信业务计费类型判断修改
---2016/9/3：  DSL业务漫游字段直传解析
---2016/9/19： 余额查询、累积量查询接口修改
---2016/9/24： 费率切换问题修改
---2016/9/28： PGW超时长截单问题修改
---2016/10/25：跨天累积量，在ext中传上次累积量
---2016/10/27：余额查询，SM没传latnID问题
---2016/11/2： 高额风险业务返回CCA问题
---2016/12/5： 业务代码优化
