#include <stdio.h>
#include <string.h>
#include <uuid/uuid.h>
#include <string>
using namespace std;

typedef unsigned char   UINT8_T;
typedef unsigned int    UINT32_T;
typedef unsigned long   UINT64_T;

class UUIDUtil
{
public:
	UUIDUtil();
	string CreateUUID();
	string CreateShortUUID();
	UINT8_T HexToDec(char hex);

private:
	UINT64_T GetMostSignificantBits();
	UINT64_T GetLeastSignificantBits();
	
private:
	char m_sHexUid[64];
	char m_sDecToBase64[64];
};
