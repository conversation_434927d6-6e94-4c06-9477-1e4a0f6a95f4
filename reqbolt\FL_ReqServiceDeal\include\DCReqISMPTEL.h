/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqISMPTEL.h
*Indentifier：
*
*Description：
*		增值业务处理类
*Version：
*		V1.0
*Author:
* 	       ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_ISMP_TEL_H__
#define __DC_REQ_ISMP_TEL_H__
#include "DCReqISMP.h"
#include "DCBizMsgDef.h"
#include "DCOcpMsgDef.h"
#include "BizDataDef.h"


class DCReqISMPTEL : public DCReqISMP
{
	public:

		DCReqISMPTEL();
		virtual ~DCReqISMPTEL();

	private:

		virtual int SwitchReqType(STBizMsg* bizMsg);
		int Init(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
		int Update(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
		int Term(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
		int Event(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
		int Debit(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
		int Refund(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
        //新增cc_requst_type为5的xdr转换消息
		int XDR(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
		int composeRER(SSessionCache& cacheData, STBizMsg* bizMsg, SCCRDataUnit* USU);
		int XdrEvent(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg);
};

#endif

