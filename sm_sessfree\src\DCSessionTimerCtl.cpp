#include "DCBaseFlow.h"
#include "DCSMPara.h"
#include "DCLogMacro.h"
#include <unistd.h>
#include <stdlib.h>
#include <map>
#include "DCDBManer.h"
#include "DCGrayscaleRoute.h"
#include <time.h>


class DCSessionTimerCtl : public DCBaseFlow
{
public:
	DCSessionTimerCtl(const char* category, const char* func, const char* version)
		:DCBaseFlow(category,func,version)
	{
		m_next = 0;
		m_iKpiDelaySec = 0;
	}

	virtual ~DCSessionTimerCtl()
	{
	}

	virtual const char* desc()
	{
        return "sessiontimer control flow";
	}
	int GrayRefresh();

protected:
	virtual int init();

	virtual int process(void* input, void* output);

private:
    long 	m_next;
	int     m_iKpiDelaySec;
	long lasttime;
	std::map<std::string, std::string>* m_mapParam;
};

int DCSessionTimerCtl::init()
{
    int ret = 0;
    ret = drf()->regist("smpara", new DCSMPara());
    if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "regist DCSMPara failed");
		return -1;
	}

	std::map<std::string, std::string>* pMapParam = (std::map<std::string, std::string>*)gethandle("SERVICEPARAM");
	if (!pMapParam)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPARAM handle");
		return -1;
	}

	int iDelayMs = 0;
	std::map<std::string, std::string>::iterator itParam = pMapParam->find("DelayMs");
	if (itParam != pMapParam->end())
	{
		iDelayMs = atoi(itParam->second.c_str());
	}

	// 这段逻辑保持和kpi一致
	m_iKpiDelaySec = iDelayMs / 1000;
	if (m_iKpiDelaySec > 60)
	{
		m_iKpiDelaySec = 60;
	}
	else if (m_iKpiDelaySec < 5)
	{
		m_iKpiDelaySec = 5;
	}

	int iSec = m_iKpiDelaySec;
	for ( ; iSec > 5; iSec--)
	{
		if (60 % iSec == 0)
		{
			break;
		}
	}

	m_iKpiDelaySec = iSec;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "delay sec [%d]", m_iKpiDelaySec);
	lasttime = time(NULL);
	return 0;
}

int DCSessionTimerCtl::GrayRefresh()
{
	time_t curtime = time(NULL);
	int iRet = 0;
	m_mapParam = (std::map<std::string, std::string>*)gethandle("SERVICEPARAM");
	if (!m_mapParam)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPARAM handle");
		return -1;
	}
	std::map<std::string, std::string>::iterator itParam = m_mapParam->find("nGrayRefreshIntr");
	if ((curtime - lasttime) % atoi(itParam->second.c_str()) == 0)
	{
		lasttime = curtime;
		itParam = m_mapParam->find("sSubscriber");
		if (itParam != m_mapParam->end())
		{
			iRet = DCGrayscaleRoute::instance()->init(dbm(),  itParam->second.c_str()); //刷新数据
			if (iRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DCGrayscaleRoute update data failed,Subscriber=[%s]", itParam->second.c_str());
				return -1;
			}
		}
		else
		{
			iRet = -1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "no find Subscriber");
		}
	}
	return 0;
}


int DCSessionTimerCtl::process(void* input, void* output)
{
	long now = time(NULL);
    if(m_next > now)
	{
		sleep(m_next-now);
	}
	now = time(NULL);

    call_all(input, output);

	long curr = time(NULL);
    if(drf()->getval<DCParaCom>("smpara")->nReleaseFlag == 0)
	{
		m_next = curr + m_iKpiDelaySec;
	}
	else
	{
		m_next = curr + 1;
	}
	GrayRefresh();
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "", "session timer used [%ld] sec", curr-now);
	return 0;
}

DYN_PLUGIN_CREATE(DCSessionTimerCtl, "FL_SessFree", "FL_SessFreeCtl", "1.0.0")
