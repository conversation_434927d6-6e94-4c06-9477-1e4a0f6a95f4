#ifndef NUMCHECK_CPP
#define NUMCHECK_CPP

#include <stdlib.h>
#include "numcheck.h"
#include <string.h>

regmatch_t 		NumCheck::m_pm;
regex_t			NumCheck::m_reg[10];
size_t 			NumCheck::m_nmatch = 1;
int				NumCheck::m_accessMapMax = 0;
int				NumCheck::m_accessMapMin = 0;
int				NumCheck::m_countryMapMax = 0;
int				NumCheck::m_countryMapMin = 0;
int				NumCheck::m_areaMapMax = 0;
int				NumCheck::m_areaMapMin = 0;

int				NumCheck::m_defaultArea = 0;
int				NumCheck::m_defaultProvince = 0;
int				NumCheck::m_defaultCarriers = 0;
TSMPara*         NumCheck::m_smpara  = NULL;

bool 			NumCheck::m_isDefault = false;
map<int, ACCESS_INFO *>		*NumCheck::m_accessMap;
map<int, COUNTRY_INFO *>		*NumCheck::m_countryMap;
map<int, AREA_INFO *>		*NumCheck::m_areaMap;
map<long long, MSISDN_MAP_INFO*> *NumCheck::m_msisdn;
map<int, int>		*NumCheck::m_prefixMap;

string          NumCheck::m_PARTNERIDMap = "";



NumCheck::NumCheck()
{

}

NumCheck::~NumCheck()
{

}

void NumCheck::Init(TSMPara *smpara)
{
	char value[1024] = {0};
	char regStr0[1024] = {0};
	char regStr1[1024] = {0};
	char regStr2[1024] = {0};
	char regStr3[1024] = {0};

	NumCheck::m_smpara = smpara;

	NumCheck::m_accessMap = smpara->GetAccess();
	NumCheck::m_countryMap = smpara->GetCountry();
	NumCheck::m_areaMap = smpara->GetArea();
	NumCheck::m_prefixMap = smpara->GetCallPrefix();
	m_defaultArea = smpara->GetCommonPara()->invalidMsisdnArea;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","m_prefixMap size %d",NumCheck::m_prefixMap->size());
	
	map<int, int>::iterator iter = NumCheck::m_prefixMap->begin();
	sprintf(value, "%d", iter->second);
	iter++;
	int i = 0;
	while(iter != NumCheck::m_prefixMap->end())
	{
		sprintf(value, "%s|%d",value, iter->second);
		iter++;
		i++;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","prefix count:%d,value %s",i,value);
	sprintf(regStr0, "^86(%s)[0-9]{8}$", value);//以86开头并且后8位是0~9的数字
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","regStr0 %s",regStr0);
	sprintf(regStr1, "^(%s)[0-9]{8}$", value);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","regStr1 %s",regStr1);

	sprintf(regStr2, "^86[0-9]{10,13}$");//以86开头并且后10到13位是0~9的数字,总长不超过15位
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","regStr2 %s",regStr2);

	sprintf(regStr3, "^860(%s)[0-9]{8}$", value);//以86开头并且后10到13位是0~9的数字,总长不超过15位
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","regStr3 %s",regStr3);


	regcomp(&m_reg[0], regStr0, REG_EXTENDED);	//带86手机号码
	regcomp(&m_reg[1], regStr1, REG_EXTENDED);//手机号码
	regcomp(&m_reg[2], "^[0-9]{4}[-/]((0[1-9])|(1[0-2]))[-/]((0[1-9])|((1|2)[0-9])|30|31)[-/][0-2][0-4][:][0-5][0-9][:][0-5][0-9]$", REG_EXTENDED);//时间格式如2008-08-08-20-20-30
	regcomp(&m_reg[3], "^[0-9]{1,}$", REG_EXTENDED);//非空数值
	regcomp(&m_reg[4], regStr2, REG_EXTENDED);	//带86开头特殊号码
	regcomp(&m_reg[5], regStr3, REG_EXTENDED);	//带860开头手机号码

	DCParseXml* parse = DCParseXml::Instance();
	const char * param = parse->GetParam("PARTNERIDMap", "SM");
	if(!param)
	{
		m_PARTNERIDMap = "";
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCSConfig PARTNERIDMap is null set default Empty");	
	}
	else
	{
		m_PARTNERIDMap = param;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCSConfig PARTNERIDMap is [%s]",param);
	}
	

	countMaxMinLength();

	
	const AREA_INFO *areaInfo = smpara->GetAreaInfo(m_defaultArea);
	if(areaInfo)
	{
		m_defaultProvince = areaInfo->province;
		m_defaultCarriers = areaInfo->carriers;
	}
	else
	{
		m_defaultProvince = 0;
		m_defaultCarriers = 0;
	}
}

int NumCheck::SortNum(const char *str, SPhone* phoneNum, unsigned long time, int flag)
{
	m_isDefault = false;
	int ret = 0;
	int pos = 0;
	char buffer[16];
	AREA_INFO *area = NULL;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","sortNum number %s",str);

	//过滤掉号码头D76,例如号码D7613912345678/D760285660185
	if(0 == strncmp(str,"D76",3))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","D76 number");
		str += 3;
	}
	
	if(*str != '0' && *str != '+')	//接入号匹配
	{
		if((pos = IsAccessCode(str)) != -1)
		{
			strncpy(buffer, str, pos);
			buffer[pos] = '\0';
			phoneNum->access = atoi(buffer);
			str += pos;
		}
	}

	
	if(*str == '0' || *str == '+')
	{
		if(*str == '0')
		{
			str++;
		}
		
		if(*str == '0' || *str =='+')		//国家号匹配
		{
			str++;
			pos = IsCountryCode(str);
			if(pos == -1)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "get contry code error", "");
				return -2;
			}
			else
			{
				strncpy(buffer, str, pos);
				buffer[pos] = '\0';
				phoneNum->country = atoi(buffer);
				str += pos;
				if(phoneNum->country != 86)
				{
					if(strlen(str) > MAX_PHONE_LEN)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "too long,len[%d]", strlen(str));
						return -3;
					}
					phoneNum->phone=str;
					phoneNum->carriers = 7;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s],carriers=7", phoneNum->phone.c_str());
					goto CHECK;
					
				}
				if(IsPrefixCode(str))
				{
					str++;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "str is[%s]", str);
				}					
				if(*str == '0')
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "num is begin with 0,error", "");
					return -1;
				}
			}
		}
		else
		{
			phoneNum->country = 86;
			if(IsPrefixCode(str))
			{
				str++;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "str is[%s]", str);
			}
			
		}
				
		if(regexec(&m_reg[1], str, m_nmatch, &m_pm, 0) == 0) //手机号码
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "mobile number", "");
			if(IsMobileCode(str, phoneNum) == 0)
			{
				phoneNum->phone=str;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
				goto CHECK;
			}
			
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -4", "");
			return -4;
		}		 
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "other number", "");
			 
			if((area = IsAreaCode(str)) != NULL)	//区号匹配
			{
				string temp = str;
				phoneNum->area = area->area;
				phoneNum->province = area->province;
				phoneNum->carriers = 1;
				phoneNum->networkType = 1;//添加默认网络运营商
				str += area->length;
				getNetWorkType(temp.c_str(),phoneNum);
				if(flag != 2 && *str == '0')
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "num is invalid for 0", "");
					return -1;
				}
				if(strlen(str) > MAX_PHONE_LEN)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "too long num[%d]", strlen(str) );
					return -3;
				}
				phoneNum->phone=str;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
				
				goto CHECK;
				
			}
			else if(-1 != (ret = IsFixedPhoneCode(str, phoneNum)))
			{
				str += ret;
				phoneNum->phone=str;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s],+ or 00 country code phone fixed number", phoneNum->phone.c_str());
				goto CHECK;
			}
		}
		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -1,invalid", "");
		return -1;
		
	}
	else if(regexec(&m_reg[0], str, m_nmatch, &m_pm, 0) == 0)		//86开头手机号
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "86+mobile number", "");
		phoneNum->country = 86;
		str += 2;
		ret = IsMobileCode(str, phoneNum); 
		if(ret == 0)
		{
			phoneNum->phone=str;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
			goto CHECK;
			
		}
		else if(ret == -2)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -5", "");
			return -5;
		}
		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -4", "");
		return -4;
	}
	else if(regexec(&m_reg[5], str, m_nmatch, &m_pm, 0) == 0)		//860开头手机号
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "860+mobile number", "");
		phoneNum->country = 86;
		str += 3;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "str +3 is[%s]", str);
					
		ret = IsMobileCode(str, phoneNum); 
		if(ret == 0)
		{
			phoneNum->phone=str;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
			goto CHECK;
				
		}
		else if(ret == -2)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -5", "");
			return -5;
		}

		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -4", "");
		return -4;
	}	
	else if(regexec(&m_reg[1], str, m_nmatch, &m_pm, 0) == 0)		//手机号码
	{	
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "mobile number", "");
		ret = IsMobileCode(str, phoneNum);
		if( ret == 0)
		{
			phoneNum->country = 86;
			phoneNum->phone=str;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
			goto CHECK;
			
		}
		else if(ret == -2)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -5", "");
			return -5;
		}
		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "ret -4", "");
		return -4;
	}
	else	//其他号码,例如8609713524045/10086/10109090
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "other number", "");
			
		if(strlen(str) > MAX_PHONE_LEN)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "too long num[%d]", strlen(str) );
			return -3;
		}

		if(regexec(&m_reg[4], str, m_nmatch, &m_pm, 0) == 0)	//86开头的特殊号码
		{
			//国家码
			pos = IsCountryCode(str);
			if(pos == -1)
			{
				//无国家码
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "fixed number[%s]", str);
				strncpy(buffer, str, pos);
				buffer[pos] = '\0';
				phoneNum->country = atoi(buffer);
				str += pos;
				if(phoneNum->country != 86)
				{
					phoneNum->phone=str;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s],carriers=7", phoneNum->phone.c_str());
					phoneNum->carriers = 7;
				}
			
		   	 }
			if(!phoneNum->country)
			{
				phoneNum->country = 86;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s],set country is 86", phoneNum->phone.c_str());
			}		
			
			if(*str == '0')
			{
				str++;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "str is 0,++[%s]", str);
			} 

			if((area = IsAreaCode(str)) != NULL)//区号和省号
			{
				string temp = str;
				phoneNum->area = area->area;
				phoneNum->province = area->province;
				phoneNum->carriers = 6;
				phoneNum->networkType = 2;
				str += area->length;
				getNetWorkType(temp.c_str(),phoneNum);
				if(*str == '0')
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "str is 0,error[%s]", str);
					return -1;
				}
		
				phoneNum->phone=str;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
			}
			else if(-1 != (ret = IsFixedPhoneCode(str, phoneNum)))
			{
	            str += ret;
	            phoneNum->phone=str;
	            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "other fixed number[%s]", phoneNum->phone.c_str());
			}
			else
			{

				if((!phoneNum->area )||(!phoneNum->province))
				{
					phoneNum->area = 0;
					phoneNum->province = 0;
					phoneNum->carriers = 6;
				    phoneNum->networkType = 2;
				}
				phoneNum->phone=str;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
			}
		}
	    //非86开头的号码及固话
	    /*
		else if((area = IsAreaCode(str)) != NULL) // 区号和省号
		{
			phoneNum->area = area->area;
			phoneNum->province = area->province;
			phoneNum->carriers = 6;
			phoneNum->networkType = 2;
			str += area->length;
			if(*str == '0')
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "str is 0,error[%s]", str);
				return -1;
			}
	
			phoneNum->phone=str;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
		}
		*/
		else if( (ret = IsMobileCode(str, phoneNum))== 0)
		{
			phoneNum->country = 86;
			phoneNum->phone=str;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s]", phoneNum->phone.c_str());
			goto CHECK;
			
		}
		else
		{
			if((ret=IsFixedPhoneCode(str, phoneNum)) !=-1)
			{
			
				str += ret;
				phoneNum->phone=str;
				phoneNum->country = 86;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s],fixed num,ret[%d]", phoneNum->phone.c_str(),ret);
			}
			else
			{
				phoneNum->country = 86;
				phoneNum->area = 0;
				phoneNum->province = 0;
				phoneNum->carriers = 1;
				phoneNum->networkType = 1;
				phoneNum->phone=str;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone is[%s],ret[%d]", phoneNum->phone.c_str(),ret);
			}
		}
		
		
	}
	
CHECK:	if(1 == flag)//查询 spr_np ，判断运营商
	{	
		char rcarrier[32] = {0};
		int ret = NumCheck::m_smpara->GetSprNp(atol(phoneNum->phone.c_str()), rcarrier, time, sizeof(rcarrier));
		if(0 == ret)
		{
			//(0==strncmp(rcarrier, "001", 3))?(phoneNum->carriers = 1):((0==strncmp(rcarrier, "002", 3))?(phoneNum->carriers = 2):((0==strncmp(rcarrier, "003", 3))?(phoneNum->carriers = 3):(phoneNum->carriers)));
            map<string,int> mapPARTNERIDMap;
			NumCheck::m_smpara->ParseString(m_PARTNERIDMap,mapPARTNERIDMap,";",",");
			string strinnetwork = rcarrier;
			if (strinnetwork.length() > 3)
			{
			    strinnetwork = strinnetwork.substr( 0, 3 );
				map<string,int>::iterator iter = mapPARTNERIDMap.find(strinnetwork);
				if(iter != mapPARTNERIDMap.end())
				{
			       phoneNum->carriers = iter->second;
				}
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "country[%d],province[%d],area[%d],carriers[%d],in_network[%s]",  phoneNum->country,phoneNum->province,phoneNum->area,phoneNum->carriers,rcarrier);
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone[%s],country[%d],province[%d],area[%d],carriers[%d]",  phoneNum->phone.c_str(),phoneNum->country,phoneNum->province,phoneNum->area,phoneNum->carriers);
	return 0;
}


int NumCheck::InitMsisdn(map<long long, MSISDN_MAP_INFO*> *msisdn)
{
	NumCheck::m_msisdn = msisdn;
	return 0;
}

//宽带业务不需要解析出号码前的区号，只需要解析出国家码
//宽带业务不做号码规整
int NumCheck::SortNumDSL( const char *str, SPhone* phoneNum)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "parase num[%s]", str);	
	phoneNum->phone = str;	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone[%s],country[%d],province[%d],area[%d],carriers[%d]",  phoneNum->phone.c_str(),phoneNum->country,phoneNum->province,phoneNum->area,phoneNum->carriers);
	
	return 0;
}

//wlan业务号码规整，只支持手机号
int NumCheck::SortNumWLAN(const char *str, SPhone* phoneNum)
{
	int ret = 0;
	int pos = 0;
	char buffer[16];
	
	if(*str == '0' || *str == '+')
	{
		if(*str == '0')
		{
			str++;
		}
		
		if(*str == '0' || *str =='+')		//国家号匹配
		{
			str++;
			pos = IsCountryCode(str);
			if(pos == -1)
			{
				return -2;
			}
			else
			{
				strncpy(buffer, str, pos);
				buffer[pos] = '\0';
				phoneNum->country = atoi(buffer);
				str += pos;
				if(phoneNum->country != 86)
				{
					if(strlen(str) > 20)
					{
						return -3;
					}
					phoneNum->phone = str;
					phoneNum->carriers = 7;
					return 0;
				}
				if(*str == '0')
				{
					return -1;
				}
			}
		}
		else
		{
			phoneNum->country = 86;
		}
				
		if(regexec(&m_reg[1], str, m_nmatch, &m_pm, 0) == 0) //手机号码
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "mobile number", "");
			if(IsMobileCode(str, phoneNum) == 0)
			{
				phoneNum->phone = str;
				return 0;
			}
			return -4;
		}	
	}
	else if(regexec(&m_reg[0], str, m_nmatch, &m_pm, 0) == 0)		//86开头手机号
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "86+mobile number", "");
		phoneNum->country = 86;
		str += 2;
		ret = IsMobileCode(str, phoneNum); 
		if(ret == 0)
		{
			phoneNum->phone = str;
			return 0;
		}
		else if(ret == -2)
		{
			return -5;
		}
		return -4;
	}
	else if(regexec(&m_reg[1], str, m_nmatch, &m_pm, 0) == 0)		//手机号码
	{	
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "mobile number", "");
		ret = IsMobileCode(str, phoneNum);
		if( ret == 0)
		{
			phoneNum->country = 86;
			phoneNum->phone = str;
			return 0;
		}
		else if(ret == -2)
		{
			return -5;
		}
		return -4;
	}

	return 0;
}

const ACCESS_INFO * NumCheck::GetAccessInfo(int access)
{
	map<int, ACCESS_INFO*>::iterator accessIter = m_accessMap->find(access);
	if(accessIter == m_accessMap->end())
	{
		return NULL;
	}
	else
	{
		return accessIter->second;
	}
}

int NumCheck::IsDigital(const char *str)
{
	if(regexec(&m_reg[3], str, m_nmatch, &m_pm, 0) == 0)
	{
		return 0;
	}
	DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "not digital number", str);
	return -1;
}

int NumCheck::IsTimeFormat(const char *str)
{
	if(regexec(&m_reg[2], str, m_nmatch, &m_pm, 0) == 0)
	{
		return 0;
	}
	
	return -1;
}

long long NumCheck::Atoll(const char *str)
{
	long long temp = 0;
	int high = 0;
	int low = 0;
	char buffer[10];
	int size = strlen(str);
	if(size > 18)
	{
		return temp;
	}
	if(size >9)
	{
		strncpy(buffer, str, size-9);
		buffer[size-9] = '\0';
		high = atoi(buffer);		//高字节
		str += (size - 9);
	}
	low = atoi(str);			//低字节
	temp = (long long)high * 1000000000LL +(long long)low;
	return temp;
	
}

void NumCheck::lltoa(const long long num, char *str)
{}

int NumCheck::IsAccessCode(const char *str)
{
	int size = strlen(str);
	int i = 0;
	int temp = 0;
	char buffer[16];

	if(size < m_accessMapMin)
	{
		return -1;
	}
	if(size > m_accessMapMax)
	{
		size = m_accessMapMax;
	}

	for(i=size; i>=m_accessMapMin; i--)
	{
		strncpy(buffer, str, i);
		buffer[i] = '\0';
		temp = atoi(buffer);
		if(0==temp)
		{
			continue;
		}		
		if(m_accessMap->find(temp) != m_accessMap->end())
		{	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get spr_access_numbe.access_number[%d]",temp);
			return i;
		}
	}

	return -1;
}

int NumCheck::IsCountryCode(const char *str)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get[%s]'s country code in spz_country", str);
	int size = strlen(str);
	int i = 0;
	int temp = 0;
	char buffer[16];

	if(size < m_countryMapMin)
	{
		return -1;
	}
	if(size > m_countryMapMax)
	{
		size = m_countryMapMax;
	}

	for(i=size; i>=m_countryMapMin; i--)
	{
		strncpy(buffer, str, i);
		buffer[i] = '\0';
		temp = atoi(buffer);
		if(m_countryMap->find(temp) != m_countryMap->end())
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find area_code[%d] in spz_country", temp);
			return i;
		}
	}

	return -1;
}

AREA_INFO * NumCheck::IsAreaCode(const char *str)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get[%s]'s area_code,prov_code in spz_city", str);
	int size = strlen(str);
	int i = 0;
	int temp = 0;
	char buffer[16];
	map<int, AREA_INFO *>::iterator area;

	if(size < m_areaMapMin)
	{
		return NULL;
	}
	if(size > m_areaMapMax)
	{
		size = m_areaMapMax;
	}

	for(i=size; i>=m_areaMapMin; i--)
	{
		strncpy(buffer, str, i);
		buffer[i] = '\0';
		temp = atoi(buffer);
		if((area = m_areaMap->find(temp)) != m_areaMap->end())
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find area_code[%d] in spz_city", temp);
			(area->second)->length = i;
			return area->second;
		}
	}
	return NULL;
}

int NumCheck::IsFixedPhoneCode(const char *str, SPhone* phoneNum)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "check fixed phone number[%s]",  str);
	int ret = -1;

	int flag = 0;
	char buf_phone[20] = {0};
	strncpy(buf_phone,str,sizeof(buf_phone));
	char *p = buf_phone;
	int len = strlen(buf_phone)-1;
	p = p+len;
	int i = len;    
    while(i > 0)
	{   
		*p = '0';
		MSISDN_MAP_INFO msisdn_info;
		NumCheck::m_smpara->GetRangeCodeMap(Atoll(buf_phone), msisdn_info);
		if(1 == msisdn_info.flag)
		{
			phoneNum->province = msisdn_info.homeProv;
			phoneNum->area = msisdn_info.areaCode;
			phoneNum->carriers = msisdn_info.cspid;
			phoneNum->networkType = msisdn_info.networkType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d]in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
			flag = 1;
			ret = 0;
			break;
		} 
		p = p-1;
		i--;
	}
		
	if(ret == -1)
	{	
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no find section in spr_msisdn_area_map[%s]",  str);
		if(0 == NumCheck::m_smpara->GetCommonPara()->invalidMsisdnDeal)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "invalid begin_msisdn[%s]",  buf_phone);
			ret = -1;
			return ret;
		}
	}	  
	if( (flag == 1) && (ret == 0))
	{
		char buf[20]={0};
		int area_len = 0;	
		sprintf(buf,"%d",phoneNum->area);	
		area_len= strlen(buf);
		str = str+area_len;
		ret = area_len;		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "area[%d],ret[%d]",  phoneNum->area,ret);
	 }

	if(ret >=20 ||ret <= -1)//保护
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, -1,"", "ret error[%d],set -1", ret);
		ret = -1;
	}
	
	return ret;	
	
}
/*
int NumCheck::IsMobileCode(const char *str, SPhone* phoneNum)
{
	int ret = -1;
	char head7[16] = {0};
	char head8[16] = {0};
	strncpy(head7, str, 7);
	strcat(head7, "0000");
	strncpy(head8, str, 8);
	strcat(head8, "000");
	long phone[2] = {0};
	phone[0] = atol(head7);
	phone[1] = atol(head8);

	long phoneAll = atol(str);//巢湖割接，需要把133开头的号码全匹配配置在号段表
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phoneAll[%ld]",phoneAll);
	//安徽巢湖的号码号段表的区号跟主产品实例表的不一致，就是这个号码归属和用户实际对应的本地网不在一起，需要详细配置

	//全匹配
	MSISDN_MAP_INFO* msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phoneAll,1);
	if(msisdn_info)
	{
		phoneNum->province = msisdn_info->homeProv;
		phoneNum->area = msisdn_info->areaCode;
		phoneNum->carriers = msisdn_info->cspid;
        phoneNum->networkType = msisdn_info->networkType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
		ret = 0;
	}

	//千号段万号段匹配
	if(-1 == ret)
	{
		msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phone[1]);
		if(msisdn_info)
		{
			phoneNum->province = msisdn_info->homeProv;
			phoneNum->area = msisdn_info->areaCode;
			phoneNum->carriers = msisdn_info->cspid;
	        phoneNum->networkType = msisdn_info->networkType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
			ret = 0;
		}
		else
		{
			msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phone[0]);	
			if(msisdn_info)
			{
				phoneNum->province = msisdn_info->homeProv;
				phoneNum->area = msisdn_info->areaCode;
				phoneNum->carriers = msisdn_info->cspid;
				phoneNum->networkType = msisdn_info->networkType;

				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
				ret = 0;
			}
		}
	}

	//没有匹配到
	if(ret == -1)
	{
		msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phoneAll);	
		if(msisdn_info)
		{
			phoneNum->province = msisdn_info->homeProv;
			phoneNum->area = msisdn_info->areaCode;
			phoneNum->carriers = msisdn_info->cspid;
			phoneNum->networkType = msisdn_info->networkType;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get total nbr province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
			ret = 0;
		}
	}
	
	if(ret == -1)
	{	
		if(0 == NumCheck::m_smpara->GetCommonPara()->invalidMsisdnDeal)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "invalid begin_msisdn[%ld]",  phone[0]);
			ret = -1;
			return ret;
		}
		else if(1 == NumCheck::m_smpara->GetCommonPara()->invalidMsisdnDeal)
		{
			phoneNum->province = m_defaultProvince;
			phoneNum->area= m_defaultArea;
			phoneNum->carriers = 1;
			phoneNum->networkType = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
			ret = 0;
		}
	}

	return ret;
}
*/
int NumCheck::IsMobileCode(const char *str, SPhone* phoneNum)
{
	int ret = -1;
	/*
	char head7[16] = {0};
	char head8[16] = {0};
	strncpy(head7, str, 7);
	strcat(head7, "0000");
	strncpy(head8, str, 8);
	strcat(head8, "000");
	long phone[2] = {0};
	phone[0] = atol(head7);
	phone[1] = atol(head8);
	*/

	long phoneAll = atol(str);//巢湖割接，需要把133开头的号码全匹配配置在号段表
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phoneAll[%ld]",phoneAll);
	//安徽巢湖的号码号段表的区号跟主产品实例表的不一致，就是这个号码归属和用户实际对应的本地网不在一起，需要详细配置

	char szPhone[16] = {0};
	strcpy(szPhone, str);
	for(int i = strlen(szPhone); i > 0; i--)
	{
		szPhone[i] = '\0';
		// 缩位匹配
		MSISDN_MAP_INFO msisdn_info;
		NumCheck::m_smpara->GetGsmCodeMap(Atoll(szPhone),msisdn_info);
		//MSISDN_MAP_INFO* msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phoneAll,1);
		if(1 == msisdn_info.flag)
		{
			phoneNum->province = msisdn_info.homeProv;
			phoneNum->area = msisdn_info.areaCode;
			phoneNum->carriers = msisdn_info.cspid;
	        phoneNum->networkType = msisdn_info.networkType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] by par_gsm_code_all,phone[%s]",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType,szPhone);
			ret = 0;
			return ret;
		}

		
		//千号段万号段匹配
		/*
		if(-1 == ret)
		{
			msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phone[1]);
			if(msisdn_info)
			{
				phoneNum->province = msisdn_info->homeProv;
				phoneNum->area = msisdn_info->areaCode;
				phoneNum->carriers = msisdn_info->cspid;
		        phoneNum->networkType = msisdn_info->networkType;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
				ret = 0;
			}
			else
			{
				msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phone[0]);	
				if(msisdn_info)
				{
					phoneNum->province = msisdn_info->homeProv;
					phoneNum->area = msisdn_info->areaCode;
					phoneNum->carriers = msisdn_info->cspid;
					phoneNum->networkType = msisdn_info->networkType;

					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
					ret = 0;
				}
			}
		}
		*/
		
	}

	
	/*
	//没有匹配到
	if(ret == -1)
	{
		msisdn_info= NumCheck::m_smpara->GetMsisdnAreaMap(phoneAll);	
		if(msisdn_info)
		{
			phoneNum->province = msisdn_info->homeProv;
			phoneNum->area = msisdn_info->areaCode;
			phoneNum->carriers = msisdn_info->cspid;
			phoneNum->networkType = msisdn_info->networkType;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get total nbr province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
			ret = 0;
		}
	}
	*/
	
	
	if(ret == -1)
	{	
		if(0 == NumCheck::m_smpara->GetCommonPara()->invalidMsisdnDeal)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "invalid begin_msisdn[%s]",  str);
			ret = -1;
			return ret;
		}
		else if(1 == NumCheck::m_smpara->GetCommonPara()->invalidMsisdnDeal)
		{
			m_isDefault = true;
			phoneNum->province = m_defaultProvince;
			phoneNum->area= m_defaultArea;
			phoneNum->carriers = 1;
			phoneNum->networkType = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get province[%d],area[%d],carriers[%d],networkType[%d] in spr_msisdn_area_map",  phoneNum->province,phoneNum->area,phoneNum->carriers,phoneNum->networkType);
			ret = 0;
		}
	}

	return ret;
}


int NumCheck::IsPrefixCode(const char *str)//是否为手机号码头前缀
{
	int icallprefix=0;
	string strCallPrefix=str;
	strCallPrefix=strCallPrefix.substr(0,4);
	icallprefix = atoi(strCallPrefix.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone[%s] find prefix[%d],strphone[%s]",str,icallprefix,strCallPrefix.c_str());

	map<int,int>::iterator iter=NumCheck::m_prefixMap->find(icallprefix);
	if(iter!=NumCheck::m_prefixMap->end())
	{
		return 1;
	}
	return 0;
}

void NumCheck::countMaxMinLength()
{
	NumCheck::m_accessMapMax 	= 0;
	NumCheck::m_accessMapMin 	= 0;
	NumCheck::m_countryMapMax 	= 0;
	NumCheck::m_countryMapMin 	= 0;
	NumCheck::m_areaMapMax 		= 0;
	NumCheck::m_areaMapMin 		= 0;
	
	map<int, ACCESS_INFO *>::iterator 		accessIter;
	map<int, COUNTRY_INFO *>::iterator 		countryIter;
	map<int, AREA_INFO *>::iterator 		areaIter;

	accessIter = m_accessMap->begin();
	countryIter = m_countryMap->begin();
	areaIter = m_areaMap->begin();

	for(; accessIter!=m_accessMap->end(); accessIter++)
	{
		if(accessIter == m_accessMap->begin())
		{
			m_accessMapMax = accessIter->first;
			m_accessMapMin = accessIter->first;
		}
		if(accessIter->first > m_accessMapMax)
		{
			m_accessMapMax = accessIter->first;
		}
		if(accessIter->first < m_accessMapMin)
		{
			m_accessMapMin = accessIter->first;
		}
	}
	for(; countryIter!=m_countryMap->end(); countryIter++)
	{
		if(countryIter == m_countryMap->begin())
		{
			m_countryMapMax = countryIter->first;
			m_countryMapMin = countryIter->first;
		}
		if(countryIter->first > m_countryMapMax)
		{
			m_countryMapMax = countryIter->first;
		}
		if(countryIter->first < m_countryMapMin)
		{
			m_countryMapMin = countryIter->first;
		}
	}
	for(; areaIter!=m_areaMap->end(); areaIter++)
	{
		if(areaIter == m_areaMap->begin())
		{
			m_areaMapMax = areaIter->first;
			m_areaMapMin = areaIter->first;
		}
		if(areaIter->first > m_areaMapMax)
		{
			m_areaMapMax = areaIter->first;
		}
		if(areaIter->first < m_areaMapMin)
		{
			m_areaMapMin = areaIter->first;
		}
	}

	if(m_accessMapMax >0)
		m_accessMapMax = (int)log10((double)m_accessMapMax) + 1;
	if(m_accessMapMin >0)
		m_accessMapMin = (int)log10((double)m_accessMapMin) + 1;
	if(m_countryMapMax >0)
		m_countryMapMax = (int)log10((double)m_countryMapMax) + 1;
	if(m_countryMapMin >0)
		m_countryMapMin = (int)log10((double)m_countryMapMin) + 1;
	if(m_areaMapMax >0)
		m_areaMapMax = (int)log10((double)m_areaMapMax) + 1;
	if(m_areaMapMin >0)
		m_areaMapMin = (int)log10((double)m_areaMapMin) + 1;
}

int NumCheck::getNetWorkType(const char *str, SPhone* phoneNum)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "check fixed phone number[%s]",  str);
	int ret = -1;

	int flag = 0;
	char buf_phone[20] = {0};
	strncpy(buf_phone,str,sizeof(buf_phone));
	char *p = buf_phone;
	int len = strlen(buf_phone)-1;
	p = p+len;
	int i = len;    
    while(i > 0)
	{   
		*p = '0';
		MSISDN_MAP_INFO msisdn_info;
		NumCheck::m_smpara->GetRangeCodeMap(Atoll(buf_phone), msisdn_info);
		if(1 == msisdn_info.flag)
		{
			phoneNum->carriers = msisdn_info.cspid;
			phoneNum->networkType = msisdn_info.networkType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "carriers[%d],networkType[%d]in spr_msisdn_area_map",  phoneNum->carriers,phoneNum->networkType);
			flag = 1;
			ret = 0;
			break;
		} 
		p = p-1;
		i--;
	}
	return ret;	
	
}

bool NumCheck::GetDefaultFlag()
{
	return m_isDefault;
}
#endif
