/*******************************************
*Copyrights  2016，深圳天源迪科计算机有限公司
*					平台 项目组
*All rights reserved.
*
*Filename：
*		TConfig.h
*Indentifier：
*		这里填入该文件的标识（参见软件配置管理）
*Description：
*		TConfig 头文件
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
*      
*	
********************************************/
#ifndef __TCONFIG_H__
#define __TCONFIG_H__

#define SM_FILE_NAME_LEN		256
#define SM_SERVICE_MAX           9



#define SM_CFG_ROOT_ELEM		     "configuration"
#define SM_CFG_SERV_ELEM 			 "serv"
#define SM_CFG_SERV_HOST			 "host"
#define SM_CFG_SERV_HOST_NAME	     "name"
#define SM_CFG_SERV_MQ		         "mq"
#define SM_CFG_SERV_MQ_ADDRESS		 "servAddr"

#define SM_CFG_SERV_LOG				 "log"
#define SM_CFG_SERV_LOG_PATH		 "path"
#define SM_CFG_SERV_LOG_LEVEL		 "level"


#define SM_CFG_CDR_ELEM 			"cdr"
#define SM_CFG_CDR_HOST				"host"
#define SM_CFG_CDR_HOST_NAME		"name"
#define SM_CFG_CDR_PATH				"path"
#define SM_CFG_CDR_PATH_BASE		"base"
#define SM_CFG_CDR_PATH_CURRENT		"current"
#define SM_CFG_CDR_PATH_BACKUP		"backup"
#define SM_CFG_CDR_PATH_SAVE		"save"
#define SM_CFG_CDR_PATH_2HB			"HB"   		//存放按照HB规范生成的话单

#define SM_CFG_CDR_LOG				"log"
#define SM_CFG_CDR_LOG_PATH			"path"
#define SM_CFG_CDR_LOG_LEVEL		"level"

#define SM_CFG_CDR_END				"switch"
#define SM_CFG_CDR_END_SIZE			"size"
#define SM_CFG_CDR_END_TIME			"time"
#define SM_CFG_CDR_MQ		        "mq"
#define SM_CFG_CDR_MQ_ADDRESS		"servAddr"
  
//日志配置
typedef struct
{
	unsigned int	level;					//日志级别
	char	path[SM_FILE_NAME_LEN];	//日志服务器地址
}SLogConfig;



//服务配置
typedef struct
{
	char			host[SM_FILE_NAME_LEN];		//主机名
	SLogConfig		log;						//日志
	char            mqaddr[SM_FILE_NAME_LEN];	
}SServConfig;

//话单路径
typedef struct
{
	char cur[SM_FILE_NAME_LEN];					//当前路径
	char bak[SM_FILE_NAME_LEN];				//备份路径
	char sav[SM_FILE_NAME_LEN];				//备份所有话单
	int  savflag;									//备份标识
	char base[SM_FILE_NAME_LEN];				//基础路径
	char postfix[SM_FILE_NAME_LEN];			//文件后缀
	char testcur[SM_FILE_NAME_LEN];
	char testbak[SM_FILE_NAME_LEN];
}SCDRPathConfig;

//文件切换配置
typedef struct
{
	unsigned int size;							//单位K
	unsigned int time; 							//单位S
}SCDRSwitchConfig;


typedef struct
{
	char 			host[SM_FILE_NAME_LEN];		//主机名
	char            testhost[SM_FILE_NAME_LEN];
	SCDRPathConfig	path;						//路径
	SLogConfig		log;							//日志
	SCDRSwitchConfig	exchange;					//切换
	int  service[SM_SERVICE_MAX];
}SCDRConfig;

//系统配置
typedef struct
{
	SServConfig serv;							//服务
	SCDRConfig cdr;								//话单
}SSMConfig;


class TConfig
{
public:

	static TConfig* instance();

	int load(const char* cfg, char* error);
	int loadLog();
	int loadcdrlog();
	char *getCfgPath();

	int getPerfLog();

	int GetPerfUsec();
	
	int destroy();



public:

	const SSMConfig* config(); 
	
	bool getErrCodeFlag();
	
	void setErrCodeFlag(bool bFlag);

private:

	TConfig();
	~TConfig();

private:

	SSMConfig* m_pSMConfig;
	static TConfig* m_instance;	
	char cfgpath[512];
};

#define GET_SM_CFG()   TConfig::instance()->config()

#endif
