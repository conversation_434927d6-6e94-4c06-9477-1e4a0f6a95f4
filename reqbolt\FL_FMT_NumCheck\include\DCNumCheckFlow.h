#ifndef __DC_NUMCHECKFLOW_H__
#define __DC_NUMCHECKFLOW_H__

#include <stdio.h>
#include <utility>
#include "DCOBJSet.h"
#include "DCBasePlugin.h"
#include "DCDBManer.h"
#include "DCReqFmt.h"
#include "TSMPara.h"


class DCNumCheckFlow : public DCBasePlugin
{
	public:	
		DCNumCheckFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCNumCheckFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);

	private:
		DCOBJSetPool* m_pool;
		DCDBManer* m_dbm;		
		DCReqFmt m_fmt;
		TSMPara *m_smpara;

};
#endif
