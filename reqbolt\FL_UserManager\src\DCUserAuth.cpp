#include "DCUserAuth.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "DCUserBase.h"
#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "DCOBJSet.h"
#include "numcheck.h"
#include "ErrorCode.h"
#include "DCServEvtCheck.h"


int DCUserAuth::init()
{
	m_userInfo = new SUserInfo;
	TSMPara *psmpara = dynamic_cast<TSMPara *>(DCRFData::instance()->get("TSMPara"));
	NumCheck::Init(psmpara);

	return 0;
}

const char* DCUserAuth::desc()
{
	return "FC_UserAuth:查找用户资料，实名制鉴权";
}
	
//消息头|公共消息|业务消息
int DCUserAuth::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();	
	SCCRBase* base_ccr = pset->get<SCCRBase>();
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;
       memset(m_userInfo, 0x0, sizeof(*m_userInfo));
	bizMsg->m_userinfo = m_userInfo;

	SCCRVOICE* voice = NULL;
	SCCRDSL* dsl = NULL;
	
	DCUserBase base(smpara,bizMsg->m_dbm);
	int ret = 0;

	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:	
			voice = pset->get<SCCRVOICE>();
			if(4 == bizMsg->m_requestType)
			{
				m_userInfo->bizType = 4;
			}
			else if(12==voice->eventTypeBCSM)
			{
				if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
				{
					m_userInfo->bizType = 12;
				}
				else
				{
					m_userInfo->bizType = 2;
				}
			}
			else if(2==voice->eventTypeBCSM || 3==voice->eventTypeBCSM)
			{	
				if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
				{
					m_userInfo->bizType = 11;
				}
				else
				{
					m_userInfo->bizType = 1;
				}
			}
			break;
		case SMS:
			if(SM_REQUESTED_ACTION_DEBIT == base_ccr->requestAction)
			{
				if(bizMsg->m_requestType == 5)
					m_userInfo->bizType = 13;
				else
					m_userInfo->bizType = 3;
			}
			else if(SM_REQUESTED_ACTION_REFUND == base_ccr->requestAction)
			{
				if(bizMsg->m_requestType == 5)
					m_userInfo->bizType = 15;
				else
					m_userInfo->bizType = 5;			
			}
			break;
		case DATA:
		case CCG:
	    case PGW:
            if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
            {
                    m_userInfo->bizType = 17;
            }
            else
            {
                    m_userInfo->bizType = 7;
            }
			break;
        case DATA_5G:
            if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
            {
                    m_userInfo->bizType = 17;
            }
            else
            {
                    m_userInfo->bizType = 7;
            }
            break;
		case HRS:
		case ISMP:
			if (SM_REQUESTED_ACTION_DEBIT == base_ccr->requestAction)
			{	
				if(bizMsg->m_requestType == 5)
					m_userInfo->bizType = 16;
				else
					m_userInfo->bizType = 6;
			}else if(SM_REQUESTED_ACTION_REFUND == base_ccr->requestAction)
			{
				if(bizMsg->m_requestType == 5)
					m_userInfo->bizType = 15;
				else
					m_userInfo->bizType = 5;
			}
			else if(SM_REQUESTED_ACTION_CHECK == base_ccr->requestAction || 
					SM_REQUESTED_ACTION_QUERY_RATABLE_1 == base_ccr->requestAction || 
					SM_REQUESTED_ACTION_QUERY_RATABLE_2 == base_ccr->requestAction)
			{
				m_userInfo->bizType = 4;
			}
			break;
		case DSL:		
			dsl = pset->get<SCCRDSL>();
			if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
			{
				m_userInfo->bizType = 18;			
			}
			else
			{
				m_userInfo->bizType = 8;
			}
			break;
		default:
			break;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "serviceContextID [%d], user info bizType[%d]",bizMsg->m_serviceContextID,m_userInfo->bizType );

	//if(bizMsg->m_serviceContextID != DSL)
	//	base.GetAreaCode(base_ccr->subscription, bizMsg);


	if(DSL == bizMsg->m_serviceContextID)
	{		
		//宽带号码分析不涉及运营商类型字段，固定填1
		base_ccr->subscription.carriers = 1;
		if(10 == base_ccr->subscriptionType)//卡用户
		{
			ret =  base.GetUserAccNbr(base_ccr->subscription, m_userInfo, bizMsg);
			if(SM_OCP_USER_UNKNOWN == ret && dsl->productSpecID == "2200")
			{
				NumCheck::SortNumWLAN(base_ccr->subscriptionData.c_str(), &(base_ccr->subscription));
				ret =  base.GetUserAccArea(base_ccr->subscription, m_userInfo, bizMsg);
			}
			base_ccr->subscription.province = m_userInfo->province;
			base_ccr->subscription.area = m_userInfo->area;
		}
		else
		{
			ret = base.GetUserAccountInfo(base_ccr->subscription, m_userInfo, bizMsg);

			if((SM_OCP_USER_UNKNOWN == ret )&& ((dsl->productSpecID == "2200")||(dsl->productSpecID == "2202")))
			{
				if (dsl->productSpecID == "2200")
				{
					NumCheck::SortNumWLAN(base_ccr->subscriptionData.c_str(), &(base_ccr->subscription));
					ret = base.GetUserAccArea(base_ccr->subscription, m_userInfo, bizMsg);
					base_ccr->subscription.province = m_userInfo->province;
					//wlan宽带业务，传R904为2203给RB，控制按照计费号码查询
					dsl->productSpecID = "22001";
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "productSpecID[%s]", dsl->productSpecID.c_str());
				}
				else
				{
					NumCheck::SortNumWLAN(base_ccr->subscriptionData.c_str(), &(base_ccr->subscription));
					ret = base.GetUserAccArea(base_ccr->subscription, m_userInfo, bizMsg);
					base_ccr->subscription.province = m_userInfo->province;
					//wlan宽带业务，网元传2202，转换为22021给RB
					dsl->productSpecID = "22021";
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "transfer productSpecID to[%s]",dsl->productSpecID.c_str());	
				}
			}
			else
			{
	    			base_ccr->subscription.province = m_userInfo->province;
				base_ccr->subscription.area = m_userInfo->area;
			}
		}

		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, ret,"", " userinfo error[%d]", ret);
		    return ret;
		}
	}
	else
	{
		ret = base.GetUserAccArea(base_ccr->subscription, m_userInfo, bizMsg);
		DCServEvtCheck::instance()->SetCacheKey("ACC_NBR", base_ccr->subscription.phone);
		if (bizMsg->m_serviceContextID == HRS && ret == SM_OCP_USER_UNKNOWN)
		{
			bizMsg->m_userType = 1;
		}
		else if (ret != RET_SUCCESS)
		{
			if (DATA_5G == bizMsg->m_serviceContextID)
			{
				if (bizMsg->m_offline || bizMsg->m_requestType == SM_SESSION_XDR_CODE)
				{
					bizMsg->m_iCdrRet = ret;
					if (bizMsg->m_iOfflineXDREptFlag == 0)
					{
						bizMsg->m_iOfflineXDREptFlag = EPT_5G_UNKNOW_USER_CDR;
					}

					return RET_CDR;
				}
			}

			return ret;
		}
	}
	
	if(voice != NULL)
	{
		if(bizMsg->m_cdrCallType == 1)
		{
			voice->calling.area = base_ccr->subscription.area;
		}
		else if(bizMsg->m_cdrCallType == 2)
		{
			voice->called.area = base_ccr->subscription.area;
		}
	}

	if (DATA_5G != bizMsg->m_serviceContextID)
	{	
		bizMsg->m_iRollFlag = atoi(base_ccr->smExt.kv["rollFlag"].c_str());
	}
	//实名制用户控制	
	if(SM_SESSION_INITIAL_CODE == bizMsg->m_requestType || SM_SESSION_EVENT_CODE == bizMsg->m_requestType)
	{
		ret = base.GetAuthUser(m_userInfo, bizMsg);
		if(1 == ret)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "not auth user[%ld], errCode[%d]", m_userInfo->servID, ret);
			switch(bizMsg->m_serviceContextID)
			{
				case VOICE:
					bizMsg->m_resultcode = 
						smpara->GetCommonPara()->INAuthUserResultCode;
					break;
				case SMS:
					bizMsg->m_resultcode =
						smpara->GetCommonPara()->P2PSMSAuthUserResultCode;
					break;
				case CCG:
				case DATA:
					bizMsg->m_resultcode = 
						smpara->GetCommonPara()->PSAuthUserResultCode;
					break;
				case HRS:
				case ISMP:
					bizMsg->m_resultcode = 
						smpara->GetCommonPara()->ISMPAuthUserResultCode;
					break;
				case DSL:
					bizMsg->m_resultcode =
						smpara->GetCommonPara()->DSLAuthUserResultCode;
					break;
				case PGW:
					bizMsg->m_resultcode = 
						smpara->GetCommonPara()->PSAuthUserResultCode;
					break;
                case DATA_5G:
                    bizMsg->m_resultcode =
                        smpara->GetCommonPara()->_5GAuthUserResultCode;
                    break;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "now resultCode[%d],after sendcca to be RET_AOC",bizMsg->m_resultcode );
			base.sendCCA(bizMsg);
			bizMsg->m_resultcode = RET_AOC;
			return RET_AOC;
		}
	}
	
	
	return 0;
}


DYN_PLUGIN_CREATE(DCUserAuth, "FUNC_USERAUTH", "FC_UserAuth", "1.0.0")


