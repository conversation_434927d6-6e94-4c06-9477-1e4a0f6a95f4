include ../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)
SRC_HOME=$(PWD)
SRC_PATH=$(SRC_HOME)/src
LIB_PATH=$(RELEASE_PATH)/sessfree/plugin
FMT_BASE=$(LBSPUBROOT)/SM
DFS_HOME=$(LBSPUBROOT)/third/DCFService
DGR= $(THIRD_HOME)/DCGrayscaleRoute
COMMON_INC=$(FMT_BASE)/common/include
inc=-I$(DFM_INC_PATH) \
	-I$(SRC_HOME)/include \
	-I$(ITF)/include \
	-I$(AVRO)/include \
	-I$(DCFC_INC) \
	-I$(DFS_HOME)/include \
	-I$(DCLOGCLI)/include \
	-I$(TXML)/include \
	-I$(COMMON_INC) \
	-I$(KPI_SENDER_INC) \
	-I$(DGR)/include
	
	
lib=-L$(DFM)/lib -L$(DCFC_LIB) -L$(DCLOGCLI)/lib -L$(TXML)/lib -L$(ZK_LIB) \
 -L$(RELEASE_PATH)/lib -L$(DFS_HOME)/lib -L$(DGR)/lib -ldfm -ldcfclient_new -lrt -ldclogcli -ltinyxml -lpthread -lCommonIF -lzookeeper_mt -ldcfservice -L$(KPI_SENDER_LIB)  -lkpisender -ldcgrayscaleroute

version=1.0.0

libtarget=$(LIB_PATH)/libfl_sessiontimerctl.so \
		$(LIB_PATH)/libfc_sessiontimerscan.so \
		$(LIB_PATH)/libfc_sessiontimercut.so  

bintarget=$(RELEASE_PATH)/sessfree/bin/sm_sessfree

.PHONY:all clean dup

all: $(libtarget) $(bintarget)

$(LIB_PATH)/libfl_sessiontimerctl.so:$(SRC_PATH)/DCSessionTimerCtl.o $(SRC_PATH)/DCSMPara.o 
	$(CC) $(DFLAGS) -o $@ $^
	
$(LIB_PATH)/libfc_sessiontimerscan.so:$(SRC_PATH)/DCSessionTimerScan.o  $(SRC_PATH)/DCSMPara.o 
	$(CC) $(DFLAGS) -o $@ $^ 

$(LIB_PATH)/libfc_sessiontimercut.so:$(SRC_PATH)/DCSessionTimerCut.o $(SRC_PATH)/DCSMPara.o 
	$(CC) $(DFLAGS) -o $@ $^ 
	
$(bintarget):$(SRC_PATH)/sm_sessfree.o $(SRC_PATH)/CKV.o $(SRC_PATH)/CSocketClient.o
	$(CC) $(LFLAGS) -o $@ $^ $(lib)
%.o:%.cpp
	$(CC) $(CFLAGS) -c -o $@ $< $(inc)
	
clean:
	-rm -f $(libtarget) $(bintarget) $(SRC_PATH)/*.o
	
dup:
	@cp -pf $(bintarget) $(PROJECT_RPATH)/bin && echo "dup $(bintarget) to $(PROJECT_RPATH)/bin"
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
