/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCEptTimeoutREA.h
*Indentifier：
*		
*Description：
*		超时异常处理基类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_EPT_TIMEOUT_REA_H__
#define __DC_EPT_TIMEOUT_REA_H__

#include "DCBizEpt.h"
#include "DCBasePlugin.h"
#include "DCAnsPara.h"
//
class DCEptTimeoutREA:public DCBizEpt,public DCBasePlugin
{
	public:

		DCEptTimeoutREA(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */) 	   :DCBasePlugin(category,func,version)
		{
		
		}

		virtual ~DCEptTimeoutREA()
		{
		
		}


		virtual int Work(void *data);

	protected:
		virtual int init();
		virtual int process(void* input, void* output);

		virtual int SwitchCommon(STBizMsg* bizMsg);
		virtual int SwitchVOICE(STBizMsg* bizMsg);
		virtual int SwitchSMS(STBizMsg* bizMsg);
		virtual int Switch5G(STBizMsg* bizMsg);
		virtual int SwitchDATA(STBizMsg* bizMsg);
		virtual int SwitchISMP(STBizMsg* bizMsg);
		virtual int SwitchDSL(STBizMsg* bizMsg);
		virtual int SwitchPGW(STBizMsg* bizMsg);
	private:
		DCAnsPara* m_anspara;
};

#endif

