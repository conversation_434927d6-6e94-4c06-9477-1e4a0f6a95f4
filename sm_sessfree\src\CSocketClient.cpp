#include "CSocketClient.h"
#include <utility>
#include "DCLogMacro.h"
#include "DCDBManer.h"
#include "DCParseXml.h"
#include "DCDBManer.h"
#include "KVProto.h"
#include "CKV.h"
#include "ByteSwap.h"
using namespace std;

SocketClient::SocketClient()
{
	m_port = 0;
	m_connfd = 0;
	m_state = 0;
	m_buf[0] = 0;
	m_time = 0;
	m_timeflag = 0;
	m_host[0] = 0;
	m_uri[0] = 0;
	m_pDB = new DCDBManer;
}

SocketClient::~SocketClient()
{
	delete m_pDB;
	m_pDB = NULL;
}

int SocketClient::init(const char* sqlfile)
{
	m_addr = DCParseXml::Instance()->GetParam("addr", "smsessfree/sc");
	m_port = atoi(DCParseXml::Instance()->GetParam("port", "smsessfree/sc"));
	m_time = atol(DCParseXml::Instance()->GetParam("time", "smsessfree/sc"));
	sprintf(m_host,"%s",DCParseXml::Instance()->GetParam("host", "smsessfree/sc"));
	sprintf(m_uri,"%s",DCParseXml::Instance()->GetParam("uri", "smsessfree/sc"));
       m_pDB->Init(sqlfile);
}

int SocketClient::Connect()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "connect to server:%s:%d",m_addr.c_str(),m_port);

	if(m_state!=0)	return 0;
	int ret = 0;
	/*struct hostent *host; 
	struct sockaddr_in serv_addr; 
	if((host=gethostbyname(m_addr.c_str()))==NULL) 
	{ 
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "gethname err[ip:%s fd:%d]",m_addr.c_str(),m_connfd);

		return -1;
	}
	
	if ((m_connfd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
	{ 
		DCBIZLOG(DCLOG_LEVEL_ERROR,  0, "", "get socket err[%s]", strerror(errno));
		return -1;
	}

	bzero(&serv_addr,sizeof(serv_addr)); 	
	serv_addr.sin_family=AF_INET; 
	serv_addr.sin_port=htons(m_port); 
	serv_addr.sin_addr = *((struct in_addr *)host->h_addr);  
	if (connect(m_connfd, (struct sockaddr *)&serv_addr,  
		sizeof(struct sockaddr)) == -1) 
	{ 
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "connect err[ip:%s,port:%d,fd:%d] , errorinfo[%s]", m_addr.c_str(), m_port,m_connfd, strerror(errno));
		CloseFD();
		return -1;
	} 
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "connect successful!","");*/
	struct addrinfo hints, *res, *ressave;
	bzero(&hints, sizeof(hints));
	hints.ai_family = AF_UNSPEC;
	hints.ai_socktype = SOCK_STREAM;
	hints.ai_protocol = IPPROTO_IP;
 	char szPort[16] = {0};
	sprintf(szPort,"%d",szPort);
	if (0 != (ret = getaddrinfo(m_addr.c_str(), szPort, &hints, &res)))
	{
		cout << "getaddrinfo error: " << gai_strerror(ret) << endl;
		return -1;
	}
 
	ressave = res;
	while (NULL != res)
	{
		if (-1 == (m_connfd = socket(res->ai_family, res->ai_socktype, res->ai_protocol)))
        {
	        cout << "create socket error: " << strerror(errno) << endl;
	        res = res->ai_next;
	        continue;
        }
 
		if (-1 == connect(m_connfd, res->ai_addr, res->ai_addrlen))
		{
			cout << "connect error: " << strerror(errno) << endl;
            close(m_connfd);
			res = res->ai_next;
            continue;
		}
 
		break;
	}
	if (NULL == res)
	{
		cout << "Effor::getaddrinfo res is null "<< endl;
		return -1;
	}
	freeaddrinfo(ressave);

	m_state = 1;

	ret=sendAuthMsg();
	return ret;
}

int SocketClient::CloseFD()
{
	if(m_connfd)
	close(m_connfd);
	m_state = 0;
	return 0;	
}

int SocketClient::sendAuthMsg()//发送鉴权消息
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "start to send authmsg","");

	int ret = 0;
	unsigned int len = 0;
	char buf[1024] = {0};

	KVHead head={0};
	CKVNode reqnode("reg");
	reqnode.add(make_ckv("H000",m_uri));
	reqnode.add(make_ckv("H001",m_host));
	reqnode.add(make_ckv("H002", 1 ));
    compose_group(0, buf+sizeof(KVHead), sizeof(buf)-sizeof(KVHead), len, &reqnode);

	len +=	sizeof(KVHead);
	
	head.version = 1;
	head.flag = KV_FR;
	head.proto = hton16(8);
	head.msg = hton32(1);
	head.size =  hton32(len);
	memcpy(buf,&head,sizeof(KVHead));
	
	ret = send(m_connfd, buf, len, 0);
	if (ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "send failed ret[%d]", ret);
		CloseFD();
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send authmsg:%s successful,msg len:%u,head size:%u",buf,len,head.size);
	
	// 收取AUTH消息，检测RC
	CKVNode ansnode("reg");
	CKVVal *val =NULL;
	unsigned int size = 0;
	int result = 0;
	len=recv(m_connfd,buf, 512, 0);//接收整个应答消息,不校验消息头
	if(len)
	{
		decompose_group(0,buf+sizeof(KVHead),len-sizeof(KVHead),&ansnode);//解析消息体
		val=ansnode.get("RC");
		if(NULL==val)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "get auth ans msg 'RC' failed", "");	
			return -1;
		}
		else
		{
			result=	val->getInt32();
			
			if(result)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "auth failed result[%d]", result);	
				return -1;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "auth SUCESSFUL", "");				
			}
		}
	}

	return 0;
}

int SocketClient::composemsg()
{
	long smsum[6] = {0,0,0,0,0,0};
	long resum[4] = {0,0,0,0};
	long sum=0;
	unsigned int len = 0;//整个消息的长度
	KVHead head={0};
	m_buf[0] = 0;
	CKVNode stateNode("state");//根节点
	stateNode.add(make_ckv("proc", "sm_sess"));//子节点proc

	UDBSQL* pQuery = NULL;
	try
    {
    	pQuery = m_pDB->GetSQL("q_sm_voice_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			smsum[0] += sum;
		}
		pQuery->Close();
		stateNode.add(make_ckv("voice",smsum[0]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "voice", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_sm_sms_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1, sum);
			smsum[1] += sum;
		}
		pQuery->Close();
		stateNode.add(make_ckv("sms",smsum[1]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "sms", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_sm_ismp_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			smsum[2] += sum;
		}
		pQuery->Close();
		stateNode.add(make_ckv("ismp",smsum[2]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "ismp", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_sm_data_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			smsum[3] += sum;
		}
		pQuery->Close();
		stateNode.add(make_ckv("data",smsum[3]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "data", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_sm_pgw_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			smsum[4] += sum;
		}
		pQuery->Close();
		stateNode.add(make_ckv("pgw",smsum[4]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "pgw", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_sm_dsl_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			smsum[5] += sum;
		}
		pQuery->Close();
		stateNode.add(make_ckv("dsl",smsum[5]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "dsl", "dbexp:%s", e.ToString());
    }
	
	int ret =0;
	ret = compose_group(0, m_buf+sizeof(KVHead), sizeof(m_buf)-sizeof(KVHead), len, &stateNode);
	if(ret < 0)
	{	
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "compose_group failed: ret[%d]",ret);
	}
	
	len +=	sizeof(KVHead);

	head.version = 1;
	head.flag= KV_FR;
	head.proto = hton16(8);
	head.msg = hton32(3);
	head.size =  hton32(len);
	memcpy(m_buf,&head,sizeof(KVHead));
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "compose msg buf[%s]",m_buf);

	ret=sendMsg(m_buf,len);
	if(ret<0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "send failed: ret[%d]",ret);
	}

	CKVNode stateNode2("state");//根节点
	stateNode2.add(make_ckv("proc", "re_sess"));//子节点proc
	try
    {
    	pQuery = m_pDB->GetSQL("q_re_voice_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			resum[0] += sum;
		}
		pQuery->Close();
		stateNode2.add(make_ckv("voice",resum[0]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "voice", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_re_data_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			resum[1] += sum;
		}
		pQuery->Close();
		stateNode2.add(make_ckv("data",resum[1]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "data", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_re_pgw_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			resum[2] += sum;
		}
		pQuery->Close();
		stateNode2.add(make_ckv("pgw",resum[2]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "pgw", "dbexp:%s", e.ToString());
    }
	
	try
    {
    	pQuery = m_pDB->GetSQL("q_re_dsl_all");
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1,sum);
			resum[3] += sum;
		}
		pQuery->Close();
		stateNode2.add(make_ckv("dsl",resum[3]));
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_WARN, 1001, "dsl", "dbexp:%s", e.ToString());
    }

	len = 0;
	ret =0;
	ret = compose_group(0, m_buf+sizeof(KVHead), sizeof(m_buf)-sizeof(KVHead), len, &stateNode2);
	if(ret < 0)
	{	
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "compose_group failed: ret[%d]",ret);
	}
	
	len +=	sizeof(KVHead);

	head.version = 1;
	head.flag= KV_FR;
	head.proto = hton16(8);
	head.msg = hton32(3);
	head.size =  hton32(len);
	memcpy(m_buf,&head,sizeof(KVHead));
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "compose msg buf[%s]",m_buf);

	ret=sendMsg(m_buf,len);
	if(ret<0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "send msg failed[%s]",m_buf);
	}
	return 0;
}
int SocketClient::sendMsg(char *buf,int length)
{
	if(0==m_state) 
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "disconnect to server,not send msg","");	
		return -1;//连接不在，直接退出
	}
	int ret=send(m_connfd, buf, length, MSG_NOSIGNAL);
	if(-1==ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "cannot send msg to the server,errno:%d",ret);	
		CloseFD();//认为连接断开，重置连接状态
	}
	return ret;
}
void SocketClient::routine()
{
	int ret = 0;
	signal(SIGPIPE,SIG_IGN); 
	while(1)
	{
		if(m_state==0)
		{
			ret=Connect();	
			if(ret)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "connect server failed[%d]", ret);
				sleep(2);
				continue;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "reconnect server ok", "");				
			}

		}
		composemsg();
		usleep(m_time*1000);
	}
}