#include "DCRoutBolt.h"
#include "DCStormProtocol.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include <ace/Log_Msg.h>
#include <ace/Log_Msg_Backend.h>
#include <ace/Log_Record.h>
#include "DCParseXml.h"
#include "DCSeriaOp.h"
#include "ErrorCode.h"
#include <vector>
#include "DCPerfStatistic.h"
#include "DCDBManer.h"
#include "DCMCastEvtFun.h"
#include "UHead.h"
#include "DCRbMsgDef.h"
#include "DCOcpMsgDef.h"
#include "DCEptMsgDef.h"
using namespace std;
using namespace ocs;

class DCACELogAgent : public ACE_Log_Msg_Backend
{
public:
	DCACELogAgent(){}
	virtual ~DCACELogAgent(){}

	virtual int 	open (const ACE_TCHAR *logger_key){}
	virtual int 	reset (void){}
	virtual int 	close (void){}
	virtual ssize_t log (ACE_Log_Record &logr)
	{
		int level = 0;
		switch (logr.priority())
		{
			case 1:
				level = DCLOG_LEVEL_TRACE;
				break;
			case 2:
				level = DCLOG_LEVEL_DEBUG;
				break;
			case 3:
				level = DCLOG_LEVEL_DVIEW;
				break;
			case 4:
				level = DCLOG_LEVEL_INFO;
				break;
			case 5:
				level = DCLOG_LEVEL_WARN;
				break;
			case 6:
				level = DCLOG_LEVEL_ERROR;
				break;
			case 7:
				level = DCLOG_LEVEL_FATAL;
				break;
			default:
				level = DCLOG_LEVEL_ERROR;
				break;
		}
		DCSYSLOG(level, 0, "L%lu|%s", logr.priority(), logr.msg_data());
            return logr.msg_data_len();
	}
};

DCRoutBolt::DCRoutBolt()
{

}

DCRoutBolt::~DCRoutBolt()
{

}

int DCRoutBolt::Refresh(const char * path)
{
	return 0;
}

int DCRoutBolt::SetWacther()
{
	//监听
	return 0;
}


int DCRoutBolt::Initialize(const tydic::storm::DCStormConfig& config)
{
	int ret = 0;
	char buf[512]={0};
	m_checktime = 0;


	ACE_LOG_MSG->clr_flags(ACE_Log_Msg::STDERR);
	ACE_LOG_MSG->msg_backend( new DCACELogAgent() );
	ACE_LOG_MSG->priority_mask( LM_DEBUG|LM_INFO|LM_NOTICE|LM_WARNING|LM_ERROR|LM_CRITICAL, ACE_Log_Msg::PROCESS);
	ACE_LOG_MSG->open("ace", ACE_Log_Msg::CUSTOM);

	char *szconfig =getenv("OCS_CONFIG");
	if(NULL==szconfig)
	{
	  return -1;
	}

	string val = (const_cast<tydic::storm::DCStormConfig&>(config)).GetConfig("billing.cfg.spec");
	sprintf(buf,"%s/sm_cfg%s.xml",szconfig,val.c_str());
	ret = DCParseXml::Instance()->Init("SM",buf);
	if(ret)
	{
		return -1;
	}

    int taskid = atoi(config.GetTaskId().c_str());
	const char* topology = GetTopology().c_str();

	const char* logpath = DCParseXml::Instance()->GetParam("logAddr","Common/log");
	int loglevel  = atoi(DCParseXml::Instance()->GetParam("level","Common/log"));
	//--------------------------------性能日志级别、阈值设置-------------------------------------//
	int perf_level = 0;//性能日志级别设置
	const char* perf_level_param = DCParseXml::Instance()->GetParam("perf","Common/log");
	if(perf_level_param)
	{
		perf_level = atoi(perf_level_param);
	}
	int perf_threshold = 50;//性能日志阈值获得
	const char* perf_threshold_param = DCParseXml::Instance()->GetParam("perf.ms","Common/log");
	if(perf_threshold_param)
	{
		perf_threshold = atoi(perf_threshold_param);
	}
	//日志初始化
	ret = DCLOGINIT("ocs","sm_routbolt",loglevel,logpath);
	if(ret)
	{
	  return -1;
	}

	DCLOG_SETLEVEL(DCLOG_CLASS_PERF,perf_level);
	DCLOG_SETCTL(DCLOG_MASK_PERF,perf_threshold*1000);//单位转化为微秒

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d, module:%s",logpath, loglevel, "sm_reqbolt");
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init log successful, topology[%s] taskid[%d]", topology, taskid);

	//注册统计指标
	m_tstat = m_pm.get_statistic()->get_position("routbolt");


       //注册监听事件
      /*BoltEventFun evfun(m_pm.get_mcm(), m_pm.get_drf());
      evfun.register_event(topology, taskid, "reqbolt");*/

	DCMCastManer* m_mcm = new DCMCastManer();
	const char* mcast = getenv("OCS_MCAST_CMD_ADDR");
	if(mcast)
	{
		ret = m_mcm->init(mcast);
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,ret,"","init	DCMCastManer failed: %s", strerror(errno));
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init  DCMCastManer success");

		LogEventFun evfun(m_mcm);
		evfun.register_event("routbolt");
	}

	m_de = new DCSeriaDecoder(ESeriaBinString);
	return 0;
}

int DCRoutBolt::Process(tydic::storm::Tuple &tuple)
{
	DCPerfTimeVCollect collet(m_tstat, true);
	int msgsize = tuple.GetSize();
	int java_que_size = tuple.GetExecuteQSize();
	std::string msginfo = tuple.GetValues(0);
	int taskid  = tuple.GetTaskID(msginfo);
	std::string recvCCRMsg = tuple.GetValues(1);
	std::string sendmsg;
	char buf[20]={0};
	char servAddr[25]={0};
	long  jm_usec = 0;
	char sRouteId[128]={0};

	strncpy(servAddr, recvCCRMsg.c_str(), 25);
	recvCCRMsg.erase(0, 25);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","servAddr[%s], recv ccr msg[%s],taskid[%d]", servAddr, recvCCRMsg.c_str(),taskid);

	// 获取头部固定16字节
	strncpy(buf, recvCCRMsg.c_str(), 16);
	jm_usec = collet.m_begin.tv_sec*1000000 + collet.m_begin.tv_usec - strtol(buf, NULL, 10);

	// 删除头部16字节
	recvCCRMsg.erase(0, 16);
	ocs::UHead uhd;
	ocs::SCCRBase base;
	ocs::rbhead rbhead;
	ocs::SEPTMsg eptMsg;
	//std::vector<uint8_t> in = HexDecode(recvCCRMsg.c_str()+16,  recvCCRMsg.size()-16 < 64 ? recvCCRMsg.size()-16 : 64);

	std::vector<uint8_t> in = HexDecode(recvCCRMsg.c_str(), recvCCRMsg.size());
	m_de->set(in);
	try
	{
		m_de->decode(uhd);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","decode uhead failed");
		return -1;
	}

	//获取路由的字段
	try
	{
		if(uhd.car == "REQ")
		{
			m_de->decode(base);
			string strTempNbr = base.subscriptionData;
			if(0==strncmp(strTempNbr.c_str(),"86",2))
			{
			    strTempNbr = base.subscriptionData.substr(2,base.subscriptionData.length()-2);
			}
			sprintf(sRouteId, "%s", strTempNbr.c_str());
		}
		else if(uhd.car == "ANS")
		{
			m_de->decode(rbhead);
			sprintf(sRouteId, "%d%ld", rbhead.type, rbhead.prodinstid);
		}
		else if(uhd.car == "EPT")
		{
			sprintf(sRouteId, "%s", uhd.uid.c_str());
		}
		else if(uhd.car == "FREE")
		{
			m_de->decode(eptMsg);
			sprintf(sRouteId, "%s", eptMsg.sessionID.c_str());
			char *p = strrchr(sRouteId,';');
			if(p)
			{
				*p = '\0';
			}
		}
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","decode body failed");
		return -1;
	}

		//头部加上固定16位时间戳
	struct timeval tmv;
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	recvCCRMsg.insert(0, servAddr,25);
    recvCCRMsg.insert(25, buf,16);

	if(uhd.car == "REQ")
	{
		tydic::storm::Tuple tRnd;
		tRnd.SetValues(msginfo);
		tRnd.SetValues(recvCCRMsg);
		tRnd.SetValues(sRouteId);
		tydic::storm::EmitDirect(-1, tRnd, "ReqBoltStream");
		tydic::storm::exeAck(tuple.GetID());
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to reqbolt sseq[%s] sRouteId[%s] msgsize[%d]",msginfo.c_str(), sRouteId, msgsize);
	}
	else if(uhd.car == "ANS")
	{
		tydic::storm::Tuple tRnd;
		tRnd.SetValues(msginfo);
		tRnd.SetValues(recvCCRMsg);
		tRnd.SetValues(sRouteId);
		tydic::storm::EmitDirect(-1, tRnd, "AnsBoltStream");
		tydic::storm::exeAck(tuple.GetID());
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to ansbolt sseq[%s]	sRouteId[%s] msgsize[%d]",msginfo.c_str(), sRouteId, msgsize);
	}
	else if(uhd.car == "EPT" || uhd.car == "FREE")
	{
		tydic::storm::Tuple tRnd;
		tRnd.SetValues(msginfo);
		tRnd.SetValues(recvCCRMsg);
		tRnd.SetValues(sRouteId);
		tydic::storm::EmitDirect(-1, tRnd, "EptBoltStream");
		tydic::storm::exeAck(tuple.GetID());
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to eptbolt sseq[%s]	sRouteId[%s] msgsize[%d]",msginfo.c_str(), sRouteId, msgsize);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","Unknow Uhd car[%s]", uhd.car.c_str());
	}
	collet.stop();
	// 输出统计信息
	if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0)
	{
	    DCPERFLOG((int)(collet.m_usec+jm_usec),"PERF routbolt:[routbolt.jm=1|%ld][reqbolt.que=%d][java.que=%d]", jm_usec,msgsize,java_que_size);
	}
	return 0;
}

