﻿/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCEptBolt.h
*Indentifier：
*		
*Description：
*		异常处理功能组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_EPTBOLT_H__
#define __DC_EPTBOLT_H__

#include <DCBolt.h>
#include <stdio.h>
#include "DCPluginManer.h"
#include <time.h>
#include "DCSeriaOp.h"
#include <string.h>
#include <pthread.h> 
#include "DCEvtCheck.h"
#include "DCKpiSender.h"


class DCPerfTimeStats;
class DCEptBolt: public tydic::storm::DCBolt {
public:
	int Initialize(const tydic::storm::DCStormConfig& config);
	int Process(tydic::storm::Tuple &tuple);
	DCEptBolt();
	virtual ~DCEptBolt();
	int Refresh(const char * path);
	int SetWacther();

	void GetHostIp(std::string &IP);
	void Compress(std::string& buf);
    void SplitString(const std::string& str, char sep, std::list<std::string>& vec);
    int SendToKpiBolt(std::string msginfo);

	void svc();

private:
	string ReSetUid(string strMsgInfo,int idup, string& uid);
	DCPluginManer m_pm;
	DCPerfTimeStats*  m_tstat;
	time_t m_checktime;
	char m_Topic[64];
	char m_payflagTopic[64];
	char m_testTopic[64];
	std::string m_strIP;
	DCSeriaEncoder* m_en;
	DCSeriaPrinter m_print;
	pthread_t m_tid;
    DCKpiMon *m_ptrBPMon;
};

extern "C" {

tydic::storm::DCStormBase* CreateInstance()
{
	return new DCEptBolt();
}

void ReleaseInstance(tydic::storm::DCStormBase* pStorm)
{
	if (pStorm != NULL)
		delete pStorm;
}

}

#endif

