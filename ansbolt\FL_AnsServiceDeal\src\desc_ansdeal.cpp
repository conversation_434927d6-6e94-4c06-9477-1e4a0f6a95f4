extern "C" {
const char* mod_desc=
"@组件名称：libAnsServiceDeal.so.x.x.x\n"
"@创建日期：2016/6/12\n"
"@修改日期：2016/11/28\n"
"@当前版本：1.0.0\n"
"@功能描述：接收ratingbolt发送的rba消息，基于ccrmessage.json接口组装cca应答消息，发送给sp程序处理。\n"
"@版本历史：\n"
"@1.0.0：\n"
"---2016/9/01：余额查询优化\n"
"---2016/9/07：修改DSL返回的CCA中未传GSU的问题\n"
"---2016/9/08: 增值业务离线清单FEE字段出负数问题\n"
"---2016/9/19: 模拟拨测缺陷修改\n"
"---2016/10/07: 将serviceContextIdstr字段放到ext中传给批价\n"
"---2016/10/13: 免费号码流程数据业务没有插入TOPOLOGY\n"
"---2016/10/14: 离线业务更新使用量和时间\n"
"---2016/10/21: 统一添加UHead消息头，涉及所有程序，需全部一起升级\n"
"---2016/10/24: 语音业务离线剔重\n"
"---2016/11/19: 数据业务tariffidinfo和accumlatorinfo查会话表顺序错误缺陷\n"
;
}
