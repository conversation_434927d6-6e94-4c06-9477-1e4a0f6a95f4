/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdrNormalDATA.h
*Indentifier：
*		
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef _DCBIZ_CDR_NORMAL_DATA_H_
#define _DCBIZ_CDR_NORMAL_DATA_H_

#include "DCBizCdrNormal.h"
#include "TCDRDict.h"

class DCBizCdrNormalDATA:public DCBizCdrNormal
{
	public:

		DCBizCdrNormalDATA();
		~DCBizCdrNormalDATA();
		
	protected:
				
		virtual int ComposeDATA(STBizMsg* bizMsg);
		
		int SelectPretreatColumn(STBizMsg* bizMsg);

		int PretreatAAA(STBizMsg* bizMsg);

		int PretreatCCG(STBizMsg* bizMsg);

		int PutCdr_CCG(STBizMsg* bizMsg, DataCDRInfo &datacdr,string & rg);
		int PutCdr_AAA(STBizMsg* bizMsg,  DataCDRInfo &datacdr,string & rg);
		
		int updateCdrInfo(STBizMsg* bizMsg,char *sessinID);
		int ComposeCCGCDR(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,const char* rg,SCDRData &cdr);
		int ComposeAAACDR(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,const char* rg,SCDRData &cdr);
	 
};

#endif


