#include "DCRoamDealFlow.h"
#include "TSMPara.h"
#include "DCOcpMsgDef.h"
#include "DCOBJSet.h"
#include "DCSeriaOp.h"
#include "DCLogMacro.h"
using namespace ocs;

int DCRoamDealFlow::init()
{
	m_roamjudge = new DCRoamJudge;
	m_area      = new AREA_INFO;
	memset(m_area,0,sizeof(AREA_INFO));
	return 0;
}

//消息头|公共消息|业务消息
int DCRoamDealFlow::process(void* input, void* output)
{
	int ret = 0;
	DCOBJSet* pset = (DCOBJSet*)input;

	STBizMsg* bizMsg = pset->get<STBizMsg>();
	SCCRBase* base =  pset->get<SCCRBase>();
	bizMsg->m_visit = m_area;

       /*
	if(bizMsg->m_serviceContextID != DSL)
		m_roamjudge->GetAreaCode(base->subscription, bizMsg);
	*/
	
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				SCCRVOICE* voice =  pset->get<SCCRVOICE>();
				m_roamjudge->RoamVoice(bizMsg,base,voice);
			}
			break;
		case DATA:
		case CCG:
		case PGW:
			{
				SCCRDATA* data =  pset->get<SCCRDATA>();
				m_roamjudge->RoamDATA(bizMsg,base,data);
			}
			break;
        case DATA_5G:
            {
                SCCR5GInfo* data =  pset->get<SCCR5GInfo>();
                m_roamjudge->RoamDATA_5G(bizMsg,base,data);
            }
            break;
		case SMS:
			{
				SCCRSMS* sms =  pset->get<SCCRSMS>();
				m_roamjudge->RoamSMS(bizMsg,sms);
			}
			break;
		case ISMP:
		case HRS:
			break;
		case DSL:
			break;
		default:
			break;
	}
	

	output = pset;
	return ret;
}



DYN_PLUGIN_CREATE(DCRoamDealFlow, "FC_ROAMDEAL", "FC_RoamDeal", "1.0.0")


