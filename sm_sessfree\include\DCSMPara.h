#ifndef __DCSMPARA_H__
#define __DCSMPARA_H__

#include "DCRFData.h"

#define VOICE		1
#define SMS		    2
#define DATA		3
#define ISMP		4
#define DSL		    5
#define PGW		    6
#define CCG		    7
#define RATA		8
#define HRS		    9  //HIGH-RISK-SERVICE
#define DATA_5G        11

#define NE_SESSION_TIMEOUT						-5006	//5012

struct DCParaCom
{
	int nFreeAddCdr;    //免费号码是否出单，0,1,2
    int longCDRTime;	//PGW时长超长截单
    int nReleaseFlag;   //超时释放配置，0:正常业务使用,1:升级时使用
	int nAmountLimit;	//每秒释放的超时会话数
    int iNotifyFlag;
	int iFastReleaseFlag;
	int iCheckLatnId;
};

class SSystemPara;
class DCSMPara : public BData
{
public:
	DCSMPara();

	~DCSMPara();

	virtual int init(DCDBManer* dbm);

	virtual int work(EBDIDX idx);

	virtual void* data(EBDIDX idx);

	virtual void clear(EBDIDX idx);

	DCParaCom* getPara();

private:

	int LoadComPara(EBDIDX idx);

    int LoadSystemPara(const char *paraGroup, const char *paraKey, SSystemPara *systemPara);

private:
	DCDBManer*			m_dbm;
	DCParaCom 			m_para[2];

};

#endif // __DCSMPARA_H__
