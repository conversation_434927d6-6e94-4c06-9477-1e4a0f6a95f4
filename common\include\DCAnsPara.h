/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCAnsPara.h
*Indentifier：
*
*Description：
*       SM系统参数及业务参数
*Version：
*       V1.0
*Author:
*
*Finished：
*
*History:
*
********************************************/
#ifndef _DCANSPARA_H_
#define _DCANSPARA_H_

#include <map>
#include <string>
#include <sys/stat.h>
#include <stdio.h>
#include <string.h>
#include <algorithm>
#include <math.h>
#include <stdlib.h>

#include "SMParaStruct.h"
#include "BizDataDef.h"
#include <vector>
#include "DCDBManer.h"
#include "DCRFData.h"
using namespace std;

typedef struct
{
	SCommonPara		    m_commonPara;
	SINPara				m_INPara;
	SPSPara				m_PSPara;
	SISMPPara			m_ISMPPara;
	SP2PSMSPara			m_P2PSMSPara;
	SDSLPara			m_DSLPara;
	S5GPara				m_5GPara;
	map<int, SResultCode*> 	  m_result_code_map;
	vector<string>            m_vecCdrByService;//按照RG出单，根据service-context-id
	vector<int>				  m_vecEptBillResultCode;			//ABM异常对账的RB 结果码
	vector <int> 			  Vec_Aoc_Message_type;
	vector <int> 			  Vec_Aoc_Ofr_Id;
	map<int, SAocPara*>		  m_aoc_para_map;
}AnsSMPARA;

typedef struct
{
	UDBSQL* m_pSELECT_SM_SYSTEM_PARAMETER;
	UDBSQL* m_pSELECT_SM_RESULTCODE_MAP;
	UDBSQL* m_pSELECT_SM_AOC_GLOBAL_CFG;
}AnsParaSQL;


class DCAnsPara : public BData
{
	public:
		SCommonPara *GetCommonPara();
		SINPara * GetINPara();
		SPSPara * GetPSPara();
		S5GPara * Get5GPara();
		SISMPPara* GetISMPPara();
		SP2PSMSPara* GetP2PSMSPara();
		SDSLPara * GetDSLPara();
		bool IsCfgServContexID(const char * pServiceContextID);
		int GetOCPResultCode(int resultCode);
		int IsAoc_Message_Type(int message_type);
		int IsAoc_Ofr_ID(int ofr_id);
		SAocPara * GetAocPara(int serviceType, int reqType, int callType);
		/*初始化*/
	    virtual int init(DCDBManer* dbm);

		/*按索引更新数据*/
		virtual int work(EBDIDX idx);

		/*按索引获取数据指针*/
	    virtual void* data(EBDIDX idx)
		{
			return m_para[idx];
		}

		/*按索引清空数据*/
	    virtual void clear(EBDIDX idx);


		DCAnsPara();

		~DCAnsPara();


	private:

		int LoadSystemPara(const char *paraGroup, const char *paraKey, SSystemPara *systemPara);
		int LoadCommonPara();
		int LoadINPara();
		int LoadPSPara();
		int Load5GPara();
		int LoadISMPPara();
		int LoadDSLPara();
		int LoadResultCodeMap();

		int LodeSizeComp();//刷新前后数据对比

		void ParseString(string& szSourString,vector<string> &vecDestString,const char* szSeparator);
		void ParseString(string& szSourString,vector<int> &vecDest,const char* szSeparator);
		int  ParseShortCDR(const char *value);
		void ParseLongCDR(char * value);
		int  ParseFreeCdrField(int servicetype,const char* value);
		int LoadAocPara();

	private:
		AnsParaSQL m_sql;

		AnsSMPARA *m_para[2];
		AnsSMPARA * m_pSMPARA;
		DCDBManer* m_dbm;
};

#endif
