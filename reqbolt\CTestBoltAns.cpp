/*
 * CTestBoltAns.cpp
 *
 *  Created on: 2015年6月4日
 *      Author: tydic
 */

#include "CTestBoltAns.h"
#include "DCStormProtocol.h"
#include "DCOcpMsgDef.h"
#include "UHead.h"
#include "OCPDefTEL.h"
#include "DCPerfStatistic.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>

CTestBoltAns::CTestBoltAns():m_en(ESeriaBinString),m_de(ESeriaBinString)
{

}

CTestBoltAns::~CTestBoltAns()
{

}

int CTestBoltAns::Initialize(const tydic::storm::DCStormConfig& config) {

	char buf[512] = {0};
	char *szconfig =getenv("OCS_CONFIG");
	if(NULL==buf)
	{
		return -1;
	}

	sprintf(buf,"%s/sm_cfg.xml",szconfig);
	int ret = DCParseXml::Instance()->Init("SM",buf);
	if(ret)
	{
		return -1;
	}

	const char* logpath = DCParseXml::Instance()->GetParam("logAddr","Common/log");
	int loglevel  = atoi(DCParseXml::Instance()->GetParam("level","Common/log"));
	//const char* logmodule = DCParseXml::Instance()->GetParam("reqmodule","SM/log");
	
	//日志初始化
	ret = DCLOGINIT("ocs","re_bolt",loglevel,logpath);
	if(ret)
	{
	  return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d, module:%s",logpath, loglevel, "re_bolt");
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init log successful");

	//mq初始化
	const char *brokers = DCParseXml::Instance()->GetParam("mqservAddr","Common/mq");
	/*const char *pGroup = DCParseXml::Instance()->GetParam("reqgroup","SM/queue"); 

	m_Topic[0] = DCParseXml::Instance()->GetParam("voice","SM/queue");
	m_Topic[1] = DCParseXml::Instance()->GetParam("sms","SM/queue");
	m_Topic[2] = DCParseXml::Instance()->GetParam("data","SM/queue");
	m_Topic[3] = DCParseXml::Instance()->GetParam("ismp","SM/queue");
	m_Topic[4] = DCParseXml::Instance()->GetParam("dsl","SM/queue");
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Topic:%s, %s, %s, %s, %s", m_Topic[0], m_Topic[1], m_Topic[2], m_Topic[3], m_Topic[4]);*/
	
	m_producer = new DCMqProduceServer(MQ_ROCKET);
	ret = m_producer->Init(brokers,"reqBoltGroup");
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init producer failed!");
		return -1;
	}
	m_producer->Start();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init producer successful!");

  	m_pool = DCOBJSetPool::instance();
	m_pool->reg<ocs::UHead>();
	m_pool->reg<ocs::SCCRBase>();	
	m_pool->reg<ocs::SCCRVOICE>();
	m_pool->reg<ocs::SCCRSMS>();
	m_pool->reg<ocs::SCCRDATA>();
	m_pool->reg<ocs::SCCRISMP>();
	m_pool->reg<ocs::SCCRDSL>();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "register object to pool successful!");

        m_tstat = new DCPerfTimeStats();
        memset(m_tstat, 0, sizeof(DCPerfTimeStats));
	return 0;
}

int CTestBoltAns::Process(tydic::storm::Tuple &tuple) 
{
       DCPerfTimeVCollect collet(m_tstat, true);
	std::string recvCCRMsg = tuple.GetValues(1);
	std::string sendmsg;
	std::string strTopic;
	int serviceflag = 0;
       struct timeval tmv;
       char buf[20];
       long jm_usec = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","recv ccr msg[%s]", recvCCRMsg.c_str());

       // 获取头部固定16字节
       strncpy(buf, recvCCRMsg.c_str(), 16);
       jm_usec = collet.m_begin.tv_sec*1000000 + collet.m_begin.tv_usec - strtol(buf, NULL, 10);

       // 删除头部16字节
       recvCCRMsg.erase(0, 16);

	DCOBJSet* pset = m_pool->get();
	ocs::UHead*    uhd  = pset->get<ocs::UHead>();
	ocs::SCCRBase* base = pset->get<ocs::SCCRBase>();
	
    //公共消息反序列化  
	vector<uint8_t> vectorMsg;
	try
	{
		vectorMsg = HexDecode(recvCCRMsg.c_str(),recvCCRMsg.size());
		m_de.set(vectorMsg);
		m_de.decode(*uhd);
		m_de.decode(*base);

		std::string skey = base->sessionId + "|" + uhd->uid;
		 strTopic= base->anstopic;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, base->trace);	// 设置号码跟踪
		 
		//打印base内容
		m_print.clear();
		m_print.print(*uhd);
		m_print.print(*base);
	
	    //业务消息反序列化
		switch(base->ServiceInformation)
		{
			case __IN_Information:	
				{
					ocs::SCCRVOICE* voice = pset->get<ocs::SCCRVOICE>();			
					m_de.decode(*voice);
					//打印VOICE内容
					m_print.print(*voice);
					serviceflag = 1;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "VOICE[%s]",m_print.data());
				}
				break;
			case __P2PSMS_Information:	
				{
					ocs::SCCRSMS* sms = pset->get<ocs::SCCRSMS>();			
					m_de.decode(*sms);
					//打印P2PSMS内容
					m_print.print(*sms);
					serviceflag = 2;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SMS[%s]", m_print.data());
				}
				break;
			case __PS_Information:
				{
					ocs::SCCRDATA* data = pset->get<ocs::SCCRDATA>();			
					m_de.decode(*data);
					//打印DATA内容
					m_print.print(*data);
					serviceflag = 3;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DATA[%s]", m_print.data());
				}
				break;
			case __ISMP_Information:	
				{
					ocs::SCCRISMP* ismp = pset->get<ocs::SCCRISMP>();			
					m_de.decode(*ismp);
					//打印ISMP内容
					m_print.print(*ismp);			
					serviceflag = 4;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ISMP[%s]", m_print.data());
				}
				break;
			case __DSL_Information:	
				{
					ocs::SCCRDSL* dsl = pset->get<ocs::SCCRDSL>();			
					m_de.decode(*dsl);
					//打印DSL内容
					m_print.print(*dsl);				
					serviceflag = 5;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DSL[%s]", m_print.data());
				}
				break;
			default:
				break;
		}	
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","decode failed");
		return -1;
	}	
	//组装CCA消息
	ocs::SCCAMsg cca;	
	cca.sessionID = base->sessionId;
	cca.requestType = base->requestType;
	cca.requestNumber = base->requestNumber;
	cca.serial = base->serial;
	cca.ServiceFlowID = base->serviceFlowId;
	cca.ServiceContextID = serviceflag;
	cca.resultCode = 2001;


	
	ocs::UHead uhd1;
	uhd1.uid = uhd->uid;

  	//打印cca内容
	DCSeriaPrinter p2;
	p2.print(uhd1);
	p2.print(cca);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "cca[%s]", p2.data());
	
	m_en.clear();
	m_en.encode(uhd1);
	m_en.encode(cca);		
	sendmsg = HexEncode(m_en.data(),m_en.size());

        // 头部加固定16位微妙时间戳
        gettimeofday(&tmv, NULL);
        sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
        sendmsg.insert(0, buf);

	int ret = m_producer->Produce(sendmsg.c_str(), sendmsg.length(),strTopic /*m_Topic[serviceflag-1]*/);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","produce cca  failed,ret[%d],content:\n[%s]\n", ret, sendmsg.c_str());
	} 
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "produce cca succeed[%s]", sendmsg.c_str());
	}


	tydic::storm::Tuple tRnd;	
	tRnd.SetValues("1");
	tRnd.SetValues("ok");
	tydic::storm::EmitDirect(-1,tRnd);		

	m_pool->put(pset);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "end flow :%d", ret);
        
       // 输出性能日志
       if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0)
        {
                collet.stop();
                DCPERFLOG((int)m_tstat->s_us,"PERF SM ANS:[sm.jm=1|%ld][sm.ans=1|%lu]", jm_usec,m_tstat->s_us);
                m_tstat->s_nr = 0;
                m_tstat->s_us = 0;
                m_tstat->lst_us = 0;
        }
	return 0;
}
