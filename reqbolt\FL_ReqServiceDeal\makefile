include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include

REQSERVICE_INC=$(PWD)/include
REQSERVICE_SRC=$(PWD)/src
REQSERVICE_OBJ=$(PWD)/obj
REQSERVICE_CPP=DCReq.cpp DCReqSMS.cpp DCReqSMSTEL.cpp DCReqISMPTEL.cpp DCReqISMP.cpp desc_ReqServiceDeal.cpp \
		       DCReqDATA.cpp DCReqDATATEL.cpp DCReqPGW.cpp DCReqVOICE.cpp DCReqVOICETEL.cpp DCReqDSL.cpp DCReqDSLTEL.cpp  DCReqDealFlow.cpp DCReqIndex.cpp DCReq5G.cpp
           
REQSERVICE_SRCS=$(addprefix $(REQSERVICE_SRC)/, $(REQSERVICE_CPP))
REQSERVICE_OBJS=$(patsubst $(REQSERVICE_SRC)/%.cpp, $(REQSERVICE_OBJ)/%.o, $(REQSERVICE_SRCS))

CFLAGS += -std=c++11

TLIB= $(RELEASE_PATH)/plugin/libReqServiceDeal.so

INCLUDE =-I$(REQSERVICE_INC) \
				-I$(COMMON_INC) \
				-I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
         -I$(DFM_INC_PATH) \
         -I$(MQ)/include \
         -I$(KPI_SENDER_INC) -I$(EVT_CHECK_INC) -I$(CTG_CHECK)/inc

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(KPI_SENDER_LIB) -L$(EVT_CHECK_LIB) -L$(CTG_CHECK_LIB)
LIBSLIST= -lCommonIF -luuid -lkpisender -lCheckSDK -lCtgCheckSDK

libtarget=$(TLIB)

tmpvar:=$(call CreateDir, $(REQSERVICE_OBJ))
.PHONY:all clean dup

all:$(TLIB)	
$(TLIB): $(REQSERVICE_OBJS)
	@echo "build libReqServiceDeal.so----"
	$(CC) $(DFLAGS)  -o $(TLIB) $(REQSERVICE_OBJS) $(LIBPATH) $(LIBSLIST)
$(REQSERVICE_OBJS):$(REQSERVICE_OBJ)/%.o:$(REQSERVICE_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	
$(REQSERVICE_SRC)/desc_ReqServiceDeal.cpp:$(REQSERVICE_SRC)/desc_ReqServiceDeal.clog
	$(TOOL)/clogtool -i $< -o $@

clean:
	@rm -rf $(REQSERVICE_OBJS)

dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
	
