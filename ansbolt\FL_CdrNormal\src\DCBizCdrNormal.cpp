﻿#include "DCBizCdrNormal.h"
#include "DCLogMacro.h"
#include "TCDRDict.h"
#include "BizLenDef.h"
#include "DCAnsPara.h"
#include "TConfig.h"
#include "CDR.h"
#include "ErrorCode.h"
#include "BizDataDef.h"
#include "func_sqlindex.h"
#include "DCCommonIF.h"
#include "DCSeriaOp.h"
#include "DCMqProduceServer.h"
#include <sys/time.h>
#include "UStaMsg.h"

using namespace ocs;

DCBizCdrNormal::DCBizCdrNormal()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "DCBizCdrNormal", "");
}

DCBizCdrNormal::~DCBizCdrNormal()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "~DCBizCdrNormal", "");
}

int DCBizCdrNormal::Work(void *data)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Work");
	STBizMsg* bizMsg = (STBizMsg*)data;
	if (NULL == data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "null msg");
		return -1;
	}


	m_para = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = m_para->GetCommonPara();
	m_fence = commonPara->CdrFence;
	m_endfence = commonPara->CdrEndchar;


	//m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));

	//PERF LOG
	//bizMsg->m_perf.GetTimeT4_B();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "service type[%d]", bizMsg->m_serviceContextID);

	int ret = RET_SUCCESS;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				ret = ComposeVOICE(bizMsg);
			}
			break;
		case SMS:
			{
				ret = ComposeSMS(bizMsg);
			}
			break;
		case DATA:
		case CCG:
			{
				ret = ComposeDATA(bizMsg);

			}
			break;
		case PGW:
			{
				ret = ComposePGW(bizMsg);
			}
			break;
		case ISMP:
			{
				ret = ComposeISMP(bizMsg);
			}
			break;
		case DSL:
			{
				ret = ComposeDSL(bizMsg);
			}
			break;
		case DATA_5G:
			{
				ret = Compose5G(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "service type error[%d]", bizMsg->m_serviceContextID);
			}
			break;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  bizMsg->m_sessionID, "session over","");

	if (bizMsg->m_bExistMSCCFiltered || bizMsg->m_bImsFilter || bizMsg->m_iOfflineXDREptFlag)
	{
		return ret;
	}

	if (bizMsg->m_serviceContextID != DATA_5G)
	{
		// 获取量本信息，累积量信息，发送stabolt
		ret = GetAccumulationInfo(bizMsg);
		if (ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE,	"", "GetAccumulationInfo failed!");
		}
	}

	return ret == 0 ? bizMsg->m_iCdrRet : ret;
}

int DCBizCdrNormal::ComposeVOICE(STBizMsg* bizMsg)
{
	return 0;
}

int DCBizCdrNormal::ComposeSMS(STBizMsg* bizMsg)
{
	return 0;
}

int DCBizCdrNormal::ComposeDATA(STBizMsg* bizMsg)
{
	return 0;
}

int DCBizCdrNormal::ComposePGW(STBizMsg* bizMsg)
{
	return 0;
}
int DCBizCdrNormal::ComposeISMP(STBizMsg* bizMsg)
{
	return 0;
}

int DCBizCdrNormal::ComposeDSL(STBizMsg* bizMsg)
{
	return 0;
}

int DCBizCdrNormal::Compose5G(STBizMsg* bizMsg)
{
    return 0;
}
int DCBizCdrNormal::DeleteSession(STBizMsg* bizMsg)
{
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	char value[BIZ_DATA_LEN_256]={0};

	UDBSQL *pDelete = NULL;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			 pDelete = dbm->GetSQL(Voice_DeleteSession);
			 if(bizMsg->m_spiltflag > 1)
                      {
                           strncpy(value, bizMsg->m_childsessionID,sizeof(value));
                      }
                      else
                      {
                           strncpy(value, bizMsg->m_sessionID,sizeof(value));
                      }
			break;
		case SMS:
			pDelete = dbm->GetSQL(SMS_DeleteSession);
			strncpy(value, bizMsg->m_sessionID,sizeof(value));
			break;
		case DATA:
		case CCG:
			{
				pDelete = dbm->GetSQL(DATA_DeleteSessionStoreRG);
				sprintf(value, "%s%%", bizMsg->m_sessionID);
			}
			break;
		case PGW:
			{
				pDelete = dbm->GetSQL(PGW_CDR_DeleteSession);
				sprintf(value, "%s%%", bizMsg->m_sessionID);
			}
			break;
		case ISMP:
			{
				pDelete = dbm->GetSQL(ISMP_DeleteSession);
				strncpy(value, bizMsg->m_sessionID,sizeof(value));
			}
			break;
		case DSL:
			{
				pDelete = dbm->GetSQL(DSL_DeleteSession);
                            if(bizMsg->m_spiltflag > 1)
                            {
                                 strncpy(value, bizMsg->m_childsessionID,sizeof(value));
                            }
                            else
                            {
                                 strncpy(value, bizMsg->m_sessionID,sizeof(value));
                            }

			}
			break;
        case DATA_5G:
			{
				//删除子会话会话
				pDelete = dbm->GetSQL(_5G_DeleteChildSession); //d_5g_childsession
				try
				{
					pDelete->DivTable(bizMsg->m_sessionID);
					pDelete->UnBindParam();
					pDelete->BindParam(1, bizMsg->m_sessionID);
					pDelete->Execute();
					pDelete->Connection()->Commit();
				}
				catch(UDBException& e)
				{
					pDelete->Connection()->Rollback();
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select  execption[%s]", e.ToString());
					return SM_OCP_UNABLE_TO_COMPLY;
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "delete session store ok, main sessionID[%s]", bizMsg->m_sessionID);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE,  "", "service type error[%d]", bizMsg->m_serviceContextID);
			}
			break;
	}

	try
	{
		pDelete->DivTable(bizMsg->m_sessionID);
		pDelete->UnBindParam();
		pDelete->BindParam(1, value);
		pDelete->Execute();
		pDelete->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		pDelete->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select  execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "delete session store ok[%s]",value);

	return 0;

}

int DCBizCdrNormal::ProduceCdr(STBizMsg* bizMsg,UCDRData &cdr,int iPayFlag, int iUserType)
{
	//DCMqProduceServer* producer = (DCMqProduceServer*)bizMsg->m_producer;

	DCSeriaEncoder en(ESeriaBinString);
	if(bizMsg->m_version == 2)
		cdr.head.flag = SM_CDR_FLAG_NORMAL;
	else if(bizMsg->m_version == 1)
		cdr.head.flag = SM_CDR_FLAG_OFFLINE;
	cdr.head.contextid = bizMsg->m_szServiceContextIDStr;
	cdr.head.latnid = bizMsg->m_userinfo->ilatnid;
	cdr.head.cycleid = (int)bizMsg->billcycle;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, bizMsg->m_serviceContextID, "", "cdr.head.latnid[%d]", cdr.head.latnid);
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			cdr.head.service = SM_CDR_TYPE_IN;
			break;
		case SMS:
			cdr.head.service = SM_CDR_TYPE_SMS;
			break;
		case DATA:
		case CCG:
		case PGW:
			cdr.head.service = SM_CDR_TYPE_PS;
			break;
		case ISMP:
			if(2 == iUserType)
			{
				cdr.head.service = SM_CDR_TYPE_HRS_AFTER;   // 高风险后付费用户
			}
			else if (1 == iUserType)
			{
				cdr.head.service = SM_CDR_TYPE_HRS;   // 高风险预付费用户
			}
			else
			{
				cdr.head.service = SM_CDR_TYPE_ISMP; // 非高风险业务
			}
			break;
		case DSL:
			cdr.head.service = SM_CDR_TYPE_DSL;
			break;
		case DATA_5G:
			cdr.head.service = SM_CDR_TYPE_5G;
			break;
		default:
			break;
	}

	try
	{
		en.encode(cdr);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	string str = HexEncode(en.data(),en.size());
	int ret = 0;
	if(bizMsg->m_testFlag == 0)
	{
		//DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "send to normal cdr topic[%s],payflag topic[%s]", bizMsg->m_cdrTopic,bizMsg->m_payflagTopic);
		//ret = producer->Produce(str.c_str(), str.length(), bizMsg->m_cdrTopic);

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "send to payflag topic[%s]", bizMsg->m_payflagTopic);
		/*bizMsg->m_topictype = 4;

		ret = bizMsg->m_plugin->call((void *)(&str), (void *)bizMsg);*/
		bizMsg->m_pSendMsg->insert(pair<string,string>("PAYFLAGTOPIC",cdr.body));
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "send to test cdr topic[%s]", bizMsg->m_testcdrTopic);
		//ret = producer->Produce(str.c_str(), str.length(), bizMsg->m_testcdrTopic);
		/*bizMsg->m_topictype = 3;
		ret = bizMsg->m_plugin->call((void *)(&str), (void *)bizMsg);*/
		bizMsg->m_pSendMsg->insert(pair<string,string>("TESTCDRTOPIC",cdr.body));
	}
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "send cdr failed,ret[%d]", ret);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "send CDR SM_NOR_CDR_IN succeed[%s], length[%d]", str.c_str(), str.length());
	}

	return 0;
}

//获取当前日期
int DCBizCdrNormal::timestampf()
{
	char buf[16] = {0};
	unsigned long long cur = 0;
	time_t et =  time(NULL);
	struct tm *p = localtime(&et);
	sprintf(buf, "%04d%02d%02d", (1900+p->tm_year), (1+p->tm_mon), p->tm_mday);
	cur = atoll(buf);
	return cur;
}

//[ratableId:offerId,unit,amount,total]
int DCBizCdrNormal::ChangeAmoutUnit(char *pBuf, DCAnsPara* smpara)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "before accu amout:[%s]", pBuf);
	int len = 0;
	int pos = 0;
	int pos2 = 0;
	int iendFlag = 0;
	len = strlen(pBuf);
	string str = pBuf;
	memset(pBuf, 0x00, 512);
	while(pos < len-1)
	{
		char value1[64] = {0};
		char value2[64] = {0};
		char value3[64] = {0};
		char value4[64] = {0};
		char value5[64] = {0};
		int i = 0;
		int j = 0;
		int m = 0;
		int n = 0;
		int k = 0;
		int l = 0;
		k = str.find(":", pos);
		if(pos==0 && -1 != k)
		{
			strcpy(value1, str.substr(pos, k-pos).c_str());
		}
		else if(-1 != k)
		{
			strcpy(value1, str.substr(pos+1, k-pos-1).c_str());
		}
		i = str.find(",", k+1);
		if(-1 != i)
		{
			strcpy(value2, str.substr(k+1, i-k-1).c_str());
		}
		j = str.find(",", i+1);
		if(-1 != j)
		{
			strcpy(value3, str.substr(i+1, j-i-1).c_str());
		}
		l = str.find(",", i+1);
		if(-1 != j)
		{
			strcpy(value4, str.substr(i+1, j-i-1).c_str());
		}
		m = str.find(";", j+1);
		if(-1 != m)
		{
			strcpy(value5, str.substr(j+1, m-j-1).c_str());
		}
		else
		{
			strcpy(value5, str.substr(j+1, len-j-1).c_str());
			iendFlag = 1;
		}
		if((0 == strcmp(value3, "4"))||(0 == strcmp(value3, "7"))||(0 == strcmp(value3, "8")))
		{
			long long amout = 0;
			amout = atoll(value5);
                     amout = (amout+1023)/1024;

			long long amouting = 0;
			amouting = atoll(value4);
                     amouting = (amouting+1023)/1024;

			sprintf(pBuf+pos2,"%s:%s,%s,%lld,%lld;", value1, value2,value3, amouting, amout);
			pos2 = strlen(pBuf);
		}
		else
		{
			sprintf(pBuf+pos2,"%s:%s,%s,%s,%s;", value1, value2, value3, value4,value5);
			pos2 = strlen(pBuf);
		}
		pos = m;

		if(1 == iendFlag)
		{
			break;
		}
	}
	pBuf[strlen(pBuf)-1] = smpara->GetCommonPara()->CdrEndchar;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "end accu amout:[%s]", pBuf);
	return 0;
}

int DCBizCdrNormal::ParaseFeeItemByRG(STBizMsg* bizMsg,  DataCDRInfo &stCdrInfo)
{
    //SUseInfo ChargeInfo[4] = {{-1,0,0,0},{-1,0,0,0},{-1,0,0,0},{-1,0,0,0}};
    SUseInfo ChargeInfo[8];
    for(int i=0; i < 8; i++)
    {
        ChargeInfo[i].id = -1;
    }

    STFeeItem *stFeeItem = stCdrInfo.stFeeItem;

    DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
    SCommonPara* commonPara = smpara->GetCommonPara();
    char fence = commonPara->CdrFence;

    //拆分chargeinfo
    int iChargeNum = 0;
    DCCommonIF::ParseUseInfo(stCdrInfo.chargeInfo, ChargeInfo,fence);
    for (int i = 0; i < 8 && ChargeInfo[i].id != -1; i++)
    {
        iChargeNum++;
        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "parase:szChargeInfo:id[%ld],unit[%ld],change[%ld],total[%ld]",
                    ChargeInfo[i].id,ChargeInfo[i].unit,ChargeInfo[i].change,ChargeInfo[i].total);
    }

    //查找账目类型对应的度量值
    for (int i = 0, j = 0; i < 4; i++)
    {
        if (iChargeNum > 4)
        {
            while (j < iChargeNum && ChargeInfo[j].change == 0) { j++; }
        }

        if (j < iChargeNum)
        {
            stFeeItem[i].iItem = ChargeInfo[j].id;
            stFeeItem[i].iFee = ChargeInfo[j].change;
            j++;
        }
        else
        {
            stFeeItem[i].iItem = -1;
            stFeeItem[i].iFee = 0;
        }

        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "push back B035[%d],B037[%d]", stFeeItem[i].iItem, stFeeItem[i].iFee);
    }

    return 0;
}


void DCBizCdrNormal::DealwithField(char* field)
{
	if(NULL == field)
	{
		return ;
	}

	char pStrTmp[256] = {0};
	char *pStr = field;
	char c = '\0';

	while(c = *pStr)
	{
		if('^' == c)
		{
			strcpy(pStrTmp, pStr + 1);
			*pStr = '\\';
			*++pStr = '^';
			strcpy(pStr + 1, pStrTmp);
		}
		else if('\\' == c)
		{
			strcpy(pStrTmp, pStr + 1);
			*++pStr = '\\';
			strcpy(pStr + 1, pStrTmp);
		}
		pStr++;
	}
}

char * DCBizCdrNormal::ChangeCallNumberValue(char * szIn,char*szOut)
{
	//安徽区号16个 （ 550～566,缺少565 ）
    char Area[16][4]={{"550"},{"551"},{"552"},{"553"},{"554"},{"555"},{"556"},{"557"},{"558"},{"559"},{"560"},{"561"},{"562"},{"563"},{"564"},{"566"}};
	char Area0[16][5]={{"0550"},{"0551"},{"0552"},{"0553"},{"0554"},{"0555"},{"0556"},{"0557"},{"0558"},{"0559"},{"0560"},{"0561"},{"0562"},{"0563"},{"0564"},{"0566"}};

    char * pStar = szIn;

    //去掉86
    if (strncmp(pStar,"86",2) ==0)
    {
            pStar=pStar+2;
    }

    //去掉区号
    for (int i = 0; i < 16; ++i)
    {
            char *pArea=Area[i];
            if (strncmp(pStar,pArea,3)==0)
            {
                    pStar=pStar+3;
                    strcpy(szOut,pStar);
                    return pStar;
            }
    }

    for (int i = 0; i < 16; ++i)
    {
            char *pArea=Area0[i];
            if (strncmp(pStar,pArea,4)==0)
            {
                    pStar=pStar+4;
                    strcpy(szOut,pStar);
                    return pStar;
            }
    }

	strcpy(szOut,pStar);
    return pStar;

}


int DCBizCdrNormal::ParaseFeeItem(STBizMsg* bizMsg, DataCDRInfo &stCdrInfo,vector<STFeeItem>& vecFeeItem)
{
	//SUseInfo ChargeInfo[4] = {{-1,0,0,0},{-1,0,0,0},{-1,0,0,0},{-1,0,0,0}};
	SUseInfo ChargeInfo[4];
	ChargeInfo[0].id = -1;
	ChargeInfo[1].id = -1;
	ChargeInfo[2].id = -1;
	ChargeInfo[3].id = -1;

	vector<int> vecPricingPlanID;
	vector<int> vecTariffID;

	STFeeItem stFeeItem;
	vector<int>::iterator itP;
	vector<int>::iterator itT;

	bool bFind = false;

	vecFeeItem.clear();

	//拆分chargeinfo
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	DCCommonIF::ParseUseInfo(stCdrInfo.chargeInfo, ChargeInfo,fence);
	for(int i=0;i<4;i++)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "parase:szChargeInfo:id[%ld],unit[%ld],change[%ld],total[%ld]",ChargeInfo[i].id,ChargeInfo[i].unit,ChargeInfo[i].change,ChargeInfo[i].total);
	}

	//拆分PRICING_PLAN_ID
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "split plan id", "");
	DCCommonIF::SplitFirstFiled(stCdrInfo.planInfo,vecPricingPlanID,';');

	//拆分TarriffInfo，得到TARIFF_ID
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "split tariff info", "");
	DCCommonIF::SplitFirstFiled(stCdrInfo.tarifInfo,vecTariffID,';');

	//查找账目类型对应的度量值
	for(int i=0; i < 4; i++)
	{
		bFind = false;
		stFeeItem.iItem = ChargeInfo[i].id;
		stFeeItem.iFee = ChargeInfo[i].change;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "push back B035[%d],B037[%d]", stFeeItem.iItem,stFeeItem.iFee);

		//保存账目类型和费用
		vecFeeItem.push_back(stFeeItem);

	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "vecFeeItem size[%d]", vecFeeItem.size());

	for(int k=0; k<4; k++)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "order over:B035[%d],B037[%d]", vecFeeItem[k].iItem,vecFeeItem[k].iFee);
	}

	return 0;
}

//获取查询优先级的条件字段:定价计划、账目类型、事件ID、资费ID
int DCBizCdrNormal::GetPriorityParam(STBizMsg* bizMsg,  vector<int>& vecPricingPlanID,vector<int>& vecTariffID,int& nEventTypeID,SUseInfo* stChargeInfo,char *sessionID)
{
	return 0;
}

void DCBizCdrNormal::AddFeeItem(vector<STFeeItem>& vecFeeItem,SCDRData* cdr,int nFieldNum)
{
	char szFee[16] = {0};
	char szItem[16] = {0};

	if(nFieldNum ==9)
	{
		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item RE_STR_SUB_VLR[]", "");

		return;
	}

	if(nFieldNum<=4)//Fee
	{
		sprintf(szFee,"%d",vecFeeItem[nFieldNum-1].iFee);// 1~4
		sprintf(szItem,"%d",vecFeeItem[nFieldNum-1].iItem);//5~8
		if(!strcmp("0",szFee))//为空的不需要写入清单
		{
			strcat(cdr->m_body, "0");
			strcat(cdr->m_body, "|");
			return;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add Fee[%d]:[%s]", nFieldNum-1,szFee);
		DealwithField(szFee);
		strcat(cdr->m_body, szFee);
	}
	else//Acct_Item_Type_Id
	{
		sprintf(szFee,"%d",vecFeeItem[nFieldNum-5].iFee);// 1~4
		sprintf(szItem,"%d",vecFeeItem[nFieldNum-5].iItem);//5~8
		if(!strcmp("-1",szItem))//为空的不需要写入清单
		{
			strcat(cdr->m_body, "|");
			return;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add ACCT_Item_Type_ID[%d]:[%s]", nFieldNum-5,szItem);
		DealwithField(szItem);
		strcat(cdr->m_body, szItem);
	}


	strcat(cdr->m_body, "|");



	return ;
}

void DCBizCdrNormal::AddExtCdrItem(STariffAccumCDRInfo tariffAccumCdrInfo,SCDRData* cdr,int nFieldNum)
{
	char filedValue[BIZ_TEMP_LEN_256] = {0};
	if(nFieldNum ==9)
	{
		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item RE_STR_SUB_VLR[]", "");

		return;
	}
	else if(nFieldNum == 11)
	{
		strcat(cdr->m_body,"1");
		strcat(cdr->m_body,"|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PAYMENT_TYPE[1]", "");
		return;
	}
	else if(nFieldNum == 12)
	{
 		sprintf(filedValue,"%ld", tariffAccumCdrInfo.offerId);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_OFFER_ID[%s]", filedValue);
		return;
	}
	else if(nFieldNum == 13)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.acctItemId);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_ACCT_ITEM_ID[%s]", filedValue);
		return;
	}
	else if(nFieldNum == 14)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.amount);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_FEE[%s]", filedValue);
		return;
	}
	else if(nFieldNum == 15)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.measure);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_ACCT_ITEM_UNIT_TYPE[%s]", filedValue);
		return;
	}
	else if(nFieldNum == 16)
	{
        long temp = tariffAccumCdrInfo.billingDuration;
        if(tariffAccumCdrInfo.measure == 3)
        {
            temp = (temp+1023)/1024;
        }
		sprintf(filedValue,"%ld", temp);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_BILLING_DURATION[%s]", filedValue);
		return;
	}
	else if(nFieldNum == 17)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.distFee);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_BAODI_FEE[%s]", filedValue);
		return;
	}
	else if(nFieldNum == 21)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.ratableValue);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
		strcat(cdr->m_body, "|");

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_RATABLE_VALUE[%s]", filedValue);

		return;
	}
	else if(nFieldNum == 22)
	{
		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_DISCT_OUT_RATABLE[]", "");

		return;
	}
	else if(nFieldNum == 24)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.counts);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_COUNTS[%s]", filedValue);
		return;
	}
	else if (nFieldNum == 25)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.ofrinstid);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_STR_OFRINSTID[%s]", filedValue);
		return;
	}
    else if (nFieldNum == 26)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.groupid);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_STR_PRICING_PLAN_ID[%s]", filedValue);
		return;
	}
	else if (nFieldNum == 27)
	{
		sprintf(filedValue,"%ld", tariffAccumCdrInfo.basefee);
 		DealwithField(filedValue);
 		strcat(cdr->m_body, filedValue);
 		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_BASE_FEE[%s]", filedValue);
		return;
	}

	strcat(cdr->m_body, "|");
	return ;
}


void DCBizCdrNormal::AddFeeItemByRG(STFeeItem *stFeeItem,SCDRData* cdr,int nFieldNum)
{
	char szFee[16] = {0};
	char szItem[16] = {0};

	if(nFieldNum ==9)
	{
		strcat(cdr->m_body, "|");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item RE_STR_SUB_VLR[]", "");

		return;
	}

	if(nFieldNum<=4)//Fee
	{
		sprintf(szFee,"%d",stFeeItem[nFieldNum-1].iFee);// 1~4
		sprintf(szItem,"%d",stFeeItem[nFieldNum-1].iItem);//5~8
		if(!strcmp("0",szFee))//为空的不需要写入清单
		{
			strcat(cdr->m_body, "0");
			strcat(cdr->m_body, "|");
			return;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add Fee[%d]:[%s]", nFieldNum-1,szFee);
		DealwithField(szFee);
		strcat(cdr->m_body, szFee);
	}
	else//Acct_Item_Type_Id
	{
		sprintf(szFee,"%d",stFeeItem[nFieldNum-5].iFee);// 1~4
		sprintf(szItem,"%d",stFeeItem[nFieldNum-5].iItem);//5~8
		if(!strcmp("-1",szItem))//为空的不需要写入清单
		{
			strcat(cdr->m_body, "|");
			return;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add ACCT_Item_Type_ID[%d]:[%s]", nFieldNum-5,szItem);
		DealwithField(szItem);
		strcat(cdr->m_body, szItem);
	}

	strcat(cdr->m_body, "|");


	return ;
}


int DCBizCdrNormal::GetBlanceInfo2(char* ALLUSU, char* USU)
{
    int a=strlen(ALLUSU);
    if(';'!=ALLUSU[a-1] && a!=0)
    {
        ALLUSU[a] = ';';
        ALLUSU[a+1] = '\0';
    }

    a=strlen(USU);
    if(';'!=USU[a-1] && a!=0)
    {
        USU[a] = ';';
        USU[a+1] = '\0';
    }

    string strDes(ALLUSU);
    string strOrg(USU);
    char fenceStr[2]= {0};
    char endStr[2]= {0};

    fenceStr[0] = m_fence;
    endStr[0] = m_endfence;

    /*fomat*/
    int pos = 0;
    int nstart=0;
    int len = strOrg.length();
    do
    {
        pos = strOrg.find(":", pos+1);
        if(-1 != pos)
        {
            pos = strOrg.find(fenceStr, pos +1);
            if(-1 != pos)
            {
                pos = strOrg.find(fenceStr, pos +1);
                if(-1 != pos)
                {
                    string id1 = strOrg.substr(nstart, pos-nstart);
                    int tmppos = strDes.find(id1);       //A01:1001找到
                    if(-1 != tmppos)
                    {
                        tmppos = strDes.find(fenceStr, tmppos+id1.length());
                        int tmppos2 = strDes.find(fenceStr, tmppos+2);
                        long val1 = atol(strDes.substr(tmppos+1, tmppos2-tmppos-1).c_str());

                        int posOrg1 = strOrg.find(fenceStr, pos+2);//累加后替换
                        long valOrg = atol(strOrg.substr(pos+1, posOrg1-pos-1).c_str());
                        val1 +=valOrg;
                        char tmpbuf[32];
                        sprintf(tmpbuf, "%ld", val1);
						tmpbuf[strlen(tmpbuf)]='\0';
                        strDes.replace(tmppos+1, tmppos2-tmppos-1, tmpbuf);
                        pos = posOrg1;

                        tmppos = strDes.find(fenceStr, tmppos2);
                        tmppos2= strDes.find(";", tmppos);
                        //找到位置后替换
                        posOrg1 = strOrg.find(";", pos+2);
                        valOrg = atol(strOrg.substr(pos+1, posOrg1-pos-1).c_str());
                        sprintf(tmpbuf, "%ld", valOrg);

						tmpbuf[strlen(tmpbuf)]='\0';
                        strDes.replace(tmppos+1, tmppos2-tmppos-1, tmpbuf);
                        nstart = posOrg1+1;
                        pos = nstart +1;
                    }
                    else     //找不到，逐个追加
                    {
                        pos = strOrg.find(";", pos+1);
                        if(-1!=pos)
                        {
                            string id = strOrg.substr(nstart, pos-nstart);
                            strDes+=id;
                            strDes+=";";

                            pos+=1;
                            nstart = pos;
                            continue;
                        }
                    }
                }
            }
            else
            {
                break;
            }

        }
        else
        {
               break;
        }
       // pos ++;
    }while(pos != len);

    strcpy(ALLUSU, strDes.c_str());
    int lens = strlen(ALLUSU);
    ALLUSU[lens-1] = m_endfence;
    ALLUSU[lens] = '\0';
    return 0;
}

//[890039:2,0;]
//[890039:2,0;190026:2,0;]
int DCBizCdrNormal::GetChargeInfo(char* ALLUSU, char* USU)
{
    int a=strlen(ALLUSU);
    if(';'!=ALLUSU[a-1] && a!=0)
    {
        ALLUSU[a] = ';';
        ALLUSU[a+1] = '\0';
    }

    a=strlen(USU);
    if(';'!=USU[a-1] && a!=0)
    {
        USU[a] = ';';
        USU[a+1] = '\0';
    }

    string strDes(ALLUSU);
    string strOrg(USU);
    char fenceStr[2]= {0};
    char endStr[2]= {0};

    fenceStr[0] = m_fence;
    endStr[0] = m_endfence;

    /*fomat*/
    int pos = 0;
    int nstart=0;
    int len = strOrg.length();
    do
    {
        pos = strOrg.find(":", pos+1);
        if(-1 != pos)
        {
            string id2 = strOrg.substr(nstart, pos-nstart);
            int pos1 = strDes.find(id2);
            if(-1 != pos1)
            {
                pos1 = strDes.find(fenceStr, pos1+1);
                if(-1!=pos1)
                {
                    int pos2 = strDes.find(";", pos1+1);
                    long val1 = atol(strDes.substr(pos1+1, pos2-pos-1).c_str());

                    pos = strOrg.find(fenceStr, pos+1);
                    int pos3 = strOrg.find(";", pos+1);
                    long val2 = atol(strOrg.substr(pos+1, pos3-pos-1).c_str());

                    val1+=val2;
                    char tmpbuf[32] = {0};
                    sprintf(tmpbuf, "%ld", val1);
					tmpbuf[strlen(tmpbuf)]='\0';
                    strDes.replace(pos1+1, pos2-pos1-1, tmpbuf);
                    pos = pos3+1;
                    nstart = pos;
                }
                else
                {
                    break;
                }
            }
            else
            {
                pos = strOrg.find(";", pos+1);
                if(-1!=pos)
                {
                    string id = strOrg.substr(nstart, pos-nstart);
                    strDes+=id;
                    strDes+=";";

                    pos+=1;
                    nstart = pos;
                    continue;
                }
            }
        }
        else
        {
            break;
        }
       // pos ++;
    }while(pos != len);

    /*find*/
    //sprintf(ALLUSU, "%s", strDes.c_str());
    strcpy(ALLUSU, strDes.c_str());
    int lens = strlen(ALLUSU);
    ALLUSU[lens-1] = m_endfence;
    ALLUSU[lens] = '\0';
    return 0;
}

//[ofrid:acctitemid,measure,amount,billingduration,distfee,groupid,ofrinstid,ncounts;]
//806665295:896700,3,0,10240,0,2488027,180190471,1
int DCBizCdrNormal::GetTariffInfo(char* ALLUSU, char* USU)
{
	STariffInfo tariff[20];
	STariffInfo all[100];
	DCCommonIF::ParseTariffInfo(ALLUSU, all,m_fence);
	DCCommonIF::ParseTariffInfo(USU, tariff,m_fence);
	ALLUSU[0] = '\0';

	for(int i=0;i<20;i++)
	{
		if ((0 == tariff[i].offerId)
			 && (0 == tariff[i].acctItemId)
			  && (0 == tariff[i].measure)
			   && (0 == tariff[i].amount)
				&& (0 == tariff[i].billingDuration)
				 && (0 == tariff[i].distFee)
				  && (0 == tariff[i].ofr_inst_id)
				   && (0 == tariff[i].counts))
		{
			break;
		}
		else
		{
			for(int j=0;j<100;j++)
			{
				if((0 == all[j].offerId)
					 && (0 == all[j].acctItemId)
					  && (0 == all[j].measure)
					   && (0 == all[j].amount)
					    && (0 == all[j].billingDuration)
					     && (0 == all[j].distFee)
					      && (0 == all[j].ofr_inst_id)
					       && (0 == all[j].counts))
				{
					memcpy(&all[j], &tariff[i], sizeof(STariffInfo));
					break;
				}
				else
				{
					if((all[j].offerId == tariff[i].offerId)
						 && (all[j].acctItemId == tariff[i].acctItemId)
						 	&&(all[j].ofr_inst_id == tariff[i].ofr_inst_id))
					{
						all[j].amount+= tariff[i].amount;
						all[j].distFee+= tariff[i].distFee;
						all[j].billingDuration+= tariff[i].billingDuration;
						all[j].counts+= tariff[i].counts;
						break;
					}
				}
			}
		}
	}

	for(int k=0; k<100; k++)
	{
		if((all[k].offerId==0)
				&& (all[k].amount==0)
					&& (all[k].acctItemId==0)
						&& (all[k].measure==0)
							&& (all[k].billingDuration==0)
								&& (all[k].distFee==0)
									&& (all[k].ofr_inst_id==0)
					       				&& (all[k].counts==0))
		{
			break;
		}
		sprintf(ALLUSU + strlen(ALLUSU), "%ld:%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld;", all[k].offerId, all[k].acctItemId,m_fence,all[k].measure,m_fence,all[k].amount,m_fence,all[k].billingDuration,m_fence,all[k].distFee,m_fence,all[k].groupid,m_fence,all[k].ofr_inst_id,m_fence,all[k].counts,m_fence,all[k].refacctItemId);
	}

	int lens = strlen(ALLUSU);
	ALLUSU[lens-1] = m_endfence;
	ALLUSU[lens] = '\0';
	return 0;

}

//230:806665295,4,10240,225280,180190471
//[ratableId:offerId,unit,amount,total,ofrinstid]
int DCBizCdrNormal::GetAccumulateInfo(char* ALLUSU, char* USU)
{
       SAccumInfo accum[20];
	SAccumInfo all[100];
	DCCommonIF::ParseAccumlatorInfo(ALLUSU, all,m_fence);
	DCCommonIF::ParseAccumlatorInfo(USU, accum,m_fence);
	ALLUSU[0] = '\0';

	for(int i=0;i<20;i++)
	{
		if ((0 == accum[i].accu_type_id)
			 && (0 == accum[i].offerId)
			  && (0 == accum[i].unit)
			   && (0 == accum[i].amount)
				&& (0 == accum[i].total)
				 && (0 == accum[i].ofrinstid))
		{
			break;
		}
		else
		{
			for(int j=0;j<100;j++)
			{
				if((0 == all[j].accu_type_id)
					 && (0 == all[j].offerId)
					  && (0 == all[j].unit)
					   && (0 == all[j].amount)
					    && (0 == all[j].total)
					     && (0 == all[j].ofrinstid))
				{
					memcpy(&all[j], &accum[i], sizeof(SAccumInfo));
					break;
				}
				else
				{
					if((all[j].accu_type_id == accum[i].accu_type_id)
						 && (all[j].offerId == accum[i].offerId)
						 && (all[j].ofrinstid == accum[i].ofrinstid))
					{
						all[j].amount+= accum[i].amount;
						break;
					}
				}
			}
		}
	}

	for(int k=0; k<100; k++)
	{
		if((all[k].accu_type_id==0)
				&& (all[k].offerId==0)
					&& (all[k].amount==0)
						&& (all[k].total==0)
						 && (all[k].ofrinstid == 0))
		{
			break;
		}
		sprintf(ALLUSU + strlen(ALLUSU), "%ld:%ld%c%ld%c%ld%c%ld%c%ld%c%ld;",  all[k].accu_type_id,all[k].offerId,m_fence,all[k].unit,m_fence,all[k].amount,m_fence,all[k].total,m_fence,all[k].ofrinstid,m_fence,all[k].accu_id);
	}

	int lens = strlen(ALLUSU);
	ALLUSU[lens-1] = m_endfence;
	ALLUSU[lens] = '\0';
	return 0;
}


//[H13:4,1024,540672]
//[0:0,0,0;H13:4,1024,540672;]
int DCBizCdrNormal::GetBalanceInfo(char* ALLUSU, char* USU)
{
    int a=strlen(ALLUSU);
    if(';'!=ALLUSU[a-1] && a!=0)
    {
        ALLUSU[a] = ';';
        ALLUSU[a+1] = '\0';
    }

    a=strlen(USU);
    if(';'!=USU[a-1] && a!=0)
    {
        USU[a] = ';';
        USU[a+1] = '\0';
    }

    string strDes(ALLUSU);
    string strOrg(USU);
    char fenceStr[2]= {0};
    char endStr[2]= {0};

    fenceStr[0] = m_fence;
    endStr[0] = m_endfence;

    /*fomat*/
    int pos = 0;
    int nstart=0;
    int len = strOrg.length();
    do
    {
        pos = strOrg.find(":", pos+1);
        if(-1 != pos)
        {
            pos = strOrg.find(fenceStr, pos +1);
            if(-1 != pos)
            {
                string id1 = strOrg.substr(nstart, pos-nstart);
                int tmppos = strDes.find(id1);       //A01:1001找到
                if(-1 != tmppos)
                {
                    tmppos = strDes.find(fenceStr, tmppos+2);
                    int tmppos2 = strDes.find(fenceStr, tmppos+2);
                    long val1 = atol(strDes.substr(tmppos+1, tmppos2-tmppos-1).c_str());

                    int posOrg1 = strOrg.find(fenceStr, pos+2);//累加后替换
                    long valOrg = atol(strOrg.substr(pos+1, posOrg1-pos-1).c_str());
                    val1 +=valOrg;
                    char tmpbuf[32]={0};
                    sprintf(tmpbuf, "%ld", val1);
					tmpbuf[strlen(tmpbuf)]='\0';
                    strDes.replace(tmppos+1, tmppos2-tmppos-1, tmpbuf);
                    pos = posOrg1;

                    tmppos = strDes.find(fenceStr, tmppos2);
                    tmppos2= strDes.find(";", tmppos);
                    //找到位置后替换
                    posOrg1 = strOrg.find(";", pos+2);
                    valOrg = atol(strOrg.substr(pos+1, posOrg1-pos-1).c_str());
                    sprintf(tmpbuf, "%ld", valOrg);
					tmpbuf[strlen(tmpbuf)]='\0';
                    strDes.replace(tmppos+1, tmppos2-tmppos-1, tmpbuf);
                    nstart = posOrg1+1;
                    pos = nstart +1;
                }
                else     //找不到，逐个追加
                {
                    pos = strOrg.find(";", pos+1);
                    if(-1!=pos)
                    {
                        string id = strOrg.substr(nstart, pos-nstart);
                        strDes+=id;
                        strDes+=";";

                        pos+=1;
                        nstart = pos;
                        continue;
                    }
                }
            }
        }
        else
        {
            break;
        }
       // pos ++;
    }while(pos != len);

    /*find*/
    //sprintf(ALLUSU, "%s", strDes.c_str());
    strcpy(ALLUSU, strDes.c_str());
    int lens = strlen(ALLUSU);
    ALLUSU[lens-1] = m_endfence;
    ALLUSU[lens] = '\0';
    return 0;

}


int DCBizCdrNormal::CombinePlanIDInfo(char* pBufAll, char* pBuf)
{
	char tmp[256] = {0};
	long lValue = 0;

	bool bFindFlag = false;             //定价计划是否存在标识

	if(1 == m_para->GetCommonPara()->iPricePlanID)
	{
		if(0 == strlen(pBufAll))
		{
			sprintf(pBufAll, "%s", pBuf);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,SM_OTHER_TYPE,  "", "no element[%s],add", pBuf);
		}
		else
		{
			string str = pBufAll;
			int len = strlen(pBufAll);
			string::size_type pos = 0, prev_pos = 0;
			while(len)//解析出每个定价计划ID
			{
				pos = str.find("#",pos);
				if(pos>= len)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "pos[%d], len[%d]", pos, len);
					break;
				}

				if(-1 != pos)
				{
			      		//strcpy(value, (str.substr(pos,len).c_str()));
			      		prev_pos = pos + 1;
					pos++;
				}
				else
				{
					strcpy(tmp, (str.substr(prev_pos,len-prev_pos).c_str()));
					break;
				}

			}

			if(strlen(tmp))
			{
				sprintf(pBuf, "%s", tmp);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"",  "get plan id[%s]", tmp);
			}
		}
		return RET_SUCCESS;
	}


	if(0 == strlen(pBufAll))
	{
		sprintf(pBufAll, "%s", pBuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG,SM_OTHER_TYPE,  "", "no element[%s],add", pBuf);
	}
	else
	{
		string str = pBufAll;
		string::size_type pos = 0, prev_pos = 0;
		while(true)//解析出每个定价计划ID
		{
			pos = str.find(";",pos);
			if(-1 != pos)
			{
		      lValue = atol(str.substr(prev_pos,pos).c_str());

		      prev_pos = pos + 1;

				if(atol(pBuf) == lValue)
				{
					bFindFlag = true;//存在
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "find element[%d],skip", lValue);
					break;
				}
				pos++;
			}
			else
			{
				break;
			}

		}

		if(!bFindFlag)//不存在，加入
		{
			sprintf(pBufAll, "%s%s;", pBufAll,pBuf);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "no find element[%s],add", pBuf);
		}
	}



	//pBuf[strlen(pBuf)-1] = SM_CDR_END_CHAR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "combine over:element[%s]", pBufAll);

	return RET_SUCCESS;

}

//[890039:2,0;]
//[890039:2,0;190026:2,0;]
int DCBizCdrNormal::GetOriChargeInfo(char* ALLUSU, char* USU)
{
    int a=strlen(ALLUSU);
    if(';'!=ALLUSU[a-1] && a!=0)
    {
        ALLUSU[a] = ';';
        ALLUSU[a+1] = '\0';
    }

    a=strlen(USU);
    if(';'!=USU[a-1] && a!=0)
    {
        USU[a] = ';';
        USU[a+1] = '\0';
    }

    string strDes(ALLUSU);
    string strOrg(USU);
    char fenceStr[2]= {0};
    char endStr[2]= {0};

    fenceStr[0] = m_fence;
    endStr[0] = m_endfence;

    /*fomat*/
    int pos = 0;
    int nstart=0;
    int len = strOrg.length();
    do
    {
        pos = strOrg.find(":", pos+1);
        if(-1 != pos)
        {
            string id2 = strOrg.substr(nstart, pos-nstart);
            int pos1 = strDes.find(id2);
            if(-1 != pos1)
            {
                pos1 = strDes.find(fenceStr, pos1+1);
                if(-1!=pos1)
                {
                    int pos2 = strDes.find(";", pos1+1);
                    long val1 = atol(strDes.substr(pos1+1, pos2-pos-1).c_str());

                    pos = strOrg.find(fenceStr, pos+1);
                    int pos3 = strOrg.find(";", pos+1);
                    long val2 = atol(strOrg.substr(pos+1, pos3-pos-1).c_str());

                    val1+=val2;
                    char tmpbuf[32] = {0};
                    sprintf(tmpbuf, "%ld", val1);
					tmpbuf[strlen(tmpbuf)]='\0';
                    strDes.replace(pos1+1, pos2-pos1-1, tmpbuf);
                    pos = pos3+1;
                    nstart = pos;
                }
                else
                {
                    break;
                }
            }
            else
            {
                pos = strOrg.find(";", pos+1);
                if(-1!=pos)
                {
                    string id = strOrg.substr(nstart, pos-nstart);
                    strDes+=id;
                    strDes+=";";

                    pos+=1;
                    nstart = pos;
                    continue;
                }
            }
        }
        else
        {
            break;
        }
       // pos ++;
    }while(pos != len);

    /*find*/
    //sprintf(ALLUSU, "%s", strDes.c_str());
    strcpy(ALLUSU, strDes.c_str());
    int lens = strlen(ALLUSU);
    ALLUSU[lens-1] = m_endfence;
    ALLUSU[lens] = '\0';
    return 0;
}


// 十六进制转换成十进制
void DCBizCdrNormal::hextod(const char* p,int &dest)
{
        int a=strlen(p);
	 int j = 0;
        for(int i=0;i<a;i++)
	 {
		  if(*(p+i)<='f'&&*(p+i)>='a')
		   j=(int)(*(p+i))-97;
		  else if(*(p+i)<='F'&&*(p+i)>='A')
		   j=(int)(*(p+i))-65;
		  else
		   j=(int)(*(p+i))-48;
		  dest=dest+pow(16.0,a-i-1)*j;
	 }
}

//分解B07,B06,用于按B07组拆单
int DCBizCdrNormal::ParaseTariffAccumCdrInfo(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,std::vector<STariffAccumCDRInfo> &tariffVec)
{
	char sz_accuminfo[512] = {0};
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;
	
	STariffInfo tariff[12];
	SAccumInfo accum[12];
	SOriChargeInfo orichg[12];
	STariffAccumCDRInfo tmp;
	//取出tariffInfo 及accumulateInfo信息
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","TariffInfo: [%s]",stCdrInfo.tarifInfo);
	DCCommonIF::ParseTariffInfo(stCdrInfo.tarifInfo, tariff,fence);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","AccumulateInfo: [%s]",stCdrInfo.accumuInfo);
	DCCommonIF::ParseAccumlatorInfo(stCdrInfo.accumuInfo,accum,fence);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "","OriChargeInfo: [%s]",stCdrInfo.orichargeInfo);
	DCCommonIF::ParseOriChargeInfo(stCdrInfo.orichargeInfo,orichg,fence);

	//计算标批费用分摊分母
	for(int i=0;i<12;i++)
	{
		if(0 == orichg[i].acctItemId && 0 == orichg[i].measure && 0 == orichg[i].oriAmount)
		{
			break;
		}
		orichg[i].measure = 0;
		for(int j=0;j<12;j++)
		{
			if( tariff[j].billingDuration && (tariff[j].refacctItemId<=0 || tariff[j].refacctItemId == orichg[i].acctItemId))
			{
				orichg[i].measure += tariff[j].billingDuration;
			}
		}
	}

	for(int i=0;i<12;i++)
	{
		if( (0 == tariff[i].measure) && (0 == tariff[i].billingDuration) && (0 == tariff[i].amount))
		{
			continue;
		}
		else
		{
			tmp.offerId = tariff[i].offerId;
			tmp.acctItemId = tariff[i].acctItemId;
			tmp.measure = tariff[i].measure;
			tmp.amount = tariff[i].amount;
			tmp.billingDuration = tariff[i].billingDuration;
			tmp.distFee = tariff[i].distFee;
			tmp.groupid= tariff[i].groupid;
			tmp.counts = tariff[i].counts;
			tmp.ofrinstid= tariff[i].ofr_inst_id;
			tmp.lnOriTariff = tariff[i].ori_tariff;
			tmp.lnDisctTariff = tariff[i].disct_tariff;

			//分摊标批费用
			tmp.basefee = 0;
			for(int j = 0; j < 12 && tmp.billingDuration; j++)
			{
				if(0 == orichg[j].acctItemId && 0 == orichg[j].measure && 0 == orichg[j].oriAmount)
				{
					break;
				}
				if(tariff[i].refacctItemId <= 0 || tariff[i].refacctItemId == orichg[j].acctItemId)
				{
					long lntmp = Div(orichg[j].oriAmount*tmp.billingDuration, orichg[j].measure, '3');
					tmp.basefee += lntmp;
					orichg[j].measure -= tmp.billingDuration;
					orichg[j].oriAmount -= lntmp;
				}
			}

			tariffVec.push_back(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ParseTariffInfo offerId[%ld],acctItemId[%ld],measure[%ld],amount[%ld],billingDuration[%ld],distFee[%ld],groupid[%ld],ofr_inst_id[%ld],counts[%ld],ori_tariff[%ld],disct_tariff[%ld]",
					tmp.offerId,tmp.acctItemId,tmp.measure,tmp.amount,tmp.billingDuration, tmp.distFee,tmp.groupid,
					tmp.ofrinstid,tmp.counts, tmp.lnOriTariff, tmp.lnDisctTariff);
		}
	}

	std::vector<STariffAccumCDRInfo>::iterator iter;

	for(int i=0;i<12;i++)
	{
		if((0 == accum[i].offerId)
			 && (0 == accum[i].accu_type_id)
			  && (0 == accum[i].unit)
			   && (0 == accum[i].amount)
			    && (0 == accum[i].total)
			    && (0 == accum[i].ofrinstid))
		{
			break;
		}
		else
		{
			//sprintf(sz_accuminfo,"%ld:%ld%c%ld%c%ld%c%ld%c%ld",accum[i].ratableBalanceId,accum[i].offerId,fence,accum[i].unit,fence,accum[i].amount,fence,accum[i].total,fence,accum[i].ofrinstid);
			// modify by zhuhuan, date: 20200727, 改成江西HB输出格式: accu_id:used amount|updateflag|usage_voice|usage_sms|accu_use_id|strBillCycle
			sprintf(sz_accuminfo, "%ld:%ld%c%d%c%ld%c%ld%c%ld%c%ld%c",
					accum[i].accu_id,
					accum[i].amount, fence,
					accum[i].updateflag, fence,
					accum[i].usage_voice, fence,
					accum[i].usage_sms, fence,
					accum[i].accu_use_id, fence,
					accum[i].billingCycle, endfence);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","sz_accuminfo[%s]", sz_accuminfo);


			for(iter = tariffVec.begin();iter != tariffVec.end();iter++)
			{
				if(iter->offerId == accum[i].offerId && iter->ofrinstid == accum[i].ofrinstid)
				{
					if (iter->measure == 3 && accum[i].unit == 4)
					{
						/*
						long nRatableValue = iter->billingDuration;
						if(0 != nRatableValue%1024)
						{
							nRatableValue = nRatableValue/1024+1;
						}
						else
						{
							nRatableValue = nRatableValue/1024;
						}
						*/
						iter->ratableValue = accum[i].total - accum[i].amount ;
					}

					if (iter->measure == 1)
					{
						long nRatableValue = 0 ;
						if (accum[i].unit == 1)
						{
							nRatableValue = accum[i].total - accum[i].amount ;
							if(0 != nRatableValue%60)
							{
								nRatableValue = nRatableValue/60+1;
							}
							else
							{
								nRatableValue = nRatableValue/60;
							}
							iter->ratableValue = nRatableValue;
						}

						if (accum[i].unit == 2)
						{
							/*
							 nRatableValue = iter->billingDuration;
							if(0 != nRatableValue%60)
							{
								nRatableValue = nRatableValue/60+1;
							}
							else
							{
								nRatableValue = nRatableValue/60;
							}
							*/
							iter->ratableValue = accum[i].total - accum[i].amount ;
						}
					}

					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ParseTariffInfo offerId[%ld],ofr_inst_id[%ld],measure[%ld],unit[%ld],amount[%ld],total[%ld],ratableValue[%ld] ",
						iter->offerId,iter->ofrinstid,iter->measure,accum[i].unit,accum[i].amount,accum[i].total,iter->ratableValue );

					if(iter->flag == 0)
					{
						strcpy(iter->sz_accumInfo,sz_accuminfo);
						iter->flag = 1;
					}
					else
					{
						char temp[512] = {0};
						sprintf(temp,"%s;%s",iter->sz_accumInfo,sz_accuminfo);
						strcpy(iter->sz_accumInfo,temp);
					}
					break;
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","TariffAccumulateInfo: [%s]",iter->sz_accumInfo);
			}
		}
	}

	return 0;


}

long DCBizCdrNormal::Div(long f, long v, char mode)
{
	long count = f / v;
	long tail = f - count * v;
	if(mode == '2' && tail > 0) 	// 向上圆整
		count++;
	else if(mode == '3' && 2*tail >= v) // 四舍五入
		count++;
	return count;
}


int DCBizCdrNormal::GetAccumulationInfo(STBizMsg* bizMsg)
{
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	char szSessionId[256] = {0};
	char value[256] = {0};
	int iMsgType = 0;
	UDBSQL *pQuery = NULL;
	UDBSQL *pDelete = NULL;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			pQuery = dbm->GetSQL(Voice_Query_RatingMsg);
			pDelete = dbm->GetSQL(Voice_Delete_RatingMsg);
			break;
		case DATA:
		case CCG:
			pQuery = dbm->GetSQL(DATA_Query_RatingMsg);
			pDelete = dbm->GetSQL(DATA_Delete_RatingMsg);
			break;
		case PGW:
			pQuery = dbm->GetSQL(PGW_Query_RatingMsg);
			pDelete = dbm->GetSQL(PGW_Delete_RatingMsg);
			break;
		case DSL:
			pQuery = dbm->GetSQL(DSL_Query_RatingMsg);
			pDelete = dbm->GetSQL(DSL_Delete_RatingMsg);
			break;
	}

	if(SM_SESSION_EVENT_CODE == bizMsg->m_requestType || SM_SESSION_XDR_CODE == bizMsg->m_requestType)
	{
		ocs::RatingMessageInfo_t* ratingMsg = (ocs::RatingMessageInfo_t*)bizMsg->m_ratingMsg;
		if(ratingMsg)
		{
			vector<std::string>::iterator iter;
			iter =  ratingMsg->vAccunumlationInfo.begin();
			while(iter != ratingMsg->vAccunumlationInfo.end())
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,"", "CDR ACCUMULATION:%s", iter->c_str());
				bizMsg->m_pSendMsg->insert(pair<string,string>("ACCUMULATION",*iter));
				iter++;
			}

			iter = ratingMsg->vRatableInfo.begin();
			while(iter != ratingMsg->vRatableInfo.end())
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,"", "CDR RATABLEINFO:%s", iter->c_str());
				bizMsg->m_pSendMsg->insert(pair<string,string>("RATABLEINFO",*iter));
				iter++;
			}

			iter = ratingMsg->vSumInfo.begin();
			while(iter != ratingMsg->vSumInfo.end())
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,"", "CDR SUMINFO:%s", iter->c_str());
				bizMsg->m_pSendMsg->insert(pair<string,string>("SUMINFO",*iter));
				iter++;
			}
		}
	}
	else
	{
		if(VOICE==bizMsg->m_serviceContextID || DSL==bizMsg->m_serviceContextID)
		{
			sprintf(szSessionId, "%s", bizMsg->m_sessionID);
		}
		else if(bizMsg->m_longCDR != 0 && bizMsg->m_longCDR != 3 &&  bizMsg->m_ratingGroup > 0)
		{
			sprintf(szSessionId, "%s;%ld", bizMsg->m_sessionID, bizMsg->m_ratingGroup);
		}
		else
		{
			sprintf(szSessionId, "%s%%", bizMsg->m_sessionID);
			if(bizMsg->m_serviceContextID == PGW)
			{
				pQuery = dbm->GetSQL(PGW_Query_RatingMsg_ALL);
				pDelete = dbm->GetSQL(PGW_Delete_RatingMsg_ALL);
			}
			else if(bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == DATA)
			{
				pQuery = dbm->GetSQL(DATA_Query_RatingMsg_ALL);
				pDelete = dbm->GetSQL(DATA_Delete_RatingMsg_ALL);
			}
			else
			{
				pQuery = dbm->GetSQL(_5G_Query_RatingMsg_ALL);
            	pDelete = dbm->GetSQL(_5G_Delete_RatingMsg_ALL);
			}
		}


		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, szSessionId);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "select child session[%s]", szSessionId);

		try
		{
			pQuery->Execute();
			bool findRecord = false;
			while(pQuery->Next())
			{
				findRecord = true;
				pQuery->GetValue(1, value);
				iMsgType = atoi(value);
				pQuery->GetValue(2, value);
				if(0 == iMsgType)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,"", "CDR ACCUMULATION:%s", value);
					bizMsg->m_pSendMsg->insert(pair<string,string>("ACCUMULATION",value));
				}
				else if(1 == iMsgType)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,"", "CDR RATABLEINFO:%s", value);
					bizMsg->m_pSendMsg->insert(pair<string,string>("RATABLEINFO",value));
				}
				else if(2 == iMsgType)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,"", "CDR SUMINFO:%s", value);
					bizMsg->m_pSendMsg->insert(pair<string,string>("SUMINFO",value));
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "msgType error!");
				}
			}
			if(!findRecord)
			{
				DCBIZLOG(DCLOG_LEVEL_INFO, SM_DATA_TYPE, "", "no find ratinginfo");
				return 0;
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select DATA execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}

		try
		{
			pDelete->DivTable(bizMsg->m_sessionID);
			pDelete->UnBindParam();
			pDelete->BindParam(1, szSessionId);
			pDelete->Execute();
			pDelete->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			pDelete->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "delete  execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	return 0;

}

int DCBizCdrNormal::ModifyREAMsg(STBizMsg* bizMsg,SREAInfo &REAMsg)
{
	int ret =0;

    EraseZeroItem(bizMsg);

	ret = ModifyBalanceInfo(bizMsg,REAMsg.sz_balanceInfo);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[balanceInfo]");
		return ret;
	}

	ret = ModifyBalanceInfo2(bizMsg, REAMsg.sz_balanceInfo2);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[balanceInfo2]");
		return ret;
	}

	ret = ModifyAccumlatorInfo(bizMsg, REAMsg.sz_accumlatorInfo, sizeof(REAMsg.sz_accumlatorInfo) - 1);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[accumlatorInfo]");
		return ret;
	}

	ret = ModifyTariffIdInfo(bizMsg, REAMsg.sz_tariffIdInfo, sizeof(REAMsg.sz_tariffIdInfo) - 1);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[tariffIdInfo]");
		return ret;
	}

	ret = ModifyChargeInfo(bizMsg, REAMsg.sz_chargeInfo, &REAMsg.sz_gAmount);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[chargeInfo]");
		return ret;
	}

	ret = ModifyOriChargeInfo(bizMsg, REAMsg.sz_oriChargeInfo);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "error[oriChargeInfo]");
		return ret;
	}

	return ret;
}

int DCBizCdrNormal::EraseZeroItem(STBizMsg* bizMsg)
{
        rbresult *base = (rbresult *)bizMsg->m_base;
        bool nTariffFind = false;
        bool nAccumFind = false;


        //累积量信息B06
	int size = base->accv.size();
	for (int k = 0; k < size; k++)
	{
            if(base->accv[k].amount == 0)
            {
                 nAccumFind = true;
                 break;
            }
       }

       if(!nAccumFind) return 0;

        //资费信息B07
	size = base->trv.size();
	for (int i = 0; i < size; i++)
	{
            if(base->trv[i].amount == 0 && base->trv[i].billing_duration == 0)
            {
                 nTariffFind = true;
                 break;
            }
	}

       if(!nTariffFind) return 0;

       //累积量与资费均存在为0的则删除值为0的组
       for (std::vector<accum_item_t >::iterator it = base->accv.begin(); it != base->accv.end(); )
	{
            if(it->amount == 0)
            {
                 DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "erase accum item, ratable_id[%ld], amount[%ld]", it->accu_id, it->amount);
                 it = base->accv.erase(it);
            }
            else
            {
                it++;
            }
       }

       for (std::vector<tariff_t >::iterator iv = base->trv.begin(); iv != base->trv.end(); )
	{
            if(iv->amount == 0 && iv->billing_duration == 0)
            {
                 DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "erase tariff item, ofr_id[%ld], amount[%ld]", iv->ofr_id, iv->amount);
                 iv = base->trv.erase(iv);
            }
            else
            {
                iv++;
            }
       }
       return 0;
}

int DCBizCdrNormal::ModifyBalanceInfo(STBizMsg* bizMsg, char* pBuf)
{
	int ret					= RET_SUCCESS;
	SUseInfo cur			;

	SUseInfo all[6]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	//获取原先信息
	ret = DCCommonIF::ParseUseInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//账本信息B08
	int size = base->actv.size();
	for (int i = 0; i < size; i++)
	{
		acctchangeitem_t acctitem = base->actv[i];
		if(acctitem.seq == 0)
		{
			continue;
		}
		cur.id = acctitem.acct_item_id;
		cur.unit = acctitem.unit;
		cur.change = acctitem.amount;
		cur.total = acctitem.balance;

		for(int j=0; j<6; j++)
		{
			if ((0 == all[j].id) && (0 == all[j].unit) && (0 == all[j].change) && (0 == all[j].total))
			{
				memcpy(&all[j], &cur, sizeof(SUseInfo));
				break;
			}
			else
			{
				if((all[j].id == cur.id)&&(all[j].unit == cur.unit))
				{
					all[j].change += cur.change;
					all[j].total = cur.total;
					break;
				}
			}
		}

	}

	for(int k=0; k<6; k++)
	{
		if((all[k].id==0) && (all[k].unit==0) && (all[k].change==0) && (all[k].total==0))
		{
			break;
		}
		sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld%c%ld;", all[k].id, all[k].unit, fence, all[k].change, fence, all[k].total);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "balanceInfo[%d][%s]", k, pBuf);
	}
	if(pBuf[0])
	{
		pBuf[strlen(pBuf)-1] = endfence;
	}
	return RET_SUCCESS;
}

//账户信息
//B081:B082,B086,B083,B084
int DCBizCdrNormal::ModifyBalanceInfo2(STBizMsg* bizMsg, char* pBuf)
{
	string str;
	char tmp[256]				= {0};
	char CHARINFO[2]			= {0};//存放话单间隔字符
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;



	int len=strlen(pBuf);
	if(len&&(';'!=pBuf[len-1]))   //第一次调用不作处?
	{
		pBuf[len]=';';//增加长度
		pBuf[len+1]='\0';
	}

	str=pBuf;
	int pos=0;
	long usedcount = 0;
	int endpos=0;
	//累积量信息B08
	int size = base->actv.size();
	for (int k = 0; k < size; k++)
	{

		acctchangeitem_t acctitem = base->actv[k];
		if(acctitem.seq == 0)
		{
			continue;
		}

		sprintf(tmp, "%ld:%d%c%ld%c",acctitem.acct_item_id, acctitem.unit, fence, acctitem.fee_item_id,fence);

		if(-1!=(pos=str.find(tmp, 0)))
		{
			CHARINFO[0]=fence;
			pos+=strlen(tmp);
			if(-1==(endpos=str.find(CHARINFO, pos+1)))return RET_SUCCESS;
			usedcount = acctitem.amount + atol(str.substr(pos, endpos-pos).c_str());
			memset(tmp, 0 ,sizeof(tmp));
			sprintf(tmp,"%ld%c%ld", usedcount, fence, acctitem.balance);
			if(-1==(endpos=str.find(";",endpos)))return RET_SUCCESS;
			str.replace(pos, endpos-pos,tmp);
		}
		else
		{
			sprintf(tmp, "%ld:%d%c%ld%c%ld%c%ld;",acctitem.acct_item_id, acctitem.unit, fence, acctitem.fee_item_id, fence, acctitem.amount, fence, acctitem.balance);
			str.append(tmp);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "current balanceinfo2[%ld:%d,%ld,%ld,%ld]",acctitem.acct_item_id, acctitem.unit, acctitem.fee_item_id, acctitem.amount, acctitem.balance);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "balanceinfo2[%s]",str.c_str());
	}

	strcpy(pBuf, str.c_str());
       if(pBuf[0])
       {
            pBuf[strlen(pBuf)-1] = endfence;
       }

	return RET_SUCCESS;
}

int DCBizCdrNormal::ModifyTariffIdInfo(STBizMsg* bizMsg, char* pBuf, int iBufCapacity)
{
	int ret					= RET_SUCCESS;
	STariffInfo cur			;
	STariffInfo all[100]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	bool bfee = false;
	for(int i = 0; i < (int)base->dbv.size(); i++)
	{
	    if(base->dbv[i].unit == 2)
	    {
	            bfee = true;
	    }
	}
	if(base->dbv.empty())
	{
	    bfee = true;
	}

	//获取原先信息
	ret = DCCommonIF::ParseTariffInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//资费信息B07
	int size = base->trv.size();
	for (int i = 0;  i < size; i++)
	{
		tariff_t tariff = base->trv[i];
		if(tariff.seq == 0)
		{
			continue;
		}
		cur.offerId = tariff.ofr_id;
		cur.acctItemId = tariff.acct_item_id;
		cur.measure = tariff.Measure;
		if(bizMsg->m_requestType == SM_SESSION_EVENT_REFUND_CODE  && (bizMsg->m_serviceContextID == SMS || bizMsg->m_serviceContextID == ISMP))
		{
		        cur.amount = -tariff.amount;
		}
		else
		{
		    cur.amount= tariff.amount;
		}
		
		if(tariff.Measure == 3 && !bfee)
		{
		    cur.amount = 0;
		}
		cur.billingDuration = tariff.billing_duration;
		cur.distFee = tariff.dist_fee;
		cur.groupid= tariff.GroupId;
		cur.ofr_inst_id= tariff.ofr_inst_id;
		cur.counts = tariff.counts;
		cur.ori_tariff = tariff.OriTariff;
		cur.disct_tariff = tariff.DisctTariff;

		for(int j=0; j<100; j++)
		{
			if ((0 == all[j].offerId)
			&& (0 == all[j].amount)
			&& (0 == all[j].acctItemId)
			&& (0 == all[j].measure)
			&& (0 == all[j].billingDuration)
			&& (0 == all[j].distFee))
			{
				memcpy(&all[j], &cur, sizeof(STariffInfo));
				break;
			}
			else
			{
				if(all[j].offerId == cur.offerId && all[j].acctItemId == cur.acctItemId && all[j].ofr_inst_id == cur.ofr_inst_id) //ofrId ?acctitemid都一样时，费用叠?
				{
					all[j].amount+= cur.amount;
					all[j].distFee+= cur.distFee;
					all[j].billingDuration+= cur.billingDuration;
					all[j].counts+= cur.counts;
					all[j].ori_tariff += cur.ori_tariff;
					all[j].disct_tariff = cur.disct_tariff;
					break;
				}
			}
		}
	}

	int len = 0;
	for(int k=0; k<100; k++)
	{
		if((all[k].offerId==0)
		&& (all[k].amount==0)
		&& (all[k].acctItemId==0)
		&& (all[k].measure==0)
		&& (all[k].billingDuration==0)
		&& (all[k].distFee==0))
		{
			break;
		}

		if((all[k].measure==0) && (all[k].billingDuration==0) && (all[k].amount==0))
		{
		    continue;
		}
		len = strlen(pBuf);
		sprintf(pBuf + len, "%ld:%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld;", all[k].offerId, all[k].acctItemId,fence,all[k].measure,fence,all[k].amount,fence,all[k].billingDuration,fence,all[k].distFee,fence,all[k].groupid,fence,all[k].ofr_inst_id,fence,all[k].counts,fence,all[k].ori_tariff,fence,all[k].disct_tariff);
		DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID),  "", "tariffid[%d][%s]", k, pBuf+len);
	}

	if(pBuf[0])
	{
		pBuf[strlen(pBuf)-1] = endfence;
	}
	DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID),  "", "tariffid[%s]", pBuf);

	return RET_SUCCESS;

}

int DCBizCdrNormal::ModifyAccumlatorInfo(STBizMsg* bizMsg, char* pBuf, int iBufCapacity)
{
	int ret = RET_SUCCESS;
	SAccumInfo cur ;
	SAccumInfo all[100];
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	//获取原先信息
	ret = DCCommonIF::ParseAccumlatorInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	int size = base->accv.size();
	for (int i = 0; i < size; i++)
	{
		accum_item_t accum = base->accv[i];
		if(accum.seq == 0)
		{
			continue;
		}
		cur.accu_id = accum.accu_id;
		cur.amount = accum.amount;
		cur.offerId = accum.OfrId;
		cur.unit = accum.unit;
		cur.total = accum.TotalValue;
		cur.ofrinstid = accum.OfrInstId;
		cur.ratableBalanceId = accum.ratable_balance_id;
		cur.accu_type_id = accum.accu_type_id;
		cur.billingLatn = accum.billingLatn;
		cur.iLatnid = accum.iLatnid;
		cur.updateflag = accum.updateflag;
		cur.usage_voice = accum.usage_voice;
		cur.usage_sms = accum.usage_sms;
		cur.accu_use_id = accum.accu_use_id;
		cur.billingCycle = atol(accum.BillingCycle.c_str());
		
		for(int j=0; j<100; j++)
		{
			if ((0 == all[j].accu_id) && (0 == all[j].amount))
			{
				memcpy(&all[j], &cur, sizeof(SAccumInfo));
				break;
			}
			else
			{
				if(all[j].accu_id == cur.accu_id && all[j].offerId == cur.offerId && all[j].ofrinstid == cur.ofrinstid && cur.iLatnid == accum.iLatnid)
				{
					all[j].amount+= cur.amount;
					all[j].total=all[j].total>cur.total?all[j].total:cur.total;
					all[j].usage_voice += cur.usage_voice;
					all[j].usage_sms += cur.usage_sms;
					break;
				}
				//all[j].offerId = cur.offerId;
				//all[j].unit = cur.unit;
				//all[j].total = cur.total;
				//all[j].ofrinstid = cur.ofrinstid;
			}
		}
	}
	int len = 0;
	for(int k=0; k<100; k++)
	{
		if((all[k].accu_id==0) && (all[k].amount==0))
		{
			break;
		}
		len = strlen(pBuf);
		//sprintf(pBuf + len, "%ld:%ld:%ld:%ld:%ld:%ld:%ld:%ld:%ld;", all[k].accu_type_id, all[k].offerId, all[k].unit, all[k].amount, all[k].total, all[k].ofrinstid, all[k].accu_id,all[k].billingLatn,all[k].iLatnid);
		//modify by zhuhuan, date: 20200727, 改成输出格式:accu_id:used amount|updateflag|usage_voice|usage_sms|accu_use_id|strBillCycle
		sprintf(pBuf + len, "%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%ld%c%d%c%ld%c%ld%c%ld%c%ld;",
		all[k].accu_type_id, fence,
		all[k].offerId, fence,
		all[k].unit, fence,
		all[k].amount, fence,
		all[k].total, fence,
		all[k].ofrinstid, fence,
		all[k].accu_id, fence,
		all[k].ratableBalanceId, fence,
		all[k].billingLatn, fence,
		all[k].iLatnid, fence,
		all[k].updateflag, fence,
		all[k].usage_voice, fence,
		all[k].usage_sms, fence,
		all[k].accu_use_id, fence,
		all[k].billingCycle);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "accumInfo[%d][%s]", k,pBuf + len);
	}
	
	if(strlen(pBuf) > 0)
		pBuf[strlen(pBuf)-1] = endfence;
		
	DCBIZLOG(DCLOG_LEVEL_INFO, SERVTYPE(bizMsg->m_serviceContextID),  "", "accumInfo[%s]", pBuf + len);
	return RET_SUCCESS;

}

int DCBizCdrNormal::ModifyChargeInfo(STBizMsg* bizMsg, char* pBuf,  int *pInt)
{
	int ret					= RET_SUCCESS;
	SUseInfo cur			;
	SUseInfo all[6]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;

	//获取原先信息
	ret = DCCommonIF::ParseUseInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	//账本信息B03
	int size = base->dbv.size();
	for (int i = 0; i < size; i++)
	{
		debit_item_t debit = base->dbv[i];
		if(debit.seq == 0)
		{
			continue;
		}
		cur.id = debit.acct_item_id;
		cur.unit = debit.unit;
		cur.change = debit.amount;

		if(pInt&&(cur.unit==2))*pInt=cur.change;

		if(2 != cur.unit)
		{
			cur.id = 0;
			cur.unit = 2;
			cur.change = 0;
		}

		for(int j=0; j<6; j++)
		{
			if ((0 == all[j].id) && (0 == all[j].unit) && (0 == all[j].change) && (0 == all[j].total))
			{
				memcpy(&all[j], &cur, sizeof(SUseInfo));
				break;
			}
			else
			{
				if(all[j].id == cur.id)
				{
					all[j].change += cur.change;
					break;
				}
			}
		}
	}

	for(int k=0; k<6; k++)
	{
		if((all[k].id==0) && (all[k].unit==0) && (all[k].change==0) && (all[k].total==0))
		{
			break;
		}
		sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].id, all[k].unit, fence, all[k].change);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "chargeInfo[%d][%s]", k, pBuf);
	}

       if(pBuf[0])
       {
	    pBuf[strlen(pBuf)-1] = endfence;
       }
	return RET_SUCCESS;
}

int DCBizCdrNormal::ModifyOriChargeInfo(STBizMsg* bizMsg, char* pBuf)
{
	int ret					= RET_SUCCESS;
	SOriChargeInfo cur			;
	SOriChargeInfo all[12]			;
	rbresult *base = (rbresult *)bizMsg->m_base;

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	SCommonPara* commonPara = smpara->GetCommonPara();
	char fence = commonPara->CdrFence;
	char endfence = commonPara->CdrEndchar;


	//获取原先信息
	ret = DCCommonIF::ParseOriChargeInfo(pBuf, all,fence);
	pBuf[0] = '\0';

	int size = base->oric.size();
	for (int i = 0; i < size; i++)
	{
		oricharge_t& oricharge = base->oric[i];
		if (oricharge.seq == 0)
		{
			continue;
		}
		cur.acctItemId = oricharge.acct_item_id;
		cur.measure = oricharge.Measure;
		cur.oriAmount = oricharge.ori_amount;

		for (int j = 0; j < 12; j++)
		{
			if ((0 == all[j].acctItemId) && (0 == all[j].measure) && (0 == all[j].oriAmount))
			{
				memcpy(&all[j], &cur, sizeof(SOriChargeInfo));
				break;
			}
			else
			{
				if(all[j].acctItemId == cur.acctItemId)
				{
					all[j].oriAmount+= cur.oriAmount;
					break;
				}
			}
		}
	}

	//如果有多组，只取最后一?
	for (int k = 0; k < 12; k++)
	{
		if (11 == k)
		{
			sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].acctItemId, all[k].measure, fence, all[k].oriAmount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "oriChargeInfo[%d][%s]", k, pBuf);
			break;
		}

		if ((all[k+1].acctItemId==0) && (all[k+1].measure==0) && (all[k+1].oriAmount==0))
		{
			sprintf(pBuf + strlen(pBuf), "%ld:%ld%c%ld;", all[k].acctItemId, all[k].measure, fence, all[k].oriAmount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "oriChargeInfo[%d][%s]", k, pBuf);
			break;
		}
	}

	pBuf[strlen(pBuf)-1] = endfence;
	return RET_SUCCESS;
}





