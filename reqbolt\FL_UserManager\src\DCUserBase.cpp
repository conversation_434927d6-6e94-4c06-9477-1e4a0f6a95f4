#include "DCUserBase.h"
#include "ErrorCode.h"
#include "BizDataDef.h"
#include "TConfig.h"
#include "DCMqProduceServer.h"
#include "UHead.h"
#include <sys/time.h>
#include <stdio.h>

DCUserBase::DCUserBase():m_en(ESeriaBinString)
{

}

DCUserBase::DCUserBase(TSMPara *psmpara,DCDBManer* dbm):m_en(ESeriaBinString)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
	m_psmpara = psmpara;
	m_dbm = dbm;	
}

DCUserBase::~DCUserBase()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

void DCUserBase::UnifiedCallNum(SPhone &phone, char* unified)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone access[%d],country[00%d],province[0%d],area[0%d],carriers[%d],phone[%s]",
	phone.access, phone.country, phone.province, phone.area, phone.carriers, phone.phone.c_str());

	if (86 == phone.country)
	{
		//手机
		if (strlen(phone.phone.c_str()) > 8)
		{
			char tmp[32] = {0};
			strcpy(tmp,phone.phone.c_str());
			sprintf(unified, "86%s", tmp);
		}
		else
		{
			if(phone.area)
			{
				char tmp[32] = {0};
				strcpy(tmp,phone.phone.c_str());
				sprintf(unified, "86%d%s", phone.area, tmp);
			}
			else
			{
				char tmp[32] = {0};
				strcpy(tmp,phone.phone.c_str());
				sprintf(unified, "%s", tmp);
			}
		}
	}
	else
	{
		char tmp[32] = {0};
		strcpy(tmp,phone.phone.c_str());
		sprintf(unified, "00%d%s", phone.country, tmp);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "unfied number[%s]",unified);
}

int DCUserBase::EffectDate(char* currentDate, char* expireDate, int remindDay)
{
	long long cur = atoll(currentDate);
	long long temp = cur%*********00;

	struct tm t;
	
	t.tm_year=(cur/*********00)-1900;
	t.tm_mon=(temp/*********)-1;//0~11
	temp%=*********;
	t.tm_mday=(temp/1000000);
	temp%=1000000;
	t.tm_hour=temp/10000;
	temp%=10000;
	t.tm_min=temp/100;
	temp%=100;
	t.tm_sec=temp;
	t.tm_isdst=0;
	cur = mktime(&t);

	long long exp = atoll(expireDate);
	temp = exp%*********00;
	
	t.tm_year=(exp/*********00)-1900;
	t.tm_mon=(temp/*********)-1;//0~11
	temp%=*********;
	t.tm_mday=(temp/1000000);
	temp%=1000000;
	t.tm_hour=temp/10000;
	temp%=10000;
	t.tm_min=temp/100;
	temp%=100;
	t.tm_sec=temp;
	t.tm_isdst=0;
	exp = mktime(&t);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cur[%lld], remind[%lld],expire[%lld]", cur, (cur+remindDay*24*3600), exp);
	if(cur >= exp)
	{
		return -1;
	}
	else if((cur+remindDay*24*3600) > exp)
	{
		return 1;
	}
	else
	{
		return 0;
	}
}


int DCUserBase::GetUserAccountInfo(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg)
{
	int ret = 0;
	char value[BIZ_TEMP_LEN_32]	= {0};
       std::pair<long, int>  uInst;
       std::vector<std::pair<long, int> > vInst;
        
	try
	{
		UDBSQL* pQuery = m_dbm->GetSQL(COM_USER_SELECT_TB_PRD_PRD_INST_ACCOUNT);	
		pQuery->UnBindParam();
		pQuery->BindParam(1, phone.phone);
		pQuery->Execute();
		while(pQuery->Next())
		{
			//判断是否为有效数�?
			/*
			pQuery->GetValue(3, value); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "basic_state[%s]",value);
			if(m_psmpara->IsExpire(value))
				continue;
			*/
			
			pQuery->GetValue(3, value); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "staus_cd[%s]",value);
		    if(m_psmpara->IsFiltedByStauscd(value))
			continue;
			
			//PRD_INST_ID
			pQuery->GetValue(1, value);
			uInst.first = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "prd_inst_id[%s]", value);

			pQuery->GetValue(2, value);
			uInst.second = atoi(value);			
			bizMsg->m_ilatnId = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","latn id[%s]", value);

            vInst.push_back(uInst);
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

   if(vInst.empty())
   {
    DCBIZLOG(DCLOG_LEVEL_WARN, 0,"","invalid user or invalid state at phone[%s], area[0%d], biztype[%d]",phone.phone.c_str(), phone.area, userInfo->bizType);
    return SM_OCP_USER_UNKNOWN;
    }

    for(unsigned int i = 0; i<vInst.size(); i++)
    {
        userInfo->servID = vInst[i].first;
        userInfo->ilatnid = vInst[i].second;

        ret = GetUserInfo(userInfo,bizMsg);
        if(ret != SM_OCP_USER_UNKNOWN)
        {
                break;
        }
    }
	return ret;
}

int DCUserBase::GetUserAccNbr(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_32]	= {0};
	int ret = 0;
        std::pair<long, int>  uInst;
       std::vector<std::pair<long, int> > vInst;
       
	try
	{
		UDBSQL* pQuery = m_dbm->GetSQL(COM_USER_SELECT_TB_PRD_PRD_INST_ACCNBR);	
		pQuery->UnBindParam();
		pQuery->BindParam(1, phone.phone);
		pQuery->Execute();
		while(pQuery->Next())
		{
			//判断是否为有效数�?
			/*
			pQuery->GetValue(3, value); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "basic_state[%s]",value);
			if(m_psmpara->IsExpire(value))
				continue;
			*/
			pQuery->GetValue(3, value); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "staus_cd[%s]",value);
		    if(m_psmpara->IsFiltedByStauscd(value))
			continue;
			
			//PRD_INST_ID
			pQuery->GetValue(1, value);
			uInst.first = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "prd_inst_id[%s]",value);

			pQuery->GetValue(2, value);
			uInst.second = atoi(value);			
			bizMsg->m_ilatnId = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","latn id[%s]", value);

                     vInst.push_back(uInst);
	         
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

       if(vInst.empty())
       {
		DCBIZLOG(DCLOG_LEVEL_WARN, 0,"","invalid user or invalid state at phone[%s], area[0%d], biztype[%d]",phone.phone.c_str(), phone.area, userInfo->bizType);
		return SM_OCP_USER_UNKNOWN;
        }

        for(unsigned int i = 0; i<vInst.size(); i++)
        {
                userInfo->servID = vInst[i].first;
                userInfo->ilatnid = vInst[i].second;
                
                ret = GetUserInfo(userInfo,bizMsg);
                if(ret != SM_OCP_USER_UNKNOWN)
                {
                        break;
                }
        }

	return ret;

}

int DCUserBase::GetUserAccArea(const ocs::SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_32]	= {0};
	int ret = 0;	
	 std::pair<long, int>  uInst;
       std::vector<std::pair<long, int> > vInst;
       
	UDBSQL* pQuery = m_dbm->GetSQL(COM_USER_SELECT_TB_PRD_PRD_INST_ACCAREA);//q_tb_prd_prd_inst_accarea
	try
	{
	
		pQuery->UnBindParam();
		pQuery->BindParam(1, phone.phone.c_str());	
		//sprintf(value,"0%d",phone.area);
		if(phone.province == 891) // 西藏�?99查询
		{
			sprintf(value,"%d",999);
		}
		else
		{
			sprintf(value,"%d",phone.area);
		}
		pQuery->BindParam(2, value);		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "phone[%s],area[%d],value[%s]",phone.phone.c_str(),phone.area,value);
		pQuery->Execute();
		while(pQuery->Next())
		{
			//判断是否为有效数�?
			/*
			pQuery->GetValue(3, value); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "basic_state[%s]",value);
			if(m_psmpara->IsExpire(value))
				continue;

			*/
			pQuery->GetValue(3, value); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "staus_cd[%s]",value);
		    if(m_psmpara->IsFiltedByStauscd(value))
			{
				bizMsg->m_iOfflineXDREptFlag = EPT_5G_FILTER_STATUS_CD;
				continue;
			}
			//PRD_INST_ID
			pQuery->GetValue(1, value);
			uInst.first = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "prd_inst_id[%s]",value);

			pQuery->GetValue(2, value);
			uInst.second = atoi(value);
			bizMsg->m_ilatnId = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","latn id[%s]", value);
		
	        vInst.push_back(uInst);
		}		
		
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	if(vInst.empty())
       {
		DCBIZLOG(DCLOG_LEVEL_WARN, 0,"","invalid user or invalid state at phone[%s], area[0%d], biztype[%d]",phone.phone.c_str(), phone.area, userInfo->bizType);
		return SM_OCP_USER_UNKNOWN;
        }

        for(unsigned int i = 0; i<vInst.size(); i++)
        {
                userInfo->servID = vInst[i].first;
                userInfo->ilatnid = vInst[i].second;
                
                ret = GetUserInfo(userInfo,bizMsg);
                if(ret != SM_OCP_USER_UNKNOWN)
                {
                        break;
                }
        }

	return ret;
}

int DCUserBase::GetUserInfo(SUserInfo* userInfo, STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_32]	= {0};
	char sysdate[BIZ_TEMP_LEN_32] = {0};
	char szOfrID[32]                      = {0};
	char basicStatus[8] = {0};
	char extStatus[8] = {0};
	char staus_cd[16]={0};
	char stop_type[16]={0};
	int i = 0;
	int n = 1;
	UDBSQL* pQuery = m_dbm->GetSQL(COM_USER_SELECT_TB_PRD_PRD_INST);
	UDBSQL* pQuery2 = m_dbm->GetSQL(COM_USER_SELECT_PROD_INST_STATE);
	if(userInfo->ilatnid == 558)
		n++;
	for(;n>0;n--)
	{
		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1, userInfo->servID);
			pQuery->BindParam(2,  userInfo->ilatnid);
			pQuery->Execute();

			while(pQuery->Next())
			{
				//判断是否为有效数�?
				pQuery->GetValue(14, basicStatus); 
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "basic_state[%s]",basicStatus);
				if(m_psmpara->IsExpire(basicStatus))
					continue;
				
				pQuery->GetValue(13, staus_cd); 
				strcpy(userInfo->szStausCd, staus_cd);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "staus_cd[%s]",staus_cd);
		        if(m_psmpara->IsFiltedByStauscd(staus_cd))
		        {
		        	bizMsg->m_iOfflineXDREptFlag = EPT_5G_FILTER_STATUS_CD;
					continue;
		        }

				//pQuery->GetValue(18, value); 
				//DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "product id[%s]",value);
		        //if(m_psmpara->IsFilted(atol(value)))
		        //{
		        //    continue;
		        //}
				
				sprintf(sysdate,"%ld",((SCCRBase*)(bizMsg->m_base))->timestamp);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "sysdate[%s]",sysdate);
				if(EffectDate(sysdate, value,m_psmpara->GetINPara()->remindDay) > 0)
				{
					userInfo->isRemind = 1;
				}
				//servID
				pQuery->GetValue(1, value);
				userInfo->servID = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "servID[%ld]", userInfo->servID);

				//custID
				pQuery->GetValue(2, value);
				userInfo->custID = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "custID[%ld]", userInfo->custID);

				//IMSI
				//pQuery->GetValue(3, userInfo->IMSI);
				//DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "IMSI[%s]", userInfo->IMSI);

				//basic_state
				pQuery->GetValue(14, userInfo->szBasicState);
		
				//ext_state
				pQuery->GetValue(15, extStatus);
				strncpy(userInfo->szExtStatus, extStatus, sizeof(userInfo->szExtStatus));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "ext_state[%s]", extStatus);

				pQuery->GetValue(10, value);
				userInfo->area = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "area[%s]", value);

				
				pQuery->GetValue(16, userInfo->segment_id);
				if(0==strlen(userInfo->segment_id)||0==atoi(userInfo->segment_id) ||2==atoi(userInfo->segment_id)) // 资料转换之前是通过SQL刷新segment_id=2时，userType=0，现在通过代码实现
				{
					strcpy(userInfo->userType,"0");//普通用�?
				}
				else if(1==atoi(userInfo->segment_id))
				{
					strcpy(userInfo->userType,"1");	//LTE用户		
				}
				else
				{
					strcpy(userInfo->userType,"2");	//转售用户
					strncpy(userInfo->mvnoID, userInfo->segment_id,sizeof(userInfo->mvnoID));					
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "segment_id[%s],usertype:%s,mvnoid:%s", userInfo->segment_id,userInfo->userType,userInfo->mvnoID);

				try
				{
					pQuery2->UnBindParam();
					pQuery2->BindParam(1, userInfo->servID);
					pQuery2->BindParam(2, userInfo->ilatnid);
					pQuery2->Execute();
					if(pQuery2->Next())
					{
						pQuery2->GetValue(1, stop_type);
						strcpy(userInfo->szStopType, stop_type);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "stop_type[%s]", stop_type);
					}
				}catch(UDBException& e)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetProdInstState exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
					return SM_OCP_UNABLE_TO_COMPLY;
				}
				
				 // F0E状态拨打免费号码。走免费号码流程，拨打其他号码的在禁播号码判断状�?
				if (!((strcmp(basicStatus, "F0E")  == 0) && (strcmp(extStatus, "E0B") == 0) ||(strcmp(extStatus, "E0C") ==0)))
				{
					userInfo->resultCode = m_psmpara->checkUserStateInfo(userInfo->bizType, basicStatus, extStatus, staus_cd, stop_type);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "resultCode[%d]", userInfo->resultCode);
					userInfo->isActive = 0;
					//判断是否首次使用
					if(1 == userInfo->resultCode)
					{
						userInfo->isActive = 1;
						userInfo->resultCode = 0;
					}
					else if(userInfo->resultCode)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "execption user status userInfo->servID[%ld], basic[%s], ext[%s], staus_cd[%s], stop_type[%s], resultcode[%d]", userInfo->servID, basicStatus, extStatus, staus_cd, stop_type, userInfo->resultCode);
					}
				}
				

				//aocType
				switch(bizMsg->m_serviceContextID)
				{
					case VOICE:
						pQuery->GetValue(3, value);
						userInfo->aocType = atoi(value);
						break;
					case SMS:
						pQuery->GetValue(4, value);
						userInfo->aocType = atoi(value);
						break;
					case DATA:
					case CCG:
					case PGW:
						pQuery->GetValue(5, value);
						userInfo->aocType = atoi(value);
						break;
                    case DATA_5G:
                        pQuery->GetValue(5, value);
                        userInfo->aocType = atoi(value);
                        break;
					case ISMP:
					case HRS:
						pQuery->GetValue(6, value);
						userInfo->aocType = atoi(value);
						break;
					default:;
				}
				
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "aocType[%d]", userInfo->aocType);

				//无线上网卡套�?
				/*
				//用户处于充值期，发送短信提�?
				if(0 == strcmp(basicStatus, "F0E") && 0 == strcmp(extStatus, "E0D") && 8 == userInfo->aocType)
				{
					//DATAAOC(bizMsg, userInfo->aocType);
					DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "invalid user or invalid state at userInfo->servID[%ld], ilatnid[%d], biztype[%d]",userInfo->servID, userInfo->ilatnid, userInfo->aocType);
				}
				*/
				

				//事件类业务DATAAOCTYPE�?时，AOCTYPE组合在一�?
				if(SMS == bizMsg->m_serviceContextID)
				{
					pQuery->GetValue(5, value);
					if(8 == atoi(value))
					{
						userInfo->aocType += atoi(value);
					}
				}
				else
				{
					//ofr_id
					pQuery->GetValue(9, szOfrID);
					int ret = 0;
					ret = m_psmpara->GetOfrID2G(szOfrID);
					if(1 == ret)
					{
						userInfo->iBrandType = BRAND_2G;
					}
					else
					{
						userInfo->iBrandType = BRAND_3G;
					}
					
			
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "ofr_id[%s] brand_type[%d]", szOfrID,userInfo->iBrandType);

					const AREA_INFO* areaCode= m_psmpara->GetAreaInfo(userInfo->area);
					if(NULL != areaCode)
					{
						userInfo->province = areaCode->province;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "province[%d], ilatnid[%d]", userInfo->province, userInfo->ilatnid);    
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "not find area in spz_city,province[%d], ilatnid[%d]", userInfo->province, userInfo->ilatnid);  
					}
				}


				//ACCT_ID
				pQuery->GetValue(11, value);
				userInfo->lnAcctID= atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "acct_id[%ld]",userInfo->lnAcctID);

				
				pQuery->GetValue(12, value);
				bizMsg->m_payMentMode = value;
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"", "payMentMode[%s]", value);
				if(strcmp(value, "1200") == 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "after pay user");
					bizMsg->m_userType = 2;
					if((bizMsg->m_serviceContextID == VOICE && m_psmpara->GetINPara()->iDenyHBUser == 1)||
					   (bizMsg->m_serviceContextID == SMS && m_psmpara->GetP2PSMSPara()->iDenyHBUser == 1)||
					   (bizMsg->m_serviceContextID == DATA && m_psmpara->GetPSPara()->iDenyHBUser == 1)||
					   (bizMsg->m_serviceContextID == CCG && m_psmpara->GetPSPara()->iDenyHBUser == 1)||
					   (bizMsg->m_serviceContextID == PGW && m_psmpara->GetPSPara()->iDenyHBUser == 1)||
					   (bizMsg->m_serviceContextID == DSL && m_psmpara->GetDSLPara()->iDenyHBUser == 1)||
                       (bizMsg->m_serviceContextID == ISMP && m_psmpara->GetISMPPara()->iDenyHBUser == 1)||
                       (bizMsg->m_serviceContextID == DATA_5G && m_psmpara->Get5GPara()->iDenyHBUser == 1))
					{
					    DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"",  "deny HB User, servid[%ld], latnid[%d], subnbr[%s]", userInfo->servID, userInfo->ilatnid, bizMsg->m_subNumber);
					    return SM_OCP_USER_UNKNOWN;
					}
				}
				
				i++;
				break;
		        
			}
			if(i == 0 )
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, 0,"",  "invalid user or invalid state at servID[%ld], ilatnid[%d], biztype[%d]",userInfo->servID, userInfo->ilatnid, userInfo->bizType);
				if(n == 2)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, 0,"",  "alter ilatnid 558 to 560,try query again");
					userInfo->ilatnid = 560;
					continue;
				}
				return SM_OCP_USER_UNKNOWN;
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}
	char stmp[6] = {0};
	sprintf(stmp, "%d", userInfo->ilatnid);
	bizMsg->m_pSendMsg->insert(pair<string,string>("LatnId",stmp));
	
	GetIMSI(userInfo,bizMsg);
	return userInfo->resultCode;
}

int DCUserBase::FreeNumber(STBizMsg* bizMsg, SCCRBase* base, const SPhone* calling, const SPhone* called)
{
	int freeflag = 0;
	
	SUserInfo * userInfo			= bizMsg->m_userinfo;
	//SUserInfo * userInfo;
	int ret							= 0;
	SCommonPara* commonPara = m_psmpara->GetCommonPara();
	if(commonPara)
	{
		switch(bizMsg->m_serviceContextID)
		{
			case VOICE:
				{
					if(commonPara->INFreeSwitch)
					{
						freeflag = 1;
					}
				}
				break;
			case SMS:
				{
					if(commonPara->SMSFreeSwitch)
					{
						freeflag = 1;
					}
				}
				break;
			case DATA:
			case CCG:
			case PGW:
				{
					if(commonPara->DATAFreeSwitch)
					{
						freeflag = 1;
					}
				}
				break;
            case DATA_5G:
                {
                    if(commonPara->_5GFreeSwitch)
                    {
                        freeflag = 1;
                    }
                }
                break;
			case ISMP:
			case HRS:
				{
					if(commonPara->ISMPFreeSwitch)
					{
						freeflag = 1;
					}
				}
				break;
			case DSL:
				{
					if(commonPara->DSLFreeSwitch)
					{
						freeflag = 1;
					}
				}
				break;
			default:
				{
					return 0;
				}
		}
	}
	
	long long llCallingPhone = 0;
	long long llCalledPhone = 0;
	
	if(calling)
	{
		llCallingPhone = atoll(calling->phone.c_str());
	}
	
	if(called)
	{
		llCalledPhone = atoll(called->phone.c_str());
	}
	
	if(freeflag)
	{
		int isFree = m_psmpara->checkUrgentNumberInfo(atoll(base->subscription.phone.c_str()), llCallingPhone, llCalledPhone, base->subscription.province, base->subscription.area, bizMsg->m_serviceContextID);
		if(isFree)
		{
			userInfo->bizType = 10;
			if(10 == base->subscriptionType)//卡用户
			{
				ret = GetUserAccNbr(base->subscription, userInfo, bizMsg);
				base->subscription.province = userInfo->province;
				base->subscription.area = userInfo->area;
			}
			else
			{				
				ret =1;
			}
			// bizMsg->m_userinfo = userInfo;
			if(1 == ret || ret ==0)
			{
				return 1;//free
			}
			else
			{
				return ret;
			}
		}

	}
	return 0;
}

int DCUserBase::DealFreeNumber(int RequestAction, STBizMsg* bizMsg)
{
	//char buf[20480]		= {0};
	time_t et 			= 0;	
	
	ocs::SCCRBase * base		= (SCCRBase*)bizMsg->m_base;
	ocs::SCCAMsg msg;	
	
	int nCdrFlag = 0;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "deal free number", "");
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				switch(bizMsg->m_serviceContextID)
				{
					case VOICE:
						{
							SCCRVOICE* data =(SCCRVOICE*)bizMsg->m_extend;
							DealFreeVoiceInit(base, data, bizMsg);
						}
						break;
					case SMS:
						{
						}
						break;
					case DATA:
					case CCG:
						{							
							SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
							DealFreeDataInit(base, data, bizMsg);
						}
						break;
					case PGW:
						{
							SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
							DealFreePGWInit(base, data, bizMsg);
						}
						break;
                    case DATA_5G:
                        {
                            SCCR5GInfo* data =(SCCR5GInfo*)bizMsg->m_extend;
                            DealFree5GInit(base, data, bizMsg);
                        }
                        break;
					case ISMP:
					case HRS:
						{
							
							SCCRISMP* data =(SCCRISMP*)bizMsg->m_extend;
							DealFreeIsmpInit(base, data, bizMsg);
						}
						break;
					case DSL:
						{
							SCCRDSL* data =(SCCRDSL*)bizMsg->m_extend;
							DealFreeDslInit(base, data, bizMsg);
						}
						break;
					default:
						return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				}
			}
			break;
		case SM_SESSION_UPDATE_CODE: 			
		case SM_SESSION_TERMINATION_CODE: 
			{	if (bizMsg->m_requestType == SM_SESSION_TERMINATION_CODE)
				{
					nCdrFlag = 1;
				}
				switch(bizMsg->m_serviceContextID)
				{
					case VOICE:
						{
						}
						break;
					case SMS:
						{
						}
						break;
					case DATA:
					case CCG:
						{
						}
						break;
					case PGW:
						{
						}
						break;
                    case DATA_5G:
						{
						}
						break;
					case ISMP:
					case HRS:
						{
						}
						break;
					case DSL:
						{
						}
						break;
					default:
						return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				}
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				switch(RequestAction)
				{
					case SM_REQUESTED_ACTION_DEBIT:
						{
							switch(bizMsg->m_serviceContextID)
							{
								case SMS:
									{
										SCCRSMS* data =(SCCRSMS*)bizMsg->m_extend;
										DealFreeSmsEvent(base, data, bizMsg,SM_REQUESTED_ACTION_DEBIT);
										nCdrFlag = 1;
									}
									break;
								case ISMP:
								case HRS:
									{
										SCCRISMP* data =(SCCRISMP*)bizMsg->m_extend;
										DealFreeIsmpInit(base, data, bizMsg);
										nCdrFlag = 1;
									}
									break;
								default:									
									DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE_USELESS_CCR,"", "request type error");
									return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
							}
						}
						break;
					case SM_REQUESTED_ACTION_REFUND:
						{
							switch(bizMsg->m_serviceContextID)
							{
								case SMS:
									{
										SCCRSMS* data =(SCCRSMS*)bizMsg->m_extend;
										DealFreeSmsEvent(base, data, bizMsg,SM_REQUESTED_ACTION_REFUND);
										nCdrFlag = 1;
									}
									break;
								case ISMP:
								case HRS:
									{
										SCCRISMP* data =(SCCRISMP*)bizMsg->m_extend;
										DealFreeIsmpInit(base, data, bizMsg);
										nCdrFlag = 1;
									}
									break;
								default:
									return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
							}
						}
						break ;
					case SM_REQUESTED_ACTION_CHECK: 
						{
							switch(bizMsg->m_serviceContextID)
							{
								case VOICE:
									{
									}
									break;
								case ISMP:
								case HRS:
									{
									}
									break;
								default:									
									DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE_USELESS_CCR,"", "request type error");
									return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
							}
						}
						break;
					default:					
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE_USELESS_CCR,"", "request type error");
						return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				}
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				switch(bizMsg->m_serviceContextID)
				{
					case VOICE:
						{
							SCCRVOICE* data =(SCCRVOICE*)bizMsg->m_extend;
							DealFreeVoiceInit(base, data, bizMsg);
							nCdrFlag = 1;
						}
						break;
					case SMS:
						{
							switch(RequestAction)
							{
								case SM_REQUESTED_ACTION_DEBIT:
									{
										SCCRSMS* data =(SCCRSMS*)bizMsg->m_extend;
										DealFreeSmsEvent(base, data, bizMsg,SM_REQUESTED_ACTION_DEBIT);
										nCdrFlag = 1;
									}
									break;
								case SM_REQUESTED_ACTION_REFUND:
									{
										SCCRSMS* data =(SCCRSMS*)bizMsg->m_extend;
										DealFreeSmsEvent(base, data, bizMsg,SM_REQUESTED_ACTION_REFUND);
										nCdrFlag = 1;
									}
									break;
								case SM_REQUESTED_ACTION_CHECK:
									break;
								default:
									DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE_USELESS_CCR,"", "request type error");
									return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
							}
						}
						break;
					case DATA:
					case CCG:
						{							
							SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
							int ret = DealFreeDataInit(base, data, bizMsg);
							if(ret == RET_ERROR)
								return 0;
							nCdrFlag = 1;
						}
						break;
					case PGW:
						{
							SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
							int ret =DealFreePGWInit(base, data, bizMsg);
							if(ret == RET_ERROR)
								return 0;
							nCdrFlag = 1;
						}
						break;
                    case DATA_5G:
                        {
                            SCCR5GInfo* data =(SCCR5GInfo*)bizMsg->m_extend;
                            int ret =DealFree5GInit(base, data, bizMsg);
                            if(ret == RET_ERROR)
                                return 0;
                            nCdrFlag = 1;
                        }
                        break;
					case ISMP:
					case HRS:
						{
							
							switch(RequestAction)
							{
								case SM_REQUESTED_ACTION_DEBIT:
									{
										SCCRISMP* data =(SCCRISMP*)bizMsg->m_extend;
										DealFreeIsmpInit(base, data, bizMsg);
										nCdrFlag = 1;
									}
									break;
								case SM_REQUESTED_ACTION_REFUND:
									{
										SCCRISMP* data =(SCCRISMP*)bizMsg->m_extend;
										DealFreeIsmpInit(base, data, bizMsg);
										nCdrFlag = 1;
									}
									break;
								case SM_REQUESTED_ACTION_CHECK:
									break;
								default:
									DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE_USELESS_CCR,"", "request type error");
									return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
							}
						}
						break;
					case DSL:
						{
							SCCRDSL* data =(SCCRDSL*)bizMsg->m_extend;
							DealFreeDslInit(base, data, bizMsg);
							nCdrFlag = 1;
						}
						break;
					default:
						return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				}
			}
			break;
		default:			
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE_USELESS_CCR,"", "request type error");
			return SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
	}

	
	//update term更新使用量
	if((SM_SESSION_UPDATE_CODE==bizMsg->m_requestType) ||(SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType))
	{
		et = time(NULL);
		et += 600;									//next CCR
		int invalidrgnum = 0;
		
		switch(bizMsg->m_serviceContextID)
		{
			case VOICE:
				{
					SCCRDataUnit* USU = NULL;
					USU = USU = &base->USU;;
					msg.GSUAns.duration = USU->duration;
					UDBSQL* pExec = m_dbm->GetSQL(Voice_UpdateSession_Free);
					//update会话2002错误码(Commit失败)3次重试
					int retryCount = 0;
					bool success = false;
					while (!success && retryCount < 3)
					{
						try
						{
							pExec->DivTable(bizMsg->m_sessionID);
							pExec->UnBindParam();
							pExec->BindParam(1, (long)et);
							pExec->BindParam(2, (long)USU->duration);
							pExec->BindParam(3, bizMsg->m_sessionID);
							pExec->Execute();
							pExec->Connection()->Commit();
							success = true;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "update ok,usu[%d],next time[%ld]", USU->duration, (long)et);
						}
						catch (UDBException &e)
						{
							pExec->Connection()->Rollback();
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ", e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
							if (e.GetErrorCode() == 2002) // Commit失败
							{
								retryCount++;
								if (retryCount < 3)
								{
									DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
									continue;
								}
								else
								{
									DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "Max retries reached for error code 2002");
									return SM_OCP_UNABLE_TO_COMPLY;
								}
							}
							return SM_OCP_UNABLE_TO_COMPLY;
						}
					}
				}
			break;
			case DATA:
			case CCG:
				{
					SUSU* MSCC	= NULL;
					if(base->MSCC.size() == 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"",  "missing MSCC", "");

						SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
						bizMsg->m_resultcode = 2001;
						sendCCA(bizMsg);
						
						if(m_psmpara->GetCommonPara()->nFreeAddCdr)
						{
							return RET_CDR;
						}
						else
						{
							return RET_SUCCESS;
						}
					}
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "MSCC size[%d]", base->MSCC.size());

				
					//多RG
					for(int i=0; i<base->MSCC.size(); i++)
					{
						SCCRDataUnit USU;
						
						MSCC =&( base->MSCC[i]);
						if(!MSCC)
						{
							continue;
						}
							//计算使用量
						USU.duration += MSCC->USU0.duration;
						msg.GSUAns.duration += MSCC->USU0.duration;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 druation[%u]", USU.duration);

						USU.unitTotal += MSCC->USU0.unitTotal;
						msg.GSUAns.unitTotal += MSCC->USU0.unitTotal;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitTotal[%lld]", USU.unitTotal);

						USU.unitInput += MSCC->USU0.unitInput;
						msg.GSUAns.unitInput += MSCC->USU0.unitInput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitInput[%lld]", USU.unitInput);

						USU.unitOutput += MSCC->USU0.unitOutput;
						msg.GSUAns.unitOutput += MSCC->USU0.unitOutput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitOutput[%lld]", USU.unitOutput);

						if(1 == MSCC->USU1.tariffChangeUsage)//费率切换点之后的使用量
						{
							USU.duration += MSCC->USU1.duration;
							msg.GSUAns.duration += MSCC->USU1.duration;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 druation[%u]", USU.duration);

							USU.unitTotal += MSCC->USU1.unitTotal;
							msg.GSUAns.unitTotal += MSCC->USU1.unitTotal;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitTotal[%lld]", USU.unitTotal);

							USU.unitInput += MSCC->USU1.unitInput;
							msg.GSUAns.unitInput += MSCC->USU1.unitInput;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitInput[%lld]", USU.unitInput);

							USU.unitOutput += MSCC->USU1.unitOutput;
							msg.GSUAns.unitOutput += MSCC->USU1.unitOutput;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitOutput[%lld]", USU.unitOutput);
						}
			
						//查询子会话是否存在
						char childsession[BIZ_TEMP_LEN_256] 			= {0};
						
						UDBSQL* pQuery = m_dbm->GetSQL(DATA_SelectSessionStoreRecord_003);
						try
						{
							pQuery->DivTable(bizMsg->m_sessionID);
							pQuery->UnBindParam();
							if(strlen(MSCC->ProductOfferId.c_str()))//如果网元上报Product-Offer-Id，按此来批价
							{
								sprintf(childsession, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
							}
							else
							{
								sprintf(childsession, "%s;%ld", bizMsg->m_sessionID, MSCC->ratingGroup);
							}
							pQuery->BindParam(1, childsession);
							pQuery->Execute();						
							if(pQuery->Next())//子会话存在更新子会话
							{
								DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "child session exist", "");
								UDBSQL* pExec = m_dbm->GetSQL(DATA_FREE_Update_UpdateSession);
								try
								{			
									pExec->DivTable(bizMsg->m_sessionID);
									pExec->UnBindParam();
									pExec->BindParam(1, (long)et);//SM_LNG_TIME_TO_NEXT_CCR
									pExec->BindParam(2, (long)USU.duration);//SM_LNG_ALL_USU_TIME
									pExec->BindParam(3, (long)USU.unitTotal);//SM_LNG_ALL_USU_TOTAL_OCT
									pExec->BindParam(4, (long)USU.unitInput);//SM_LNG_ALL_USU_INPUT_OCT
									pExec->BindParam(5, (long)USU.unitOutput);//SM_LNG_ALL_USU_OUTPUT_OCT
									pExec->BindParam(6, childsession);
									pExec->Execute();
									pExec->Connection()->Commit();

								}
								catch(UDBException& e)
								{
									pExec->Connection()->Rollback();
									DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
									return SM_OCP_UNABLE_TO_COMPLY;
								}
							}
							else//子会话不存在则插入子会话	
							{
								DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "child session not exist", "");

								if (SM_SESSION_UPDATE_CODE==bizMsg->m_requestType)
								{
									char value[BIZ_TEMP_LEN_256] 			= {0};
									int n_CDR_INT_LATN_ID=0;
									long ln_CDR_ACCT_ID = 0;
									char PDPAddress[32] = {0};
									char PDPAddressIpv6[54] = {0};
									//从主会话查找相关字段				 
									UDBSQL* pQueryMain = m_dbm->GetSQL(DATA_SelectSessionStoreRecord_002);
									try
									{
										pQueryMain->DivTable(bizMsg->m_sessionID);
										pQueryMain->UnBindParam();
										pQueryMain->BindParam(1, bizMsg->m_sessionID);	
										pQueryMain->Execute();



										if(pQueryMain->Next())
										{
											pQueryMain->GetValue(55, value);
											n_CDR_INT_LATN_ID = atoi(value);
											DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "n_CDR_INT_LATN_ID[%d]", n_CDR_INT_LATN_ID);

											pQueryMain->GetValue(56, value);
											ln_CDR_ACCT_ID = atol(value);
											DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "ln_CDR_ACCT_ID[%ld]", ln_CDR_ACCT_ID);

											pQueryMain->GetValue(21, PDPAddress);
											DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "PDPAddress[%s]", PDPAddress);

											pQueryMain->GetValue(65, PDPAddressIpv6);
											DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "PDPAddressIpv6[%s]", PDPAddressIpv6);
										}
								 	}
									catch(UDBException& e)
									{
										DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
										return SM_OCP_UNABLE_TO_COMPLY;
									}

									//插入子会话 
									UDBSQL* pExec = m_dbm->GetSQL(DATA_InsertSessionStoreRgFree);
									try
									{
										pExec->DivTable(bizMsg->m_sessionID);
										pExec->UnBindParam();
										pExec->BindParam(1, childsession);
										pExec->BindParam(2, SM_SESSION_UPDATE_FIRST_CODE);
										pExec->BindParam(3, (int)bizMsg->m_requestNumber);
										//pExec->BindParam(4, (long)ut.serial);
										pExec->BindParam(4, (long)MSCC->ratingGroup);
										pExec->BindParam(5, (long)et);//SM_LNG_TIME_TO_NEXT_CCR
										//pExec->BindParam(7, (long)conf->VALID_TIME);
										//pExec->BindParam(8, (long)conf->QUOTA_CONSUMPTION_TIME);
										//pExec->BindParam(9, (long)conf->VOLUME_QUOTA_THRESHOLD);
										//pExec->BindParam(10, (long)conf->TIME_QUOTA_THRESHOLD);
										//pExec->BindParam(11, (long)conf->QUOTA_HOLDING_TIME);
										pExec->BindParam(6, (long)bizMsg->timestampCCR);//RE_LNG_CALL_START_TIME
										pExec->BindParam(7, (long)bizMsg->timestampCCR);//RE_LNG_CURRENT_CCR_TIME
										//pExec->BindParam(14, bizMsg->m_subNumber);
										//if(conf->TOTAL_OCTETS)pExec->BindParam(15, conf->TOTAL_OCTETS);
										//if(conf->DURATION)pExec->BindParam(16, (long)conf->DURATION);
										//pExec->BindParam(17, lastCCRTime);//RE_LNG_LAST_CCR_TIME
										//pExec->BindParam(18, GET_SM_CFG()->serv.host);
										//pExec->BindParam(19, base->routeRecord);
										pExec->BindParam(8, 1);				//	用来标识子会话
										//pExec->BindParam(21, (long)conf->VOLUME_QUOTA_THRESHOLD_1);
										pExec->BindParam(9, (long)base->timestamp); 
										//pExec->BindParam(23, nAocType); //SM_INT_AOC_TYPE
										//pExec->BindParam(24, traceNumOnff); //SM_TRACE_NUM_ONFF
										pExec->BindParam(10, MSCC->ProductOfferId); //OCP_STR_PRODUCT_OFFER_ID 
										pExec->BindParam(11, (long)0 ); //SM_LNG_ALL_USU_TIME
										pExec->BindParam(12, (long)0); //SM_LNG_ALL_USU_TOTAL_OCT
										pExec->BindParam(13, (long)0); //SM_LNG_ALL_USU_INPUT_OCT
										pExec->BindParam(14, (long)0); //SM_LNG_ALL_USU_OUTPUT_OCT
										pExec->BindParam(15, 1);//SM_INT_FREE_FLAG
										pExec->BindParam(16, n_CDR_INT_LATN_ID);
										pExec->BindParam(17, ln_CDR_ACCT_ID);
										pExec->BindParam(18, 1);
										pExec->BindParam(19, 1);
										pExec->BindParam(20, base->topology);
										pExec->BindParam(21, bizMsg->m_szServiceContextIDStr);
										pExec->BindParam(22, PDPAddress);
										pExec->BindParam(23, PDPAddressIpv6);
										pExec->Execute();
										pExec->Connection()->Commit();
									}
									catch(UDBException& e)
									{
										pExec->Connection()->Rollback();
										DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
										return SM_OCP_UNABLE_TO_COMPLY;
									}
								}
								else if (SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType)
								{
									invalidrgnum++;
									DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "invalid rg num[%d]",invalidrgnum);
								}
							}
						}
						catch(UDBException& e)
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
							return SM_OCP_UNABLE_TO_COMPLY;
						}		

					}

					if(invalidrgnum == base->MSCC.size() )
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "all rg is invalid,return","");
						return SM_OCP_UNKNOWN_SESSION_ID;
					}
				}
			break;
		case PGW:
			{
					SUSU* MSCC							= NULL;
					if(base->MSCC.size() == 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "missing MSCC", "");

						SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
						bizMsg->m_resultcode = 2001;
						sendCCA(bizMsg);
						
						if(m_psmpara->GetCommonPara()->nFreeAddCdr)
						{
							return RET_CDR;
						}
						else
						{
							return RET_SUCCESS;
						}
					}
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "MSCC size[%d]", base->MSCC.size());

				
					//多RG
					for(int i=0; i<base->MSCC.size(); i++)
					{
						SCCRDataUnit USU ;
						
						MSCC = &(base->MSCC[i]);
						if(!MSCC)
						{
							continue;
						}

							//计算使用量
						USU.duration += MSCC->USU0.duration;
						msg.GSUAns.duration += MSCC->USU0.duration;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 druation[%u]", USU.duration);

						USU.unitTotal += MSCC->USU0.unitTotal;
						msg.GSUAns.unitTotal += MSCC->USU0.unitTotal;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitTotal[%lld]", USU.unitTotal);

						USU.unitInput += MSCC->USU0.unitInput;
						msg.GSUAns.unitInput += MSCC->USU0.unitInput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "USU0 unitInput[%lld]", USU.unitInput);

						USU.unitOutput += MSCC->USU0.unitOutput;
						msg.GSUAns.unitOutput += MSCC->USU0.unitOutput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitOutput[%lld]", USU.unitOutput);

						if(1 == MSCC->USU1.tariffChangeUsage)//费率切换点之后的使用量
						{
							USU.duration += MSCC->USU1.duration;
							msg.GSUAns.duration += MSCC->USU1.duration;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 druation[%u]", USU.duration);

							USU.unitTotal += MSCC->USU1.unitTotal;
							msg.GSUAns.unitTotal += MSCC->USU1.unitTotal;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitTotal[%lld]", USU.unitTotal);

							USU.unitInput += MSCC->USU1.unitInput;
							msg.GSUAns.unitInput += MSCC->USU1.unitInput;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitInput[%lld]", USU.unitInput);

							USU.unitOutput += MSCC->USU1.unitOutput;
							msg.GSUAns.unitOutput += MSCC->USU1.unitOutput;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "USU0+USU1 unitOutput[%lld]", USU.unitOutput);
						}
			
						//查询子会话是否存在
						char childsession[BIZ_TEMP_LEN_256] 			= {0};
						UDBSQL* pQuery = m_dbm->GetSQL(PGW_CCR_SelectSession);
						try
						{
							pQuery->DivTable(bizMsg->m_sessionID);
							pQuery->UnBindParam();
							if(strlen(MSCC->ProductOfferId.c_str()))//如果网元上报Product-Offer-Id，按此来批价
							{
								sprintf(childsession, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
							}
							else
							{
								sprintf(childsession, "%s;%ld", bizMsg->m_sessionID, MSCC->ratingGroup);
							}
							
							pQuery->BindParam(1, childsession);
							pQuery->Execute();						
							//子会话存在则更新子会话USU
							if(pQuery->Next())
							{
								
								DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "child session exist", "");
								UDBSQL* pExec = m_dbm->GetSQL(PGW_FREE_Update_UpdateSession);
								try
								{	
									pExec->DivTable(bizMsg->m_sessionID);
									pExec->UnBindParam();
									pExec->BindParam(1, (long)et);//SM_LNG_TIME_TO_NEXT_CCR
									pExec->BindParam(2, (long)USU.duration);//SM_LNG_ALL_USU_TIME
									pExec->BindParam(3, (long)USU.unitTotal);//SM_LNG_ALL_USU_TOTAL_OCT
									pExec->BindParam(4, (long)USU.unitInput);//SM_LNG_ALL_USU_INPUT_OCT
									pExec->BindParam(5, (long)USU.unitOutput);//SM_LNG_ALL_USU_OUTPUT_OCT
									pExec->BindParam(6, childsession);
									pExec->Execute();
									pExec->Connection()->Commit();

								}
								catch(UDBException& e)
								{
									pExec->Connection()->Rollback();
									DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
									return SM_OCP_UNABLE_TO_COMPLY;
								}

							}
							//子会话不存在则插入子会话
							else
							{
								if (SM_SESSION_UPDATE_CODE==bizMsg->m_requestType)
								{
									//从主会话查找相关字段
									char value[BIZ_TEMP_LEN_256] 			= {0};
									int n_CDR_INT_LATN_ID=0;
									long ln_CDR_ACCT_ID = 0;										
									UDBSQL* pQueryMain = m_dbm->GetSQL(PGW_CCR_FirstUpdate_SelectSession_main);
									try
									{
										pQueryMain->DivTable(bizMsg->m_sessionID);
										pQueryMain->UnBindParam();
										pQueryMain->BindParam(1, bizMsg->m_sessionID);	
										pQueryMain->Execute();

										if(pQueryMain->Next())
										{
											pQueryMain->GetValue(55, value);
											n_CDR_INT_LATN_ID = atoi(value);
											DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "n_CDR_INT_LATN_ID[%d]", n_CDR_INT_LATN_ID);

											pQueryMain->GetValue(56, value);
											ln_CDR_ACCT_ID = atol(value);
											DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "ln_CDR_ACCT_ID[%ld]", ln_CDR_ACCT_ID);
										}
									}
									catch(UDBException& e)
									{
										DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
										return SM_OCP_UNABLE_TO_COMPLY;
									}

									//插入子会话
									
									UDBSQL* pExec = m_dbm->GetSQL(PGW_FREE_FirstUpdate_InsertSessionChild);
									try
									{
										pExec->DivTable(bizMsg->m_sessionID);
										pExec->UnBindParam();

										pExec->BindParam(1, childsession);
										pExec->BindParam(2, SM_SESSION_UPDATE_FIRST_CODE);
										pExec->BindParam(3, (int)bizMsg->m_requestNumber);
										//pExec->BindParam(4, (long)ut.serial);
										pExec->BindParam(4, (long)MSCC->ratingGroup);
										pExec->BindParam(5, (long)et);//SM_LNG_TIME_TO_NEXT_CCR
										//pExec->BindParam(7, (long)conf->VALID_TIME);
										//pExec->BindParam(8, (long)conf->QUOTA_CONSUMPTION_TIME);
										//pExec->BindParam(9, (long)conf->VOLUME_QUOTA_THRESHOLD);
										//pExec->BindParam(10, (long)conf->TIME_QUOTA_THRESHOLD);
										//pExec->BindParam(11, (long)conf->QUOTA_HOLDING_TIME);
										pExec->BindParam(6, (long)bizMsg->timestampCCR);//RE_LNG_CALL_START_TIME
										pExec->BindParam(7, (long)bizMsg->timestampCCR);//RE_LNG_CURRENT_CCR_TIME
										//pExec->BindParam(14, bizMsg->m_subNumber);
										//if(conf->TOTAL_OCTETS)pExec->BindParam(15, conf->TOTAL_OCTETS);
										//if(conf->DURATION)pExec->BindParam(16, (long)conf->DURATION);
										//pExec->BindParam(17, lastCCRTime);//RE_LNG_LAST_CCR_TIME
										//pExec->BindParam(18, GET_SM_CFG()->serv.host);
										//pExec->BindParam(19, base->routeRecord);
										pExec->BindParam(8, 1);				//	用来标识子会话
										//pExec->BindParam(21, (long)conf->VOLUME_QUOTA_THRESHOLD_1);
										pExec->BindParam(9, (long)base->timestamp); 
										//pExec->BindParam(23, nAocType); //SM_INT_AOC_TYPE
										//pExec->BindParam(24, traceNumOnff); //SM_TRACE_NUM_ONFF
										pExec->BindParam(10, MSCC->ProductOfferId); //OCP_STR_PRODUCT_OFFER_ID 
										pExec->BindParam(11, (long)0 ); //SM_LNG_ALL_USU_TIME
										pExec->BindParam(12, (long)0); //SM_LNG_ALL_USU_TOTAL_OCT
										pExec->BindParam(13, (long)0); //SM_LNG_ALL_USU_INPUT_OCT
										pExec->BindParam(14, (long)0); //SM_LNG_ALL_USU_OUTPUT_OCT
										pExec->BindParam(15, 1);//SM_INT_FREE_FLAG
										pExec->BindParam(16, n_CDR_INT_LATN_ID);
										pExec->BindParam(17, ln_CDR_ACCT_ID);
										pExec->BindParam(18, 1);
										pExec->BindParam(19, 1);
										pExec->BindParam(20, base->topology);
										pExec->BindParam(21, bizMsg->m_szServiceContextIDStr);
										pExec->Execute();
										pExec->Connection()->Commit();
										
										DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "insert ok", "");
									}
									catch(UDBException& e)
									{
										pExec->Connection()->Rollback();
										DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
										return SM_OCP_UNABLE_TO_COMPLY;
									}
								}
								else if (SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType)
								{
									invalidrgnum++;
									DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "invalid rg num[%d]",invalidrgnum);
								}
							}
							}
						catch(UDBException& e)
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
							return SM_OCP_UNABLE_TO_COMPLY;
						}

					}

					if(invalidrgnum == base->MSCC.size() )
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "all rg is invalid,return","");
						return SM_OCP_UNKNOWN_SESSION_ID;
					}
				}
				break;
            case DATA_5G:
                {
                    SUSU* MSCC							= NULL;
                    if(base->MSCC.size() == 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "missing MSCC", "");

                        SCCR5GInfo* data =(SCCR5GInfo*)bizMsg->m_extend;
                        bizMsg->m_resultcode = 2001;
                        sendCCA(bizMsg);

                        if(m_psmpara->GetCommonPara()->nFreeAddCdr)
                        {
                            return RET_CDR;
                        }
                        else
                        {
                            return RET_SUCCESS;
                        }
                    }
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "MSCC size[%d]", base->MSCC.size());


                    //多RG
                    for(int i=0; i<base->MSCC.size(); i++)
                    {
                        SCCRDataUnit USU ;

                        MSCC = &(base->MSCC[i]);
                        if(!MSCC)
                        {
                            continue;
                        }

                            //计算使用量
                        USU.duration += MSCC->USU0.duration;
                        msg.GSUAns.duration += MSCC->USU0.duration;
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 druation[%u]", USU.duration);

                        USU.unitTotal += MSCC->USU0.unitTotal;
                        msg.GSUAns.unitTotal += MSCC->USU0.unitTotal;
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitTotal[%lld]", USU.unitTotal);

                        USU.unitInput += MSCC->USU0.unitInput;
                        msg.GSUAns.unitInput += MSCC->USU0.unitInput;
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "USU0 unitInput[%lld]", USU.unitInput);

                        USU.unitOutput += MSCC->USU0.unitOutput;
                        msg.GSUAns.unitOutput += MSCC->USU0.unitOutput;
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0 unitOutput[%lld]", USU.unitOutput);

                        if(1 == MSCC->USU1.tariffChangeUsage)//费率切换点之后的使用量
                        {
                            USU.duration += MSCC->USU1.duration;
                            msg.GSUAns.duration += MSCC->USU1.duration;
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 druation[%u]", USU.duration);

                            USU.unitTotal += MSCC->USU1.unitTotal;
                            msg.GSUAns.unitTotal += MSCC->USU1.unitTotal;
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitTotal[%lld]", USU.unitTotal);

                            USU.unitInput += MSCC->USU1.unitInput;
                            msg.GSUAns.unitInput += MSCC->USU1.unitInput;
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "USU0+USU1 unitInput[%lld]", USU.unitInput);

                            USU.unitOutput += MSCC->USU1.unitOutput;
                            msg.GSUAns.unitOutput += MSCC->USU1.unitOutput;
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "USU0+USU1 unitOutput[%lld]", USU.unitOutput);
                        }

                        //查询子会话是否存在
                        char childsession[BIZ_TEMP_LEN_256] 			= {0};
                        UDBSQL* pQuery = m_dbm->GetSQL(_5G_CCR_SelectChildSession); //q_5g_ccr_childsession
                        try
                        {
                            pQuery->DivTable(bizMsg->m_sessionID);
                            pQuery->UnBindParam();
                            if(strlen(MSCC->ProductOfferId.c_str()))//如果网元上报Product-Offer-Id，按此来批价
                            {
                                sprintf(childsession, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
                            }
                            else
                            {
                                sprintf(childsession, "%s;%ld", bizMsg->m_sessionID, MSCC->ratingGroup);
                            }

                            pQuery->BindParam(1, childsession);
                            pQuery->Execute();
                            //子会话存在则更新子会话USU
                            if(pQuery->Next())
                            {

                                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "child session exist", "");
                                UDBSQL* pExec = m_dbm->GetSQL(_5G_FREE_Update_ChildSession); //u_5g_free_childsession
                                try
                                {
                                    pExec->DivTable(bizMsg->m_sessionID);
                                    pExec->UnBindParam();
                                    pExec->BindParam(1, (long)et);//SM_LNG_TIME_TO_NEXT_CCR
                                    pExec->BindParam(2, (long)USU.duration);//SM_LNG_ALL_USU_TIME
                                    pExec->BindParam(3, (long)USU.unitTotal);//SM_LNG_ALL_USU_TOTAL_OCT
                                    pExec->BindParam(4, (long)USU.unitInput);//SM_LNG_ALL_USU_INPUT_OCT
                                    pExec->BindParam(5, (long)USU.unitOutput);//SM_LNG_ALL_USU_OUTPUT_OCT
                                    pExec->BindParam(6, bizMsg->m_sessionID);
                                    pExec->BindParam(7, childsession);
                                    pExec->Execute();
                                    pExec->Connection()->Commit();

                                }
                                catch(UDBException& e)
                                {
                                    pExec->Connection()->Rollback();
                                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
                                    return SM_OCP_UNABLE_TO_COMPLY;
                                }

                            }
                            //子会话不存在则插入子会话
                            else
                            {
                                if (SM_SESSION_UPDATE_CODE==bizMsg->m_requestType)
                                {

                                    //插入子会话

                                    UDBSQL* pExec = m_dbm->GetSQL(_5G_FREE_InsertChildSession); //i_5g_free_childsession
                                    try
                                    {
                                        pExec->DivTable(bizMsg->m_sessionID);
                                        pExec->UnBindParam();
										pExec->BindParam(1, bizMsg->m_sessionID);
                                        pExec->BindParam(2, childsession);
                                        pExec->BindParam(3, SM_SESSION_INITIAL_CODE);
										pExec->BindParam(4, (long)MSCC->ratingGroup);
                                        pExec->BindParam(5, (long)et);//SM_LNG_TIME_TO_NEXT_CCR
                                        pExec->BindParam(6, (long)bizMsg->timestampCCR);//RE_LNG_CALL_START_TIME
                                        pExec->BindParam(7, (long)bizMsg->timestampCCR);//RE_LNG_CURRENT_CCR_TIME
                                        pExec->Execute();
                                        pExec->Connection()->Commit();

                                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "insert ok", "");
                                    }
                                    catch(UDBException& e)
                                    {
                                        pExec->Connection()->Rollback();
                                        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
                                        return SM_OCP_UNABLE_TO_COMPLY;
                                    }
                                }
                                else if (SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType)
                                {
                                    invalidrgnum++;
                                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "invalid rg num[%d]",invalidrgnum);
                                }
                            }
                            }
                        catch(UDBException& e)
                        {
                            DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
                            return SM_OCP_UNABLE_TO_COMPLY;
                        }

                    }

                    if(invalidrgnum == base->MSCC.size() )
                    {
                        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "all rg is invalid,return","");
                        return SM_OCP_UNKNOWN_SESSION_ID;
                    }
                }
                break;
			case ISMP:
			case HRS:
				{

					SCCRDataUnit* USU = NULL;
					//SUSU *Psusu = NULL;
					//Psusu = base->MSCC[0]
					USU = &base->USU;
					msg.GSUAns.duration = USU->duration;
					//msg.GSUAns.money = USU->money;
					msg.GSUAns.unitTotal = USU->unitTotal;
					msg.GSUAns.unitInput = USU->unitInput;
					msg.GSUAns.unitOutput = USU->unitOutput;
					
					UDBSQL* pExec = m_dbm->GetSQL(ISMP_UpdateSessionFree);
					//update会话2002错误码(Commit失败)3次重试
					int retryCount = 0;
					bool success = false;
					while (!success && retryCount < 3)
					{
						try
						{
							pExec->BindParam(1, (long)et);				// SM_LNG_TIME_TO_NEXT_CCR
							pExec->BindParam(2, (long)USU->money);		// SM_LNG_ALL_USU_MONEY
							pExec->BindParam(3, (long)USU->duration);	// SM_LNG_ALL_USU_TIME
							pExec->BindParam(4, (long)USU->unitTotal);	// SM_LNG_ALL_USU_TOTAL_OCT
							pExec->BindParam(5, (long)USU->unitInput);	// SM_LNG_ALL_USU_INPUT_OCT
							pExec->BindParam(6, (long)USU->unitOutput); // SM_LNG_ALL_USU_OUTPUT_OCT
							pExec->BindParam(7, bizMsg->m_sessionID);
							pExec->Execute();
							pExec->Connection()->Commit();
							success = true;
						}
						catch (UDBException &e)
						{
							pExec->Connection()->Rollback();
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ", e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
							if (e.GetErrorCode() == 2002) // Commit失败
							{
								retryCount++;
								if (retryCount < 3)
								{
									DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
									continue;
								}
								else
								{
									DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "Max retries reached for error code 2002");
									return SM_OCP_UNABLE_TO_COMPLY;
								}
							}
							return SM_OCP_UNABLE_TO_COMPLY;
						}
					}
				}
			break;
			case DSL:
				{
					SCCRDataUnit* USU					= NULL;
					USU = &base->USU;
					msg.GSUAns.duration = USU->duration;					
					msg.GSUAns.unitTotal = USU->unitTotal;
					msg.GSUAns.unitInput = USU->unitInput;
					msg.GSUAns.unitOutput = USU->unitOutput;
					UDBSQL* pExec = m_dbm->GetSQL(DSL_UpdateSession_Free);
					// update会话2002错误码(Commit失败)3次重试
					int retryCount = 0;
					bool success = false;
					while (!success && retryCount < 3)
					{
						try
						{
							pExec->DivTable(bizMsg->m_sessionID);
							pExec->UnBindParam();
							pExec->BindParam(1, (long)et);				// SM_LNG_TIME_TO_NEXT_CCR
							pExec->BindParam(2, (long)USU->duration);	// SM_LNG_ALL_USU_TIME
							pExec->BindParam(3, (long)USU->unitTotal);	// SM_LNG_ALL_USU_TOTAL_OCT
							pExec->BindParam(4, (long)USU->unitInput);	// SM_LNG_ALL_USU_INPUT_OCT
							pExec->BindParam(5, (long)USU->unitOutput); // SM_LNG_ALL_USU_OUTPUT_OCT
							pExec->BindParam(6, bizMsg->m_sessionID);
							pExec->Execute();
							pExec->Connection()->Commit();
							success = true;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "update ok", "");
						}
						catch (UDBException &e)
						{
							pExec->Connection()->Rollback();
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ", e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
							if (e.GetErrorCode() == 2002) // Commit失败	
							{
								retryCount++;
								if (retryCount < 3)
								{
									DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
									continue;

								}
								else
								{
									DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "Max retries reached for error code 2002");
									return SM_OCP_UNABLE_TO_COMPLY;
								}	
							}
							return SM_OCP_UNABLE_TO_COMPLY;
						}
					}
				}
			break;
			case SMS:
				{
					
				}
			break;
		}
		
		
	}

	bizMsg->m_resultcode = 2001;
	sendCCA(bizMsg);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "cdr flag[%d],param[FREE_ADD_CDR][%d]", nCdrFlag,m_psmpara->GetCommonPara()->nFreeAddCdr);
	if(nCdrFlag &&  m_psmpara->GetCommonPara()->nFreeAddCdr)
	{
		bizMsg->m_longCDR = 0;
		return RET_CDR;//出单
	}
	else
	{
		if((SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType) ||(SM_SESSION_EVENT_CODE==bizMsg->m_requestType))
		{
			//删除会话
			DealFreeDealSession(bizMsg);
		}
		
		return RET_OVER;
	}
}


int DCUserBase::DealFreeVoiceInit(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;
	long nextCCTime						= 0;
	char szData[128]                    ={0};
	unsigned int RERcallType			= 0;
	unsigned int CDRcallType			= 0;
	int traceNumOnff					= 0;
	long TUSU                           = 0;
	int day 							= 1;
	long minsec 						= 0;
	long lastsec 						= 0;
	long pre_dtime						= 0;
	long cur_dtime						= 0;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO *subVisit                 = bizMsg->m_visit;
	unsigned int roamType				= bizMsg->m_roamtype;
	unsigned int longType				= bizMsg->m_longtype;
	SUserInfo *userInfo                 = bizMsg->m_userinfo;
	string sCreatFlag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "longType[%d], roamType[%d]", longType, roamType);

	//判断呼叫类型
	CDRcallType = bizMsg->m_cdrCallType;
	if(3==data->redirectionInfo && 3==CDRcallType)
	{
		RERcallType =3;
	}
	else if(3==CDRcallType)
	{
		RERcallType = 4;
	}
	else
	{
		RERcallType = bizMsg->m_cdrCallType;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "CDRcallType[%d]", CDRcallType);

	if("2100" == bizMsg->m_payMentMode)
	{
		sCreatFlag = "3";
	}
	else
	{
		sCreatFlag = "2";
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());

	//获取配额信息
	if((conf = m_psmpara->GetServiceQuotaConf(VOICE_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "no find service quota config[%d]", VOICE_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 2*conf->TOKEN;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);
	
	//插入会话
	traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));

	if(bizMsg->m_requestType == 5)
	{
		if(&base->USU == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "missing MSCC", "");
			return SM_OCP_MISSING_AVP;
		}
		else
		{
			TUSU = base->USU.duration;
		}
		sprintf(value,"%lld",base->starttime);
		day = DCCommonIF::GetDateDiff(value,TUSU,minsec,lastsec)+1;
	}
	for(int i = 0;i < day;i++)
	{
		if(1==day && bizMsg->m_requestType == 5)
		{
			//R603   本次计费请求开始时间
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" , cur_dtime);

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);
		
		}
		else if(0==i)
		{
		
			//R603   本次计费请求开始时间
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" , cur_dtime);

			TUSU = minsec;	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);
		}
		else if(i==day-1)
		{
		
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" , cur_dtime);

			TUSU = lastsec;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);		

		}
		else 
		{
		
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" ,cur_dtime);

			TUSU = 24*3600;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);		
		
		}
		
		UDBSQL* pExec = m_dbm->GetSQL(Voice_InsertSession_Free);
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_sessionID);
			pExec->BindParam(2, (int)bizMsg->m_requestNumber);
			pExec->BindParam(3, base->subscriptionData.c_str());
			pExec->BindParam(4, base->originHost.c_str());
			pExec->BindParam(5, (long)conf->TOKEN);
			pExec->BindParam(6, (int)bizMsg->m_requestType);
			pExec->BindParam(7, TORB_ACTION);
			pExec->BindParam(8, nextCCTime);
			pExec->BindParam(9, base->subscription.phone.c_str());
			pExec->BindParam(10, data->calling.phone.c_str());

			if(CDRcallType > 2)
			{
				pExec->BindParam(11, base->subscription.phone.c_str());
			}
			else
			{
				pExec->BindParam(11, data->called.phone.c_str());
			}

			pExec->BindParam(12, (int)RERcallType);
			pExec->BindParam(13, data->callingLAI.c_str());
			pExec->BindParam(14, data->calledLAI.c_str());

			if (data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(15, value);

			if(86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else if (data->calling.province)
			{
				sprintf(value, "0%d", data->calling.province);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(16, value);//RE_STR_CALLING_PROVINCE

			//把值绑定到RE_STR_CALLING_AREA字段，记话单用
			if(86 != data->calling.country)
			{
				sprintf(value, "00%d", data->calling.country);
			}
			else if (data->calling.area)
			{
				sprintf(value, "0%d", data->calling.area);
			}
			else
			{
				value[0] = '\0';
			}
			pExec->BindParam(17, value); //RE_STR_CALLING_AREA
			pExec->BindParam(18, data->calling.carriers);

			if(CDRcallType > 2)
			{
				if (base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
					pExec->BindParam(19, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(19, value);
				}

				if(86 != base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
				}
				else if (base->subscription.province)
				{
					sprintf(value, "0%d", base->subscription.province);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(20, value);

				if(86 != base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
				}
				else if (base->subscription.area)
				{
					sprintf(value, "0%d", base->subscription.area);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(21, value); //RE_STR_CALLED_AREA

				if (base->subscription.carriers)
				{
					pExec->BindParam(22, base->subscription.carriers);
				}
				else
				{
					pExec->BindParam(22, 0);
				}
			}
			else
			{
				if (data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
					pExec->BindParam(19, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(19, value);
				}

				if(86 != data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
				}
				else if (data->called.province)
				{
					sprintf(value, "0%d", data->called.province);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(20, value);//RE_STR_CALLED_PROVINCE

				if(86 != data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
				}
				else if (data->called.area)
				{
					sprintf(value, "0%d", data->called.area);
				}
				else
				{
					value[0] = '\0';
				}
				pExec->BindParam(21, value);//RE_STR_CALLED_AREA

				if (data->called.carriers)
				{
					pExec->BindParam(22, data->called.carriers);
				}
				else
				{
					pExec->BindParam(22, 0);
				}
			}

			if(data->called.access)
			{
				sprintf(value, "%d", data->called.access);
				pExec->BindParam(23, value);
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(23, value);
			}

			if (base->subscription.country)
			{
				sprintf(value, "00%d", base->subscription.country);
				pExec->BindParam(24,  value);
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(24, value);
			}

			if (base->subscription.province)
			{
				if(86 != base->subscription.country)
				{
					sprintf(value, "00%d", base->subscription.country);
				}
				else
				{
					sprintf(value, "0%d", base->subscription.province);
				}
				pExec->BindParam(25, value);
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(25, value);
			}

			if (base->subscription.area)
			{
				sprintf(value, "0%d", base->subscription.area);
				pExec->BindParam(26, value);//RE_STR_SUB_AREA
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(26, value);
			}

			if(base->subscription.carriers)
			{
				pExec->BindParam(27, base->subscription.carriers);
			}
			else
			{
				pExec->BindParam(27, 0);
			}

			if (subVisit->area)
			{
				if(6==roamType || 9==roamType)
				{
					sprintf(value, "00%d", subVisit->area);
				}
				else sprintf(value, "0%d", subVisit->area);
				pExec->BindParam(28, value);//RE_STR_SUB_VISIT_AREA
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(28, value);
			}

			pExec->BindParam(29, data->callingVLR);//RE_STR_CALLING_VLR
			pExec->BindParam(30, data->calledVLR);//RE_STR_CALLED_VLR
			pExec->BindParam(31, (int)longType);
			pExec->BindParam(32, (int)roamType);
			pExec->BindParam(33, (int)longType);
			pExec->BindParam(34, (int)roamType);
			if(bizMsg->m_requestType == 5)
			{
				pExec->BindParam(35, pre_dtime);//RE_LNG_CALL_START_TIME
				pExec->BindParam(36, cur_dtime);//RE_LNG_CURRENT_CCR_TIME
			}
			else
			{
				pExec->BindParam(35, (long)bizMsg->timestampCCR);//RE_LNG_CALL_START_TIME
				pExec->BindParam(36, (long)bizMsg->timestampCCR);//RE_LNG_CURRENT_CCR_TIME
			}
			pExec->BindParam(37, (long)bizMsg->m_serial);
			pExec->BindParam(38, SM_CDR_VERSION);
			pExec->BindParam(39, SM_CDR_TICKETTYPE);
			pExec->BindParam(40, base->topology.c_str());
			pExec->BindParam(41, 0);
			pExec->BindParam(42, 0);

			if(CDRcallType == 1)
			{
				pExec->BindParam(43, SERVICES_CENARIOUS_VOICE_MOC);
			}
			else if(CDRcallType == 2)
			{
				pExec->BindParam(43, SERVICES_CENARIOUS_VOICE_MTC);
			}
			else
			{
				pExec->BindParam(43, SERVICES_CENARIOUS_VOICE_CFW);
			}

			pExec->BindParam(44, base->subUnified.c_str());
			pExec->BindParam(45, data->callingUnified.c_str());
			pExec->BindParam(47, data->callingNumber.c_str());//OCP_STR_ORIGIN_CALLING_NBR

			if(CDRcallType > 2)
			{
				pExec->BindParam(46, base->subUnified.c_str());//CDR_PUB_STR_CALLED_PARTY
				pExec->BindParam(48, base->subscriptionData.c_str());//OCP_STR_ORIGIN_CALLED_NBR
			}
			else
			{
				pExec->BindParam(46, data->calledUnified.c_str());
				pExec->BindParam(48, data->calledNumber.c_str());
			}

			pExec->BindParam(49, userInfo->servID);
			pExec->BindParam(50, userInfo->custID);
			pExec->BindParam(51, MASTER_PRODUCTID_CDMA);
			pExec->BindParam(52, SM_CDR_SEQ_NUM);//SM_CDR_SEQ_NUM
			if(0 == m_psmpara->GetINPara()->iSMIfalg)//网元上报IMSI
			{
				if(0 != strcmp(data->IMSI.c_str(), ""))
				{
					pExec->BindParam(53, data->IMSI.c_str());
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "IMSI[%s]", data->IMSI.c_str());
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(53, value);
				}
			}
			else//主产品实例表中IMSI
			{
			   if(0 != strcmp(userInfo->IMSI, ""))
			   	{
			   		pExec->BindParam(53, userInfo->IMSI);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "IMSI[%s]", userInfo->IMSI);
			   	}
				else
				{
					value[0] = '\0';
					pExec->BindParam(53, value);
				}
				 
			}
			//pExec->BindParam(53, data->IMSI);
			pExec->BindParam(54, (long)(data->serviceKey));
			pExec->BindParam(55, data->bearerCapability.c_str());
			pExec->BindParam(56, data->MSC);
			pExec->BindParam(57, userInfo->aocType);
			pExec->BindParam(58, base->routeRecord);

			pExec->BindParam(59, traceNumOnff);
			pExec->BindParam(60, data->calledShortNumber.c_str());
			pExec->BindParam(61, 1);//SM_INT_BILLING_MODE

			if(data->redirectionInfo)
			{
				if(6 == data->redirectionInfo)
				{
					data->redirectionInfo = 4;
				}
				sprintf(value, "%u", data->redirectionInfo);
				pExec->BindParam(62, value);//CDR_STR_REDIRECT_TYPE
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(62, value);
			}

			if(CDRcallType > 2)
			{
				if(data->called.phone.length() != 0)
				{
					pExec->BindParam(63, data->called.phone.c_str());//CDR_STR_REDIRECT_NBR
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(63, value);
				}

				if (data->called.country)
				{
					sprintf(value, "00%d", data->called.country);
					pExec->BindParam(64, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(64, value);
				}

				if (data->called.province)
				{
					if(86 != data->called.country)
					{
						sprintf(value, "00%d", data->called.country);
					}
					else
					{
						sprintf(value, "0%d", data->called.province);
					}
					pExec->BindParam(65, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(65, value);
				}

				if (data->called.area)
				{
					sprintf(value, "0%d", data->called.area);
					pExec->BindParam(66, value);
				}
				else
				{
					value[0] = '\0';
					pExec->BindParam(66, value);
				}

				if (data->called.carriers)
				{
					pExec->BindParam(67, data->called.carriers);//RE_INT_IPMOC_FLAG
				}
				else
				{
					pExec->BindParam(67, 0);
				}
			}
			else
			{
				value[0] = '\0';
				pExec->BindParam(63, value);
				pExec->BindParam(64, value);
				pExec->BindParam(65, value);
				pExec->BindParam(66, value);
				pExec->BindParam(67, 0);
			}

			if(data->called.access)
			{
				pExec->BindParam(68, 2);
			}
			else
			{
				pExec->BindParam(68, 1);
			}
			pExec->BindParam(69, (int)CDRcallType);
			pExec->BindParam(70, (int)0);
			if(2 == CDRcallType)
			{
				value[0] = '\0';
				pExec->BindParam(71, value); //RE_STR_CALLING_VISIT_AREA
				if(6==roamType||9==roamType)	sprintf(value, "00%d", subVisit->area);
				else sprintf(value, "0%d", subVisit->area);
				pExec->BindParam(72, value);//RE_STR_CALLED_VISIT_AREA
			}
			else
			{
				if(6==roamType||9==roamType)	sprintf(value, "00%d", subVisit->area);
				else sprintf(value, "0%d", subVisit->area);
				pExec->BindParam(71, value); //RE_STR_CALLING_VISIT_AREA
				value[0] = '\0';
				pExec->BindParam(72, value); //RE_STR_CALLED_VISIT_AREA
			}

			if(userInfo->isRemind)
			{
				pExec->BindParam(73, (int)2207);
			}
			else
			{
				pExec->BindParam(73, (int)2001);
			}
			pExec->BindParam(74, data->callingCellID.c_str());//RE_STR_CALLING_CELL_ID
			pExec->BindParam(75, data->calledCellID.c_str());//RE_STR_CALLED_CELL_ID
			2==data->eventTypeBCSM?pExec->BindParam(76, data->callingCellID.c_str()):pExec->BindParam(76, data->calledCellID.c_str());
			if(userInfo->isActive)
			{
				pExec->BindParam(77, 1);//OCP_INT_ACTIVE_FLAG
			}
			else
			{
				pExec->BindParam(77, 0);
			}
			if(bizMsg->m_requestType == 5)
			{
				pExec->BindParam(78, bizMsg->billcycle);//RE_LNG_SYS_CCR_TIME
			}
			else
				pExec->BindParam(78, (long)base->timestamp);//RE_LNG_SYS_CCR_TIME
			pExec->BindParam(79, TUSU);//SM_LNG_ALL_USU_TIME
			pExec->BindParam(80, 1);//SM_INT_FREE_FLAG
			pExec->BindParam(81, userInfo->userType);
			pExec->BindParam(82, userInfo->mvnoID);
			pExec->BindParam(83, userInfo->ilatnid);
			pExec->BindParam(84, userInfo->lnAcctID);
			pExec->BindParam(85, bizMsg->m_szServiceContextIDStr);
            pExec->BindParam(86, data->IMSChargingIdentifier);
            pExec->BindParam(87, data->callaccesstype);
			bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
            pExec->BindParam(88, bizMsg->m_sBatchId.c_str());
			pExec->BindParam(89, sCreatFlag.c_str());
			char szTemp[2]={0};
			if(base->smExt.kv.count("CALLINGECGI"))
			{
				pExec->BindParam(90, base->smExt.kv["CALLINGECGI"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Voice_InsertSession_Free calling_wifi_ecgi[%s]", base->smExt.kv["CALLINGECGI"].c_str());
			}
			else
			{
				pExec->BindParam(90, szTemp);
			}
			if(base->smExt.kv.count("CALLEDECGI"))
			{
				pExec->BindParam(91, base->smExt.kv["CALLEDECGI"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Voice_InsertSession_Free called_wifi_ecgi[%s]", base->smExt.kv["CALLEDECGI"].c_str());
			}
			else
			{
				pExec->BindParam(91, szTemp);
			}
			if(base->smExt.kv.count("CALLINGWIFIACCT"))
			{
				pExec->BindParam(92, base->smExt.kv["CALLINGWIFIACCT"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Voice_InsertSession_Free calling_wifi_account[%s]", base->smExt.kv["CALLINGWIFIACCT"].c_str());
			}
			else
			{
				pExec->BindParam(92, szTemp);
			}
			if(base->smExt.kv.count("CALLEDWIFIACCT"))
			{
				pExec->BindParam(93, base->smExt.kv["CALLEDWIFIACCT"].c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Voice_InsertSession_Free called_wifi_account[%s]", base->smExt.kv["CALLEDWIFIACCT"].c_str());
			}
			else
			{
				pExec->BindParam(93, szTemp);
			}
			pExec->Execute();
			pExec->Connection()->Commit();
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "insert  ok", "");
		}
		catch(UDBException& e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","insert exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}
	return RET_SUCCESS;
}


int DCUserBase::DealFreeDataInit(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int resultCode 						= 0;
	int roamType							= 0;
	int traceNumOnff						= 0;
	long nextCCTime						= 0;
	
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO *subVisit  =bizMsg->m_visit;
	SUserInfo *userInfo =bizMsg->m_userinfo;
	int ServiceScenarious  = 700;
	int nFreeFlag = 1;

   
	//ServiceScenarious
    if(0==strcmp(data->RATType.c_str(),"32876"))
    {  
       ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
    }
	else if(0==strcmp(data->RATType.c_str(),"59"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
	}
	else if(0==strcmp(data->RATType.c_str(),"33"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}
	else if((0==strlen(data->RATType.c_str())) && (strlen(data->userLocationInfo.c_str()) !=0) && 0==strncmp(data->userLocationInfo.c_str()+5,"EE",2))
	{
	    ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
	}
	else if((0==strlen(data->RATType.c_str())) && (strlen(data->userLocationInfo.c_str()) ==0))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X; 
	}
	else
	{
	    ServiceScenarious = SERVICES_CENARIOUS_DATA_1X; 
	}

	string sCreatFlag;
	if("2100" == bizMsg->m_payMentMode)
	{
		sCreatFlag = "3";
	}
	else
	{
		sCreatFlag = "2";
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());

	
	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",bizMsg->timestampCCR);

       
	UDBSQL* pExec = m_dbm->GetSQL(DATA_InsertSessionStoreFree);	
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, SM_CDR_SEQ_NUM);
		pExec->BindParam(2, SM_CDR_VERSION);
		pExec->BindParam(3, SM_CDR_TICKETTYPE);//CDR_PUB_INT_TICKETTYPE
		pExec->BindParam(4, timeStamp);//CDR_PUB_STR_TIMESTAMP
		pExec->BindParam(5, base->topology);
		pExec->BindParam(6, 0);//CDR_PUB_INT_CORRELATIONID
		pExec->BindParam(7, 0);//CDR_PUB_INT_TICKETSEQUENCEID
		pExec->BindParam(8, ServiceScenarious);//CDR_PUB_INT_SERVICESCENARIOUS
		pExec->BindParam(9, base->subUnified);//CDR_PUB_STR_CHARGED_PARTY
		pExec->BindParam(10, userInfo->servID);
		pExec->BindParam(11, userInfo->custID);
		pExec->BindParam(12, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(13, bizMsg->m_sessionID);
		pExec->BindParam(14, SM_SESSION_INITIAL_CODE);
		pExec->BindParam(15, (int)bizMsg->m_requestNumber);
		pExec->BindParam(16, base->originHost);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->subscription.phone);

		sprintf(value, "0%d", base->subscription.area);
		pExec->BindParam(19, value);

		if(subVisit->area)
		{
			sprintf(value, "0%d", subVisit->area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(20, value);

		pExec->BindParam(21, base->subscription.carriers);
		pExec->BindParam(22, 1);
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(23, base->starttime);
			pExec->BindParam(24, base->timestamp);
		}
		else
		{
			pExec->BindParam(23, (long)bizMsg->timestampCCR);
			pExec->BindParam(24, (long)bizMsg->timestampCCR);
		}
		pExec->BindParam(25, roamType);
		pExec->BindParam(26, (long)bizMsg->m_serial);
		pExec->BindParam(27, userInfo->aocType);
		pExec->BindParam(28, STATUS_IDLE);
		pExec->BindParam(29, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(30, traceNumOnff);
		if(0 != strcmp(userInfo->IMSI, ""))
		{
			pExec->BindParam(31, userInfo->IMSI);
		}
		else
		{
			value[0] = '\0';
			pExec->BindParam(31, value);
		}
		pExec->BindParam(32, (long)0); // SM_LNG_MSCC_VALIDITY_TIME
		pExec->BindParam(33, (long)0); // SM_LNG_QUOTA_CONSUME_TIME
		pExec->BindParam(34, (long)0); // SM_LNG_VOLUME_QUOTA_THRESHOLD
		pExec->BindParam(35, (long)0); // SM_LNG_TIME_QUOTA_THRESHOLD
		pExec->BindParam(36, (long)0); // SM_LNG_QUOTA_HOLDING_TIME
		pExec->BindParam(37, data->PDSNAddress);//OCP_STR_IP_GGSN_ADDR
		pExec->BindParam(38, data->SGSNAddress);
		pExec->BindParam(39, data->APN);
		pExec->BindParam(40, data->PDPType);
		pExec->BindParam(41, data->PDPAddress);
		//根据是否离线传RG在下面处理
		pExec->BindParam(43, data->QOS);
		pExec->BindParam(44, data->userLocationInfo);
		pExec->BindParam(45, data->RATType);
		pExec->BindParam(46, data->chargingID);
		pExec->BindParam(47, userInfo->isActive);
		pExec->BindParam(48, atol(timeStamp)); //作为上次 CCR 的时间
		pExec->BindParam(49, 0);							//	用来标识主会话
		pExec->BindParam(50, data->PDSNAddress);       //PDSN
		pExec->BindParam(51, data->userCellid);        //CELLID
		pExec->BindParam(52, data->userLac);           //MCS
		pExec->BindParam(53, data->userLocationInfo);  //userLocation_Info
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(54, bizMsg->billcycle);
			//离线免费需要插prod_offer_id和RG
			SUSU* MSCC	= NULL;
			if(base->MSCC.size() == 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"",  "missing MSCC", "");

				SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
				bizMsg->m_resultcode = 2001;
				sendCCA(bizMsg);
				return RET_ERROR;
			}
			MSCC = &(base->MSCC[0]);
			pExec->BindParam(63, MSCC->ProductOfferId);
			pExec->BindParam(42, MSCC->ratingGroup);
		}
		else
		{
			pExec->BindParam(54, (long)base->timestamp);
			pExec->BindParam(63, (long)0);
			pExec->BindParam(42, (long)0);
		}
		pExec->BindParam(55, nFreeFlag);
		pExec->BindParam(56, 2001);
		pExec->BindParam(57, userInfo->userType);
		pExec->BindParam(58, userInfo->mvnoID);
		pExec->BindParam(59, (int)bizMsg->m_serviceContextID);
		pExec->BindParam(60, data->userMsc);
		pExec->BindParam(61, userInfo->ilatnid);
		pExec->BindParam(62, userInfo->lnAcctID);
		pExec->BindParam(64, bizMsg->m_szServiceContextIDStr);		
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
		pExec->BindParam(65, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(66, bizMsg->addressIpv6);
		pExec->BindParam(67, sCreatFlag);		
		pExec->Execute();
		pExec->Connection()->Commit();
		
	}
	catch(UDBException& e)
	{
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "send CCA ok", "");
	
	return RET_SUCCESS;
}


int DCUserBase::DealFreePGWInit(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;
	int traceNumOnff					= 0;
	long nextCCTime						= 0;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO *subVisit  = bizMsg->m_visit;
	SUserInfo *userInfo  = bizMsg->m_userinfo;	
	int roamType		 = bizMsg->m_roamtype;
	int ServiceScenarious  = 700;
	int nFreeFlag = 1;

	//ServiceScenarious
    if(0==strcmp(data->RATType.c_str(),"32876"))
    {  
       ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
    }
	else if(0==strcmp(data->RATType.c_str(),"59"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
	}
	else if(0==strcmp(data->RATType.c_str(),"33"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}
	else if((0==strlen(data->RATType.c_str())) && (strlen(data->userLocationInfo.c_str()) !=0) && 0==strncmp(data->userLocationInfo.c_str()+5,"EE",2))
	{
	    ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
	}
	else if((0==strlen(data->RATType.c_str())) && (strlen(data->userLocationInfo.c_str())  ==0))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X; 
	}
	else
	{
	    ServiceScenarious = SERVICES_CENARIOUS_DATA_1X; 
	}
	
	string sCreatFlag;
	if("2100" == bizMsg->m_payMentMode)
	{
		sCreatFlag = "3";
	}
	else
	{
		sCreatFlag = "2";
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());
	
	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",bizMsg->timestampCCR);

	UDBSQL* pExec = m_dbm->GetSQL(PGW_FREE_Init_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, SM_CDR_SEQ_NUM);
		pExec->BindParam(2, SM_CDR_VERSION);
		pExec->BindParam(3, SM_CDR_TICKETTYPE);//CDR_PUB_INT_TICKETTYPE
		pExec->BindParam(4, timeStamp);//CDR_PUB_STR_TIMESTAMP
		pExec->BindParam(5, base->topology);
		pExec->BindParam(6, 0);//CDR_PUB_INT_CORRELATIONID
		pExec->BindParam(7, 0);//CDR_PUB_INT_TICKETSEQUENCEID
		pExec->BindParam(8, ServiceScenarious);//CDR_PUB_INT_SERVICESCENARIOUS
		pExec->BindParam(9, base->subUnified);//CDR_PUB_STR_CHARGED_PARTY
		pExec->BindParam(10, userInfo->servID);
		pExec->BindParam(11, userInfo->custID);
		pExec->BindParam(12, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(13, bizMsg->m_sessionID);
		pExec->BindParam(14, SM_SESSION_INITIAL_CODE);
		pExec->BindParam(15, (int)bizMsg->m_requestNumber);
		pExec->BindParam(16, base->originHost);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->subscription.phone);

		sprintf(value, "0%d", base->subscription.area);
		pExec->BindParam(19, value);

		if(subVisit->area)
		{
			sprintf(value, "0%d", subVisit->area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(20, value);

		pExec->BindParam(21, base->subscription.carriers);
		pExec->BindParam(22, 1);
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(23, base->starttime);
			pExec->BindParam(24, base->timestamp);
		}
		else
		{
			pExec->BindParam(23, (long)bizMsg->timestampCCR);
			pExec->BindParam(24, (long)bizMsg->timestampCCR);
		}
		pExec->BindParam(25, roamType);
		pExec->BindParam(26, (long)bizMsg->m_serial);
		pExec->BindParam(27, userInfo->aocType);
		pExec->BindParam(28, STATUS_IDLE);
		pExec->BindParam(29, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(30, traceNumOnff);
		if(0 != strcmp(userInfo->IMSI, ""))
		{
			pExec->BindParam(31, userInfo->IMSI);
		}
		else
		{
			value[0] = '\0';
			pExec->BindParam(31, value);
		}
		pExec->BindParam(32, 0); // SM_LNG_MSCC_VALIDITY_TIME
		pExec->BindParam(33, 0); // SM_LNG_QUOTA_CONSUME_TIME
		pExec->BindParam(34, 0); // SM_LNG_VOLUME_QUOTA_THRESHOLD
		pExec->BindParam(35, 0); // SM_LNG_TIME_QUOTA_THRESHOLD
		pExec->BindParam(36, 0); // SM_LNG_QUOTA_HOLDING_TIME
		pExec->BindParam(37, data->PDSNAddress);//OCP_STR_IP_GGSN_ADDR
		pExec->BindParam(38, data->SGSNAddress);
		pExec->BindParam(39, data->APN);
		pExec->BindParam(40, data->PDPType);
		pExec->BindParam(41, data->PDPAddress);
		//根据是否离线传RG在下面处理
		pExec->BindParam(43, data->QOS);
		pExec->BindParam(44, data->userLocationInfo);
		pExec->BindParam(45, data->RATType);
		pExec->BindParam(46, data->chargingID);
		pExec->BindParam(47, userInfo->isActive);
		pExec->BindParam(48, atol(timeStamp)); //作为上次 CCR 的时间
		pExec->BindParam(49, 0);							//	用来标识主会话
		pExec->BindParam(50, data->PDSNAddress);       //PDSN
		pExec->BindParam(51, data->userCellid);        //CELLID
		pExec->BindParam(52, data->userLac);           //MCS
		pExec->BindParam(53, data->userLocationInfo);  //userLocation_Info
		memset(value, 0, sizeof(value));
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(54, bizMsg->billcycle);
			//离线免费需要插prod_offer_id和RG
			SUSU* MSCC	= NULL;
			if(base->MSCC.size() == 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"",  "missing MSCC", "");

				SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;
				bizMsg->m_resultcode = 2001;
				sendCCA(bizMsg);
				return RET_ERROR;
			}
			MSCC = &(base->MSCC[0]);
			sprintf(value,"%s",MSCC->ProductOfferId.c_str());
			pExec->BindParam(63, value);
			pExec->BindParam(42, MSCC->ratingGroup);
		}
		else
		{
			pExec->BindParam(54, (long)base->timestamp);
			sprintf(value,"%s","0");
			pExec->BindParam(63, value);
			pExec->BindParam(42, (long)0);
		}
		pExec->BindParam(55, nFreeFlag);
		pExec->BindParam(56, 2001);
		pExec->BindParam(57, userInfo->userType);
		pExec->BindParam(58, userInfo->mvnoID);
		pExec->BindParam(59, (int)bizMsg->m_serviceContextID);
		pExec->BindParam(60, data->userMsc); 
		pExec->BindParam(61, userInfo->ilatnid);
		pExec->BindParam(62, userInfo->lnAcctID);
		pExec->BindParam(64, bizMsg->m_szServiceContextIDStr);
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
		pExec->BindParam(65, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(66, bizMsg->addressIpv6);		
		pExec->BindParam(67, sCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
		
	}
	catch(UDBException& e)
	{
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "send CCA ok", "");
	
	return RET_SUCCESS;
}

int DCUserBase::DealFree5GInit(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg)
{
    int ret 							= RET_SUCCESS;
    int resultCode 						= 0;
    int traceNumOnff					= 0;
    long nextCCTime						= 0;
    char value[BIZ_TEMP_LEN_256] 		= {0};
    char timeStamp[BIZ_TEMP_LEN_16]		= {0};
    AREA_INFO *subVisit  = bizMsg->m_visit;
    SUserInfo *userInfo  = bizMsg->m_userinfo;
    int roamType		 = bizMsg->m_roamtype;
    int ServiceScenarious  = 700;
    int nFreeFlag = 1;
    if(0==strcmp(data->rATType.c_str(),"32876"))
    {
       ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
    }
    else if(0==strcmp(data->rATType.c_str(),"59"))
    {
       ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
    }
    else if(0==strcmp(data->rATType.c_str(),"33"))
    {
       ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
    }
    else
    {
        ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
    }
    string sCreatFlag;
    if("2100" == bizMsg->m_payMentMode)
    {
        sCreatFlag = "3";
    }
    else
    {
        sCreatFlag = "2";
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());
    time_t et;
    time(&et);
    nextCCTime = et + 300;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "next cc time[%ld]", nextCCTime);
    sprintf(timeStamp,"%ld",bizMsg->timestampCCR);

/*    UDBSQL* pExec = m_dbm->GetSQL(_5G_FREE_Init_InsertSession); //i_5g_free_init_session
    try
    {
        pExec->DivTable(bizMsg->m_sessionID);
        pExec->UnBindParam();
        pExec->BindParam(1, SM_CDR_SEQ_NUM);
        pExec->BindParam(2, SM_CDR_VERSION);
        pExec->BindParam(3, SM_CDR_TICKETTYPE);//CDR_PUB_INT_TICKETTYPE
        pExec->BindParam(4, timeStamp);//CDR_PUB_STR_TIMESTAMP
        pExec->BindParam(5, base->topology);
        pExec->BindParam(6, 0);//CDR_PUB_INT_CORRELATIONID
        pExec->BindParam(7, 0);//CDR_PUB_INT_TICKETSEQUENCEID
        pExec->BindParam(8, ServiceScenarious);//CDR_PUB_INT_SERVICESCENARIOUS
        pExec->BindParam(9, base->subUnified);//CDR_PUB_STR_CHARGED_PARTY
        pExec->BindParam(10, userInfo->servID);
        pExec->BindParam(11, userInfo->custID);
        pExec->BindParam(12, MASTER_PRODUCTID_CDMA);
        pExec->BindParam(13, bizMsg->m_sessionID);
        pExec->BindParam(14, SM_SESSION_INITIAL_CODE);
        pExec->BindParam(15, (int)bizMsg->m_requestNumber);
        pExec->BindParam(16, base->originHost);
        pExec->BindParam(17, nextCCTime);
        pExec->BindParam(18, base->subscription.phone);

        sprintf(value, "0%d", base->subscription.area);
        pExec->BindParam(19, value);

        if(subVisit->area)
        {
            sprintf(value, "0%d", subVisit->area);
        }
        else
        {
            value[0] = '\0';
        }
        pExec->BindParam(20, value);

        pExec->BindParam(21, base->subscription.carriers);
        pExec->BindParam(22, 1);
        if(bizMsg->m_requestType == 5)
        {
            pExec->BindParam(23, base->starttime);
            pExec->BindParam(24, base->timestamp);
        }
        else
        {
            pExec->BindParam(23, (long)bizMsg->timestampCCR);
            pExec->BindParam(24, (long)bizMsg->timestampCCR);
        }
        pExec->BindParam(25, roamType);
        pExec->BindParam(26, (long)bizMsg->m_serial);
        pExec->BindParam(27, userInfo->aocType);
        pExec->BindParam(28, STATUS_IDLE);
        pExec->BindParam(29, base->routeRecord);

        traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
        pExec->BindParam(30, traceNumOnff);
        if(0 != strcmp(userInfo->IMSI, ""))
        {
            pExec->BindParam(31, userInfo->IMSI);
        }
//        pExec->BindParam(37, data->PDSNAddress);//OCP_STR_IP_GGSN_ADDR
//        pExec->BindParam(38, data->SGSNAddress);
        pExec->BindParam(39, data->dNNID);//APN
//        pExec->BindParam(40, data->PDPType);
//        pExec->BindParam(41, data->PDPAddress);
        //根据是否离线传RG在下面处理
//        pExec->BindParam(43, data->QOS);
//        pExec->BindParam(44, data->userLocationInfo);
        pExec->BindParam(45, data->rATType);
        pExec->BindParam(46, data->chargingId);
        pExec->BindParam(47, userInfo->isActive);
        pExec->BindParam(48, atol(timeStamp)); //作为上次 CCR 的时间
        pExec->BindParam(49, 0);							//	用来标识主会话
//        pExec->BindParam(50, data->PDSNAddress);       //PDSN
        pExec->BindParam(51, data->userLocationinfo.nrCellId);        //CELLID
        pExec->BindParam(52, data->userLocationinfo.tac);           //MCS
//        pExec->BindParam(53, data->userLocationInfo);  //userLocation_Info
        if(bizMsg->m_requestType == 5)
        {
            pExec->BindParam(54, bizMsg->billcycle);
            //离线免费需要插prod_offer_id和RG
            SUSU* MSCC	= NULL;
            if(base->MSCC.size() == 0)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"",  "missing MSCC", "");

                SCCR5GInfo* data =(SCCR5GInfo*)bizMsg->m_extend;
                bizMsg->m_resultcode = 2001;
                sendCCA(bizMsg);
                return RET_ERROR;
            }
            MSCC = &(base->MSCC[0]);
            pExec->BindParam(63, MSCC->ProductOfferId);
            pExec->BindParam(42, MSCC->ratingGroup);
        }
        else
        {
            pExec->BindParam(54, (long)base->timestamp);
            pExec->BindParam(63, (long)0);
            pExec->BindParam(42, (long)0);
        }
        pExec->BindParam(55, nFreeFlag);
        pExec->BindParam(56, 2001);
        pExec->BindParam(57, userInfo->userType);
        pExec->BindParam(58, userInfo->mvnoID);
        pExec->BindParam(59, (int)bizMsg->m_serviceContextID);
//        pExec->BindParam(60, data->userMsc);
        pExec->BindParam(61, userInfo->ilatnid);
        pExec->BindParam(62, userInfo->lnAcctID);
        pExec->BindParam(64, bizMsg->m_szServiceContextIDStr);
        bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
        pExec->BindParam(65, bizMsg->m_sBatchId.c_str());
        pExec->BindParam(66, bizMsg->addressIpv6);
        pExec->BindParam(67, sCreatFlag);
        pExec->Execute();
        pExec->Connection()->Commit();

    }
    catch(UDBException& e)
    {
        pExec->Connection()->Rollback();
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
        return SM_OCP_UNABLE_TO_COMPLY;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "send CCA ok", "");
*/
    return RET_SUCCESS;
}
int DCUserBase::DealFreeIsmpInit(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode						= 0;
	int payFlag 						= 1;
	int  traceNumOnff					= 0;
	long nextCCTime						= 0;

	TSERVICE_QUOTA_CONF *conf			= NULL;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	SUserInfo *userInfo                 = bizMsg->m_userinfo;
	//用户类型标示:0,非高风险业务;1，预付费高风险业务;2，后付费高风险业务
	int iUserMessFlag	= 0;
	
	string sCreatFlag;
	if("2100" == bizMsg->m_payMentMode)
	{
		sCreatFlag = "3";
	}
	else
	{
		sCreatFlag = "2";
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());

	
	//获取配额信息
	if((conf = m_psmpara->GetServiceQuotaConf(ISMP_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"",  "no find service quota config[%d]",ISMP_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + conf->VALID_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "nextCCTime[%ld]", nextCCTime);
    	sprintf(timeStamp,"%ld",base->timestamp);	
	//计算计费方类型
	if(data->chargingPartyType >= 3)
	{
		payFlag = 3;
	}
	else
	{
		payFlag = data->chargingPartyType;
	}

	//判断网元是否有上传预占
	if(!base->RSU.money && !base->RSU.duration && !base->RSU.unitInput && !base->RSU.unitOutput && !base->RSU.unitTotal)
	{
		base->RSU.duration = conf->DURATION;
		base->RSU.unitInput = conf->INPUT_OCTETS;
		base->RSU.unitOutput = conf->OUTPUT_OCTETS;
		base->RSU.unitTotal = conf->TOTAL_OCTETS;
	}


	UDBSQL* pExec = m_dbm->GetSQL(ISMP_InsertSessionFree);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID);
		pExec->BindParam(2, SM_CDR_SEQ_NUM);
		pExec->BindParam(3, SM_CDR_VERSION);
		pExec->BindParam(4, SM_CDR_TICKETTYPE);
		pExec->BindParam(5, timeStamp);
		pExec->BindParam(6, base->topology);
		pExec->BindParam(7, 0);
		pExec->BindParam(8, 0);
		if(2 == iUserMessFlag)
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP_HRS_A);
		}
		else
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP);
		}
		pExec->BindParam(10, payFlag);
		pExec->BindParam(11, userInfo->servID);
		pExec->BindParam(12, userInfo->custID);
		pExec->BindParam(13, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(14, 0);
		pExec->BindParam(15, TORB_ACTION);
		pExec->BindParam(16, (int)data->chargingType);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->originHost);
		pExec->BindParam(19, (long)bizMsg->m_serial);
		pExec->BindParam(20, (long)bizMsg->timestampCCR);
		pExec->BindParam(21, (long)bizMsg->timestampCCR);
		pExec->BindParam(22, (int)bizMsg->m_requestType);
		pExec->BindParam(23, (int)bizMsg->m_requestNumber);
		pExec->BindParam(24, (long)conf->VALID_TIME);
		pExec->BindParam(25, data->messageID.c_str());
		pExec->BindParam(26, (int)data->chargingPartyType);
		pExec->BindParam(27, data->SPID.c_str());
		pExec->BindParam(28, (int)data->serviceEnableType);
		pExec->BindParam(29, (int)data->chargingType);
		pExec->BindParam(30, data->productID.c_str());
		pExec->BindParam(31, data->productOfferID.c_str());
		pExec->BindParam(32, (int)data->serviceType);
		pExec->BindParam(33, data->contentID.c_str());
		pExec->BindParam(34, data->mediaType.c_str());
		pExec->BindParam(35, data->clientIP.c_str());
		pExec->BindParam(36, base->subscriptionType);
		pExec->BindParam(37, base->subscription.phone.c_str());
		pExec->BindParam(38, data->calling.phone.c_str());
		pExec->BindParam(39, data->called.phone.c_str());
		pExec->BindParam(40, "");

		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(41, value);

		if(86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else
		{
			if (data->calling.area)
			{
				sprintf(value, "0%d", data->calling.area);
			}
			else
			{
				value[0] = '\0';
			}
		}
		pExec->BindParam(42, value);

		if(86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		else
		{
			if (data->called.area)
			{
				sprintf(value, "0%d", data->called.area);
			}
			else
			{
				value[0] = '\0';
			}
		}
		pExec->BindParam(43, value);
		pExec->BindParam(44, "");
		pExec->BindParam(45, data->callingNumber.c_str());
		pExec->BindParam(46, data->calledNumber.c_str());
		pExec->BindParam(47, base->subscription.carriers);
		pExec->BindParam(48, data->calling.carriers);
		pExec->BindParam(49, data->called.carriers);
		pExec->BindParam(50, 1);

		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(51, value);

		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(52, value);

		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(53, value);
		pExec->BindParam(54, "");
		pExec->BindParam(55, (long)conf->QUOTA_CONSUMPTION_TIME);
		pExec->BindParam(56, (long)conf->VOLUME_QUOTA_THRESHOLD);
		pExec->BindParam(57, (long)conf->TIME_QUOTA_THRESHOLD);
		pExec->BindParam(58, (long)conf->QUOTA_HOLDING_TIME);
		pExec->BindParam(59, base->subUnified.c_str());
		pExec->BindParam(60, data->callingUnified.c_str());
		pExec->BindParam(61, data->calledUnified.c_str());
		pExec->BindParam(62, userInfo->aocType);
		pExec->BindParam(63, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(64, traceNumOnff);

		pExec->BindParam(65, "");
		pExec->BindParam(66, "");
		pExec->BindParam(67, "");
		pExec->BindParam(68, "");
		pExec->BindParam(69, "");
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(70, bizMsg->billcycle);
		}
		else
			pExec->BindParam(70, (long)base->timestamp);
		pExec->BindParam(71, 1);//SM_INT_FREE_FLAG
		pExec->BindParam(72, (long)0);//SM_LNG_ALL_USU_MONEY
		pExec->BindParam(73, (long)0);//SM_LNG_ALL_USU_TIME
		pExec->BindParam(74, (long)0);//SM_LNG_ALL_USU_TOTAL_OCT
		pExec->BindParam(75, (long)0);//SM_LNG_ALL_USU_INPUT_OCT
		pExec->BindParam(76, (long)0);//SM_LNG_ALL_USU_OUTPUT_OCT
		pExec->BindParam(77, 2001);//SM_INT_RESULT_CODE
		pExec->BindParam(78, userInfo->ilatnid);
		pExec->BindParam(79, userInfo->lnAcctID);
		pExec->BindParam(80, bizMsg->m_szServiceContextIDStr);		
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
		pExec->BindParam(81, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(82, sCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
		
	}
	catch(UDBException& e)
	{
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	return RET_SUCCESS;
}


int DCUserBase::DealFreeDslInit(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;
	int traceNumOnff					= 0;
	long nextCCTime						= 0;
	long TUSU                           = 0;
	int day 							= 1;
	long minsec 						= 0;
	long lastsec 						= 0;
	long pre_dtime						= 0;
	long cur_dtime						= 0;
	long unitTotal						= 0;
	long unitInput						= 0;
	long unitOutput						= 0;
			
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO *subVisit                 = bizMsg->m_visit;
	SUserInfo *userInfo                 = bizMsg->m_userinfo;

	string sCreatFlag;
	if("2100" == bizMsg->m_payMentMode)
	{
		sCreatFlag = "3";
	}
	else
	{
		sCreatFlag = "2";
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());


	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 600;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone)); 
	if(bizMsg->m_requestType == 5)
	{
		if(&base->USU == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "missing MSCC", "");
			return SM_OCP_MISSING_AVP;
		}
		else
		{
			TUSU = base->USU.duration;
			unitTotal = base->USU.unitTotal;
	        unitInput = base->USU.unitInput;
	 		unitOutput = base->USU.unitOutput;
		}
		sprintf(value,"%lld",base->starttime);
		day = DCCommonIF::GetDateDiff(value,TUSU,minsec,lastsec)+1;
	}
	for(int i=0;i<day;i++)
	{
		if(1==day && bizMsg->m_requestType == 5)
		{
			//R603   本次计费请求开始时间
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" , cur_dtime);

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);
		
		}
		else if(0==i)
		{
		
			//R603   本次计费请求开始时间
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" , cur_dtime);

			TUSU = minsec;	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);
		}
		else if(i==day-1)
		{
		
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" , cur_dtime);

			TUSU = lastsec;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);		

		}
		else 
		{
		
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "pre_dtime[%ld]" , pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cur_dtime[%ld]" ,cur_dtime);

			TUSU = 24*3600;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "USU[%ld]", TUSU);		
		
		}
		
		UDBSQL* pExec = m_dbm->GetSQL(DSL_InsertSession_Free);
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_sessionID);
			pExec->BindParam(2, base->originHost);
			pExec->BindParam(3, (int)bizMsg->m_requestNumber);
			pExec->BindParam(4, data->callingNumber.c_str());
			pExec->BindParam(5, data->calledNumber.c_str());
			pExec->BindParam(6, base->usename.c_str());
			pExec->BindParam(7, data->productSpecID.c_str());
			pExec->BindParam(8, data->NASIP.c_str());
			pExec->BindParam(9, data->frameIP.c_str());
			pExec->BindParam(10, data->userNodeID.c_str());
			pExec->BindParam(11, SM_SESSION_INITIAL_CODE);
			pExec->BindParam(12, TORB_ACTION);
			pExec->BindParam(13, nextCCTime);//SM_LNG_TIME_TO_NEXT_CCR
			pExec->BindParam(14, userInfo->aocType);
			pExec->BindParam(15, (int)base->subscriptionType);
			pExec->BindParam(16, base->subscription.phone.c_str());

			sprintf(value, "0%d", base->subscription.area);
			pExec->BindParam(17, value);

			pExec->BindParam(18, base->subscription.carriers);
			pExec->BindParam(19, base->subscription.country);
			pExec->BindParam(20, data->callingNumber.c_str());
			pExec->BindParam(21, data->calledNumber.c_str());
			pExec->BindParam(22, bizMsg->m_roamtype);
			if(bizMsg->m_requestType == 5)
			{
				pExec->BindParam(23, pre_dtime);//RE_LNG_CALL_START_TIME
				pExec->BindParam(24, cur_dtime);//RE_LNG_CURRENT_CCR_TIME
			}
			else
			{
				pExec->BindParam(23, (long)bizMsg->timestampCCR);
				pExec->BindParam(24, (long)bizMsg->timestampCCR);
			}
			pExec->BindParam(25, SM_CDR_SEQ_NUM);
			pExec->BindParam(26, 1);
			pExec->BindParam(27, SM_CDR_VERSION);
			pExec->BindParam(28, SM_CDR_TICKETTYPE);
			pExec->BindParam(29, timeStamp);
			pExec->BindParam(30, base->topology);
			pExec->BindParam(31, 0);
			pExec->BindParam(32, 0);
			pExec->BindParam(33, SERVICES_CENARIOUS_DSL);
			pExec->BindParam(34, base->subUnified.c_str());
			pExec->BindParam(35, data->callingNumber.c_str());
			pExec->BindParam(36, data->calledNumber.c_str());
			pExec->BindParam(37, userInfo->servID);
			pExec->BindParam(38, userInfo->custID);
			pExec->BindParam(39, MASTER_PRODUCTID_CDMA);

			sprintf(value, "0%d", subVisit->area);
			pExec->BindParam(40, value);
			pExec->BindParam(41, base->routeRecord);

			pExec->BindParam(42, traceNumOnff);
			pExec->BindParam(43, (long)bizMsg->m_serial);
			if(bizMsg->m_requestType == 5)
			{
				pExec->BindParam(44, bizMsg->billcycle);//RE_LNG_SYS_CCR_TIME
			}
			else
				pExec->BindParam(44, (long)base->timestamp);
			pExec->BindParam(45, 1);//SM_INT_FREE_FLAG
			pExec->BindParam(46, TUSU);//SM_LNG_ALL_USU_TIME
			pExec->BindParam(47, unitTotal);//OCP_LNG_USU_TOTAL_OCT
			pExec->BindParam(48, unitInput);//OCP_LNG_USU_INPUT_OCT
			pExec->BindParam(49, unitOutput);//OCP_LNG_USU_OUTPUT_OCT
			pExec->BindParam(50, 2001);//SM_INT_RESULT_CODE
			pExec->BindParam(51, userInfo->ilatnid);
			pExec->BindParam(52, userInfo->lnAcctID);
			pExec->BindParam(53, bizMsg->m_szServiceContextIDStr);			
			bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
            pExec->BindParam(54, bizMsg->m_sBatchId.c_str());
			pExec->BindParam(55, bizMsg->addressIpv6);			
			pExec->BindParam(56, sCreatFlag);
			pExec->Execute();
			pExec->Connection()->Commit();
			
		}
		catch(UDBException& e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}
	return RET_SUCCESS;
}


int DCUserBase::DealFreeSmsEvent(SCCRBase* base, SCCRSMS* data, STBizMsg* bizMsg,int RequestAction)
{
	int ret 								= RET_SUCCESS;
	int resultCode 						= 0;
	int iPayFlag 							= 1;
	int traceNumOnff						= 0;
	long nextCCTime						= 0;

	char value[BIZ_TEMP_LEN_256] 			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	char subStr[BIZ_TEMP_LEN_32]		= {0};
	SUserInfo *userInfo = bizMsg->m_userinfo;
	
	string sCreatFlag;
	if("2100" == bizMsg->m_payMentMode)
	{
		sCreatFlag = "3";
	}
	else
	{
		sCreatFlag = "2";
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "payMentMode[%s] CreatFlag[%s]", bizMsg->m_payMentMode.c_str(), sCreatFlag.c_str());

	int debitflag = m_psmpara->GetP2PSMSPara()->debitflag;
	if( (1 == debitflag) && (SM_REQUESTED_ACTION_REFUND == base->requestAction))
	{
		
		UDBSQL* pQuery = m_dbm->GetSQL(SMS_GetOcsSmDebitInfo);
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, data->SMID.c_str());
			pQuery->Execute();
			if(pQuery->Next())
			{
				pQuery->GetValue(1, subStr);
				base->subUnified = subStr;
				//CDR消息
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "subscriptionNumber[%s]", subStr);

				pQuery->GetValue(2, subStr);
				//strcpy(data->callingUnified.c_str(), subStr);	//CDR消息
				data->callingUnified = subStr;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "callingNumber[%s]", subStr);

				pQuery->GetValue(3,	subStr);
				//strcpy(data->calledUnified.c_str(), subStr);		//CDR消息
				data->calledUnified = subStr;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "calledNumber[%s]", subStr);

			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "default info ", "");
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "nextCCTime[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	//计算计费方类型
	if(!strcasecmp(base->subscription.phone.c_str(), data->calling.phone.c_str() ))
	{
		iPayFlag = 1;
	}
	else if(!strcasecmp(base->subscription.phone.c_str(), data->called.phone.c_str() ))
	{
		iPayFlag = 2;
	}
	else
	{
		iPayFlag = 3;
	}

	if((!data->called.area) && data->called.country == 86)
	{
		data->called.area = m_psmpara->GetCommonPara()->currentProvCode;
		data->called.area = m_psmpara->GetCommonPara()->currentAreaCode;
	}
	

	//插入会话表

	UDBSQL* pExec = m_dbm->GetSQL(SMS_InsertSession_Free);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		/*
			"OCP_STR_SESSION_ID,"1
			"OCP_INT_REQ_NBR,"2
			"OCP_STR_ORIGIN_HOST,"3
			"OCP_STR_MSG_ID,"4
			"OCP_STR_SMSC_ADDRESS,"5
			"OCP_INT_MSG_LENGTH,"6
			"OCP_STR_ORIGIN_CALLING_NBR,"7
			"OCP_STR_ORIGIN_CALLED_NBR,"8
			"SM_LNG_SERIAL,"9
			"SM_INT_REQ_TYPE,"10
		*/
		pExec->BindParam(1, bizMsg->m_sessionID );
		pExec->BindParam(2, (int)bizMsg->m_requestNumber);
		pExec->BindParam(3, base->originHost.c_str());
		pExec->BindParam(4, data->SMID.c_str());
		pExec->BindParam(5, data->SMSCAddress.c_str());
		pExec->BindParam(6, data->SMLength );
		pExec->BindParam(7, data->callingNumber.c_str());
		pExec->BindParam(8, data->calledNumber.c_str());
		pExec->BindParam(9, (long)bizMsg->m_serial);
		pExec->BindParam(10, (int)bizMsg->m_requestType);

		/*
			"SM_INT_SESSION_STATUS,"11
			"SM_LNG_TIME_TO_NEXT_CCR,"12
			"RE_LNG_CURRENT_CCR_TIME,"13
			"RE_INT_PAY_FLAG,"14
			"RE_STR_SUB_NBR,"15
			"RE_STR_SUB_AREA,"16
			"RE_INT_SUB_OPERATOR,"17
			"RE_INT_SUB_COUNTRY,"18
			"RE_STR_OA_SUB_NBR,"19
			"RE_STR_OA_SUB_AREA,"20
		*/
		pExec->BindParam(11, TORB_ACTION);
		pExec->BindParam(12, (long)nextCCTime);
		pExec->BindParam(13, (long)bizMsg->timestampCCR);
		pExec->BindParam(14, iPayFlag);
		pExec->BindParam(15, base->subscription.phone.c_str());
		value[0] = '\0';
		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
		}
		pExec->BindParam(16, value);
		pExec->BindParam(17, base->subscription.carriers);
		value[0] = '\0';
		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
		}
		pExec->BindParam(18, value);
		
		pExec->BindParam(19, data->calling.phone.c_str());
		value[0] = '\0';
		if (data->calling.area)
		{
			sprintf(value, "0%d", data->calling.area);
		}
		pExec->BindParam(20, value);
		/*
			"RE_INT_OA_SUB_OPERATOR,"21
			"RE_INT_OA_SUB_COUNTRY,"22
			"RE_STR_DA_SUB_NBR,"23
			"RE_STR_DA_SUB_AREA,"24
			"RE_INT_DA_SUB_OPERATOR,"25
			"RE_INT_DA_SUB_COUNTRY"26
			"CDR_PUB_INT_VERSION,"27
			"CDR_PUB_INT_TICKETTYPE,"28
			"CDR_PUB_STR_HOSTID,"29
			"CDR_PUB_INT_CORRELATIONID,"30
		*/

		pExec->BindParam(21, data->calling.carriers);
		value[0] = '\0';
		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		pExec->BindParam(22, value);
		
		pExec->BindParam(23, data->called.phone.c_str());
		value[0] = '\0';
		if (data->called.area)
		{
			sprintf(value, "0%d", data->called.area);
		}
		pExec->BindParam(24, value);
		pExec->BindParam(25, data->called.carriers);
		value[0] = '\0';
		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		pExec->BindParam(26, value);
		pExec->BindParam(27, SM_CDR_VERSION);
		pExec->BindParam(28, SM_CDR_TICKETTYPE);
		pExec->BindParam(29, base->topology);
		pExec->BindParam(30, 0);
		/*
			"CDR_PUB_INT_TICKETSEQUENCEID,"31
			"CDR_PUB_INT_SERVICESCENARIOUS,"32
			"CDR_PUB_LNG_SERVID,"33
			"CDR_PUB_LNG_CUSTID,"34
			"CDR_PUB_STR_MASTERPRODUCTID,"35
			"SM_CDR_SEQ_NUM,"36
			"CDR_PUB_STR_CHARGED_PARTY,"37
			"CDR_PUB_STR_CALLING_PARTY,"38
			"CDR_PUB_STR_CALLED_PARTY "39
		*/
		pExec->BindParam(31, 0);
		pExec->BindParam(32, SERVICES_CENARIOUS_SMS);
		pExec->BindParam(33, userInfo->servID);
		pExec->BindParam(34, userInfo->custID);
		pExec->BindParam(35, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(36, -1);
		pExec->BindParam(37, base->subUnified.c_str());
		pExec->BindParam(38, data->callingUnified.c_str());
		pExec->BindParam(39, data->calledUnified.c_str());
		pExec->BindParam(40, userInfo->aocType);
		pExec->BindParam(41, base->routeRecord);
		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone)); 
		pExec->BindParam(42, traceNumOnff);
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(43, bizMsg->billcycle);
		}
		else
			pExec->BindParam(43, (long)base->timestamp);
		pExec->BindParam(44, 2001);
		value[0] = '\0';
		pExec->BindParam(45, value);
		pExec->BindParam(46, value);
		pExec->BindParam(47, userInfo->ilatnid);
		pExec->BindParam(48, userInfo->lnAcctID);
		pExec->BindParam(49, bizMsg->m_szServiceContextIDStr);		
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_psmpara->GetCommonPara()->iBatchIdTime, 1);
		pExec->BindParam(50, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(51, sCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();

	}
	catch(UDBException& e)
	{
		pExec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "insert ok ");
	
	return RET_SUCCESS;
}

int DCUserBase::DealFreeDealSession(STBizMsg* bizMsg)
{
	char value[128] = {0};
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "delete session", "");
	if(bizMsg->m_serviceContextID == DATA || bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == PGW)
	{
		sprintf(value, "%s%%", bizMsg->m_sessionID);
	}
	
	UDBSQL* pDelete = NULL;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pDelete = m_dbm->GetSQL(Voice_DeleteSession);
			}
			break;
		case SMS:
			{
				pDelete = m_dbm->GetSQL(SMS_DeleteSession);
			}
			break;
		case DATA:
		case CCG:
			{				
				pDelete = m_dbm->GetSQL(DATA_DeleteSessionStoreRG);
			}
			break;
		case PGW:
			{				
				pDelete = m_dbm->GetSQL(PGW_CDR_DeleteSession);
			}
			break;
        case DATA_5G:
            {
				//删除子会话会话
				pDelete = m_dbm->GetSQL(_5G_DeleteChildSession); //d_5g_childsession
				sprintf(value, "%s", bizMsg->m_childsessionID);
			}
            break;
		case ISMP:
		case HRS:
			{				
				pDelete = m_dbm->GetSQL(ISMP_DeleteSession);
			}
			break;
		case DSL:
			{				
				pDelete = m_dbm->GetSQL(DSL_DeleteSession);

			}
			break;
		default:
			{
				return RET_ERROR;
			}
			break;
	}
	try
	{
		pDelete->DivTable(bizMsg->m_sessionID);
		pDelete->UnBindParam();
		pDelete->BindParam(1, value);	
		pDelete->Execute();
		pDelete->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "delete SESSION_STORE service type[%d]", bizMsg->m_serviceContextID);
	}
	catch(UDBException& e)
	{
		pDelete->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	return RET_SUCCESS;
}

int DCUserBase::BlackNumber(const SPhone& Phone, STBizMsg* bizMsg, const SPhone* calling, const SPhone* called)
{
	char value[BIZ_TEMP_LEN_32] = {0};
	int forbiddenflag = 0;
	int blackflag = 0;
	SCommonPara* commonPara = m_psmpara->GetCommonPara();
	if(commonPara)
	{
		switch(bizMsg->m_serviceContextID)
		{
			case VOICE:
				{
					if(commonPara->INForbindenSwitch)
					{
						blackflag = 1;
					}
				}
				break;
			case SMS:
				{
					if(commonPara->P2PSMSForbindenSwitch)
					{
						blackflag = 1;
					}
				}
				break;
			case DATA:
			case CCG:
				{
					if(commonPara->PSForbindenSwitch)
					{
						blackflag = 1;
					}
				}
				break;
			case ISMP:
			case HRS:
				{
					if(commonPara->ISMPForbindenSwitch)
					{
						blackflag = 1;
					}
				}
				break;
			case DSL:
				{
					if(commonPara->DSLForbindenSwitch)
					{
						blackflag = 1;
					}
				}
				break;
			default:
				{
					return 0;
				}
		}
	}

	if(blackflag)
	{		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "serviceType[%d], networkType[%d], area[%d], phone[%s], calling area[%d], calling phone[%s]", bizMsg->m_serviceContextID, Phone.networkType, Phone.area, Phone.phone.c_str(), calling->area, calling->phone.c_str());
		UDBSQL* pQuery = m_dbm->GetSQL(COM_FORBID_SELECT_SM_FORBIDDEN);		
		try
		{
			pQuery->UnBindParam();

			sprintf(value, "%d", bizMsg->m_serviceContextID);
			pQuery->BindParam(1, value);
			
			sprintf(value, "%d", Phone.networkType);
			pQuery->BindParam(2, value);
				
			pQuery->Execute();
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "go to query",  "");	
			while(pQuery->Next())
			{
				char szAccnbr[BIZ_TEMP_LEN_32] = {0};
				char szForbidGroupType[2] ={0};

				char bill_latn_code[16]={0};
				char billing_accr[32]={0};
				char dest_latn_code[16]={0};
				char dest_accr[32]={0};

				char billing_area[16]={0};
				char calling_area[16]={0};
			
				sprintf(billing_area, "0%d", Phone.area);
				sprintf(calling_area, "0%d", calling->area);
				int nPos = 0;
				
							
				//禁拨组内号码与被叫匹配
				pQuery->GetValue(2, szAccnbr);//ACCNBR
				string strAccnbr = szAccnbr;

				//禁拨组类型
				pQuery->GetValue(3, szForbidGroupType);//FORBIDDEN_GROUP_TYPE

				//计费latncode
				pQuery->GetValue(4, bill_latn_code);

				//计费billingaccr
				pQuery->GetValue(5, billing_accr);

				//dest_latn_code
				pQuery->GetValue(6, dest_latn_code);

				//dest_accr
				pQuery->GetValue(7, dest_accr);
				DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", 
					"forbidden accnbr[%s],"
					"forbidden_group_type[%s],"
					"billing latn code[%s]，"
					"billing accnbr[%s],"
					"dest latn code[%s],"
					"dest accnbr[%s]", 
				szAccnbr, szForbidGroupType,bill_latn_code, billing_accr,dest_latn_code,dest_accr);

				if((0==strcmp(bill_latn_code,"#")||0==strcmp(bill_latn_code,billing_area))&&(0==strcmp(billing_accr,"#")||0==strcmp(billing_accr,Phone.phone.c_str()))&&(0==strcmp(dest_latn_code,"#")||0==strcmp(dest_latn_code,calling_area))&&(0==strcmp(dest_accr,"#")||0==strcmp(dest_accr,calling->phone.c_str())))
				{
					//目的禁拨组号
					pQuery->GetValue(1, value);//Forbidden_Group_Code
					DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "forbidden_group_code[%s]",  value);
					if(!strcmp("#", value))
					{
						DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "the phone [%s] have a forbidden rule",  Phone.phone.c_str());
						forbiddenflag = 1;
						continue;
					}
					
					//禁播白名单
					if(!strcasecmp((const char *)szForbidGroupType, "W"))
					{
						if(!strcmp(called->phone.c_str(), szAccnbr))//白名单全匹配
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "frobidden white:accnbr[%s] all mathched", szAccnbr);
							return 0;
						}

						nPos = strAccnbr.find("%", 0);
						if(!strncmp(called->phone.c_str(), strAccnbr.c_str(),nPos) && (0 != nPos) )//白名单模糊匹配
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "frobidden white:accnbr[%s] part mathched",  szAccnbr);
							return 0;
						}
					}

					
					//禁播黑名单
					if(!strcasecmp((const char *)szForbidGroupType, "B"))
					{
						if(!strcmp(called->phone.c_str(), szAccnbr))//黑名单全匹配
						{
							DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "frobidden black:accnbr[%s] all mathched", szAccnbr);
							forbiddenflag = 1;
							continue;
						}

						nPos = strAccnbr.find("%", 0);
						if(!strncmp(called->phone.c_str(),  strAccnbr.c_str(),nPos) && (0 != nPos))//黑名单模糊匹配
						{
							DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "frobidden black:accnbr[%s] part mathched", szAccnbr);
							forbiddenflag = 1;
							continue;
						}
					}
				}
				
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","q_sm_forbidden exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}
	
	return forbiddenflag;
}

int DCUserBase::GetAuthUser(SUserInfo* userInfo, STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_32]	= {0};
	int ret = 0;
	int flag = 0;		
	UDBSQL* pQuery = m_dbm->GetSQL(COM_USER_SELECT_TB_IF_REALNAME);
	try
	{
		pQuery->UnBindParam();
		pQuery->BindParam(1, userInfo->servID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			flag = atoi(value);
			if(1 == flag)
			{
				ret = 0;	
			}
			else
			{
				ret = 1;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "userFlag[%d]", flag);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"",  "not find prdInstID[%ld]", userInfo->servID);
			ret = -1;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","Get IF_REALNAME exception[%s],sql[%s],errcode[%d] ",e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return ret;
}

int DCUserBase::GetIMSI(SUserInfo* userInfo, STBizMsg* bizMsg)
{	
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;	
	
	char value[8] = {0};

	//根据用户号码在TB_NBR_LATN_REL中查询实际的归属区号
	UDBSQL *pQuery = dbm->GetSQL(COM_USER_SELECT_IMSI);
	try
	{	
		pQuery->UnBindParam();
		pQuery->BindParam(1, userInfo->servID);
		pQuery->BindParam(2, userInfo->ilatnid);
		pQuery->Execute();
		while(pQuery->Next())
		{
			//pQuery->GetValue(1, value);			
			//if (strcmp(value,"23")== 0) //IMSI的属性ID是23
			{
				pQuery->GetValue(2, userInfo->IMSI);			
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "IMSI[%s]", userInfo->IMSI);

				return 0 ;
			}			
		}		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "not found IMSI by servID[%ld][%d]",userInfo->servID, userInfo->ilatnid);
		
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	
	return 0;
}

int DCUserBase::GetAreaCode(SPhone & phone, STBizMsg * bizMsg)
{
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;	
	if(!m_smpara->GetCutArea(phone.area) || !m_smpara->GetCommonPara()->iLatnRelQueryControl)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "NOAreaCode[%d]", phone.area);
		return 0;
	}
	char value[8] = {0};

	//根据用户号码在TB_NBR_LATN_REL中查询实际的归属区号
	UDBSQL *pQuery = dbm->GetSQL(COM_USER_SELECT_TB_NBR_LATN_REL);
	try
	{	
		pQuery->UnBindParam();
		pQuery->BindParam(1, phone.phone);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			phone.area = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "real area[%d]", phone.area);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "not found area by phone[%s]", phone.phone.c_str());
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	
	return 0;
}

int DCUserBase::sendCCA(STBizMsg* bizMsg)
{
	char serviceFlowId[48];   //Service-Flow-Id
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	serviceFlowId[0] = 'l';
	DCCommonIF::GetServiceFlowID(serviceFlowId);
	cca.sessionID  =  bizMsg->m_sessionID;	
	cca.ServiceContextID  = bizMsg->m_serviceContextID;
	cca.resultCode = bizMsg->m_resultcode;
	if(bizMsg->m_resultcode != 2001)
		cca.msgType = 99;
	else
		cca.msgType = 1;
	cca.serial     = bizMsg->m_serial;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.ServiceFlowID = serviceFlowId;

       if(cca.ServiceContextID == VOICE || cca.ServiceContextID == DSL || cca.ServiceContextID == ISMP)
        {
                cca.GSUAns.gUnit = RB_UNIT_CODE_SECOND;
                cca.GSUAns.duration = 300;
        }
    else if(cca.ServiceContextID == DATA_5G || cca.ServiceContextID == PGW || cca.ServiceContextID == DATA || cca.ServiceContextID == CCG)
	{
		SCCRBase * base		= (SCCRBase*)bizMsg->m_base;
		if(base != NULL)
		{
			for(int i = 0;i<(base->MSCC).size();i++)
			{
				ocs::DATAUSU USU;
				USU.ResultCode = 2001;
				USU.ratinggroup = (base->MSCC)[i].ratingGroup;
				USU.ProductOfferId = (base->MSCC)[i].ProductOfferId;
                            USU.Unit = RB_UNIT_CODE_TOTAL_BYTES;
                            USU.Unittotal = 20*1024*1024L;
                            USU.ValidityTime = 3600;
                            USU.QuotaHoldingTime = 1800;
                            USU.TimeQuotaThreshold = 60;
                            USU.VolumeQuotaThreshold = 1024;
				cca.MSCC.push_back(USU);
			}
		}
	}
    
	//打印cca内容
	//m_print.clear();
	//m_print.print(uhd);
	//m_print.print(cca);
	DCSeriaPrinter p;
	p.print(uhd);
	p.print(cca);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CCA[%s]", p.data());
	
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "", "encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	bizMsg->m_RARFlag = RET_OVER;
	
/*	DCMqProduceServer* producer = bizMsg->m_producer;

	string sendmsg = HexEncode(m_en.data(),m_en.size());

	int topicnum = 0;
	if(CCG == bizMsg->m_serviceContextID || DATA == bizMsg->m_serviceContextID || PGW == bizMsg->m_serviceContextID)
	{
		topicnum = 3;
	}
	else
	{
		topicnum = bizMsg->m_serviceContextID;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID, "TopicNum[%d]", topicnum);

        struct timeval tmv;
       char buf[20];
 
       // 头部加固定16位微妙时间戳
       gettimeofday(&tmv, NULL);
       sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
       sendmsg.insert(0, buf);
    
	int ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), bizMsg->m_anstopic);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Produce CCADATA failed, ret=%d\n", ret);	
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA to MQ,msglen:%d",m_en.size());
	}		*/
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA data ok[%s]",bizMsg->data.c_str());
	return RET_OVER;
	
}


int DCUserBase::IsRollUser(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg)
{
	int ret = 0;
	return 0;
	/*
	if (!userInfo)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Error User Info Null Pointer");
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	// ��ѯDMDB�û��������ر�
	char szRollSql[256] = {0};
	sprintf(szRollSql, "%s|%d", _5G_RollUser, userInfo->ilatnid);
	UDBSQL* query = m_dbm->GetSQL(szRollSql);
	if (!query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "not find sqlcode[%s]", szRollSql);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	try
	{
		query->DivTable(bizMsg->m_sessionID);
		query->UnBindParam();
		query->BindParam(1, phone.phone.c_str());             // �û�����
		query->BindParam(2, bizMsg->m_operListId.c_str());    // OperListId
		query->Execute();
		if (query->Next())
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "phone[%s], operListId[%s], latnid[%d] roll user.",
				     phone.phone.c_str(), bizMsg->m_operListId.c_str(), userInfo->ilatnid);
			ret = 1;
		}
		else
		{
			ret = 0;
		}
	}
	catch(UDBException&e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "dbept[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	*/

	 return ret;
}
