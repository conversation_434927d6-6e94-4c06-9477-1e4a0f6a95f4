#include "DCEptTimeoutREA.h"
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "DCOBJSet.h"
#include "DCBizMsgDef.h"
#include "DCRFData.h"

int DCEptTimeoutREA::Work(void *data)
{
	STBizMsg* bizMsg = (STBizMsg*)data;

	//SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), bizMsg->m_sessionID, "service type[%u]", bizMsg->m_serviceContextID);

	//PERF LOG
	//bizMsg->m_type = MSG_TYPE_TIMEOUT_REA;
	//bizMsg->m_perf.GetTimeT3_B();

	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			return SwitchCommon(bizMsg);
			break;
		case SMS:
			return SwitchCommon(bizMsg);
			break;
		case DATA:
		case CCG:
		case PGW:
			return SwitchDATA(bizMsg);
			break;
		case DATA_5G:
			return Switch5G(bizMsg);
			break;
		case ISMP:
		case HRS:
			return SwitchCommon(bizMsg);
			break;
		case DSL:
			return SwitchCommon(bizMsg);
			break;
		default:
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "unknow service type[%d]", bizMsg->m_serviceContextID);
			break;
	}

	//PERF LOG
	//bizMsg->m_perf.GetTimeT3_E();

	return RET_SUCCESS;
}

int DCEptTimeoutREA::SwitchCommon(STBizMsg* bizMsg)
{
	int ret = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  bizMsg->m_sessionID, "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
				return ret;
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return RET_CDR;
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_EVENT_REFUND_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_EVENT_BALANCE_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "unknow request type", "");
				return RET_SUCCESS;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCEptTimeoutREA::SwitchVOICE(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCEptTimeoutREA::SwitchSMS(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCEptTimeoutREA::SwitchPGW(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCEptTimeoutREA::Switch5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				if(!bizMsg->m_anspara->GetPSPara()->nOfflineAllUsu)//0
				{
					SendTermRER(bizMsg);
				}
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "unknow request type");
			}
			break;
	}

	return RET_SUCCESS;
}


int DCEptTimeoutREA::SwitchDATA(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				if(!bizMsg->m_anspara->GetPSPara()->nOfflineAllUsu)//0
				{
					// SendReaTermRER(bizMsg);
					SendTermRER(bizMsg);
				}
				return RET_CDR;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return RET_CDR;
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "unknow request type");
			}
			break;
	}

	return RET_SUCCESS;
}

int DCEptTimeoutREA::SwitchISMP(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCEptTimeoutREA::SwitchDSL(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}
//
int DCEptTimeoutREA::init()
{
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	return 0;
}

int DCEptTimeoutREA::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;

	STBizMsg* bizMsg = pset->get<STBizMsg>();
	bizMsg->m_anspara = m_anspara;
	m_pMsendMsg =(std::multimap<string,string>*)bizMsg->m_pSendMsg;
	int ret = Work(bizMsg);
	return ret;

}

DYN_PLUGIN_CREATE(DCEptTimeoutREA, "FC_EPTTIMEOUTREA", "FC_EptTimeoutREA", "1.0.0")

