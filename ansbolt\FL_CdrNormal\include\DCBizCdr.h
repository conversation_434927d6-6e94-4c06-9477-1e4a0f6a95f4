/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdr.h
*Indentifier：
*
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F

*Finished：
*
*History:
********************************************/
#ifndef _DCBIZ_CDR_H_
#define _DCBIZ_CDR_H_
#include "DCBasePlugin.h"
#include "DCOBJSet.h"
#include "DCBizCdrNormal.h"
//#include "DCBizOLDCdrNormal.h"
#include "DCDBManer.h"

class DCBizCdr : public DCBasePlugin
{
	public:

		DCBizCdr(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)
			:DCBasePlugin(category,func,version)
		{
			//m_dbm = dbm();
		}

		~DCBizCdr()
		{

		}


	protected:
		virtual int init();
		virtual int process(void* input, void* output);
	private:
		int initsql();
		int oldinitsql();
		int getPayFalg(STBizMsg* bizMsg,int & nPayFlag);


	private:
		DCOBJSetPool* m_pool;
		DCBizCdrNormal *m_cdrnormal[7];
		//DCBizOLDCdrNormal *m_oldcdrnormal[6];
		DCAnsPara *m_anspara;

		DCDBManer *m_dbm;
		char m_Topic[64];
		char m_payflagTopic[64];
		char m_testTopic[64];
};

#endif
