#include "DCAnsDSL.h"
#include "DCLogMacro.h"
#include <sys/time.h>

using namespace ocs;

DCAnsDSL::DCAnsDSL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "", "");
}

DCAnsDSL::~DCAnsDSL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "", "");
}

int DCAnsDSL::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "null msg", "");
		return -1;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;
	
	//m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));
	
	//PERF LOG
	//bizMsg->m_perf.GetTimeT2_B();
	int ret = RET_SUCCESS;
	if(bizMsg->m_requestType == SM_SESSION_XDR_CODE)
		ret = XdrEvent(bizMsg);
	else
		ret = ComposeCCA(bizMsg);

	if(RET_CDR == ret || RET_SUCCESS == ret)
	{	

	}
	else
	{	
		bizMsg->m_resultcode = ret;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
		return ret;
	}

	//PERF LOG
	//bizMsg->m_perf.GetTimeT2_E();

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
	return ret;
}

int DCAnsDSL::ComposeCCA(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}
int DCAnsDSL::XdrEvent(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

