#include "DCRoamJudge.h"
#include "TSMPara.h"
#include "ErrorCode.h"
#include "func_sqlindex.h"
#include "OCPMsgDef.h"
#include <sys/time.h>
#include "DCLogMacro.h"
#include "DCCommonIF.h"

DCRoamJudge::DCRoamJudge()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

DCRoamJudge::~DCRoamJudge()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

int DCRoamJudge::RoamVoice(STBizMsg* bizMsg,ocs::SCCRBase* base,ocs::SCCRVOICE* data)
{
	int roamType = 0;
	int CDRcallType = bizMsg->m_cdrCallType;
	TSMPara * smpara = bizMsg->m_smpara;
	AREA_INFO *subVisit = bizMsg->m_visit;
	int longType = 0;
	char buftmp[128];
	if(CDRcallType == 1)
       {
             data->calling.area = base->subscription.area;
       }
       else if(CDRcallType == 2)
       {
             data->called.area = base->subscription.area;
       }

	if(SM_SESSION_INITIAL_CODE == bizMsg->m_requestType || bizMsg->m_requestType == 5)
	{
		switch(CDRcallType)
		{
			case 1:
				{
					if (0 == data->VoLTEFlag)
					{
						if(0 == smpara->GetINPara()->roamMSCorVLR)
						{
							strcpy(buftmp,data->MSC.c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "switch[0] msc roma", "");
							roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->callingLAI.c_str(), data->callingCellID.c_str(),CDRcallType);
							data->MSC = buftmp;
						}
						else
						{
							strcpy(buftmp,data->callingVLR.c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "switch[1] vlr roma", "");
							roamType = RoamMscVlr(bizMsg,base->subscription, data->called, subVisit, buftmp, data->callingLAI.c_str(), data->callingCellID.c_str(),CDRcallType);
							data->callingVLR = buftmp;
						}
					}
					else
					{
						int AreaNumber=atoi(data->callingAreaNumber.c_str());
						const AREA_INFO* pVisitorHome	= NULL;
						pVisitorHome = smpara->GetAreaInfo(AreaNumber);
						if(pVisitorHome)
						{
							subVisit->province = pVisitorHome->province;
							subVisit->area=AreaNumber;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "visit area[%d]", subVisit->area);
						}
						else
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "not find area in spz_city, area[%d]", AreaNumber);
						}

						if (data->calling.province != subVisit->province)//省际漫游
						{
							roamType = 4;
						}
						else
						{
							if(data->calling.area == subVisit->area)//非漫游
							{
								roamType = 0;
							}
							else//省内漫游
							{
								roamType = 1;
							}
						}
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "roamType[%d]", roamType);
					}

					if((!data->called.area) && data->called.country == 86)
					{
						if((6==roamType) || (9==roamType))
						{
							data->called.province = subVisit->area;
							data->called.country = subVisit->area;
							data->called.carriers = 7;
						}
						else
						{
							data->called.province = subVisit->province;
							data->called.country = data->calling.country;
							data->called.carriers = data->calling.carriers;
						}
						data->called.area = subVisit->area;
						strcpy(buftmp,data->calledUnified.c_str());
						DCCommonIF::UnifiedCallNum(data->called, buftmp);
						data->calledUnified = buftmp;
					}
					if((!data->calling.area) && data->calling.country == 86)
					{
						if((6==roamType) || (9==roamType))
						{
							data->calling.province = subVisit->area;
							data->calling.country = subVisit->area;
							data->calling.carriers = 7;
						}
						else
						{
							data->calling.province = subVisit->province;
							data->calling.country = data->called.country;
							data->calling.carriers = data->called.carriers;
						}
						data->calling.area = subVisit->area;
						strcpy(buftmp,data->calledUnified.c_str());
						DCCommonIF::UnifiedCallNum(data->calling, buftmp);
						data->calledUnified = buftmp;
					}

					longType = LongVOICE(bizMsg, data->calling, data->called, subVisit, roamType);

					//info 日志
					DCDATLOG("SM00005:%d%s%s%s%s%d", roamType, data->MSC.c_str(), data->callingVLR.c_str(), data->callingCellID.c_str(), data->callingLAI.c_str(), longType);
				}
			break;
			case 2:
				{
					if (0 == data->VoLTEFlag)
					{
						if(0 == smpara->GetINPara()->roamMSCorVLR)
						{
							strcpy(buftmp,data->MSC.c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "switch[0] msc roma", "");
							roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->calledLAI.c_str(), data->calledCellID.c_str(),CDRcallType);
							data->MSC = buftmp;
						}
						else
						{
							strcpy(buftmp,data->calledVLR.c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "switch[1] vlr roma", "");
							roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->calledLAI.c_str(), data->calledCellID.c_str(),CDRcallType);
							data->calledVLR = buftmp;
						}
					}
					else
					{
						int AreaNumber=atoi(data->calledAreaNumber.c_str());
						const AREA_INFO* pVisitorHome	= NULL;
						pVisitorHome= smpara->GetAreaInfo(AreaNumber);
						if(pVisitorHome)
						{
							subVisit->province = pVisitorHome->province;
							subVisit->area=AreaNumber;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "visit area[%d]", subVisit->area);
						}
						else
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "not find area in spz_city, area[%d]", AreaNumber);
						}

						if (data->called.province != subVisit->province)//省际漫游
						{
							roamType = 4;
						}
						else
						{
							if(data->called.area == subVisit->area)//非漫游
							{
								roamType = 0;
							}
							else//省内漫游
							{
								roamType = 1;
							}
						}
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "roamType[%d]", roamType);
					}
					if((!data->called.area) && data->called.country == 86)
					{
						if((6==roamType) || (9==roamType))
						{
							data->called.province = subVisit->area;
							data->called.country = subVisit->area;
							data->called.carriers = 7;
						}
						else
						{
							data->called.province = subVisit->province;
							data->called.country = data->calling.country;
							data->called.carriers = data->calling.carriers;
						}
						data->called.area = subVisit->area;
						strcpy(buftmp,data->calledUnified.c_str());
						DCCommonIF::UnifiedCallNum(data->called, buftmp);
						data->calledUnified = buftmp;
					}
					if((!data->calling.area) && data->calling.country == 86)
					{
						if((6==roamType) || (9==roamType))
						{
							data->calling.province = subVisit->area;
							data->calling.country = subVisit->area;
							data->calling.carriers = 7;
						}
						else
						{
							data->calling.province = subVisit->province;
							data->calling.country = data->called.country;
							data->calling.carriers = data->called.carriers;
						}
						data->calling.area = subVisit->area;
						strcpy(buftmp,data->calledUnified.c_str());
						DCCommonIF::UnifiedCallNum(data->calling, buftmp);
						data->calledUnified = buftmp;
					}
					longType = LongVOICE(bizMsg, data->calling, data->called, subVisit, roamType);

					//info 日志
					DCDATLOG("SM00005:%d%s%s%s%s%d", roamType, data->MSC.c_str(), data->calledVLR.c_str(), data->calledCellID.c_str(), data->calledLAI.c_str(), longType);
				}
			break;
			case 3:
				{
					if (0 == data->VoLTEFlag)
					{
						if(0 == smpara->GetINPara()->roamMSCorVLR)
						{
							strcpy(buftmp,data->MSC.c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "switch[0] msc roma", "");
							roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->callingLAI.c_str(), data->callingCellID.c_str(),CDRcallType);
							data->MSC = buftmp;
						}
						else
						{
							strcpy(buftmp,data->callingVLR.c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "switch[1] vlr roma", "");
							roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->callingLAI.c_str(), data->callingCellID.c_str(),CDRcallType);
							data->callingVLR = buftmp;
						}
					}
					else
					{
						int AreaNumber=atoi(data->callingAreaNumber.c_str());
						const AREA_INFO* pVisitorHome	= NULL;
						pVisitorHome= smpara->GetAreaInfo(AreaNumber);
						if(pVisitorHome)
						{
							subVisit->province = pVisitorHome->province;
							subVisit->area=AreaNumber;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "visit area[%d]", subVisit->area);
						}
						else
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "not find area in spz_city, area[%d]", AreaNumber);
						}

						if (base->subscription.province != subVisit->province)//省际漫游
						{
							roamType = 4;
						}
						else
						{
							if(base->subscription.area == subVisit->area)//非漫游
							{
								roamType = 0;
							}
							else//省内漫游
							{
								roamType = 1;
							}
						}
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "roamType[%d]", roamType);
					}

					if((!data->called.area) && data->called.country == 86)
					{
						if((6==roamType) || (9==roamType))
						{
							data->called.province = subVisit->area;
							data->called.country = subVisit->area;
							data->called.carriers = 7;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "set called.prov=visit.area[%d],called.country=visit.area[%d]", subVisit->area,subVisit->area);
						}
						else
						{
							data->called.province = subVisit->province;
							data->called.country = data->calling.country;
							data->called.carriers = data->calling.carriers;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "set called.prov=visit.prov[%d],called.country=calling.country[%d]", subVisit->province,data->calling.country);
						}
						data->called.area = subVisit->area;
						strcpy(buftmp,data->calledUnified.c_str());
						DCCommonIF::UnifiedCallNum(data->called, buftmp);
						data->calledUnified = buftmp;
					}
					if((!data->calling.area) && data->calling.country == 86)
					{
						if((6==roamType) || (9==roamType))
						{
							data->calling.province = subVisit->area;
							data->calling.country = subVisit->area;
							data->calling.carriers = 7;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "set calling.prov=visit.area[%d],calling.country=visit.area[%d]", subVisit->area,subVisit->area);
						}
						else
						{
							data->calling.province = subVisit->province;
							data->calling.country = data->called.country;
							data->calling.carriers = data->called.carriers;
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "set calling.prov=visit.prov[%d],calling.country=called.country[%d]", subVisit->province,data->called.country);
						}
						data->calling.area = subVisit->area;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "set calling.area=visit.area[%d]", subVisit->area);
						strcpy(buftmp,data->calledUnified.c_str());
						DCCommonIF::UnifiedCallNum(data->calling, buftmp);
						data->calledUnified = buftmp;
					}
					longType = LongVOICE(bizMsg, base->subscription, data->called, subVisit,roamType);

					//info 日志
					DCDATLOG("SM00005:%d%s%s%s%s%d", roamType, data->MSC.c_str(), data->callingVLR.c_str(), data->callingCellID.c_str(), data->callingLAI.c_str(), longType);
				}
			break;

			}
			bizMsg->m_longtype = longType;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "longType[%d], roamType[%d]", longType, roamType);
			if(roamType > 9)
			{
				return SM_OCP_UNABLE_TO_COMPLY;
			}
	}
	else
	{
		int nSwitchUpdateTermRoam = smpara->GetINPara()->initRoma;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "call type[%d],INIT_ROMAN_CALLED[%d]", CDRcallType, nSwitchUpdateTermRoam);
		if(2==CDRcallType && data && (1 == nSwitchUpdateTermRoam))//被叫流程做一下漫游判断
		{
			if (0 == data->VoLTEFlag)
			{
				//被叫流程TSDL号判断
				if((11 == strlen(data->calledVLR.c_str())) && ('1' == data->calledVLR[0]))
				{
					data->calledVLR[8]=0;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "tsdl calledVLR[%s]", data->calledVLR.c_str());
				}

				if(0 == smpara->GetINPara()->roamMSCorVLR)
				{
					strcpy(buftmp,data->MSC.c_str());
					roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->calledLAI.c_str(), data->calledCellID.c_str(),CDRcallType);
					data->MSC = buftmp;
				}
				else
				{
					strcpy(buftmp,data->calledVLR.c_str());
					roamType = RoamMscVlr(bizMsg, base->subscription, data->called, subVisit, buftmp, data->calledLAI.c_str(), data->calledCellID.c_str(),CDRcallType);
					data->calledLAI = buftmp;
				}
			}
			else
			{
				int AreaNumber=atoi(data->calledAreaNumber.c_str());
				const AREA_INFO* pVisitorHome	= NULL;
				pVisitorHome= smpara->GetAreaInfo(AreaNumber);
				if(pVisitorHome)
				{
					subVisit->province = pVisitorHome->province;
					subVisit->area=AreaNumber;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "visit area[%d]", subVisit->area);
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "not find area in spz_city, area[%d]", AreaNumber);
				}

				if (base->subscription.province != subVisit->province)//省际漫游
				{
					roamType = 4;
				}
				else
				{
					if(base->subscription.area == subVisit->area)//非漫游
					{
						roamType = 0;
					}
					else//省内漫游
					{
						roamType = 1;
					}
				}
			}
		}

		//info 日志
		DCDATLOG("SM00005:%d%s%s%s%s%d", roamType, data->MSC.c_str(), data->calledVLR.c_str(), data->calledCellID.c_str(), data->calledLAI.c_str(), longType);
	}

	bizMsg->m_roamtype= roamType;
	return 0;

}

int DCRoamJudge::RoamMscVlr(STBizMsg* bizMsg,const SPhone& home, const SPhone& called, AREA_INFO* visit, char* MSCorVLR, const char* LAC, const char* CELLID,int calltype)
{
	int roamType 	= 0;
	const AREA_INFO* pVisitorHome	= NULL;
	MSC_COUNTRY* pcountry = NULL;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "MSCorVLR[%s],calltype[%d]", MSCorVLR,calltype);

	if (MSCorVLR && (strlen(MSCorVLR) > 0))
	{
		MSC_INFO *mscinfo = NULL;

		//被叫流程特殊处理
		if(2 == calltype)
		{
			if(0==strncmp(MSCorVLR, "00861",5))//被叫国内漫游流程特殊处理:去掉前4位0086之后，保留从1开始的8位
			{
				//MSCorVLR+=4;
				memmove(MSCorVLR, MSCorVLR+4, strlen(MSCorVLR+4)+1);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Converted MSCorVLR[%s]", MSCorVLR);

				if((strlen(MSCorVLR)>8) && ('1' ==MSCorVLR[0]))
				{
					MSCorVLR[8]=0;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "cut calledVLR[%s]", MSCorVLR);
				}
			}
			else if(0==strncmp(MSCorVLR, "00",2))//被叫国际漫游的:TLDN变长处理，取SPR_MSC表里配置的最长的
			{
				int len = strlen(MSCorVLR);
				while(len)
				{
					mscinfo = smpara->GetMscVlrCodeInfo(MSCorVLR);
					if(NULL == mscinfo)
					{
						MSCorVLR[len] = 0;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "VLR[%s]", MSCorVLR);
						len --;
						continue;
					}
					else
					{
						strncpy(visit->szcarriers,mscinfo->carriers,sizeof(visit->szcarriers));
		 				visit->area = atoi(mscinfo->area_code);

						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "cut out VLR[%s]", MSCorVLR);
						if(0==strncmp(MSCorVLR, "00853",5)||0==strncmp(MSCorVLR, "00852",5)||0==strncmp(MSCorVLR, "00886",5))
						{
						 	roamType = 9;
						}
						else
						{
							// 国际漫游
							roamType = 6;
						}

                                          pcountry = smpara->GetMscCountryInfo(MSCorVLR);
						if (NULL != pcountry)
						{
                                                  string tmp = smpara->GetSectorId(pcountry->iccarrier_code, pcountry->country_code);
        						strcpy(visit->sector_id ,tmp.c_str());
        						if (strlen(visit->sector_id) == 0)
        						{
        							DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find sectorID");
        						}
                                            }

						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "roamtype[%d], sector_id[%s]", roamType, visit->sector_id);
						return roamType;
					}
				}
			}
		}


		//主叫流程或者被叫的省际漫游(00861的)
 		mscinfo = smpara->GetMscVlrCodeInfo(MSCorVLR);
 		if(mscinfo)
 		{
 			strncpy(visit->szcarriers,mscinfo->carriers,sizeof(visit->szcarriers));
 			visit->area = atoi(mscinfo->area_code);

 			if(0==strncmp(mscinfo->area_code, "00", 2))
 			{
 				if(1==calltype||3==calltype)//主叫流程
 				{
 					//港澳台
 					if(0==strcmp(mscinfo->area_code, "00853")||0==strcmp(mscinfo->area_code, "00852")||0==strcmp(mscinfo->area_code, "00886"))
 					{
 						roamType = 9;
 					}
 					else
					{
						roamType = 6;//其它国际漫游
 					}
                                   pcountry = smpara->GetMscCountryInfo(MSCorVLR);
                                   if (NULL != pcountry)
                                    {
                                           string tmp = smpara->GetSectorId(pcountry->iccarrier_code, pcountry->country_code);
        					strcpy(visit->sector_id ,tmp.c_str());
        					if (strlen(visit->sector_id) == 0)
        					{
        						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find sectorID");
        					}
                                    }

					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "roamtype[%d], sectorID[%s]", roamType, visit->sector_id);
 					return roamType;
 				}

 			}

 			pVisitorHome= smpara->GetAreaInfo(visit->area);
 			if(NULL != pVisitorHome)
 			{
 				visit->province = pVisitorHome->province;
 			}
 			else
 			{
 				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "no find area[0%d] in spz_city", visit->area);
 			}
 			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "MSC or VLR province[0%d], area[0%d]", visit->province, visit->area);

 			if (home.province != visit->province)//省际漫游
 			{
 				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "home.province[%d] != visit.province[%d],romatype[4]", home.province,visit->province);
 				roamType = 4;
 			}
 			else
 			{
 				if(home.area == visit->area)//非漫游
 				{
 					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "home.area = visit.area[%d],romatype[0]", visit->area);
 					roamType = 0;
 				}
 				else//省内漫游
 				{
 					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "home.area[%d] != visit.area[%d],romatype[1]", home.area,visit->area);
 					roamType = 1;
 				}
 			}
 		}



		if(NULL == mscinfo)//在交换机表中没找到
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "invalid MSC or VLR[%s]", MSCorVLR);
			visit->province = smpara->GetINPara()->defaultProv;
			visit->area = smpara->GetINPara()->defaultArea;
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "set default visit.province[%d],default visit.area[%d]", visit->province,visit->area);

			if (home.province != visit->province)//省际漫游
			{
				roamType = 4;
			}
			else
			{
				if(home.area == visit->area)//非漫游
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "home.area = default visit.area[%d],romatype[0]", home.area);
					roamType = 0;
				}
				else//省内漫游
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "home.area[%d] != default visit.area[%d],romatype[1]", home.area,visit->area);
					roamType = 1;
				}
			}
		}
	}
	else//交换机地址为空
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "missing MSC or VLR,set visit.prov=home.prov[%d],visit.area=home.area[%d],roma=0", home.province,home.area);
		visit->province = home.province;
		visit->area = home.area;
		roamType = 0;
	}

	if(roamType > 4)
	{
		return roamType;
	}

	//边漫判断
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "eage roam switch[%d],romatype[%d]", smpara->GetINPara()->roamCELLIDorLAC,roamType);
	if(4 == roamType)
	{
		roamType = RoamEdge(bizMsg, home, called, visit, LAC, CELLID ,MSCorVLR, 0);
	}
	else
	{
		roamType = RoamEdge(bizMsg, home, called, visit, LAC, CELLID ,MSCorVLR);
	}
	/*
	switch(smpara->GetINPara()->roamCELLIDorLAC)
	{
		case 0://省内漫游直接使用MSC判断
			{
				return roamType;
			}
			break;
		case 1://省内漫游使用CELLID判断
			{
				if(4 == roamType)
				{
					// roamType = RoamCELLID(bizMsg, home, called, visit, LAC, CELLID, 0);
					roamType = RoamEdge(bizMsg, home, called, visit, LAC, CELLID ,MSCorVLR, 0);
				}
				else
				{
					// roamType = RoamCELLID(bizMsg, home, called, visit, LAC, CELLID);
					roamType = RoamEdge(bizMsg, home, called, visit, LAC, CELLID ,MSCorVLR);
				}
			}
			break;
		case 2://省内漫游使用LAC判断
			{
				if(4 == roamType)
				{
					roamType = RoamLAC(bizMsg, home, called, visit, LAC, CELLID, 0);
				}
				else
				{
					roamType = RoamLAC(bizMsg, home, called, visit, LAC, CELLID);
				}
			}
			break;
		default:;
	}
	*/
	if(-1==roamType)//未找到省内漫游、省内边漫的类型，使用上传MSCorVLR的地区区号，省会区号
	{
		//省际漫游
		if (home.province != visit->province)
		{
			roamType = 4;
		}
		else
		{
			if(home.area == visit->area)//非漫游
			{
				roamType = 0;
			}
			else//省内漫游
			{
				roamType = 1;
			}
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);
	return roamType;
}

int DCRoamJudge::RoamLAC(STBizMsg* bizMsg,const SPhone& home,const SPhone& called, AREA_INFO* visit, const char* lac, const char* cellid,int provflag)
{
	int roamType = -1;//默认值是-1，
	const AREA_INFO* pVisitorHome= NULL;
	const char *info = NULL;

	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "edge roam switch[%d] lac [%s] ,cellid [%s]", smpara->GetINPara()->edgeRoamSwitch, lac, cellid);
	switch(smpara->GetINPara()->edgeRoamSwitch)
	{
		case 0:
			break;
		case 1:
			if(cellid && (strlen(cellid) > 0))
			{
				info = cellid;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing edge CELL ID", "");
			}
			break;
		case 2:
			if(lac && (strlen(lac) > 0))
			{
				info = lac;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing edge LAC", "");
			}
			break;
		default:;
	}

	if(info)
	{
		if(info == lac)
		{
			int ret = smpara->GetEdgeRoamLac(lac, (int)home.area,visit);
			if(-1 != ret)
			{
				return ret;
			}
		}
		else if(info == cellid)
		{
			int ret = smpara->GetEdgeRoamCellid(cellid, (int)home.area,visit);
			if(-1 != ret)
			{
				return ret;
			}
		}
	}
	if(lac && (strlen(lac) > 0) && provflag)
	{
		int area_code = 0;
		area_code = smpara->GetSprLAC(lac);
		if(-1 == area_code)
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "find no lac[%s]", lac);
		}
		else
		{
			visit->area = area_code;
			pVisitorHome= smpara->GetAreaInfo(visit->area);
			visit->province = pVisitorHome->province;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cellid province[0%d], area[0%d]", visit->province, visit->area);

			if(home.area == visit->area)//非漫游
			{
				roamType = 0;
			}
			else//省内漫游
			{
				roamType = 1;
			}
		}

	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing lac info", "");
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);

	return roamType;
}

int DCRoamJudge::RoamCELLID(STBizMsg* bizMsg, const SPhone& home,const SPhone& called, AREA_INFO* visit, const char* lac, const char* cellid, int provflag)
{
	int roamType = -1;//默认值是-1，
	const AREA_INFO* pVisitorHome= NULL;
	const char *info = NULL;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "edge roam switch[%d] lac[%s] ,cellid[%s]", smpara->GetINPara()->edgeRoamSwitch, lac, cellid);
	switch(smpara->GetINPara()->edgeRoamSwitch)
	{
		case 0:
			break;
		case 1:
			if(cellid && (strlen(cellid) > 0))
			{
				info = cellid;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing edge CELL ID", "");
			}
			break;
		case 2:
			if(lac && (strlen(lac) > 0))
			{
				info = lac;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing edge LAC", "");
			}
			break;
		default:
			break;
	}

	if(info)
	{
		if(info == lac)
		{
			int ret = smpara->GetEdgeRoamLac(lac, (int)home.area,visit);
			if(-1 != ret)
			{
				return ret;
			}
		}
		else if(info == cellid)
		{
			int ret = smpara->GetEdgeRoamCellid(cellid, (int)home.area,visit);
			if(-1 != ret)
			{
				return ret;
			}
		}

	}

	if(cellid && (strlen(cellid) > 0) && provflag)
	{
		int area_code = 0;
		area_code = smpara->GetSprCELL(cellid);
		if(-1 == area_code)
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "find no cellid[%s]", cellid);
		}
		else
		{
			visit->area = area_code;
			pVisitorHome= smpara->GetAreaInfo(visit->area);
			visit->province = pVisitorHome->province;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cellid province[0%d], area[0%d]", visit->province, visit->area);

			if(home.area == visit->area)//非漫游
			{
				roamType = 0;
			}
			else//省内漫游
			{
				roamType = 1;
			}
		}

	}
	else if(provflag)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing CELL ID", "");
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);
	return roamType;
}

int DCRoamJudge::RoamEdge(STBizMsg* bizMsg, const SPhone& home,const SPhone& called, AREA_INFO* visit, const char* lac, const char* cellid,  const char* MSCorVLR, int provflag)
{
	int roamType = -1;//默认值是-1，
	const AREA_INFO* pVisitorHome= NULL;
	const char *info = NULL;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
       char msidsn[32] = {0};
	strncpy(msidsn, home.phone.c_str(), home.phone.length() + 1);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "edge roam switch[%d] lac[%s] ,cellid[%s]", smpara->GetINPara()->edgeRoamSwitch, lac, cellid);

	int ret = smpara->GetRoamBorderType(msidsn, MSCorVLR , lac , cellid, home, visit);
	if(-1 != ret)
	{
		return ret;
	}

	switch(smpara->GetINPara()->edgeRoamSwitch)
	{
		case 0:
			break;
		case 1:
			if(cellid && (strlen(cellid) > 0) && provflag)
			{
				int area_code = 0;
				area_code = smpara->GetSprCELL(cellid);
				if(-1 == area_code)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "find no cellid[%s]", cellid);
				}
				else
				{
					visit->area = area_code;
					pVisitorHome= smpara->GetAreaInfo(visit->area);
					visit->province = pVisitorHome->province;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cellid province[0%d], area[0%d]", visit->province, visit->area);

					if(home.area == visit->area)//非漫游
					{
						roamType = 0;
					}
					else//省内漫游
					{
						roamType = 1;
					}
				}

			}
			else if(provflag)
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing CELL ID", "");
			}
			break;
		case 2:
			if(lac && (strlen(lac) > 0) && provflag)
			{
				int area_code = 0;
				area_code = smpara->GetSprLAC(lac);
				if(-1 == area_code)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "find no lac[%s]", lac);
				}
				else
				{
					visit->area = area_code;
					pVisitorHome= smpara->GetAreaInfo(visit->area);
					visit->province = pVisitorHome->province;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cellid province[0%d], area[0%d]", visit->province, visit->area);

					if(home.area == visit->area)//非漫游
					{
						roamType = 0;
					}
					else//省内漫游
					{
						roamType = 1;
					}
				}

			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  "", "missing lac info", "");
			}
			break;
		default:
			break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);
	return roamType;
}


//0-基本通话
// 2-省内长途
// 3-省际长途
// 4-国际长途
// 5-港奥台长途

int DCRoamJudge::LongVOICE(STBizMsg* bizMsg, const SPhone& calling, const SPhone& called, AREA_INFO* visit,int roamtype)
{
	int ret = 0;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "visit province[0%d], area[0%d], roamtype[%d]", visit->province, visit->area, roamtype);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "calling province[0%d], area[0%d]", calling.province, calling.area);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "called province[0%d], area[0%d]", called.province, called.area);
	//国际长途，区分港澳台漫游
	if(9 == roamtype)
	{
		if(86 != called.country)
		{
			if(visit->area== called.country)ret=0;//例如漫游到香港拨打香港电话
			else ret=4;//国际长途 例如漫游到香港拨打国外电话
		}
		else
		{
			ret = 5; //港澳台长途 例如漫游到香港拨打大陆电话
		}
	}
	else if(6==roamtype)
	{
		if(visit->area== called.country)ret=0;//例如漫游到美国拨打美国电话
		else ret=4;//国际 长途 例如漫游到美国拨打香港电话
	}
	else 	if (calling.country != called.country)
	{
		ret = 4;
		//港澳台
		if ((852 == called.country) || (853 == called.country) || (886 == called.country))
		{
			ret = 5;
		}
	}
	else
	{
		const AREA_INFO* pCalled= smpara->GetAreaInfo(called.area);
		if(pCalled)
		{
			if (visit->province != pCalled->province)//省际长途
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long type=3","");
				ret = 3;
			}
			else
			{
				//省内长途
				if (visit->area != pCalled->area)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long type=2","");
					ret = 2;
				}
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "not find called area[%d]",called.area);
		}

		//边界漫游处理

		// A(0951),B(0952) A和B同省，AB边界基站X归属A，覆盖区为B
		// 1> A在X拨打A：R5011=0，R5010=0
		// 2> A在X拨打B：R5011=0，R5010=2
		// 3> B在X拨打B：R5011=7，R5010=0
		// 4> B在X拨打A：R5011=7，R5010=2(原来判断为0)

		// C(0451),(0551) C和D不同省，CD边界基站Y归属C，临界区为D
   		// 5> C在Y拨打C：R5011=0，R5010=0
   		// 6> C在Y拨打D：R5011=0，R5010=3
		// 7> D在Y拨打D：R5011=8，R5010=0
		// 8> D在Y拨打C：R5011=8，R5010=3(原来判断为0)
		if(roamtype == 7)
		{
			//第4> 种情况
			//visit.area == pCalled->area 排除A在X拨打C的情况
			//calling.area != pCalled->area 排除A在X拨打A的情况
			if( (visit->area == pCalled->area) &&  (calling.area != pCalled->area) && (1 == smpara->GetINPara()->nRomaLongRelation))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long type=2:roma=7 && visit.area == called.area && calling.area != pCalled->area","");
				ret = 2;
			}
			else if(calling.area == pCalled->area)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long type=0:roma=7 && calling.area == Called->area","");
				ret = 0;
			}
		}


		if(roamtype == 8)
		{
			//第8> 种情况
			//visit.area == pCalled->area 排除D在Y拨打A的情况
			//calling.area != pCalled->area 排除D在Y拨打A的情况
			if( (visit->area == pCalled->area) &&  (calling.area != pCalled->area) && (1 == smpara->GetINPara()->nRomaLongRelation))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long type=3:roma=8 && visit.area == called.area && calling.area != pCalled->area","");
				ret = 3;
			}
			else if(calling.area == pCalled->area)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long type=0:roma=8 && calling.area == Called->area","");
				ret = 0;
			}
		}

	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "LongVOICE [%d]", ret);

	return ret;
}

int DCRoamJudge::RoamDATA(STBizMsg* bizMsg,ocs::SCCRBase* base,ocs::SCCRDATA* data)
{
    int ret 			= RET_SUCCESS;
	int roamType 	    = 0;
	AREA_INFO *subVisit = bizMsg->m_visit;
	if(bizMsg->visitAreaCode.size() > 0)
	{
		subVisit->area = atoi(bizMsg->visitAreaCode.c_str());
	}
	if(DATA==bizMsg->m_serviceContextID || CCG==bizMsg->m_serviceContextID)
	{
		roamType = RoamDataMsc(bizMsg, base->subscription,data->userMsc.c_str(),subVisit);
		if(roamType<0)
		{
			//省际漫游
			roamType = RoamDataPDSN(bizMsg, base->subscription, subVisit, data->PDSNAddress.c_str());
		}

		roamType = RoamDataCellMsc(bizMsg, data->userLocationInfo.c_str(), base->subscription,data->userCellid.c_str(),data->userMsc.c_str(),subVisit,roamType);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "roam type [%d]", roamType);

		//info 日志
		DCDATLOG("SM00006:%d%s%s%s%s", roamType, data->userLocationInfo.c_str(), data->userMsc.c_str(), data->PDSNAddress.c_str(), data->userCellid.c_str());
	}
	else if(PGW ==bizMsg->m_serviceContextID)
	{
		if(bizMsg->m_if4Gpp)
		{
			//用tac判断省际省内漫游
			roamType = RoamByTac(bizMsg, base->subscription,data->userLac.c_str(),subVisit);
		}
		else
		{
			//用msc判断省际省内漫游
			roamType = RoamDataMsc(bizMsg, base->subscription,data->userMsc.c_str(),subVisit);
		}
		if(roamType<0)
		{
			//省际漫游,lte用sgsn判断漫游
			roamType = RoamDataSGSN(bizMsg, base->subscription, subVisit, data->SGSNAddress.c_str(), data->userMsc.c_str());
		}

		roamType =RoamDataCellMsc(bizMsg, data->userLocationInfo.c_str(), base->subscription,data->userCellid.c_str(),data->userMsc.c_str(),subVisit,roamType);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "roam type [%d]", roamType);

		//info 日志
		DCDATLOG("SM00007:%d%s%s%s%s%s", roamType, data->userLocationInfo.c_str(), data->userMsc.c_str(), data->userLac.c_str(), data->SGSNAddress.c_str(), data->userCellid.c_str());
	}

   	bizMsg->m_roamtype = roamType;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);

   return roamType;
}

int DCRoamJudge::RoamDATA_5G(STBizMsg* bizMsg,ocs::SCCRBase* base,ocs::SCCR5GInfo* data)
{
    AREA_INFO *visit = bizMsg->m_visit;
	MSC_COUNTRY *pCountry = NULL;
	TSMPara *smParam = bizMsg->m_smpara;
	SGSN_INFO *pSGSNInfo = NULL;
	SUserInfo *userInfo = bizMsg->m_userinfo;
	string ratType;
	string strTAC;
	string nrCellID;
	if(base->MSCC.size()>0)
	{
		ratType = base->MSCC[0].rATType;
		strTAC = base->MSCC[0].userLocInfoMation.tac;
		nrCellID = base->MSCC[0].userLocInfoMation.nrCellId;
	}
	if(ratType.size() == 0)
	{
		ratType = data->rATType;
		strTAC = data->userLocationinfo.tac;
		nrCellID = data->userLocationinfo.nrCellId;
	}
	if(ratType == "EUTRA")
	{
		// chfproxy保证这里获取到的IPV4和IPV6至少有一个不为空
		string strIpAddr = data->ServingNFIDInfo.pduIPv4Address;
		if (strIpAddr.empty())
		{
			strIpAddr = data->ServingNFIDInfo.pduIPv6Address;
		}
		// 查询spr_sgsn表获取省号 q_spr_sgsn
		if (strIpAddr.find(":") !=  strIpAddr.npos)
		{
			pSGSNInfo = smParam->GetSprSGSN_IPV6(strIpAddr.c_str());
		}
		else
		{
			size_t iFind = strIpAddr.rfind(".");
			int i = 0;
			//查两次
			while (iFind != strIpAddr.npos && pSGSNInfo == NULL && i < 2)
			{
				strIpAddr = strIpAddr.substr(0, iFind);
				pSGSNInfo = smParam->GetSprSGSN(strIpAddr.c_str());
				i++;
				iFind = strIpAddr.rfind(".");
			}
		}
		if(pSGSNInfo)
		{
			visit->province = atoi(pSGSNInfo->prov_code);
			visit->area = atoi(pSGSNInfo->area_code);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get province[%d] area[%d],ip[%s]",visit->province,visit->area,strIpAddr.c_str());
		}
		else
		{
			visit->province = smParam->GetINPara()->defaultProv;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "use default province[%d]",visit->province);
		}

		// 根据tac查询区号
		MSC_INFO tacInfo;
		int iRet = smParam->GetTac_5GSA(strTAC.c_str(),visit->province, tacInfo);
		if (iRet < 0)
		{ //设置默认的拜访地
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "not find tac[%s]",strTAC.c_str());
			if(!pSGSNInfo)
			{
				visit->area = smParam->GetINPara()->defaultArea;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "not find tac and ip, use default province[%d], areacode[0%d]", visit->province, visit->area);
			}
		}
		else
		{
			visit->area = atoi(tacInfo.area_code);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get area[%d] by tac",visit->area);
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "province[%d], area[0%d], visit province[%s] visit area[0%d]",userInfo->province, userInfo->area, pSGSNInfo->prov_code, visit->area);

	}
	else if(ratType == "NR")
	{

        if(Judge5GCNPLMN(bizMsg,base))
		{
			GetVistInfo(bizMsg,nrCellID,base->smExt.kv["servingCNPlmnId"]);
		}
		else
		{
		    nrCellID = nrCellID.substr(0,4);
			SGSN_INFO nrInfo;
			int iRet = smParam->GetNrCellID(nrCellID, nrInfo);
			if(iRet < 0)
			{
				visit->province = smParam->GetINPara()->defaultProv;
				visit->area = smParam->GetINPara()->defaultArea;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "not find nrCellID[%s], use default province[%d], areacode[0%d]",nrCellID.c_str(), visit->province, visit->area);
			}
			else
			{
				visit->province = atoi(nrInfo.prov_code);
				visit->area = atoi(nrInfo.area_code);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get province[%d] area[%d],nrCellID[%s]",visit->province,visit->area,nrCellID.c_str());
			}
		}	

	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "rattype error[%s]", ratType.c_str());
		return -1;

	}
	do {
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "user province[%d], area[0%d]", userInfo->province, userInfo->area);
		if (userInfo->province == visit->province)
		{
			if (userInfo->area != visit->area)
			{ // 归属地区号不同, 则表示是省内
				bizMsg->m_roamtype = ROAM_PROV;
				break;
			}

			bizMsg->m_roamtype = 0;
			break;
		}

		bizMsg->m_roamtype = ROAM_DIFF_PROV;
	}while (0);

	return bizMsg->m_roamtype;
}

int DCRoamJudge::RoamDataPDSN(STBizMsg* bizMsg, const SPhone& sub, AREA_INFO* visit, const char* PDSN)
{
	char value[32] 	= {0};

	PDSN_INFO *pdsninfo = NULL;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	pdsninfo = m_smpara->GetSprPDSN(PDSN);
	if(pdsninfo)
	{
		visit->province = pdsninfo->prov_code;
		if(bizMsg->visitAreaCode.size() == 0)
		{
			visit->area = pdsninfo->area_code;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "use msg visitAreaCode[%s]",bizMsg->visitAreaCode.c_str());
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "visit.province[%d],sub.province[%d]", visit->province,sub.province);
		if(visit->province != sub.province)
		{
			return 4;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "visit.province != sub.province:roma type[4]", "");
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "visit.province = sub.province:roma type[0]", "");
		return 0;

	}

	DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "invaild PDSN[%s]", PDSN);

	//设置默认的拜访地
	visit->province = m_smpara->GetINPara()->defaultProv;
	if(bizMsg->visitAreaCode.size() == 0)
	{
		visit->area = m_smpara->GetINPara()->defaultArea;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "use msg visitAreaCode[%s]",bizMsg->visitAreaCode.c_str());
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "set default visit.province[%d],visit.area[%d]:roma type[4]", visit->province,visit->area);

	return 4;
}

int DCRoamJudge::RoamDataSGSN(STBizMsg* bizMsg, const SPhone& sub, AREA_INFO* visit, const char* SGSN, const char* MSC)
{
	char value[32] 	= {0};
	MSC_COUNTRY* pcountry = NULL;
	TSMPara* m_smpara = bizMsg->m_smpara;
	if((!SGSN) || (strlen(SGSN) == 0))
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "NULL SGSN", "");
		return 0;
	}
	SGSN_INFO *sgsninfo = NULL;
	strcpy(value, SGSN);
	int itemp = 0;
	for(itemp=strlen(SGSN)-1; itemp >= 0; itemp --)
	{
		if('.'==SGSN[itemp])
		{
			break;
		}
	}
	value[itemp] = 0;
	sgsninfo = m_smpara->GetSprSGSN(value);
	if(sgsninfo)
	{
		char str[12] = {0};
		char tmp[12] = {0};
		strcpy(str, sgsninfo->prov_code);
		visit->province = atoi(str);
		strcpy(tmp, sgsninfo->area_code);
		if(bizMsg->visitAreaCode.size() == 0)
		{
			visit->area = atoi(tmp);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "use msg visitAreaCode[%s]",bizMsg->visitAreaCode.c_str());
		}
		if(visit->province != sub.province)
		{
			const COUNTRY_INFO* pVisitorHome	= NULL;
			pVisitorHome = m_smpara->GetCountryInfo(visit->province);
			if (pVisitorHome)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "country[%d], prov[%s], area[%s]", pVisitorHome->country, str, tmp);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "country[NULL], prov[%s], area[%s]", str, tmp);
			}


			if(0 == strncmp(str, "00", 2))
			{
				if((852 == visit->province) || (853 == visit->province) || (856 == visit->province))
				{
					return 9;		//港澳台漫游
				}
				else if(86 == visit->province)
				{
					return 4;		//省际漫游
				}

				if(pVisitorHome && pVisitorHome->country)
				{
					pcountry = m_smpara->GetMscCountryInfo(MSC);
					if (NULL != pcountry)
					{
						string  buf = m_smpara->GetSectorId(pcountry->iccarrier_code, pcountry->country_code);
						strcpy(visit->sector_id ,buf.c_str());
						if (strlen(visit->sector_id))
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find sectorID");
						}
					}
					return 6;		//国际漫游
				}
			}
			return 4;
		}
        else
        {
		     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "visit->province == sub.province, roamType = 0");
        }
        return 0;
	}
	DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "invaild SGSN[%s]", SGSN);

	//设置默认的拜访地
	visit->province = m_smpara->GetINPara()->defaultProv;
	if(bizMsg->visitAreaCode.size() == 0)
	{
		visit->area = m_smpara->GetINPara()->defaultArea;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "use msg visitAreaCode[%s]",bizMsg->visitAreaCode.c_str());
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "set default visit.province[%d],visit.area[%d]:roma type[4]", visit->province,visit->area);
	return 4;

}

int DCRoamJudge::RoamDataCellMsc(STBizMsg* bizMsg, const char* userinfo, const SPhone& sub,const char* cellid,const char* msc,AREA_INFO* visit,int roamtype)
{
    	int ret 			= RET_SUCCESS;
	int roamType 	    = 0;
	string eventTypeID;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "msc[%s],cellid[%s],roamtype[%d], userinfo[%s]", msc,cellid,roamtype, userinfo);
        const char* bsid = userinfo;
        if(bizMsg->m_if4Gpp)
        {
                bsid = msc;
        }
	/*
	if(msc && (strlen(msc) > 0))
	{

	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "missing msc", "");
		if(roamtype>=0)
		{
			return roamtype;
		}
	}

	if(cellid && (strlen(cellid) > 0))
	{

	}
	else
	{

		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "missing cellid", "");
		if(roamtype>=0)
		{
			return roamtype;
		}
	}
	*/

	if(4==roamtype)//省际边漫判断
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "inter-provincial  roam,cellid[%s],msc[%s],subarea[%d]", cellid,msc,sub.area);
		// roamType = m_smpara->GetEdgeRoamDataCellid(cellid,msc, sub.area,visit,roamtype);
		eventTypeID = m_smpara->GetRoamBorderData(bsid, bizMsg->m_userinfo->ilatnid);
		if(eventTypeID.length())
		{
			strcpy(bizMsg->m_eventType, eventTypeID.c_str());
			roamType = 8;
		}
		else
		{
		   roamType = 4; //非省际边漫
		}
	}
	else if(1==roamtype)//省内边漫
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "provincial roam,cellid[%s],msc[%s],subarea[%d]", cellid,msc,sub.area);
		// roamType = m_smpara->GetEdgeRoamDataCellid(cellid,msc, sub.area,visit,roamtype);
		eventTypeID = m_smpara->GetRoamBorderData(bsid, bizMsg->m_userinfo->ilatnid);
		if(eventTypeID.length())
		{
			strcpy(bizMsg->m_eventType, eventTypeID.c_str());
			roamType = 7;
		}
		else
		{
		   roamType = 1; //非省内边漫，则省内漫游
		}

	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);
   	return roamType;
}

int DCRoamJudge::RoamDataMsc(STBizMsg* bizMsg, const SPhone& sub,const char* msc,AREA_INFO* visit)
{
    int ret 			= RET_SUCCESS;
	int roamType 	    = 0;
	MSC_INFO *mscinfo = NULL;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	const AREA_INFO* pVisitorHome	= NULL;

	if(msc && (strlen(msc) > 0))
	{
		mscinfo = m_smpara->GetMscVlrCodeInfo(msc);
		if(mscinfo->area_code)
		{
			if(bizMsg->visitAreaCode.size() == 0)
			{
				visit->area = atoi(mscinfo->area_code);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "use msg visitAreaCode[%s]",bizMsg->visitAreaCode.c_str());
			}
			pVisitorHome= m_smpara->GetAreaInfo(visit->area);
			if(NULL == pVisitorHome)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "not configure msc[%s]", msc);
				return	-1;
			}
			visit->province = pVisitorHome->province;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "MSC  province[0%d], area[0%d]", visit->province, visit->area);

			if (sub.province != visit->province)//省际漫游
			{
				roamType = 4;
			}
			else
			{
				if(sub.area == visit->area)//非漫游
				{
					roamType = 0;
				}
				else//省内漫游
				{
					roamType = 1;
				}
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roam by msc,roamtype[%d]", roamType);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "not configure msc[%s]", msc);
			return  -1;
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "table spr_msc not configure msc[%s]", msc);
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);
	return roamType;
}


int DCRoamJudge::RoamByTac(STBizMsg* bizMsg, const SPhone& sub,const char* tac,AREA_INFO* visit)
{
    int ret 			= RET_SUCCESS;
	int roamType 	    = 0;
	MSC_INFO mscinfo;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	memset(&mscinfo,0,sizeof(mscinfo));
	const AREA_INFO* pVisitorHome	= NULL;

	if(tac && (strlen(tac) > 0))
	{
		ret=m_smpara->GetTacVlrCodeInfo(tac,mscinfo);
		if(ret==0)
		{
			if(bizMsg->visitAreaCode.size() == 0)
			{
				visit->area = atoi(mscinfo.area_code);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "use msg visitAreaCode[%s]",bizMsg->visitAreaCode.c_str());
			}
			pVisitorHome= m_smpara->GetAreaInfo(visit->area);
			if(NULL == pVisitorHome)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "not configure tac[%s]", tac);
				return	-1;
			}
			visit->province = pVisitorHome->province;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "TAC  province[0%d], area[0%d]", visit->province, visit->area);

			if (sub.province != visit->province)//省际漫游
			{
				roamType = 4;
			}
			else
			{
				if(sub.area == visit->area)//非漫游
				{
					roamType = 0;
				}
				else//省内漫游
				{
					roamType = 1;
				}
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roam by tac,roamtype[%d]", roamType);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "not configure tac[%s]", tac);
			return  -1;
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "table spr_msc not configure msc[%s]", tac);
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "roamType [%d]", roamType);
	return roamType;
}


int DCRoamJudge::RoamDsl(STBizMsg* bizMsg, const SPhone & home, AREA_INFO* visit, const char * nasIP)
{
	int ret = RET_SUCCESS;
	char value[BIZ_TEMP_LEN_256] = {0};
	if(!nasIP)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE,  bizMsg->m_sessionID, "no nasIP", "");
		return -1;
	}

	DSL_INFO *dslinfo = NULL;
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;
	dslinfo = smpara->GetSprDSL(nasIP);
	if(dslinfo)
	{
		visit->province = dslinfo->prov_code;
		visit->area = dslinfo->area_code;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "nasIP province[0%d], area[0%d]", visit->province, visit->area);
		if(visit->province != home.province)
		{
			return 4;
		}
		if(visit->area != home.area)
		{
			return 1;
		}
		return 0;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE,  bizMsg->m_sessionID, "nasIP useless[%s]", nasIP);
		visit->province = home.province;
		visit->area = home.area;
		return 4;
	}

	return RET_SUCCESS;
}

int DCRoamJudge::RoamSMS(STBizMsg* bizMsg, SCCRSMS *data)
{
	AREA_INFO *subVisit = bizMsg->m_visit;
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;

	char value[128]={0};
	strncpy(value,data->mscAddr.c_str(),sizeof(value)-1);

	int ret = RoamSMSMsc(value, subVisit,data->roamingtype, bizMsg);
	if(-1 == ret)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, -1, bizMsg->m_sessionID, "RoamSMS failed");
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE,  "", "get visit carriers[%s]", subVisit->szcarriers);

	int roamType = RoamSMSByRoamingType(value,data->roamingtype, subVisit, smpara);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_P2PSMS_TYPE,  "", "roamtype[%d]", roamType);

	if((!data->called.area) && data->called.country == 86)
	{
		data->called.area = smpara->GetCommonPara()->currentProvCode;
		data->called.area = smpara->GetCommonPara()->currentAreaCode;
	}
	bizMsg->m_roamtype = roamType;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "", "roamType [%d]", roamType);

	//info 日志
	DCDATLOG("SM00008:%d", roamType);
	return 0;
}

int DCRoamJudge::RoamSMSMsc( char* MSCorVLR, AREA_INFO* visit,  int iCCRRoamType, STBizMsg* bizMsg)
{

	char szRealMSCAddr[33] = {0};
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	strcpy(szRealMSCAddr, MSCorVLR);

	int iLen = strlen(szRealMSCAddr);
	if ( 0 == iLen )
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, -1 , "",  "NULL MSC-ADDRESS", "");
		return -1;
	}


	MSC_INFO *pmscinfo = NULL;
	while( iLen )
	{
		szRealMSCAddr[iLen] = 0;

		pmscinfo = m_smpara->GetMscVlrCodeInfo(szRealMSCAddr);
		if (pmscinfo)
		{
			strcpy(visit->szcarriers, pmscinfo->carriers);
			visit->area = atoi(pmscinfo->area_code);

			strcpy(MSCorVLR, szRealMSCAddr);

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE , "",  "mscAddr[%s], real mscAddr[%s]", MSCorVLR, szRealMSCAddr);
			return 0;
		}
		else
		{
			//港澳台国际漫游缩位匹配1 4 5
			if (1 == iCCRRoamType || 4==iCCRRoamType || 5 == iCCRRoamType )
			{
				iLen--;
				continue;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,-1, "",  "get real MSCorVLR[%s] failed",MSCorVLR);
				return -1;
			}
		}
	}
}

//根据漫游方式和漫游类型判断出来漫游类型
int DCRoamJudge::RoamSMSByRoamingType(char* MSCorVLR,int &roamingtype, AREA_INFO* visit,TSMPara* smpara)
{
   int roamType = 0;
   MSC_COUNTRY* pcountry = NULL;
   if(0==strcmp(MSCorVLR,"0")||0==strlen(MSCorVLR))
   {
      if(1==roamingtype)
      {
         return roamType;
  	  }
   }
   else
   {
	if(1==roamingtype)
	{
		roamType = 11;//CtoC漫游
	}
	else if(4==roamingtype)
	{
		roamType = 12;//CtoG和记漫游
	}
	else if(5==roamingtype)
	{
		roamType = 13;//CtoG沃达丰漫游
	}
	pcountry = smpara->GetMscCountryInfo(MSCorVLR);
	if (NULL != pcountry)
	{
		string tmp = smpara->GetSectorId(pcountry->iccarrier_code, pcountry->country_code);
		strcpy(visit->sector_id ,tmp.c_str());
		if (strlen(visit->sector_id))
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, "", "find sectorID[%s]", visit->sector_id);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, "", "no find sectorID");
		}
	}
   }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "", "roamType [%d]", roamType);
   return roamType;
}


int DCRoamJudge::GetAreaCode(SPhone & phone, STBizMsg * bizMsg)
{
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;
	if(!m_smpara->GetCutArea(phone.area) || !m_smpara->GetCommonPara()->iLatnRelQueryControl)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "NOAreaCode[%d]", phone.area);
		return 0;
	}
	char value[8] = {0};

	//根据用户号码在TB_NBR_LATN_REL中查询实际的归属区号
	UDBSQL *pQuery = dbm->GetSQL(COM_USER_SELECT_TB_NBR_LATN_REL);
	try
	{
		pQuery->UnBindParam();
		pQuery->BindParam(1, phone.phone);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			phone.area = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "real area[%d]", phone.area);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID), "", "not found area by phone[%s]", phone.phone.c_str());
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,	"", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return 0;
}

bool DCRoamJudge::Judge5GCNPLMN(STBizMsg* bizMsg,ocs::SCCRBase* base)
{
	string strTemp=bizMsg->m_smpara->Get5GPara()->str5GCNPLMN;
	string strServingCNPlmnId=base->smExt.kv["servingCNPlmnId"];
	if(!strTemp.empty() && !strServingCNPlmnId.empty())
	{
		size_t nPos=strTemp.find(strServingCNPlmnId);
		if(nPos!=std::string::npos)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "find servingCNPlmnId[%s] in table SM_SYSTEM_PARAMETERs[%s]", strServingCNPlmnId.c_str(),strTemp.c_str());
			return true;
		}
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", " servingCNPlmnId[%s] not in table SM_SYSTEM_PARAMETER[%s]", strServingCNPlmnId.c_str(),strTemp.c_str());
	return false;
}

//从6位缩位到2位查找,找到为止
void DCRoamJudge::GetVistInfo(STBizMsg* bizMsg,string nrCellID,string strCNPLmn)
{
	bool bNotFind=true;
	AREA_INFO *visit = bizMsg->m_visit;
	TSMPara *smParam = bizMsg->m_smpara;
	SGSN_INFO nrInfo;
	for(int i=6;i>=2;i--)
	{
		string TemprCellID = nrCellID.substr(0,i);
		if(0==smParam->GetNrByCellIDAndCnplmn(strCNPLmn,TemprCellID, nrInfo))
		{
			bNotFind=false;
			visit->province = atoi(nrInfo.prov_code);
			visit->area = atoi(nrInfo.area_code);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get province[%d] area[%d],nrCellID[%s],CNPLmn[%s]",visit->province,visit->area,TemprCellID.c_str(),strCNPLmn.c_str());
			break;
		}

	}

	if(bNotFind)
	{
		visit->province = smParam->GetINPara()->defaultProv;
		visit->area = smParam->GetINPara()->defaultArea;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "not find nrCellID[%s], use default province[%d], areacode[0%d]",nrCellID.c_str(), visit->province, visit->area);
	}

}



