/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAocDealFlow.h
*Indentifier：
*
*Description：
*		应答流程组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_AOC_DEAL_FLOW_H__
#define __DC_AOC_DEAL_FLOW_H__
#include "DCBasePlugin.h"
#include "DCOBJSet.h"
#include "DCAnsPara.h"
#include "DCAocBase.h"




class DCAocDealFlow :  public DCBasePlugin
{
	public:	
		DCAocDealFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCAocDealFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		DCAocBase*m_aoc;
		DCAnsPara *m_anspara;
		DCOBJSetPool* m_pool;

};

#endif

