/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsDSLTEL.h
*Indentifier：
*
*Description：
*		DSL业务处理类
*Version：
*		V1.0
*Author:
*		ZY.F
		
*Finished：
*		
*History:
********************************************/

#ifndef __DC_ANS_DSL_TEL_H__
#define __DC_ANS_DSL_TEL_H__

#include "DCAnsDSL.h"

class DCAnsDSLTEL : public DCAnsDSL
{
	public:

		DCAnsDSLTEL();
		virtual ~DCAnsDSLTEL();

	protected:
		virtual int XdrEvent(STBizMsg* bizMsg);
		virtual int ComposeCCA(STBizMsg* bizMsg);
};

#endif

