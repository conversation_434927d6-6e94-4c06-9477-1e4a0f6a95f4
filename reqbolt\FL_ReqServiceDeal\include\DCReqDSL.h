/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqDSL.h
*Indentifier：
*
*Description：
*		DSL业务处理类
*Version：
*		V1.0
*Author:
*		ZY.F
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_DSL_H__
#define __DC_REQ_DSL_H__
#include "DCReq.h"
#include "BizLenDef.h"
#include "ErrorCode.h"
#include "BizDataDef.h"
#include "TSMPara.h"
#include "TConfig.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "REMsgTypeDef.h"
#include "DCCommonIF.h"


class DCReqDSL : public DCReq
{
	public:

		DCReqDSL();
		virtual ~DCReqDSL();

	public:

		virtual int Work(void *data);
		
		virtual int SwitchReqType(STBizMsg* bizMsg);

		int sendInitCCA(STBizMsg* bizMsg);
};

#endif

