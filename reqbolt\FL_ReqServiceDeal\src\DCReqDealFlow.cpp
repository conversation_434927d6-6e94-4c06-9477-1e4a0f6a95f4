#include "DCReqDealFlow.h"
#include "TSMPara.h"
#include "DCReqVOICETEL.h"
#include "DCReqSMSTEL.h"
#include "DCReqDATATEL.h"
#include "DCReqISMPTEL.h"
#include "DCReqDSLTEL.h"
#include "DCReqPGW.h"
#include "DCReq5G.h"
#include "DCOBJSet.h"
#include "ErrorCode.h"


int DCReqDealFlow::init()
{

	int ret = 0;
	
	DCReqVOICETEL* reqVoiceTel = new DCReqVOICETEL();
	m_req[0] = reqVoiceTel;

	DCReqSMSTEL* reqSmsTel = new DCReqSMSTEL();
	m_req[1] = reqSmsTel;

	DCReqDATATEL* reqDataTel = new DCReqDATATEL();
	m_req[2] = reqDataTel;

	DCReqISMPTEL* reqIsmpTel = new DCReqISMPTEL();
	m_req[3] = reqIsmpTel;

	DCReqDSLTEL* reqDslTel = new DCReqDSLTEL();
	m_req[4] = reqDslTel;

	DCReqPGW* reqPgwTel = new DCReqPGW();
	m_req[5] = reqPgwTel;

	DCReq5G* req5GTel = new DCReq5G();
	m_req[6] = req5GTel;
	return ret;
}

int DCReqDealFlow::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	output = input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	int ret = 0;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	if(bizMsg->m_serviceContextID == HRS)
	{
		ret = m_req[3]->Work(bizMsg);
	}
	else if(DATA_5G == bizMsg->m_serviceContextID)
	{
		ret = m_req[6]->Work(bizMsg);
	}	
	else if(bizMsg->m_serviceContextID < VOICE || bizMsg->m_serviceContextID > CCG )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "","unknown serviceContextID :%d",bizMsg->m_serviceContextID);
		return RET_ERROR;
	}
	else
	{
		if(CCG == bizMsg->m_serviceContextID)
		{		
			ret = m_req[2]->Work(bizMsg);
		}
		else
		{
			ret = m_req[bizMsg->m_serviceContextID-1]->Work(bizMsg);
		}
		
	}
	if(ret!=0 && RET_OVER != ret)
	{
		bizMsg->m_resultcode = ret;
	}
	return ret;
}


DYN_PLUGIN_CREATE(DCReqDealFlow, "FC_REQDEAL", "FC_ReqServiceDeal", "1.0.0")


