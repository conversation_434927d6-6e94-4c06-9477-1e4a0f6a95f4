#include "DCParseXml.h"
#include "DCLogMacro.h"
#include "DCPluginManer.h"
#include "DCMCastEvtFun.h"
#include <unistd.h>
#include <vector>
#include <map>
#include <string>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include "CSocketClient.h"
#include "TThread.h"
//#include "DCFLocalClient.h"
#include "DCFLocalClientNew.h"
#include "DCFClient.h"
#include "DCKpiSender.h"
#include "DCSMPara.h"
using namespace dcf_new;

int  SplitString(const char* pszStr, const char cSeparator, std::list<std::string>& vecStr)
{
	if (!pszStr)
	{
		return 0;
	}

	std::string strField;
	strField.clear();
	for (const char* p = pszStr; *p; p++)
	{
		if ((*p) != cSeparator)
		{
			strField.push_back(*p);
			continue;
		}

		vecStr.push_back(strField);
		strField.clear();
	}

	vecStr.push_back(strField);

	return 0;
}
int main(int argc, char*argv[])
{
	int ret = 0;

    if (argc < 2)
	{
        printf("need start param: 1. cfg name!\n");
		return 0;
	}
	// 加载配置文件
    char *configfile = getenv("OCS_CONFIG");
	if(!configfile)
	{
		printf("getenv OCS_CONFIG failed!\n");
		return 1;
	}

	char sfile[256]={0};
	string cfgName = argv[1];
	if (cfgName.find('/') != cfgName.npos)
	{
		sprintf(sfile, "%s", cfgName.c_str());
	}
	else
	{
		sprintf(sfile, "%s/%s", configfile, cfgName.c_str());
	}

    ret = DCParseXml::Instance()->Init("ALL", sfile);
    if(ret < 0)
	{
		printf("load [%s] failed\n", sfile);
		return 1;
	}

	// 初始化日志组件
	const char* logpath = DCParseXml::Instance()->GetParam("logAddr", "Common/log");
    const char* loglevel = DCParseXml::Instance()->GetParam("level", "Common/log");

      if(!logpath || !loglevel)
	{
		printf("empty log path or level param for smsessfree\n");
		return 1;
	}

	ret = DCLOGINIT("ocs","smsessfree", atoi(loglevel), logpath);

	if(ret)
	{
		printf("DCLOGINIT failed!\n");
		return 1;
	}
       DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d, module:%s",logpath, loglevel, "smsessfree");

	// 注册监听事件
	DCMCastManer mcm;
	const char* mcast = getenv("OCS_MCAST_CMD_ADDR");
	if(mcast)
	{
		ret = mcm.init(mcast);
		if(ret < 0)
		{
		        DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init  DCMCastManer failed: %s", strerror(errno));
		        return ret;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,-1,"","init  DCMCastManer success");
		
		LogEventFun evfun(&mcm);
		evfun.register_event("smsessfree");
	}

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "", "init DCMqProduceServer success");

    string zkServ = DCParseXml::Instance()->GetParam("zk.serv","smsessfree/DCFClient");
    string zkPath = DCParseXml::Instance()->GetParam("zk.proxy.path","smsessfree/DCFClient");
    int iWorkNum = atoi(DCParseXml::Instance()->GetParam("wk.num","smsessfree/DCFClient"));
    
    DCFClient m_dcfClient;
    ret = m_dcfClient.init(zkServ.c_str(), zkPath.c_str(), iWorkNum);
    if (ret < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init  DCFClient Failed.");
        return ret;
    }

	DCFLocalClient m_pclient;
	m_pclient.setSerivcePassword(DCParseXml::Instance()->GetParam("login_password","smsessfree/zk"));
	m_pclient.setAESPassword(DCParseXml::Instance()->GetParam("aes_password","smsessfree/zk"));
	std::string addr = DCParseXml::Instance()->GetParam("addr","smsessfree/zk");
	std::string root = DCParseXml::Instance()->GetParam("root","smsessfree/zk");
	m_pclient.setZookAddrAndRootDir(addr.c_str(),root.c_str());
	if (m_pclient.start())
	{
		printf("start client failed{zook:%s,root:%s}.\n", addr.c_str(), root.c_str());
		return -1;
	}
	// 加载topic信息
    std::map<std::string, std::string> mapService;
    int nMax = 0;
	const char* param = DCParseXml::Instance()->GetParam("groupnum", "smsessfree/service_route");
	if(!param || atoi(param) <= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "", "invalid param[groupnum=%s]", param);
		return 1;
	}
	nMax = atoi(param);
	char buf[64];
    for(int i=1; i<=nMax;i++)
	{
		sprintf(buf, "smsessfree/service_route/group%d", i);
		const char* topology = DCParseXml::Instance()->GetParam("topology", buf);
		const char* service = DCParseXml::Instance()->GetParam("service", buf);
        const char* ansService = DCParseXml::Instance()->GetParam("ansService", buf);
		if(!topology || !service ||!ansService)
		{
			continue;
		}
		mapService[topology] = std::string(service) + std::string("|")+std::string(ansService);
		DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "", "find topology[%s],service[%s],ansService[%s]", topology, service, ansService);
		
		m_pclient.subscribeService(service);
	}

	// 初始化性能指标日志
	std::string sDelayMs = DCParseXml::Instance()->GetParam("KpiDelayMs", "smsessfree/BPoint");
	std::string sFlag = DCParseXml::Instance()->GetParam("KpiFlag", "smsessfree/BPoint");
	std::string sProtocol = DCParseXml::Instance()->GetParam("KpiProtocol", "smsessfree/BPoint");
	std::string sAddr = DCParseXml::Instance()->GetParam("KpiAddr", "smsessfree/BPoint");
	

	DCKpiSender::instance()->SetParam("delay", sDelayMs.c_str());
	DCKpiSender::instance()->SetParam("flag", sFlag.c_str());
	DCKpiSender::instance()->SetParam("protocol", sProtocol.c_str());
	DCKpiSender::instance()->SetParam("addr", sAddr.c_str());

	if (DCKpiSender::instance()->Init() != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init DCKpiSender failed");
		return -1;
	}

	std::list<string> vListLatn;
	vListLatn.push_back("999");    // 只是适配一下KpiSend, 无实际意义

	DCKpiMon* ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "GTH", "SMSESSFREE");//对应于大屏后台程序的处理方式
	if (ptrBPMon)
	{
		DCKpiSender::instance()->group_all_init(ptrBPMon, "TraceKpi", vListLatn);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get Kpi Handle failed, ErrorCode[%d][%s]",
			DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
		return -1;
	}

	std::map<std::string,std::string> mapParam;
	
	mapParam["sSubscriber"] = DCParseXml::Instance()->GetParam("Subscriber", "smsessfree");
	mapParam["sSmRouteProcess"] = DCParseXml::Instance()->GetParam("RouteCTGProcess", "smsessfree");
	mapParam["nGrayRefreshIntr"] = DCParseXml::Instance()->GetParam("GrayRefreshIntr", "smsessfree");

    std::string sqlPost;
    const char *sPost = DCParseXml::Instance()->GetParam("sql_postfix", "smsessfree");
    if(sPost)
    {
        sqlPost = sPost;
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "Get sql_postfix[%s]",sqlPost.c_str());
    }

	// 初始化插件管理器
	DCPluginManer pm;
	pm.set_handle("SERVICEPRD", &m_pclient); //先设置服务句柄到插件管理器中
	pm.set_handle("SERVICETOPIC", &mapService);
    pm.set_handle("SERVICEPROXY", &m_dcfClient);  // 设置发送消息到chfproxy的客户端句柄
    pm.set_handle("SERVICEPARAM", &mapParam);
    char bufTmp[32] = {0};
    sprintf(bufTmp,"smsessfree|%s",sqlPost.c_str());
    ret = pm.init(configfile, bufTmp, DFM_USE_DBM|DFM_USE_REFRESH);
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "", "init DCPluginManer failed");
		return 1;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "", "init DCPluginManer success");

	// 获取执行流程句柄
	DCBaseFlow* flow = pm.get_flow("sessfree");
	if(!flow)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "", "not find flow[sessfree]");
		return 1;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "", "success to find flow[sessfree]");
	//启动监控线程
    sprintf(sfile, "%s/%s%s.sql.xml", configfile, "smsessfree",sqlPost.c_str());
    
	SocketClient *client = new SocketClient;
	client->init(sfile);
	client->start();

	// 循环执行定时任务
	while(1)
	{
		flow->call(NULL,NULL);
	}
	return 0;
 }

