#include "DCAocDealFlow.h"
#include "DCRbMsgDef.h"
#include "DCLogMacro.h"
#include "DCAocBase.h"
#include "DCSeriaOp.h"


int DCAocDealFlow::init()
{
	int ret = 0;	
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	m_aoc = new DCAocBase();
	
	return 0;
}

int DCAocDealFlow::process(void* input, void* output)
{
	int ret = 0;

	DCOBJSet* pset = (DCOBJSet*)input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	rbresult *base = pset->get<rbresult>();
	bizMsg->m_base = base;
	bizMsg->m_anspara = m_anspara;
	bizMsg->m_dbm = dbm();	
	
	if(bizMsg->m_serviceContextID < VOICE || bizMsg->m_serviceContextID > CCG )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,RB_RBA_MSG_ERROR, "","unknown serviceContextID :%d",bizMsg->m_serviceContextID);
		return RB_RBA_MSG_ERROR;
	}
	else
	{
		ret = m_aoc->Work(bizMsg);
	}
	
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "CALL RET=%d",ret);
	return ret;

}


DYN_PLUGIN_CREATE(DCAocDealFlow, "FC_AOCDEAL", "FC_AocDeal", "1.0.0")



