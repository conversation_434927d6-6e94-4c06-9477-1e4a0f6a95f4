/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCReqBaseFlow.h
*Indentifier：
*
*Description：
*      reqbolt主类
*Version：
*       V1.0
*Author:
*       ZYF
*Finished：
*
*History:
*
********************************************/

#ifndef DC_REQBOLT_H_
#define DC_REQBOLT_H_

#include <DCBolt.h>
#include <stdio.h>
#include <time.h>
#include "DCPluginManer.h"
#include "DCSeriaOp.h"
#include <string.h>
#include <pthread.h>
#include <list>
#include "DCKpiSender.h"
#include "DCPerf.h"

//#include "DCPerfStatistic.h"
class DCSeriaEncoder;
class DCPerfTimeStats;
class DCReqBolt: public tydic::storm::DCBolt {
public:
	int Initialize(const tydic::storm::DCStormConfig& config);
	int Process(tydic::storm::Tuple &tuple);
	DCReqBolt();
	virtual ~DCReqBolt();
	int Refresh(const char * path);
	int SetWacther();
	void GetHostIp(std::string &IP);
	void Compress(std::string& buf);
	std::string ReSetUid(std::string strMsgInfo, int c);
	void SplitString(const std::string& str, char sep, std::list<std::string>& vec);
	void svc();
	int SendToKpiBolt(std::string msginfo);
	int SendToCheckBolt(std::string& msginfo, std::string sAuditId);
    void TimestampToDate(time_t timestamp, char *pszDate);

private:
	DCPluginManer m_pm;
	DCPerfTimeStats*  m_tstat;
	time_t m_checktime;
	char m_Topic[128];
	char m_payflagTopic[128];
	char m_testTopic[128];
	std::string m_strIP;
	DCSeriaEncoder* m_en;
	DCSeriaPrinter m_print;
	DCKpiMon *m_ptrBPMon;
	pthread_t m_tid;
	DCPerf m_perf;
};


extern "C" {

tydic::storm::DCStormBase* CreateInstance()
{
	return new DCReqBolt();
}

void ReleaseInstance(tydic::storm::DCStormBase* pStorm)
{
	if (pStorm != NULL)
		delete pStorm;
}

}

#endif /* TEST_CTESTBOLT_H_ */

