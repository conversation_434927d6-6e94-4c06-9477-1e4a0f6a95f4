#ifndef __CsocketClient_H__
#define __CsocketClient_H__
#include <sys/socket.h>
#include <netinet/in.h>
#include <netdb.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <signal.h>

#include <strings.h>
#include <string.h>
#include <stdlib.h>

#include "TThread.h"
#include <errno.h>
#include <map>
#include <string>
#include "DCDBManer.h"
#include <iostream>

using namespace std;
 
 


class SocketClient:public TThread
{
	public:
		SocketClient();
		~SocketClient();
		int init(const char* sqlfile);
		int Connect();
		int sendAuthMsg();
		void routine();
		int composemsg();
		int sendMsg(char *buf,int length);
		int CloseFD();
	private:
		int m_state;
		int m_port;
		int m_connfd;
		string m_addr;
		char m_host[64];
		char m_uri[64];
		long long m_timeflag;
		unsigned long m_time;
		char m_buf[1024*128];
		socklen_t m_socklen;
		pid_t   m_childpid; 
		sockaddr_in m_servaddr;
		DCDBManer *m_pDB;
};
#endif

