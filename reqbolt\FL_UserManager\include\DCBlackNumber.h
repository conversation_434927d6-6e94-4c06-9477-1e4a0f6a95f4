/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBlackNumber.h
*Indentifier：
*
*Description：
*		消息解析类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_BLACKNUMBER_H__
#define __DC_BLACKNUMBER_H__
#include "DCBasePlugin.h"

class DCBlackNumber : public DCBasePlugin
{
	public:	
		DCBlackNumber(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCBlackNumber()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		virtual const char* desc();

};
#endif
