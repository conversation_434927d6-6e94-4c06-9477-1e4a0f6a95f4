#include "DCBasePlugin.h"
#include "DCLogMacro.h"
#include "DCEptMsgDef.h"
#include "DCDBManer.h"
#include "DCSeriaOp.h"
#include "DCSMPara.h"
#include "UHead.h"
#include <vector>
#include <map>
#include <string>
#include <string.h>
#include <utility>
#include <sys/time.h>
//#include "DCFLocalClient.h"
#include "DCFLocalClientNew.h"
#include "DCGrayscaleRoute.h"


using namespace std;
using namespace dcf_new;
class DCSessionTimerCut : public DCBasePlugin
{
public:
	DCSessionTimerCut(const char* category, const char* func, const char* version)
		:DCBasePlugin(category,func,version)
		,m_pclient(NULL)
		,m_mTopic(NULL)
		,m_en(ESeriaBinString)
	{
		m_hseq = getpid()%1000000*100;
		m_tseq = time(NULL);
		m_sseq[0] = 0x0;
	}

	virtual ~DCSessionTimerCut()
	{
	}

	virtual const char* desc()
	{
        return "session time cut cdr";
	}
protected:
	virtual int init();

	virtual int process(void* input, void* output);

    int pgw_session_cut();

    int produce(ocs::UHead& uhd, ocs::SEPTMsg& msg);

    long time2sec(long long date);

	int GetGrayServName(const string& RouteProcess, string& serviceName, string& sLatnid);

private:
    std::vector<std::pair<ocs::UHead, ocs::SEPTMsg> > 	m_data;
    DCFLocalClient*         m_pclient;
    std::map<std::string, std::string>* m_mTopic;
	DCSeriaEncoder					m_en;
	DCSeriaPrinter      m_pPrint;
	int			m_hseq;
	long			m_tseq;
	char 	       m_sseq[20];
	time_t lasttime;
	map<string, string>* m_mapParam;
	//灰度发布
	string sSubscriber;
	string sSmRouteProcess;
	int nGrayRefreshIntr;
};

int DCSessionTimerCut::init()
{
	UDBSQL* pquery = NULL;
	pquery = dbm()->GetSQL("q_pgw_timer_longcdr");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_pgw_timer_longcdr sql");
		return -1;
	}

	m_pclient = (DCFLocalClient*)gethandle("SERVICEPRD");
    if(!m_pclient)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPRD handle");
		return -1;
	}

	m_mTopic = (std::map<std::string, std::string>*)gethandle("SERVICETOPIC");
	if(!m_mTopic)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICETOPIC handle");
		return -1;
	}

	m_mapParam = (std::map<std::string, std::string>*)gethandle("SERVICEPARAM");
	if (!m_mapParam)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPARAM handle");
		return -1;
	}

	std::map<std::string, std::string>::iterator itParam = m_mapParam->find("sSubscriber");
	if (itParam != m_mapParam->end())
	{
		sSubscriber = itParam->second;
	}
	itParam = m_mapParam->find("sSmRouteProcess");
	if (itParam != m_mapParam->end())
	{
		sSmRouteProcess = itParam->second;
	}
	itParam = m_mapParam->find("nGrayRefreshIntr");
	if (itParam != m_mapParam->end())
	{
		nGrayRefreshIntr = atoi(itParam->second.c_str());
	}
	//灰度配置数据初始化
	int iret = DCGrayscaleRoute::instance()->init(dbm(),sSubscriber.c_str()); //刷新数据
	if (iret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCGrayscaleRoute init data failed,Subscriber=[%s]", sSubscriber.c_str());
		return -1;
	}
	lasttime = time(NULL);

	return 0;
}

int DCSessionTimerCut::process(void* input, void* output)
{
	pgw_session_cut();
	return 0;
}

int DCSessionTimerCut::pgw_session_cut()
{
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	long start_time = 0;
	time_t et = time(NULL);
	int trace = 0;
	int longtime = 0;
	int	nAmountLimit = 0;
	char value[64] = {0};
	longtime = drf()->getval<DCParaCom>("smpara")->longCDRTime;
	nAmountLimit = drf()->getval<DCParaCom>("smpara")->nAmountLimit;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "pgw", "longtime:%d, nAmountLimit:%d",longtime,nAmountLimit);
	if(longtime <= 0)
	{
		return 0;     
	}
	m_data.clear();
	//1702059884
	std::string skey;
	UDBSQL* pQuery = dbm()->GetSQL("q_pgw_timer_longcdr");
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(2, start_time);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "pgw", "start time:%ld",start_time);
			if( time2sec(start_time) + longtime <= et )
			{
				pQuery->GetValue(1, value);
				pQuery->GetValue(3, msg.sessionID);
				pQuery->GetValue(4, trace);

				msg.topology = value;
				pQuery->GetValue(5, value);
				msg.szServiceContextIDStr = value;
				msg.sreq = 20;
				msg.servicecontextid = PGW;
				msg.type = 5;
				msg.trace = trace;
				msg.version = 2;
				m_tseq++;
				if(m_tseq >= 10000000000L) m_tseq = 1;
				sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
				uhd.uid = m_sseq;
			
				skey = m_sseq;
				DCLOG_SETKEY(skey.c_str());
				DCLOG_SETCTL(DCLOG_MASK_TRACE, msg.trace);
				m_data.push_back(std::make_pair(uhd,msg));
				
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "pgw", "longcdr,sessionid:%s",msg.sessionID.c_str());
			}
		}
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "pgw", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }

	for(unsigned int i = 0; i< m_data.size(); i++)
	{
		produce(m_data[i].first, m_data[i].second);
		if( (nAmountLimit > 0) && ((i+1)%nAmountLimit==0))
		{
			sleep(1);
		}
	}
	m_data.clear();
	return 0;
}

int DCSessionTimerCut::produce(ocs::UHead& uhd, ocs::SEPTMsg& msg)
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 1001, "", "DCSessionTimerCut::produce SessionId[%s] Uid[%s]HOSTID:%s",msg.sessionID.c_str(),uhd.uid.c_str() ,msg.topology.c_str());
	int ret = 0;
    struct timeval tmv;
    char buf[20];
	char value[256];
	const char *topology = NULL;
	uhd.car = "EPT";
	strcpy(value,msg.topology.c_str());
	msg.version = 2;
	if(msg.topology != "")
	topology = strtok(value,";");
	else
	topology = "";
	std::map<std::string, std::string>::iterator it = m_mTopic->find(topology);
    if(it == m_mTopic->end())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001,  msg.sessionID.c_str(), "not find serviceName for topology[%s]", topology);
		return -1;
	}
	std::size_t  sep = it->second.find('|');
	std::string  serviceName = it->second.substr(0, sep);
	msg.anstopic = it->second.substr(sep+1);
	uhd.car = "EPT";

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(msg);
		m_pPrint.print(uhd);
		m_pPrint.print(msg);
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCSessionTimerCut seedMsg:%s",m_pPrint.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "encode failed");
		return -1;
	}
	std::string sendmsg = HexEncode(m_en.data(),m_en.size());

      // 头部加固定16位微妙时间戳
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	sendmsg.insert(0, buf);
    GetGrayServName(sSmRouteProcess, serviceName, msg.EptExt["LatnId"]);
	
    ret = m_pclient->invokeAsync(serviceName, uhd.uid, sendmsg, "", 0);
    if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "send [%s]failed\n",serviceName.c_str());
		int iNum = 0;
		while(ret < 0)
		{
			iNum++;
			m_pclient = (DCFLocalClient*)gethandle("SERVICEPRD");
			DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPRD handle, reconnect times[%d]", iNum);
			ret = m_pclient->invokeAsync(serviceName, uhd.uid, sendmsg, "", 0);
			if(iNum > 3)
			{
				break;
			}
			sleep(1);
		}
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send to service:%s, uuid:%s,content:%s", serviceName.c_str(), uhd.uid.c_str(),sendmsg.c_str());
	}
	return 0;
}

long DCSessionTimerCut::time2sec(long long date)
{
    //20101001231105
    time_t tsec = 0;
    if((date/10100101010101)>=1)
    {
        struct tm strtime = {0};
        strtime.tm_year = date/10000000000-1900;
        strtime.tm_mon = (date%10000000000)/100000000-1;
        strtime.tm_mday = (date%100000000)/1000000;
        strtime.tm_hour = (date%1000000)/10000;
        strtime.tm_min = (date%10000)/100;
        strtime.tm_sec = date%100;

        tsec = mktime(&strtime);
    }
	return tsec;
}

//根据灰度路由规则表获取服务名
int DCSessionTimerCut::GetGrayServName(const string& RouteProcess, string& serviceName, string& sLatnId)
{
	//路由规则获取服务名称
	string sServiceName = "";
	map<string,string> inMapParam;

	inMapParam.insert(map<string,string>::value_type("LatnID",sLatnId));

	int ret = DCGrayscaleRoute::instance()->GetRouteServiceName(RouteProcess.c_str(), inMapParam, sServiceName);
	if (ret < 0 || 0 == sServiceName.length())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get RouteServiceName failed.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get RouteServiceName[%s]",sServiceName.c_str());
	}
	serviceName = sServiceName;
	return 0;
}


DYN_PLUGIN_CREATE(DCSessionTimerCut, "FC_SessCut", "FC_SessCutScan", "1.0.0")
