#include "DCBlackNumber.h"
#include "TSMPara.h"
#include "DCLogMacro.h"
#include "DCOBJSet.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "DCUserBase.h"
#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "ErrorCode.h"


int DCBlackNumber::init()
{
	int ret = 0;

	return ret;
}

const char* DCBlackNumber::desc()
{
	return "FC_BlackNumber";
}

//消息头|公共消息|业务消息
int DCBlackNumber::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();	
	SCCRBase* base = pset->get<SCCRBase>();
	TSMPara* m_smpara = (TSMPara*) bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	SUserInfo* userInfo = (SUserInfo * )bizMsg->m_userinfo;

	
	if(SM_SESSION_INITIAL_CODE == bizMsg->m_requestType || SM_SESSION_EVENT_CODE == bizMsg->m_requestType || SM_SESSION_EVENT_REFUND_CODE == bizMsg->m_requestType || (SM_SESSION_UPDATE_CODE == bizMsg->m_requestType && 1== m_smpara->GetCommonPara()->iUpdateUserStateSwitch))
	{
		userInfo->resultCode = m_smpara->checkUserStateInfo(userInfo->bizType, userInfo->szBasicState, userInfo->szExtStatus, userInfo->szStausCd, userInfo->szStopType);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "resultCode[%d]", userInfo->resultCode);
		if(bizMsg->m_serviceContextID == HRS && userInfo->resultCode == SM_OCP_USER_UNKNOWN)
		{
			bizMsg->m_userType = 1;
		}
		if(1 == userInfo->resultCode)
		{
			userInfo->isActive = 1;
			userInfo->resultCode = 0;
		}
		else if(userInfo->resultCode)
		{
			return userInfo->resultCode;
		}
	}
	
	if(VOICE!=bizMsg->m_serviceContextID && ISMP!=bizMsg->m_serviceContextID && HRS!=bizMsg->m_serviceContextID && SMS !=bizMsg->m_serviceContextID)
	{
		return 0;
	}
	DCUserBase userbase(m_smpara,pdbm);

	int ret = 0;
	if(VOICE==bizMsg->m_serviceContextID)
	{
		SCCRVOICE* data = pset->get<SCCRVOICE>();
		if(3 == bizMsg->m_cdrCallType || 4==bizMsg->m_cdrCallType)
		{
			ret = userbase.BlackNumber(base->subscription, bizMsg, &data->calling, &base->subscription);
		}
		else if(2 == bizMsg->m_cdrCallType)
		{
			ret = userbase.BlackNumber(base->subscription, bizMsg, &data->called, &data->calling);		
		}
		else
		{
			ret = userbase.BlackNumber(base->subscription, bizMsg, &data->calling, &data->called);				
		}
	}
	if(ISMP==bizMsg->m_serviceContextID || HRS ==bizMsg->m_serviceContextID)
	{
		SCCRISMP* data = pset->get<SCCRISMP>();
		ret = userbase.BlackNumber(base->subscription, bizMsg, &data->calling, &data->called);
	}
	if(SMS==bizMsg->m_serviceContextID)
	{
		SCCRSMS* data = pset->get<SCCRSMS>();
		ret = userbase.BlackNumber(base->subscription, bizMsg, &data->calling, &data->called);
	}
	//ret = userbase.BlackNumber(base->subscription, bizMsg, &data->calling, &data->called);

	if(1 == ret)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "the service type about of [%s] is black", base->subscription.phone.c_str());
		return SM_BLACK_LIST;		
	}
	else if(ret > 0)
	{
		return ret;
	}

	return 0;
}


DYN_PLUGIN_CREATE(DCBlackNumber, "FUNC_BLACKNUMBER", "FC_BlackNumber", "1.0.0")

