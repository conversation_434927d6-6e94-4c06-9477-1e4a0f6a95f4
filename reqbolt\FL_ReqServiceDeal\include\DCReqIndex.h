#ifndef _REQ_INDEX_H_
#define _REQ_INDEX_H_
#include <string>
#include <vector>
#include<set>
#include <map>
#include <iterator>
#include "time.h"
#include "md5.h"
struct TimeChain
{
	std::string key;
	time_t lastTime;
	TimeChain *pre;
	TimeChain *next;
	TimeChain()
	{
		lastTime = time(NULL);
		pre = NULL;
		next = NULL;
	}
	TimeChain(std::string inputKey)
	{
		key = inputKey;
		lastTime = time(NULL);
		pre = NULL;
		next = NULL;
	}
};
struct SeqTimeChain
{
	unsigned int key1;
	time_t lastTime;
	std::string key2;
	SeqTimeChain *pre;
	SeqTimeChain *next;
	SeqTimeChain()
	{
		lastTime = time(NULL);
		pre = NULL;
		next = NULL;
	}
	SeqTimeChain(unsigned int inputKey1,std::string inputKey2)
	{
		key1 = inputKey1; //seq
		key2 = inputKey2; //sessionmd5
		lastTime = time(NULL);
		pre = NULL;
		next = NULL;
	}
};
struct TempRGCount
{
	int RGSize;
	std::set<long> v_rg;
	SeqTimeChain *SeqTimePos;
};
struct ReqIndex
{
	std::vector<unsigned int> index;
	std::map<unsigned int,TempRGCount> v_TRequest;
	TimeChain *timePos;
};
class DCReqIndex
{
public:
	DCReqIndex();
	~DCReqIndex();
	void SetTimeoutSec(int sec,int SeqSec);
	bool IsDup(std::string &sessionID,unsigned int seq,bool IsXDrMsg,long RG = -1,int allNum = 1);
	int IsEmpty();
	int GetSize();
	int GetChainSize();
private:
	bool UpdateIndex(ReqIndex *req,unsigned int seq,bool isNeedToWrite);
	void ClearTImeoutChain();
	void InsertChain(TimeChain *p);
	void UpdateChain(TimeChain *p);

	void ClearSeqTimeOutChain();
	void InsertSeqChain(SeqTimeChain *p);
	void UpdateSeqChain(SeqTimeChain *p);
	void EraseSeqTimeChain(SeqTimeChain *p);
private:
	std::map<std::string,ReqIndex> m_index;
	TimeChain *head;
	TimeChain *tail;
	SeqTimeChain *Q_head;
	SeqTimeChain *Q_tail;
	time_t timeoutSec;
	time_t m_seqtimeoutSec;
	int chainSize;
};
#endif