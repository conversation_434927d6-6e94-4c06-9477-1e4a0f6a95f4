/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqVOICE.h
*Indentifier：
*
*Description：
*		语音业务处理类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
*		
********************************************/
#ifndef __DC_REQ_VOICE_H__
#define __DC_REQ_VOICE_H__
#include "DCReq.h"
#include "BizLenDef.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "TConfig.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "func_sqlindex.h"
#include "REMsgTypeDef.h"
#include "DCCommonIF.h"


class DCReqVOICE : public DCReq
{
	public:

		DCReqVOICE();
		virtual ~DCReqVOICE();

	public:

		virtual int Work(void *data);
		
		virtual int SwitchReqType(STBizMsg* bizMsg);
		virtual int Check(SCCRBase* base, STBizMsg* bizMsg);
		virtual int GetCallStartTime(long termtime, int overtime, char* value);
		virtual int sendCCA(STBizMsg* bizMsg);
};

#endif

