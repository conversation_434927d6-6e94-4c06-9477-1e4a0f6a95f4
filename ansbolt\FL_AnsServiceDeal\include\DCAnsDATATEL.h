/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsDATATEL.h
*Indentifier：
*
*Description：
*		数据业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_DATA_TEL_H__
#define __DC_ANS_DATA_TEL_H__

#include "DCAnsDATA.h"


class DCAnsDATATEL:public DCAnsDATA
{
	public:

		DCAnsDATATEL();
		virtual ~DCAnsDATATEL();

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);
		int XdrEvent(STBizMsg* bizMsg);
		int InitAns(STBizMsg* bizMsg);
		int UpdateAns(STBizMsg* bizMsg);
		int TermAns(STBizMsg* bizMsg);

		int SetCCAMsg(STBizMsg* bizMsg);
};

#endif

