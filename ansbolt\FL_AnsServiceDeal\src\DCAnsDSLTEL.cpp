#include "DCAnsDSLTEL.h"
#include "UStaMsg.h"
#include "DCCdrIndex.h"
DCAnsDSLTEL::DCAnsDSLTEL()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, SM_DSL_TYPE,  "", "", "");
}

DCAnsDSLTEL::~DCAnsDSLTEL()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, SM_DSL_TYPE,  "", "", "");
}

int DCAnsDSLTEL::ComposeCCA(STBizMsg* bizMsg)
{
	int ret 									= 0;
	int aocType 								= 0;

	char value[BIZ_TEMP_LEN_2048 + 1] 			= {0};
	int cost_unit 							= 0;
	int cost_amount 						= 0;
	int balance                             = 0;
	int nResultCode						= 0;
	int amountInfo = 0;
	long lEventTypeID = 0;//事件类型ID
	int nSendCCAFlag = 0;


	ocs::SCCRDataUnit TUSU 	 ;
	ocs::SCCRDataUnit USU 	 ;

	long lnRelastGsuTime = 0;

	int roamType = 0;
	long lnDiscountFee = 0;

	char szEndTime[20] = {0};
	char szCurrentTime[20] = {0};

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	ocs::rbresult* base = (ocs::rbresult*)bizMsg->m_base;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;

	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	int PayFlag = atoi(ext->kv["PayFlag"].c_str());


	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "get m_extend StragegyId[%ld]", StragegyId);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "get m_extend PayFlag[%d]", PayFlag);

	SREAInfo REAMsg ;


	long sessionStartTime = 0;
	long timing = 0;
	char szSessionSTime[20] = {0};
	long nDuration = 0;
	long nUnitInput = 0;
	long nUnitOutput = 0;
	long nUnitTotal = 0;

	long discount_fee                       = 0;
	long discount_totalfee                       = 0;

	int gUnit = 0;
	long long gNum = 0;

	if(base->gsv.size()>0)
	{
		gUnit = base->gsv[0].unit;
		gNum = base->gsv[0].amount;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "gunit[%d],gnum[%d]", gUnit, gNum);

	//info 日志
	DCDATLOG("SM00011:%d%ld", gUnit, gNum);

	UDBSQL *pQuery  = NULL;
	pQuery =  pdbm->GetSQL(DSL_GetSessionInfo);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);

		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(22, value);//SM_INT_FREE_FLAG
			int nFreeFlag=atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "free flag[%d]",nFreeFlag);

                     pQuery->GetValue(41, value);//OCP_INT_REQ_NBR
			bizMsg->m_requestNumber =atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "request number[%s]",value);

			pQuery->GetValue(2, value);//SM_INT_SESSION_STATUS
			int nStatu = atoi(value);

			//信令跟踪
			pQuery->GetValue(7, value);
			if(0==bizMsg->m_trace_flag)bizMsg->m_trace_flag=atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "SM_TRACE_NUM_ONFF[%s]", value);

			pQuery->GetValue(8, value);
			sessionStartTime = atol(value);
			sprintf(szSessionSTime,"%ld",sessionStartTime);

			pQuery->GetValue(9, value);
			timing = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "Current CCR time[%ld]", timing);

			pQuery->GetValue(23, value);//RE_LNG_LAST_GSU_TIME
			lnRelastGsuTime= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "last gsu time[%s]", value);

			pQuery->GetValue(10, bizMsg->m_subNumber);//RE_LNG_LAST_GSU_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "m_subNumber[%s]", bizMsg->m_subNumber);


			//总使用时长
			pQuery->GetValue(18, value);
			USU.duration = atoi(value);
			nDuration = USU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "duration[%u]", USU.duration);

			//总使用总流量
			pQuery->GetValue(19, value);
			USU.unitTotal = atol(value);
			nUnitTotal = USU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitTotal[%lld]", USU.unitTotal);

			//总使用上行流量
			pQuery->GetValue(20, value);
			USU.unitInput = atol(value);
			nUnitInput = USU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitInput[%lld]", USU.unitInput);

			//总使用下行流量
			pQuery->GetValue(21, value);
			USU.unitOutput = atol(value);
			nUnitOutput = USU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitOutput[%lld]", USU.unitOutput);


			//总使用时长
			pQuery->GetValue(3, value);
			TUSU.duration = atoi(value)+USU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "duration[%u]", TUSU.duration);

			//总使用总流量
			pQuery->GetValue(4, value);
			TUSU.unitTotal = atol(value)+USU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitTotal[%lld]", TUSU.unitTotal);

			//总使用上行流量
			pQuery->GetValue(5, value);
			TUSU.unitInput = atol(value)+USU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitInput[%lld]", TUSU.unitInput);

			//总使用下行流量
			pQuery->GetValue(6, value);
			TUSU.unitOutput = atol(value)+USU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitOutput[%lld]", TUSU.unitOutput);

			pQuery->GetValue(24, value);
          	lnDiscountFee = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "lnDiscountFee[%s],base dist_fee[%ld]", value,base->dist_fee);
			discount_totalfee = lnDiscountFee+base->dist_fee;


			pQuery->GetValue(25, value);
			strncpy(REAMsg.sz_balanceInfo,value,sizeof(REAMsg.sz_balanceInfo));// CDR_PUB_STR_BALANCEINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrBalanceInfo[%s]", value);

			pQuery->GetValue(26, value);
			strncpy(REAMsg.sz_balanceInfo2,value,sizeof(REAMsg.sz_balanceInfo2));// CDR_PUB_STR_BALANCEINFO2
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrBalanceInfo2[%s]", value);

			pQuery->GetValue(27, value);
			strncpy(REAMsg.szPricingPlanID,value,sizeof(REAMsg.szPricingPlanID));// CDR_PUB_STR_PRICING_PLAN_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrPlanId[%s]", value);

			pQuery->GetValue(28,value);
			strncpy(REAMsg.sz_accumlatorInfo,value,sizeof(REAMsg.sz_accumlatorInfo));// CDR_PUB_STR_ACCUMLATORINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrAccumInfo[%s]", value);

			pQuery->GetValue(29,value);
			strncpy(REAMsg.sz_tariffIdInfo,value,sizeof(REAMsg.sz_tariffIdInfo));// CDR_PUB_STR_TARIFFID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrTariffnfo[%s]", value);

			pQuery->GetValue(30, value);
			strncpy(REAMsg.sz_chargeInfo,value,sizeof(REAMsg.sz_chargeInfo));// CDR_PUB_STR_CHARGEINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrChargeInfo[%s]",value);

			//转售提醒
			pQuery->GetValue(31, value);
			bizMsg->m_userType= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "usertype [%s]", value);

			//转售提醒
			pQuery->GetValue(32, bizMsg->m_mvnoId);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "mvnoid [%s]", bizMsg->m_mvnoId);

			//pQuery->GetValue(36, bizMsg->m_serial);
			//DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "searial [%lld]", bizMsg->m_serial);

			pQuery->GetValue(42, value);
			strncpy(REAMsg.sz_oriChargeInfo,value,sizeof(REAMsg.sz_oriChargeInfo));// CDR_PUB_STR_ORICHARGE_INFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "oriChargeInfo[%s]",value);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "insert  execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "sub number=%s,requesttype=%d,requestnumber=%d", bizMsg->m_subNumber,bizMsg->m_requestType,bizMsg->m_requestNumber);


	if((SM_SESSION_UPDATE_CODE == bizMsg->m_requestType) || (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType))
	{
		ret = ModifyREAMsg(bizMsg,REAMsg);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_RATING_FAILED,  bizMsg->m_sessionID, "parse rating info failed");
			return SM_OCP_RATING_FAILED;
		}

		//获取B036,B037
		ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
		if(ret != RET_SUCCESS)
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "GetCostInfo error ", "");
		}

	}

	lEventTypeID = base->evt_id;;//CDR_LNG_ EVENT_TYPE_ID
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "",  "get R616[%ld]", lEventTypeID);

	if(SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		//获取B213
		balance = AccumlateBalance(bizMsg);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "",  "accumlate B213[%d]", balance);

	}

	//info 日志
	DCDATLOG("SM00012:%s%s%s%s%s%s%ld", REAMsg.sz_balanceInfo, REAMsg.sz_accumlatorInfo,\
										REAMsg.sz_tariffIdInfo, REAMsg.sz_chargeInfo,\
										REAMsg.sz_balanceInfo2, REAMsg.szPricingPlanID, lEventTypeID);

	UDBSQL *pExec = pdbm->GetSQL(DSL_UpdateSession_ans);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新返回信息
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, REAMsg.sz_balanceInfo);
			pExec->BindParam(2, REAMsg.sz_accumlatorInfo);
			pExec->BindParam(3, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(4, REAMsg.sz_chargeInfo);
			pExec->BindParam(5, bizMsg->m_resultcode);
			pExec->BindParam(6, STATUS_IDLE);
			pExec->BindParam(7, REAMsg.sz_balanceInfo2);
			pExec->BindParam(8, REAMsg.szPricingPlanID); // CDR_LNG_ PRICING_PLAN_ID
			pExec->BindParam(9, lEventTypeID);			 // CDR_LNG_ EVENT_TYPE_ID
			pExec->BindParam(10, (long)gNum);			 // RE_LNG_LAST_GSU_TIME
			pExec->BindParam(11, discount_totalfee);	 // CDR_DISCOUNT_FEE
			pExec->BindParam(12, (long)TUSU.duration);
			pExec->BindParam(13, TUSU.unitTotal);
			pExec->BindParam(14, TUSU.unitInput);
			pExec->BindParam(15, TUSU.unitOutput);
			pExec->BindParam(16, sessionStartTime);
			pExec->BindParam(17, timing);
			pExec->BindParam(18, 0);
			pExec->BindParam(19, (long)0);
			pExec->BindParam(20, (long)0);
			pExec->BindParam(21, StragegyId);
			pExec->BindParam(22, PayFlag);
			pExec->BindParam(23, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(24, OfrInstId.c_str());

			pExec->BindParam(25, bizMsg->m_sessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "DSL_UpdateSession execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "update session ok\n", "");

	//保存量本信息，累积量信息
	if(SM_SESSION_INITIAL_CODE != bizMsg->m_requestType)
	{
		int ret = UpsetRatingMsg(bizMsg);
		if(RET_ERROR == ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  bizMsg->m_sessionID,	"UpsetRatingMsg ERROR!","");
		}
	}

	if(1 == nSendCCAFlag)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "",  "ret cdr","");
		return RET_CDR;
	}

	//组装CCA消息
	ocs::SCCAMsg cca;
	ocs::DATAUSU gsu;

	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
                            cca.FinalFlag = base->final_flag;
				cca.GSUAns.gUnit = gUnit;
				if( gUnit== CREDITUNIT_TIMELEN)
					cca.GSUAns.duration = gNum;
				if( gUnit== CREDITUNIT_MONEY)
				{}
				if( gUnit== CREDITUNIT_TOTALVAL)
					cca.GSUAns.unitTotal = gNum;
				if( gUnit== CREDITUNIT_UPVAL)
					cca.GSUAns.unitInput= gNum;
				if( gUnit== CREDITUNIT_DOWNVAL)
					cca.GSUAns.unitOutput= gNum;

			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
                            cca.FinalFlag = base->final_flag;
				cca.GSUAns.gUnit = gUnit;
				if( gUnit== CREDITUNIT_TIMELEN)
					cca.GSUAns.duration = gNum;
				if( gUnit== CREDITUNIT_MONEY)
				{}
				if( gUnit== CREDITUNIT_TOTALVAL)
					cca.GSUAns.unitTotal = gNum;
				if( gUnit== CREDITUNIT_UPVAL)
					cca.GSUAns.unitInput= gNum;
				if( gUnit== CREDITUNIT_DOWNVAL)
					cca.GSUAns.unitOutput= gNum;
				cca.Cost.costUnit = cost_unit;
				cca.Cost.valueDigits = cost_amount;
				cca.Cost.exponent = 0;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
                            cca.FinalFlag = 0;
				cca.AOC.balance = balance;
				cca.Cost.costUnit = cost_unit;
				cca.Cost.valueDigits = cost_amount;
				cca.Cost.exponent = 0;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "unknow session state", "");
				return RB_SM_UNABLE_TO_COMPLY;
			}
			break;
	}


	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = bizMsg->m_resultcode;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 5;
	if(bizMsg->m_testFlag)
	{
		cca.BalaInfo = REAMsg.sz_balanceInfo;
		cca.AccuInfo = REAMsg.sz_accumlatorInfo;
		cca.TariInfo = REAMsg.sz_tariffIdInfo;
		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		cca.AcctInfo = flag;
	}

	// 写队列
	//ret = ProduceCCA(bizMsg, cca, bizMsg->m_anstopic);

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce DSL failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}

	DCCdrIndex cdrIndex;
	char startTime[16] = {0};
	sprintf(startTime,"%ld",sessionStartTime);
	char endTime[16] = {0};
	sprintf(endTime,"%ld",timing);
	cdrIndex.SetIndexInfo(bizMsg,startTime,endTime);
	string strIndex;
	cdrIndex.ToString(strIndex);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "cdrIndex[%s]", strIndex.c_str());
	/*bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce DSL failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID,  "Produce DSL Successful topic[%s]", bizMsg->m_anstopic);*/

	// 宽带业务超长截单 add by fangda 2018/11/21
	if (SM_SESSION_UPDATE_CODE ==  bizMsg->m_requestType)
	{
	   if(TUSU.duration >= smpara->GetDSLPara()->iDslLongCdrTime)
	   {
		   	bizMsg->m_longCDR = 1;//正常流程为1
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "long voice cdr,tusu[%d],config[%d]", TUSU.duration,smpara->GetDSLPara()->iDslLongCdrTime);
			return RET_CDR;//update 时直接返回到话单程序，term时需继续处理，term不处理超长
	    }
	}

	//释放会话
	if (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "",  "ret cdr","");
		return RET_CDR;
	}

	return RET_SUCCESS;
}
int DCAnsDSLTEL::XdrEvent(STBizMsg *bizMsg)
{
	int ret 									= 0;
	char value[BIZ_TEMP_LEN_2048 + 1] 			= {0};
	int cost_unit 							= 0;
	int cost_amount 						= 0;
	int balance                             = 0;
	long lEventTypeID = 0;//事件类型ID
	int nSendCCAFlag = 0;


	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	ocs::SCCRDataUnit TUSU 	 ;
	ocs::SCCRDataUnit USU 	 ;

	long lnRelastGsuTime = 0;
	long lnDiscountFee = 0;
	char szEndTime[20] = {0};
	char szCurrentTime[20] = {0};

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	int nAmount = atoi(ext->kv["nAmount"].c_str());
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	int PayFlag = atoi(ext->kv["PayFlag"].c_str());


	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "get m_extend StragegyId[%ld]", StragegyId);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "get m_extend PayFlag[%d]", PayFlag);

	SREAInfo REAMsg ;


	long sessionStartTime = 0;
	char szSessionSTime[20] = {0};
	long nDuration = 0;
	long nUnitInput = 0;
	long nUnitOutput = 0;
	long nUnitTotal = 0;

	long discount_fee                       = 0;
	long discount_totalfee                       = 0;

	int gUnit = 0;
	long long gNum = 0;

	if(base->gsv.size()>0)
	{
		gUnit = base->gsv[0].unit;
		gNum = base->gsv[0].amount;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "gunit[%d],gnum[%d]", gUnit, gNum);

	UDBSQL *pQuery  = NULL;
	pQuery =  pdbm->GetSQL(DSL_GetSessionInfo);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_childsessionID);

		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(22, value);//SM_INT_FREE_FLAG
			int nFreeFlag=atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "free flag[%d]",nFreeFlag);

                     pQuery->GetValue(41, value);//OCP_INT_REQ_NBR
			bizMsg->m_requestNumber =atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "request number[%s]",value);

			pQuery->GetValue(2, value);//SM_INT_SESSION_STATUS
			int nStatu = atoi(value);

			//信令跟踪
			pQuery->GetValue(7, value);
			if(0==bizMsg->m_trace_flag)bizMsg->m_trace_flag=atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "SM_TRACE_NUM_ONFF[%s]", value);


			pQuery->GetValue(9, szSessionSTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szSessionSTime[%s]", szSessionSTime);

			pQuery->GetValue(23, value);//RE_LNG_LAST_GSU_TIME
			lnRelastGsuTime= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "last gsu time[%s]", value);

			pQuery->GetValue(10, bizMsg->m_subNumber);//RE_LNG_LAST_GSU_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "m_subNumber[%s]", bizMsg->m_subNumber);


			//使用时长
			pQuery->GetValue(18, value);
			USU.duration = atoi(value);
			nDuration = USU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "duration[%lld]", USU.duration);
			//使用总流量
			pQuery->GetValue(19, value);
			USU.unitTotal = atol(value);
			nUnitTotal = USU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitTotal[%lld]", USU.unitTotal);

			//使用上行流量
			pQuery->GetValue(20, value);
			USU.unitInput = atol(value);
			nUnitInput = USU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitInput[%lld]", USU.unitInput);

			//使用下行流量
			pQuery->GetValue(21, value);
			USU.unitOutput = atol(value);
			nUnitOutput = USU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitOutput[%lld]", USU.unitOutput);

			//总使用时长
			pQuery->GetValue(3, value);
			TUSU.duration = atoi(value)+USU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "all duration[%u]", TUSU.duration);

			//总使用总流量
			pQuery->GetValue(4, value);
			TUSU.unitTotal = atol(value)+USU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "all unitTotal[%lld]", TUSU.unitTotal);

			//总使用上行流量
			pQuery->GetValue(5, value);
			TUSU.unitInput = atol(value)+USU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "all unitInput[%lld]", TUSU.unitInput);

			//总使用下行流量
			pQuery->GetValue(6, value);
			TUSU.unitOutput = atol(value)+USU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "all unitOutput[%lld]", TUSU.unitOutput);

			pQuery->GetValue(24, value);
          	       lnDiscountFee = atol(value);
			discount_totalfee = lnDiscountFee+base->dist_fee;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "lnDiscountFee[%s]", value);

			pQuery->GetValue(25, value);
			strncpy(REAMsg.sz_balanceInfo,value,sizeof(REAMsg.sz_balanceInfo));// CDR_PUB_STR_BALANCEINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrBalanceInfo[%s]", value);

			pQuery->GetValue(26, value);
			strncpy(REAMsg.sz_balanceInfo2,value,sizeof(REAMsg.sz_balanceInfo2));// CDR_PUB_STR_BALANCEINFO2
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrBalanceInfo2[%s]", value);

			pQuery->GetValue(27, value);
			strncpy(REAMsg.szPricingPlanID,value,sizeof(REAMsg.szPricingPlanID));// CDR_PUB_STR_PRICING_PLAN_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrPlanId[%s]", value);

			pQuery->GetValue(28,value);
			strncpy(REAMsg.sz_accumlatorInfo,value,sizeof(REAMsg.sz_accumlatorInfo));// CDR_PUB_STR_ACCUMLATORINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrAccumInfo[%s]", value);

			pQuery->GetValue(29,value);
			strncpy(REAMsg.sz_tariffIdInfo,value,sizeof(REAMsg.sz_tariffIdInfo));// CDR_PUB_STR_TARIFFID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrTariffnfo[%s]", value);

			pQuery->GetValue(30, value);
			strncpy(REAMsg.sz_chargeInfo,value,sizeof(REAMsg.sz_chargeInfo));// CDR_PUB_STR_CHARGEINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "szCdrChargeInfo[%s]",value);

			//pQuery->GetValue(36, bizMsg->m_serial);
			//DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "searial [%lld]", bizMsg->m_serial);

			pQuery->GetValue(42, value);
			strncpy(REAMsg.sz_oriChargeInfo,value,sizeof(REAMsg.sz_oriChargeInfo));// CDR_PUB_STR_ORICHARGE_INFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "oriChargeInfo[%s]", value);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "sub number=%s,requesttype=%d,requestnumber=%d", bizMsg->m_subNumber,bizMsg->m_requestType,bizMsg->m_requestNumber);


	ret = ModifyREAMsg(bizMsg,REAMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_RATING_FAILED,  bizMsg->m_sessionID, "parse rating info failed");
		return SM_OCP_RATING_FAILED;
	}

	//获取B036,B037
	ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "GetCostInfo error ", "");
	}

	lEventTypeID = base->evt_id;;//CDR_LNG_ EVENT_TYPE_ID
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "",  "get R616[%ld]", lEventTypeID);

	//获取B213
	balance = AccumlateBalance(bizMsg);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "",  "accumlate B213[%d]", balance);

	memset(szCurrentTime,0x00,sizeof(szCurrentTime));
	memset(szEndTime,0x00,sizeof(szEndTime));
	if(nAmount > 0 && ext->kv["eliminateflag"] == "1")//时长计费
    {
		strcpy(szEndTime,szSessionSTime);
		DCCommonIF::SetSecTimeToDate(DCCommonIF::dateToSec(szEndTime) - nAmount+1, szCurrentTime);
    }
    else//非时长计费
    {
		strcpy(szEndTime,szSessionSTime);
		DCCommonIF::SetSecTimeToDate(DCCommonIF::dateToSec(szEndTime) - nDuration, szCurrentTime);
    }
	if(ext->kv["eliminateflag"] == "1")
	{
		int Measure = atoi(ext->kv["Measure"].c_str());
		switch(Measure)
		{
			case 1:
				nDuration = nAmount;
			break;
			case 3:
				nUnitTotal = nAmount;
			break;
			case 4:
				nUnitInput = nAmount;
			break;
			case 5:
				nUnitOutput = nAmount;
			break;
			default:
				break;
		}
	}
	UDBSQL *pExec = pdbm->GetSQL(DSL_UpdateSession_ans);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新返回信息
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, REAMsg.sz_balanceInfo);
			pExec->BindParam(2, REAMsg.sz_accumlatorInfo);
			pExec->BindParam(3, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(4, REAMsg.sz_chargeInfo);
			pExec->BindParam(5, bizMsg->m_resultcode);
			pExec->BindParam(6, STATUS_IDLE);
			pExec->BindParam(7, REAMsg.sz_balanceInfo2);
			pExec->BindParam(8, REAMsg.szPricingPlanID); // CDR_LNG_ PRICING_PLAN_ID
			pExec->BindParam(9, lEventTypeID);			 // CDR_LNG_ EVENT_TYPE_ID
			pExec->BindParam(10, (long)gNum);			 // RE_LNG_LAST_GSU_TIME
			pExec->BindParam(11, discount_totalfee);	 // CDR_DISCOUNT_FEE
			pExec->BindParam(12, nDuration);
			pExec->BindParam(13, nUnitTotal);
			pExec->BindParam(14, nUnitInput);
			pExec->BindParam(15, nUnitOutput);
			pExec->BindParam(16, atol(szCurrentTime)); // RE_LNG_CALL_START_TIME
			pExec->BindParam(17, atol(szEndTime));	   // RE_LNG_CURRENT_CCR_TIME
			pExec->BindParam(18, 0);
			pExec->BindParam(19, (long)cost_amount);
			pExec->BindParam(20, (long)balance);
			pExec->BindParam(21, StragegyId);
			pExec->BindParam(22, PayFlag);
			pExec->BindParam(23, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(24, OfrInstId.c_str());

			pExec->BindParam(25, bizMsg->m_childsessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "DSL_UpdateSession execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "update session ok\n", "");


	// 拆分出的子会话直接出单
	if(bizMsg->m_spiltflag > 1)
	{
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID,  "to cdr");
            return RET_CDR;
	}

	//组装CCA消息
	ocs::SCCAMsg cca;

	cca.sessionID = bizMsg->m_sessionID;
       cca.sessionID.erase(0,3);
	cca.resultCode = bizMsg->m_resultcode;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.AOC.balance = balance;
	cca.Cost.costUnit = cost_unit;
	cca.Cost.valueDigits = cost_amount;
	cca.Cost.exponent = 0;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 5;
	if(bizMsg->m_testFlag)
	{
		cca.BalaInfo = REAMsg.sz_balanceInfo;
		cca.AccuInfo = REAMsg.sz_accumlatorInfo;
		cca.TariInfo = REAMsg.sz_tariffIdInfo;
		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		cca.AcctInfo = flag;
	}

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce DSL failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCCdrIndex cdrIndex;
	cdrIndex.SetIndexInfo(bizMsg,szCurrentTime,szEndTime);
	/*bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce DSL failed topic[%s]", bizMsg->m_anstopic);
	}
       else
       {
	    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID,  "Produce DSL Successful topic[%s]", bizMsg->m_anstopic);
       }*/

	return RET_CDR;
}
