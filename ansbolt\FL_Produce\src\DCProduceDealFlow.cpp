#include "DCProduceDealFlow.h"
#include "DCMqProduceServer.h"
#include "DCRbMsgDef.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "DCBizMsgDef.h"
#include "BizDataDef.h"
#include <string>
#include "ErrorCode.h"
using std::string;


int DCProduceDealFlow::init()
{	
	return 0;
}

int DCProduceDealFlow::process(void* input, void* output)
{
	int ret = 0;

	string* data = (string*)input;
	STBizMsg* bizMsg = (STBizMsg* )output;

	DCMqProduceServer* producer = (DCMqProduceServer*)bizMsg->m_producer;	
	if(bizMsg->m_topictype == 1)
	{
              if(bizMsg->m_anstopic[0])
              {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  bizMsg->m_sessionID, "produce msg topic[%s]", bizMsg->m_anstopic);
		      ret = producer->Produce((*data).c_str(), (*data).length(), bizMsg->m_anstopic,"",bizMsg->m_uid);
              }
              else
              {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "" "empty topic, ignore");
              }
	}
	else if(bizMsg->m_topictype == 2)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "produce m_cdrTopic msg topic[%s]", bizMsg->m_cdrTopic);
		ret = producer->Produce((*data).c_str(), (*data).length(), bizMsg->m_cdrTopic,"",bizMsg->m_uid);
	}
	else if(bizMsg->m_topictype == 4)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "produce m_payflagTopic msg topic[%s]", bizMsg->m_payflagTopic);
		ret = producer->Produce((*data).c_str(), (*data).length(), bizMsg->m_payflagTopic,"",bizMsg->m_uid);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "produce msg topic[%s]", bizMsg->m_testcdrTopic);
		ret = producer->Produce((*data).c_str(), (*data).length(), bizMsg->m_testcdrTopic,"",bizMsg->m_uid);
	}
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "produce msg  failed,ret[%d],content:\n[%s]\n", ret, (*data).c_str());
		ret =  RET_ERROR;
	} 
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "produce msg succeed[%s]", (*data).c_str());
		ret =  RET_SUCCESS;
	}
	
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "CALL RET=%d",ret);
	return ret;

}


DYN_PLUGIN_CREATE(DCProduceDealFlow, "FC_PRODUCEDEAL", "FC_ProduceDeal", "1.0.0")

