/*
 * CTestBoltAns.h
 *
 *  Created on: 2015年6月4日
 *      Author: tydic
 */

#ifndef TEST_CTESTBOLT_ANS_H_
#define TEST_CTESTBOLT_ANS_H_

#include <DCBolt.h>
#include <stdio.h>
#include "DCOBJSet.h"
#include "DCSeriaOp.h"
#include "DCMqProduceServer.h"
#include "DCParseXml.h"
#include "DCLogMacro.h"

class DCPerfTimeStats;
class CTestBoltAns: public tydic::storm::DCBolt {
public:
	CTestBoltAns();
	virtual ~CTestBoltAns();

public:
	int Initialize(const tydic::storm::DCStormConfig& config);
	int Process(tydic::storm::Tuple &tuple);

private:
	DCOBJSetPool* m_pool;
	DCSeriaDecoder m_de;
	
	DCSeriaPrinter m_print;	
	DCSeriaEncoder m_en;
	DCMqProduceServer* m_producer;
	const char* m_Topic[5];
	DCPerfTimeStats*  m_tstat;
};

extern "C" {

tydic::storm::DCStormBase* CreateInstance()
{
	return new CTestBoltAns();
}

void ReleaseInstance(tydic::storm::DCStormBase* pStorm)
{
	if (pStorm != NULL)
		delete pStorm;
}

}

#endif /* TEST_CTESTBOLT_H_ */
