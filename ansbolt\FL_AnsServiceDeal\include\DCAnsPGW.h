/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsPGW.h
*Indentifier：
*
*Description：
*		PGW处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_PGW_H__
#define __DC_ANS_PGW_H__

#include "DCAnsDATA.h"


class DCAnsPGW:public DCAnsDATA
{
	public:

		DCAnsPGW();
		virtual ~DCAnsPGW();

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);
		
		int InitAns(STBizMsg* bizMsg);
		
		int UpdateAns(STBizMsg* bizMsg);
		
		int TermAns(STBizMsg* bizMsg);

		int XdrEvent(STBizMsg* bizMsg);

		int JudgeCutCDR(STBizMsg* bizMsg, ocs::SCCRDataUnit& TUSU, const char* szNewLocInfo, const char* szOldLocInfo);
};

#endif

