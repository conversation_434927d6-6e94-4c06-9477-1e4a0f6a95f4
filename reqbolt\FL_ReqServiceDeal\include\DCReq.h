/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReq.h
*Indentifier：
*
*Description：
*		CCR请求消息处理
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_H__
#define __DC_REQ_H__
#include "DCOcpMsgDef.h"
#include "DCSeriaOp.h"
using namespace ocs;



class DCReq
{
	public:

		DCReq();
		virtual ~DCReq();

	public:

		virtual int Work(void *data);



	public:		
		DCSeriaEncoder m_en;		
		DCSeriaPrinter m_print;
		

};

#endif
