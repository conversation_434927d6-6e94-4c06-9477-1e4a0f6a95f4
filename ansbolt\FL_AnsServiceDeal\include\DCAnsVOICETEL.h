/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsVOICETEL.h
*Indentifier：
*
*Description：
*		语音业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_VOICE_TEL_H__
#define __DC_ANS_VOICE_TEL_H__

#include "DCAnsVOICE.h"

class DCAnsVOICETEL : public DCAnsVOICE
{
	public:

		DCAnsVOICETEL();
		virtual ~DCAnsVOICETEL();

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);

		virtual int XdrEvent(STBizMsg* bizMsg);
};

#endif

