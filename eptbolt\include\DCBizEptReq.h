/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizEptReq.h
*Indentifier：
*		
*Description：
*		请求异常处理类
*Version：
*		
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DCBIZ_EPT_REQ_H__
#define __DCBIZ_EPT_REQ_H__

#include "DCBasePlugin.h"
#include "DCBizEpt.h"
#include "DCAnsPara.h"
//
class DCBizEptReq :  public DCBizEpt,public DCBasePlugin
{
	public:

		DCBizEptReq(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCBizEptReq()
		{
		
		}

		virtual int Work(void *data);


	protected:
		virtual int init();
		virtual int process(void* input, void* output);
		

		virtual int SwitchCommon(STBizMsg* bizMsg);
		virtual int SwitchDATA(STBizMsg* bizMsg);
		virtual int Switch5G(STBizMsg * bizMsg);
		
		int InitReq(STBizMsg* bizMsg);
		
		int Update(STBizMsg* bizMsg);
		int UpdateDATA(STBizMsg* bizMsg);
		int Update5G(STBizMsg* bizMsg);
		
		int Term(STBizMsg* bizMsg);
		int TermDATA(STBizMsg* bizMsg);
		int Term5G(STBizMsg* bizMsg);
		
		int Event(STBizMsg* bizMsg);
		int XDR(STBizMsg* bizMsg);
	private:
		DCAnsPara* m_anspara;
	
};

#endif

