﻿#include "DCEptBolt.h"
#include "DCStormProtocol.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include <ace/Log_Msg.h>
#include <ace/Log_Msg_Backend.h>
#include <ace/Log_Record.h>
#include "DCSeriaOp.h"
#include "DCPerfStatistic.h"
#include "DCDBManer.h"
#include "DCMCastEvtFun.h"
#include "DCTCompress.h"
#include <sys/types.h>
#include <ifaddrs.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
//#include "DCEventTracking.h"

using namespace std;


void svc();
static void* work_routine(void* handle)
{
	DCEptBolt* imp = static_cast<DCEptBolt*>(handle);
	imp->svc();
	return NULL;
}


class DCACELogAgent : public ACE_Log_Msg_Backend
{
public:
	DCACELogAgent(){}
	virtual ~DCACELogAgent(){}

	virtual int 	open (const ACE_TCHAR *logger_key){}
	virtual int 	reset (void){}
	virtual int 	close (void){}
	virtual ssize_t log (ACE_Log_Record &logr)
	{
		int level = 0;
		switch (logr.priority())
		{
			case 1:
				level = DCLOG_LEVEL_TRACE;
				break;
			case 2:
				level = DCLOG_LEVEL_DEBUG;
				break;
			case 3:
                            level = DCLOG_LEVEL_DVIEW;
                            break;
			case 4:
				level = DCLOG_LEVEL_INFO;
				break;
			case 5:
				level = DCLOG_LEVEL_WARN;
				break;
			case 6:
				level = DCLOG_LEVEL_ERROR;
				break;
			case 7:
				level = DCLOG_LEVEL_FATAL;
				break;
			default:
				level = DCLOG_LEVEL_ERROR;
				break;
		}
		DCSYSLOG(level, 0, "L%lu|%s", logr.priority(), logr.msg_data());
            return logr.msg_data_len();
	}
};

DCEptBolt::DCEptBolt():m_en(NULL)
{

}

DCEptBolt::~DCEptBolt()
{
	if(m_en)
	{
		delete m_en;
	}
}

int DCEptBolt::Refresh(const char * path)
{
	return 0;
}

int DCEptBolt::SetWacther()
{
	//监听
	return 0;
}

int DCEptBolt::Initialize(const tydic::storm::DCStormConfig& config)
{
	int ret = 0;
	char buf[512] = {0};
	m_checktime = 0;
	ACE_LOG_MSG->clr_flags(ACE_Log_Msg::STDERR);
       ACE_LOG_MSG->msg_backend( new DCACELogAgent() );
       ACE_LOG_MSG->priority_mask( LM_DEBUG|LM_INFO|LM_NOTICE|LM_WARNING|LM_ERROR|LM_CRITICAL, ACE_Log_Msg::PROCESS);
       ACE_LOG_MSG->open("ace", ACE_Log_Msg::CUSTOM);

	char *szconfig =getenv("OCS_CONFIG");
	if(NULL==szconfig)
	{
		return -1;
	}

	string val = (const_cast<tydic::storm::DCStormConfig&>(config)).GetConfig("billing.cfg.spec");
	sprintf(buf,"%s/sm_cfg%s.xml",szconfig,val.c_str());
	ret = DCParseXml::Instance()->Init("SM",buf);
	if(ret)
	{
		return -1;
	}

       int taskid = atoi(config.GetTaskId().c_str());
	const char* topology = GetTopology().c_str();

        //
	const char* logpath = DCParseXml::Instance()->GetParam("logAddr","Common/log");
	int loglevel  = atoi(DCParseXml::Instance()->GetParam("level","Common/log"));
	//--------------------------------性能日志级别、阈值设置-------------------------------------//
	int perf_level = 0;//性能日志级别设置
	const char* perf_level_param = DCParseXml::Instance()->GetParam("perf","Common/log");
	if(perf_level_param)
	{
		perf_level = atoi(perf_level_param);
	}
	int perf_threshold = 50;//性能日志阈值获得
	const char* perf_threshold_param = DCParseXml::Instance()->GetParam("perf.ms","Common/log");
	if(perf_threshold_param)
	{
		perf_threshold = atoi(perf_threshold_param);
	}
	//日志初始化
	ret = DCLOGINIT("ocs","sm_eptbolt",loglevel,logpath);
	if(ret)
	{
	  return -1;
	}
	DCLOG_SETLEVEL(DCLOG_CLASS_PERF,perf_level);
	DCLOG_SETCTL(DCLOG_MASK_PERF,perf_threshold*1000);//单位转化为微秒


       DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d, module:%s",logpath, loglevel, "sm_eptbolt");
       DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init log successful, topology[%s] taskid[%d]", topology, taskid);

	//插件初始化
	if(val.empty())
		ret = m_pm.init(szconfig, "sm",DFM_USE_DBM|DFM_USE_REFRESH|DFM_USE_NOFLOW);
	else
	{
		sprintf(buf,"sm|%s",val.c_str());
		ret = m_pm.init(szconfig, buf,DFM_USE_DBM|DFM_USE_REFRESH|DFM_USE_NOFLOW);
	}
	if(ret)
	{
	       DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin failed");
		return 1;
	}

	ret = m_pm.load_flow("eptbolt");
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin eptbolt failed");
		return 1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init plugin successful");
	strncpy(m_Topic,DCParseXml::Instance()->GetParam("cdr","SM/queue"),sizeof(m_Topic));

	strncpy(m_payflagTopic,DCParseXml::Instance()->GetParam("PayFlagCdr","SM/queue"),sizeof(m_payflagTopic));
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get cdr topic[%s],  PayFlagCdr[%s]",m_Topic,m_payflagTopic);

	strncpy(m_testTopic,DCParseXml::Instance()->GetParam("AnalogCdr","SM/queue"),sizeof(m_testTopic));
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get AnalogCdr topic[%s]",m_testTopic);

	m_en = new DCSeriaEncoder(ESeriaBinString);

	// 获取本机IP
	GetHostIp(m_strIP);
	// 埋点线程
	//DCEvtTrk::instance()->Head("Billing","SM","DCEptBolt",m_strIP.c_str(),topology);
	ret = pthread_create(&m_tid, NULL, work_routine, this);
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,ret,"","create thread failed: %d", ret);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,ret,"","create thread success");
	}

    //指标定义begin
    const char* pszParamValue = NULL;
    //埋点延时
    pszParamValue = DCParseXml::Instance()->GetParam("KpiDelayMs","SM/BPoint");
    if(pszParamValue==NULL || strcmp(pszParamValue,"")==0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get BPoint/KpiDelayMs fail.");
        return -1;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get BPoint/KpiDelayMs: %s", pszParamValue);
        DCKpiSender::instance()->SetParam("delay", pszParamValue);
    }

    //埋点开关
    pszParamValue = DCParseXml::Instance()->GetParam("KpiFlag","SM/BPoint");
    if(pszParamValue==NULL || strcmp(pszParamValue,"")==0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get BPoint/KpiFlag fail.");
        return -1;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get BPoint/KpiFlag: %s", pszParamValue);
        DCKpiSender::instance()->SetParam("flag", pszParamValue);
    }
    // bolt设置指标发送接口
    //DCKpiSender::instance()->SetSenderCallback(my_kpi_callback_sendkpi, this);


    if (DCKpiSender::instance()->Init() != 0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "DCKpiSender init fail. error_code=%d, error_info=%s", DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
        return -1;
    }

    //处理的本地网列表
    pszParamValue = DCParseXml::Instance()->GetParam("KpiLatn","SM/BPoint");
    if(pszParamValue==NULL || strcmp(pszParamValue,"")==0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get BPoint/KpiLatn fail.");
        return -1;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get BPoint/KpiLatn: %s",pszParamValue);
    }
    std::list<string> v_latn;
    string latn = pszParamValue;
    SplitString(latn,'|',v_latn);
    m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
    if (m_ptrBPMon)
    {
        DCKpiSender::instance()->group_all_init(m_ptrBPMon, "TraceKpi", v_latn);
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","monitor init fail. error code:%d, error info:%s",DCKpiSender::instance()->ErrorCode(),DCKpiSender::instance()->ErrorInfo());
        return -1;
    }

	//注册统计指标
	m_tstat = m_pm.get_statistic()->get_position("eptbolt");

       //注册监听事件
      /* BoltEventFun evfun(m_pm.get_mcm(), m_pm.get_drf());
       evfun.register_event(topology, taskid, "eptbolt");*/
   DCMCastManer* m_mcm = new DCMCastManer();
   const char* mcast = getenv("OCS_MCAST_CMD_ADDR");
   if(mcast)
   {
	   ret = m_mcm->init(mcast);
	   if(ret < 0)
	   {
		   DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init  DCMCastManer failed: %s", strerror(errno));
	   }
	   DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init  DCMCastManer success");

	   LogEventFun evfun(m_mcm);
	   evfun.register_event("eptbolt");
   }
	return 0;
}

void DCEptBolt::SplitString(const std::string& str, char sep, std::list<std::string>& vec)
{
    size_t pos = 0;
    size_t prev = 0;
    std::string sub;
    bool brun = true;
    vec.clear();
    while(brun)
    {
        pos = str.find(sep, prev);
        if(pos == std::string::npos)
        {
            sub = str.substr(prev);
            brun = false;
        }
        else
        {
            sub = str.substr(prev, pos-prev);
            prev = pos+1;
        }
        vec.push_back(sub);
    }
}

int DCEptBolt::Process(tydic::storm::Tuple &tuple)
{
	DCPerfTimeVCollect collet(m_tstat, true);
	std::string msginfo = tuple.GetValues(0);
	int taskid  = tuple.GetTaskID(msginfo);
	std::string recvEptMsg = tuple.GetValues(1);
	int msgsize = tuple.GetSize();
	string uuid;
    int java_que_size = tuple.GetExecuteQSize();
	char buf[20]={0};
	char servAddr[25]={0};
	long jm_usec = 0;
	DCEvtCheck::instance()->ClearImp();

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","sseq[%s]", msginfo.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","recv MSG[%s]", recvEptMsg.c_str());

	if(recvEptMsg.length()<10)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","msg error");
		tydic::storm::Tuple tRnd;
		tRnd.SetValues("1");
		tRnd.SetValues("");
		tydic::storm::EmitDirect(-1,tRnd);
		tydic::storm::exeAck(tuple.GetID());

		return 0;
	}

	DCBaseFlow* flow = m_pm.get_flow("eptbolt");
	if(!flow)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find flow[FL_EptBaseFLOW]");
		tydic::storm::Tuple tRnd;
		tRnd.SetValues("1");
		tRnd.SetValues("");
		tydic::storm::EmitDirect(-1,tRnd);
		tydic::storm::exeAck(tuple.GetID());

		return 1;
	}
	strncpy(servAddr, recvEptMsg.c_str(), 25);
	recvEptMsg.erase(0, 25);

	// 获取头部固定16字节
	strncpy(buf, recvEptMsg.c_str(), 16);
	jm_usec = collet.m_begin.tv_sec*1000000 + collet.m_begin.tv_usec - strtol(buf, NULL, 10);

	// 删除头部16字节
	recvEptMsg.erase(0, 16);

	//数据库重连
	time_t cursec = time(NULL);
	if((m_checktime+300) < cursec)
	{
		m_pm.get_dbm()->CheckReset();
		m_checktime = cursec;
	}
	else
	{
		m_pm.get_dbm()->FastReset();
	}
	std::multimap<string,string> mSendMsg;
	mSendMsg.clear();
	char szTaskId[16] = {0};
	sprintf(szTaskId,"%d",taskid);
	mSendMsg.insert(pair<string,string>("TaskId",szTaskId));

    int ret = flow->call(&recvEptMsg, &mSendMsg);

	std::multimap<string,string>::iterator iter;
	std::multimap<string,string>::iterator itBeg;
	std::multimap<string,string>::iterator itEnd;

	iter = mSendMsg.find("TaskId");
	if(iter != mSendMsg.end())
	{
		taskid = atoi(iter->second.c_str());
	}

	string strUID ;
	iter = mSendMsg.find("UID");
	if(iter != mSendMsg.end())
	{
		strUID = iter->second;
	}

	string sLatnId;
	iter = mSendMsg.find("LatnId");
	if(iter != mSendMsg.end())
	{
		sLatnId = iter->second;
	}
	/*
	iter = mSendMsg.find("UID");
	if(iter != mSendMsg.end())
	{
		tuple.SetUidID("uuid",iter->second,msginfo);
		uuid = iter->second;
	}
	iter = mSendMsg.find("AnsTopic");
	if(iter != mSendMsg.end())
	{
		tuple.SetUidID("serviceName",iter->second,msginfo);
	}


	itBeg = mSendMsg.lower_bound("CDRTOPIC");
	itEnd = mSendMsg.upper_bound("CDRTOPIC");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		string sMsg = iter->second;
		Compress(sMsg);
		tydic::storm::Tuple tuple2Mqbolt;
		tuple2Mqbolt.SetValues(msginfo);
		tuple2Mqbolt.SetValues(sMsg);
		tuple2Mqbolt.SetValues(m_Topic);
		string strEndFlag = "1";
		strEndFlag += "|";
		strEndFlag += m_strIP;

		strEndFlag += "|";
		char sResultCode[64]={0};
		sprintf(sResultCode, "%d", "0");
		strEndFlag += sResultCode;


		tuple2Mqbolt.SetValues(strEndFlag);
		tydic::storm::EmitDirect(-1, tuple2Mqbolt, "DCMqBoltStream");

		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to MqBolt sseq[%s]	msgsize[%d] topic[%s].",msginfo.c_str(),  iter->second.size(), m_Topic);
	}

	itBeg = mSendMsg.lower_bound("PAYFLAGTOPIC");
	itEnd = mSendMsg.upper_bound("PAYFLAGTOPIC");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		string sMsg = iter->second;
		Compress(sMsg);
		tydic::storm::Tuple tuple2Mqbolt;
		tuple2Mqbolt.SetValues(msginfo);
		tuple2Mqbolt.SetValues(sMsg);
		tuple2Mqbolt.SetValues(m_payflagTopic);
		string strEndFlag = "1";
		strEndFlag += "|";
		strEndFlag += m_strIP;

		strEndFlag += "|";
		char sResultCode[64]={0};
		sprintf(sResultCode, "%d", "0");
		strEndFlag += sResultCode;


		tuple2Mqbolt.SetValues(strEndFlag);
		tydic::storm::EmitDirect(-1, tuple2Mqbolt, "DCMqBoltStream");

		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to MqBolt sseq[%s]	msgsize[%d] topic[%s].",msginfo.c_str(),  iter->second.size(), m_payflagTopic);
	}

	itBeg = mSendMsg.lower_bound("TESTCDRTOPIC");
	itEnd = mSendMsg.upper_bound("TESTCDRTOPIC");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		string sMsg = iter->second;
		Compress(sMsg);
		tydic::storm::Tuple tuple2Mqbolt;
		tuple2Mqbolt.SetValues(msginfo);
		tuple2Mqbolt.SetValues(sMsg);
		tuple2Mqbolt.SetValues(m_testTopic);
		string strEndFlag = "1";
		strEndFlag += "|";
		strEndFlag += m_strIP;

		strEndFlag += "|";
		char sResultCode[64]={0};
		sprintf(sResultCode, "%d", "0");
		strEndFlag += sResultCode;


		tuple2Mqbolt.SetValues(strEndFlag);
		tydic::storm::EmitDirect(-1, tuple2Mqbolt, "DCMqBoltStream");

		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to MqBolt sseq[%s]	msgsize[%d] topic[%s].",msginfo.c_str(),  iter->second.size(), m_testTopic);
	}
	*/
	DCDATLOG("SM00019:%d",ret);
	itBeg = mSendMsg.lower_bound("LOGRATING");
	itEnd = mSendMsg.upper_bound("LOGRATING");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		tydic::storm::Tuple tuple2Stabolt;
		tuple2Stabolt.SetValues(msginfo);
		tuple2Stabolt.SetValues(iter->second);
		tuple2Stabolt.SetValues(strUID);
		tydic::storm::EmitDirect(-1,tuple2Stabolt,"StaBoltStream");
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to StaBolt LOGRATING sseq[%s] msgsize[%d].",msginfo.c_str(),	iter->second.size());
	}

	itBeg = mSendMsg.lower_bound("CDRMSG");
	itEnd = mSendMsg.upper_bound("CDRMSG");
	for(iter = itBeg; iter != itEnd; ++iter)
	{
		tydic::storm::Tuple tuple2Stabolt;
		tuple2Stabolt.SetValues(msginfo);
		tuple2Stabolt.SetValues(iter->second);
		tuple2Stabolt.SetValues(strUID);
		tydic::storm::EmitDirect(-1,tuple2Stabolt,"StaBoltStream");
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"","to StaBolt CDRMSG LOGRATING sseq[%s] msgsize[%d].",msginfo.c_str(),	iter->second.size());
	}

	//头部加上固定16位时间戳
	struct timeval tmv;
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	int idup = 0;
	string tmpmsginfo = ReSetUid(msginfo, idup,strUID);
	if(mSendMsg.size() != 0)
	{
		iter = mSendMsg.find("CCAMSG");
		if(iter != mSendMsg.end())
		{
		    string cca = iter->second;
			tydic::storm::Tuple tRnd;
			cca.insert(0, servAddr,25);
			cca.insert(25, buf,16);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","taskid[%d],cca :%s\n",taskid,cca.c_str());
			tRnd.SetValues(tmpmsginfo);
			tRnd.SetValues(cca);
			tydic::storm::EmitDirect(taskid,tRnd,"EndBoltStream");	

			DCEvtCheck::instance()->Push_O("ChfpA",strUID);
		}

		string sBillingNbr;
		iter = mSendMsg.find("BillingNbr");
		if(iter != mSendMsg.end())
		{
			sBillingNbr = iter->second;
		}

		itBeg = mSendMsg.lower_bound("RBRMSG");
		itEnd = mSendMsg.upper_bound("RBRMSG");
		for(iter = itBeg; iter != itEnd; ++iter)
		{
			string strMsg = iter->second;
			tydic::storm::Tuple tRnd;
			strMsg = HexEncode((const uint8_t*)strMsg.c_str(), strMsg.size());
			//strMsg.insert(0, servAddr,25);
			strMsg.insert(0, buf,16);

			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "BillingNbr:%s", sBillingNbr.c_str());
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "RBRMSG:%s", strMsg.c_str());
			tmpmsginfo = ReSetUid(msginfo,idup,strUID);
			tRnd.SetValues(tmpmsginfo);
			tRnd.SetValues(strMsg);
			tRnd.SetValues(sBillingNbr);
			tydic::storm::EmitDirect(-1,tRnd,"javaCStream");
			idup++;
			DCEvtCheck::instance()->Push_O("RER",strUID);
		}
		DCDATLOG("SM00018:%d", idup);
		tydic::storm::exeAck(tuple.GetID());
	}
	else
	{
		tydic::storm::Tuple tRnd;
		tRnd.SetValues(msginfo);
		tRnd.SetValues("over");
		tydic::storm::EmitDirect(-1,tRnd,"EndBoltStream");


		tydic::storm::exeAck(tuple.GetID());
	}

	collet.stop();

	//DCEvtTrk::instance()->IncNormal(atoi(sLatnId.c_str()),(int)collet.m_usec);
    SendToKpiBolt(msginfo);

	DCDATLOG();

    // 输出统计信息
    if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
    {
         std::string strStat;
         m_pm.get_statistic()->to_string(strStat, true);     // 过滤未使用的
         m_pm.get_dbm()->get_statistic()->to_string(strStat, true);   //过滤未使用的
         DCPERFLOG((int)(collet.m_usec+jm_usec),"PERF eptbolt:[eptbolt.jm=1|%ld][eptbolt.qe=%d][java.que=%d]%s", jm_usec,msgsize,java_que_size, strStat.c_str());
    }
    //重置统计信息
    m_pm.get_statistic()->reset();
	m_pm.get_dbm()->get_statistic()->reset();

	return 0;
}

void DCEptBolt::Compress(std::string& buf)
{
	char szSize[10] = {0};
	string strSendMsg="";
	if(buf.size() > 1024*64)
	{
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","message.size[%d] > 1024*64 must compress first. ",buf.size());
		int nRet = Compressor::Instance()->Compress(buf.c_str(),buf.size());
		char * text =  Compressor::Instance()->GetCompress();

		buf.clear();
		buf.resize(nRet+1,0);
		memcpy((void*)buf.c_str(),(void*)text,nRet);

		m_en->clear();
	    m_en->encode(buf);
		strSendMsg = HexEncode(m_en->data(),m_en->size());

		buf.clear();
		sprintf(szSize,"%.10d",nRet);
		buf = "$$$$$$";
		buf += szSize;
		buf += strSendMsg;
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","After compress the message is[%s]. ",buf.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","Don't need compress the message.");
	}
}

//获取主机IP,多个网卡的情况也只取第一个
void DCEptBolt::GetHostIp(string &IP)
{
	struct ifaddrs * ifAddrStruct = NULL,*ifAddrStruct1=NULL;
    void * tmpAddrPtr = NULL;
    getifaddrs(&ifAddrStruct);
	ifAddrStruct1 = ifAddrStruct;

    while (ifAddrStruct != NULL)
    {
        if (ifAddrStruct->ifa_addr->sa_family == AF_INET)
        {
            // check it is IPv4
            // is a valid IPv4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];

            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            if(strcmp(addressBuffer, "127.0.0.1") == 0)
            {
                ;
            }
            else
            {
                IP = addressBuffer;
				break;
            }

        }

        ifAddrStruct = ifAddrStruct->ifa_next;
    }
	freeifaddrs(ifAddrStruct1);
    return;
}

void DCEptBolt::svc()
{
	time_t check = time(NULL);
	std::vector<string> v;
	while(1)
	{
		if(check + 5 < time(NULL))
		{
			//DCEvtTrk::instance()->GetJson(v);
			for(unsigned int i = 0; i < v.size();i++)
			{
				tydic::storm::sendEventTrking(v[i]);
			}
			v.clear();
			check = time(NULL);
		}
		sleep(1);
	}
}

string DCEptBolt::ReSetUid(string strMsgInfo,int idup, string& uid)
{
	//strMsgInfo 消息样例[{"isRealtime":true,"uuid":"A0001361248900051082018011221491515769542","endBoltTaskId":"334","info":0}]
	//{"extend":"{\"hashkey\":\"266093\"}","isRealtime":true,"serviceName":"rentservTest551","uuid":"R060982990000001516676023","endBoltTaskId":"390","info":0}
	char buf[10] = {0};
    size_t p = 0, s;
	size_t p5 = 0, p6=0;
	int i = 0;
	if(string::npos != (s = strMsgInfo.find("uuid\":\"",0)))
	{
		p = s+7;
		p5 = p;
		if(string::npos != (s = strMsgInfo.find_first_of("\"", p)))
		{
			p6 = s;
		}
	}
	if(p6 > p5)
	{
		if(idup > 0)
		{
			sprintf(buf, "A%d", idup);
			strMsgInfo.insert(p6, buf);
			p6 = strMsgInfo.find_first_of("\"", p);
		}
		uid = strMsgInfo.substr(p5,p6-p5);
	}
	return strMsgInfo;
}



//发送稽核消息给指标bolt
int DCEptBolt::SendToKpiBolt(std::string msginfo)
{
    std::string strJsInfo;
    if (0 == DCKpiSender::instance()->GetKpiData(m_ptrBPMon,strJsInfo))
    {
        tydic::storm::Tuple tRndKpi;
        tRndKpi.SetValues(msginfo);
        tRndKpi.SetValues(strJsInfo);
        tydic::storm::EmitDirect(-1,tRndKpi,"DCKpiStream");
        DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "DCReqBolt::SendToKpiBolt end");
    }

    if(DCEvtCheck::instance()->GetCheckKey().size() > 0)
    {
        tydic::storm::Tuple tRndCheck;
        DCEvtCheck::instance()->GetJson(strJsInfo);
        tRndCheck.SetValues(msginfo);
        tRndCheck.SetValues(strJsInfo);
        tydic::storm::EmitDirect(-1,tRndCheck,"DCCheckStream");
        DCEvtCheck::instance()->ClearImp();
    }
}
