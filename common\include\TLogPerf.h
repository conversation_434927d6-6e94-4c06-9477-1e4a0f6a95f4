/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		TBizLogPerf.h
*Indentifier：
*		
*Description：
*		业务消息定义
*Version：
*		V1.0
*Author:
        liyn
*Finished：
*		2014-06-26
*History:
********************************************/
#ifndef _TSP_LOG_PERF_H_
#define _TSP_LOG_PERF_H_
#include <stddef.h>
#include "DCLogServer.h"

#include <sys/stat.h>
#include <stdarg.h>
#include <math.h>
#include <sys/time.h>
#include <string>
#include <stdio.h>
#include <stdlib.h>
using namespace std;

#define SP "1"
#define DCRSPOUT "2"
#define REQBOLT "3"
#define RATEREQBOLT "4"
#define ABMPROXY "5"
#define ABMSPOUT "6"
#define RATEANSBOLT "7"
#define ANSBOLT "8"
#define EPTBOLT "9"
#define RATEEPTBOLT "A"

struct BoltMsg
{
	int boltnumber;
	unsigned long starttime;
	unsigned long endtime;

	string recvmsg;
	BoltMsg()
	{
		boltnumber = 0;
		starttime = 0;
		endtime = 0;
		recvmsg = "";
	}
};

//节点 编号消息入时间 消息出时间 消息
//0~F  FF              FF         
#define DIFFTIME(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

class TLogPerf
{
	public:

		TLogPerf();
		~TLogPerf();

		void GetTimeTS();//接收CCR时间

		void GetTimeTE();//接收CCA时间

		
		void GetTimeT1_B();

		void GetTimeT1_E();

		void GetTimeT2_B();

		void GetTimeT2_E();

		void GetTimeT3_B();

		void GetTimeT3_E();

		void GetTimeT4_B();

		void GetTimeT4_E();

		int composeMsg(char *msg,char *buf);

		int composeMsg(char *type,char *msg,char *buf);
		int decomposeMsg(const char *msg,BoltMsg &boltMsg);

		
	public:
		unsigned long m_TS;	// CCR start
	

		unsigned long m_TE;	// END

		unsigned long m_T1_B;	// in format
		unsigned long m_T1_E;	// out format

		unsigned long m_T2_B;	// in biz
		unsigned long m_T2_E;	// out biz

		unsigned long m_T3_B;	// in ept
		unsigned long m_T3_E;	// out ept

		unsigned long m_T4_B;	// in cdr
		unsigned long m_T4_E;	// out cdr

	
};

#endif


