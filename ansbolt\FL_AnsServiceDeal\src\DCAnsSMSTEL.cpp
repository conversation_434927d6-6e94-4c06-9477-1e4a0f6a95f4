#include "DCAnsSMSTEL.h"
#include "ErrorCode.h"
#include "DCAnsPara.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCUDB.h"
#include "DCDBManer.h"
#include "DCLogMacro.h"
#include "DCOcpMsgDef.h"
#include "DCRbMsgDef.h"
#include "DCCommonIF.h"
#include "DCBizMsgDef.h"
#include "DCCdrIndex.h"

DCAnsSMSTEL::DCAnsSMSTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, "", "DCAnsSMSTEL");
}

DCAnsSMSTEL::~DCAnsSMSTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, "", "~DCAnsSMSTEL");
}

int DCAnsSMSTEL::ComposeCCA(STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	char value[BIZ_TEMP_LEN_2048 + 1] 			= {0};
	char buf[4096]							= {0};
	char subStr[BIZ_TEMP_LEN_32] 			= {0};
	char msg_id[BIZ_TEMP_LEN_32]			= {0};				//消息ID
	char event_time[20]						= {0};				//扣费时间
	char calling_nbr[BIZ_TEMP_LEN_32]		= {0};				//主叫号码
	char called_nbr[BIZ_TEMP_LEN_32]		= {0};				//被叫号码
	char charged_nbr[BIZ_TEMP_LEN_32]		= {0};				//计费号码
	long lEventTypeID 						= 0;				//事件类型ID
	int cost_unit 						    = 0;
	int cost_amount 						= 0;
	int balance                             = 0;
	int aocType 							= 0;
	long discount_totalfee                  = 0;
	long serial								= 0;
	DCAnsPara* m_smpara						= (DCAnsPara*)bizMsg->m_anspara;
	ocs::rbresult * base 						= (rbresult*)bizMsg->m_base;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;

	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	int PayFlag = atoi(ext->kv["PayFlag"].c_str());

	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "get m_extend StragegyId[%ld]", StragegyId);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "get m_extend PayFlag[%d]", PayFlag);


	SREAInfo REAMsg;
	//请求流程中离线时将requestType+10保存在会话表中，所以该值大于10说明离线
	int OffLine = 0;
	UDBSQL* pQuery = dbm->GetSQL(SMS_RBANS);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, subStr);
			strncpy(bizMsg->m_subNumber, subStr, sizeof(bizMsg->m_subNumber));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "sub number[%s]", bizMsg->m_subNumber);

			//pQuery->GetValue(2, bizMsg->m_serial);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "serial[%ld]", bizMsg->m_serial);

			pQuery->GetValue(3, value);
			aocType = atoi(value);
			bizMsg->m_naoc_type=aocType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "aoc type[%d]", aocType);

			pQuery->GetValue(4, value);
			bizMsg->m_requestNumber = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "request number[%d]", bizMsg->m_requestNumber);

			pQuery->GetValue(5, value);
			// bizMsg->m_requestType = atoi(value);//SM_INT_REQ_TYPE
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "request type[%d]", bizMsg->m_requestType);

			pQuery->GetValue(6, value);
			if(!bizMsg->m_trace_flag)bizMsg->m_trace_flag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "SM_TRACE_NUM_ONFF[%s]", value);

			//DEBIT 取出记录扣费信息使用的字段
			pQuery->GetValue(7, value);
			strncpy(msg_id, value, sizeof(msg_id));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "msg_id[%s]", msg_id);

			pQuery->GetValue(8, value);
			strncpy(calling_nbr, value, sizeof(calling_nbr));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "calling_nbr[%s]", calling_nbr);

			pQuery->GetValue(9, value);
			strncpy(called_nbr, value, sizeof(called_nbr));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "called_nbr[%s]", called_nbr);

			pQuery->GetValue(10, value);
			strncpy(event_time, value, sizeof(event_time));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "event_time[%s]", event_time);

			pQuery->GetValue(11, value);
			strncpy(charged_nbr , value, sizeof(charged_nbr));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "charged_nbr[%s]", charged_nbr);

			//检查是否超时，必须在更新扣费信息到数据库之前做检查。
			pQuery->GetValue(14, value);
			bizMsg->timestamps = DCC_MSG_TIMEOUT_SEC + DCCommonIF::time2sec(atol(value));

			pQuery->GetValue(17, value);
			bizMsg->m_userType =  atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usertype[%s]",value);

			pQuery->GetValue(18, bizMsg->m_mvnoId);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "mvnoid[%s]", bizMsg->m_mvnoId);

			/*time_t cursec= time(NULL);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "cur[%ld],dcc[%ld]", cursec, bizMsg->timestamps);
			if(bizMsg->timestamps <= cursec)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "timeout in-time[%ld],cur[%ld]", bizMsg->timestamps-DCC_MSG_TIMEOUT_SEC, cursec);
				ret = RET_ERROR;
				// return ret;
			}*/

			if(bizMsg->m_requestType == SM_SESSION_EVENT_CODE)
			{
				//获取B036,B037
				ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
				if(ret != RET_SUCCESS)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_P2PSMS_TYPE,	bizMsg->m_sessionID, "error[%s]", "B03");
				}
			}

			if(bizMsg->m_requestType == SM_SESSION_EVENT_CODE)
			{
				//获取B213
				balance = AccumlateBalance(bizMsg);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID,  "accumlate B213[%d]", balance);
			}

			lEventTypeID = base->evt_id;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID,  "get R616[%ld]", lEventTypeID);

			discount_totalfee = base->dist_fee;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  bizMsg->m_sessionID,  "get B0371[%ld]", discount_totalfee);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "query execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	ret = ModifyREAMsg(bizMsg,REAMsg);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, ret, bizMsg->m_sessionID, "ModifyREAMsg failed");
	}

	UDBSQL *pExec = dbm->GetSQL(SMS_UpdateSession);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, REAMsg.sz_balanceInfo);
			pExec->BindParam(2, REAMsg.sz_accumlatorInfo);
			pExec->BindParam(3, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(4, REAMsg.sz_chargeInfo);
			pExec->BindParam(5, bizMsg->m_resultcode);
			pExec->BindParam(6, STATUS_IDLE);
			pExec->BindParam(7, REAMsg.sz_balanceInfo2);
			pExec->BindParam(8, REAMsg.szPricingPlanID); // CDR_LNG_ PRICING_PLAN_ID
			pExec->BindParam(9, lEventTypeID);			 // CDR_LNG_ EVENT_TYPE_ID
			pExec->BindParam(10, discount_totalfee);	 // 优惠费用
			pExec->BindParam(11, StragegyId);
			pExec->BindParam(12, PayFlag);
			pExec->BindParam(13, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(14, OfrInstId.c_str());
			pExec->BindParam(15, ext->kv["BILLCYCLE"].c_str());
			pExec->BindParam(16, bizMsg->m_sessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_P2PSMS_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);	
					continue;	
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "update ok", "");

	int debitflag = m_smpara->GetP2PSMSPara()->debitflag;
	if(1 == debitflag)
	{
		//插入扣费信息表
		if((2001 == bizMsg->m_resultcode)&&(SM_SESSION_EVENT_CODE == bizMsg->m_requestType))
		{
			UDBSQL* pExec = dbm->GetSQL(SMS_Insert_OcsSmDebitInfo);
			try
			{
				pExec->DivTable(bizMsg->m_sessionID);
				pExec->UnBindParam();
				pExec->BindParam(1, msg_id);
				pExec->BindParam(2, charged_nbr);
				pExec->BindParam(3, calling_nbr);
				pExec->BindParam(4, called_nbr);
				pExec->BindParam(5, atol(event_time));
				pExec->Execute();
				pExec->Connection()->Commit();
			}
			catch(UDBException& e)
			{
				string sql;
				pExec->Connection()->Rollback();
				pExec->GetSqlString(sql);
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "SQL[%s]", sql.c_str());

				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE, "", "insert execption[%s]", e.ToString());
				return SM_OCP_UNABLE_TO_COMPLY;
			}

		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "insert msg_id[%s] success", msg_id);
	}


	//获取应答消息
	ocs::SCCAMsg sms;

	// sessionID
	sms.sessionID = bizMsg->m_sessionID;
       if(bizMsg->m_version == 1)
        {
              sms.sessionID.erase(0,3);
        }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "SessionID[%s]", bizMsg->m_sessionID);

	//request type
	if (bizMsg->m_requestType >= 5 || bizMsg->m_version == 1)
	{
		sms.requestType = 5;
	}
	else
	{
		sms.requestType = bizMsg->m_requestType;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "RequestType[%d]", sms.requestType);

	//request number
	sms.requestNumber = bizMsg->m_requestNumber;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "RequestNum[%d]", bizMsg->m_requestNumber);

	//Result-Code
	sms.resultCode = bizMsg->m_resultcode;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "ResultCode[%d]", bizMsg->m_resultcode);

	sms.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "serial[%ld]", bizMsg->m_serial);

	// ServiceFlowID
	sms.ServiceFlowID = bizMsg->m_ServiceFlowID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "ServiceFlowID[%s]", bizMsg->m_ServiceFlowID);

	sms.ServiceContextID = 2;

	if (bizMsg->m_requestType == SM_SESSION_EVENT_CODE || bizMsg->m_requestType == (SM_SESSION_XDR_CODE+1))
	{
		// Cost-Information
		sms.Cost.valueDigits = (long long)cost_amount;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "cost_amount[%d]", cost_amount);

		sms.Cost.exponent = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "exponent[%d]", 0);

		sms.Cost.costUnit = cost_unit;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "cost_unit[%d]", cost_unit);

		// AOC-Information
		sms.AOC.balance = balance;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "balance[%d]", balance);
	}

	if (bizMsg->m_testFlag)
	{
		sms.BalaInfo = REAMsg.sz_balanceInfo;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "BalaInfo[%s]", REAMsg.sz_balanceInfo);

		sms.AccuInfo = REAMsg.sz_accumlatorInfo;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "BalaInfo[%s]", REAMsg.sz_accumlatorInfo);

		// pREMsg->get(RB_CODE_B_ACCT_FLAG, value);
		// strcpy(sms.AcctInfo, value);
		// DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "BalaInfo[%s]", sms.AcctInfo);

		sms.TariInfo = REAMsg.sz_tariffIdInfo;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "BalaInfo[%s]", REAMsg.sz_tariffIdInfo);

		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		sms.AcctInfo = flag;
	}

	// 写队列
	//ret = ProduceCCA(bizMsg, cca, bizMsg->m_anstopic);

	string msg;
	ret = ComposeRMQMsg(bizMsg, sms, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce SMS failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}

	DCCdrIndex cdrIndex;
	cdrIndex.SetIndexInfo(bizMsg,event_time,event_time);
	string strIndex;
	cdrIndex.ToString(strIndex);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "",  "cdrIndex[%s]", strIndex.c_str());
/*	bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce SMS failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "",  "Produce SMS topic[%s] Successful", bizMsg->m_anstopic);
	}*/


/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		DelSession(bizMsg);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "",  "test msg,delete session");
		return RET_SUCCESS;
	}
*/
	return RET_CDR;
}

