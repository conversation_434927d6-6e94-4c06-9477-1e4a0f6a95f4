@组件名称：libReqBaseFlow.so.x.x.x
@创建日期：2016/6/15
@修改日期：2016/12/10
@当前版本：1.0.0
@功能描述：流程组件，反序列化ccr消息，调用请求流程功能组件
@版本历史：
@1.0.0：
---2016/6/24： reqbolt编译通过
---2016/7/11： 参数读取配置文件修改
---2016/7/28:  位置变更截单
---2016/8/15:  M2DB，增加ACE接口
---2016/8/18:  日志优化成功，查找用户资料修改
---2016/8/22:  请求实名制发送CCA topic支持可配置，用户管理流程返回结果码判断，用户管理流程状态修改
---2016/9/22:  号码跟踪问题修改
---2016/10/19: term上报不存在的rg走出单流程支撑
---2016/10/21: 统一添加UHead消息头
---2016/11/30: 多个SP对应一个top图，将CCA的topic放入消息体，传到后端
---2016/12/06: decode异常捕获
---2016/12/08: 免费号码流程未查主产品实例表问题
---2016/12/10: 解决异常时内存泄漏问题