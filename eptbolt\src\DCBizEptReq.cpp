#include "DCBizEptReq.h"
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "DCBizMsgDef.h"
#include <sys/time.h>
#include "BizDataDef.h"
#include "DCOBJSet.h"
#include "DCRFData.h"
//
int DCBizEptReq::Work(void *data)
{
	STBizMsg* bizMsg = (STBizMsg*)data;

	int ret = RET_SUCCESS;
	
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				ret =  SwitchCommon(bizMsg);
			}
			break;
		case SMS:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		case DATA:
		case CCG:
		case PGW:
			{
				ret = SwitchDATA(bizMsg);
			}
			break;
		case DATA_5G:
			{
				ret = Switch5G(bizMsg);
			}
			break;
		case ISMP:
		case HRS:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		case DSL:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "unknow service type[%d]", bizMsg->m_serviceContextID);
				SendErrorResult(bizMsg);
				ret = -1;
			}
			break;
	}

	//PERF LOG
	// bizMsg->m_perf.GetTimeT3_E();
	return ret;

}


int DCBizEptReq::SwitchCommon(STBizMsg * bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				return InitReq(bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				//只有在指定异常下回滚使用量信息
				return Update(bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				//只有在指定异常下回滚使用量信息
				return Term(bizMsg);
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				return Event(bizMsg);
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				return XDR(bizMsg);
			}
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "unknow request type", "");
				SendErrorResult(bizMsg);
			}
			break;
	}

	return 0;
}

int DCBizEptReq::SwitchDATA(STBizMsg * bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				return InitReq(bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
		case SM_SESSION_UPDATE_FIRST_CODE:
			{
				//只有在指定异常下回滚使用量信息
				return UpdateDATA(bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				//只有在指定异常下回滚使用量信息
				return TermDATA(bizMsg);
			}
			break;
              case SM_SESSION_XDR_CODE:
                    {
                            SendErrorResult(bizMsg);
                    }
                    break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "unknow request type [%d]", bizMsg->m_requestType);
				SendErrorResult(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptReq::Switch5G(STBizMsg * bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				return InitReq(bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
		case SM_SESSION_UPDATE_FIRST_CODE:
			{
				//只有在指定异常下回滚使用量信息
				return Update5G(bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				//只有在指定异常下回滚使用量信息
				return Term5G(bizMsg);
			}
			break;
              case SM_SESSION_XDR_CODE:
                    {
                            SendErrorResult(bizMsg);
                    }
                    break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "unknow request type [%d]", bizMsg->m_requestType);
				SendErrorResult(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}



int DCBizEptReq::InitReq(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_BLACK_LIST:
			{
				SendErrorResult(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptReq::Update(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case NE_USELESS:					//由免费号码返回
			{
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_USU_OVERLOAD:					//USU异常
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				UpdateErrorCode(bizMsg);
				return RET_CDR;
			}
			break;
			
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptReq::UpdateDATA(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_USU_OVERLOAD:					//USU异常
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				UpdateErrorCode(bizMsg);
				return RET_CDR;
			}
			break;			
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptReq::Update5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_USU_OVERLOAD:					//USU异常
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				UpdateErrorCode(bizMsg);
				return RET_SUCCESS;
			}
			break;			
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
	}

	return RET_SUCCESS;
}


int DCBizEptReq::Term(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case NE_USELESS:					//由免费号码返回
			{
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_USU_OVERLOAD:					//USU异常
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				UpdateErrorCode(bizMsg);
				return RET_CDR;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptReq::TermDATA(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case NE_USELESS:					//由免费号码返回
			{
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_USU_OVERLOAD:					//USU异常
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				UpdateErrorCode(bizMsg);
				return RET_CDR;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_CDR;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptReq::Term5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case NE_USELESS:					//由免费号码返回
			{
				return RET_SUCCESS;
			}
			break;
		case SM_OCP_USU_OVERLOAD:					//USU异常
			{
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				UpdateErrorCode(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
				SendTermRER(bizMsg);
				return RET_SUCCESS;
			}
			break;
	}

	return RET_SUCCESS;
}


int DCBizEptReq::Event(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case SM_OCP_MISSING_AVP:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_UNKNOWN_SESSION_ID:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case RB_USELESS:
			{
				SendErrorResult(bizMsg);
				DelSession(bizMsg);
			}
			break;
		case SM_OCP_INVALID_AVP_VALUE_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		case SM_OCP_MISSING_AVP_USELESS_CCR:
			{
				SendErrorResult(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, bizMsg->m_sessionID, "other error code", "");
				SendErrorResult(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}
int DCBizEptReq::XDR(STBizMsg* bizMsg)
{
	SendErrorResult(bizMsg);
	DelSession(bizMsg);
}
int DCBizEptReq::init()
{
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	return 0;
}

int DCBizEptReq::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	bizMsg->m_anspara = m_anspara;	
	m_pMsendMsg =(std::multimap<string,string>*)bizMsg->m_pSendMsg;
	int ret = Work(bizMsg);
	return ret;

}

DYN_PLUGIN_CREATE(DCBizEptReq, "FC_EPTREQ", "FC_EptReq", "1.0.0")
