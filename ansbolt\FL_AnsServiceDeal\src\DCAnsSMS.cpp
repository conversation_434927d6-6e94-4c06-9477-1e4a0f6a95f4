#include "DCAnsSMS.h"
#include "DCLogMacro.h"
#include <sys/time.h>
#include "ErrorCode.h"
#include "func_sqlindex.h"
#include "DCCommonIF.h"
#include "DCAnsPara.h"
#include "DCUDB.h"
#include "DCDBManer.h"

using namespace ocs;

DCAnsSMS::DCAnsSMS()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "DCAnsSMS", "");
}

DCAnsSMS::~DCAnsSMS()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE,  "~DCAnsSMS", "");
}

int DCAnsSMS::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG,  "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;

	//PERF LOG
	//bizMsg->m_perf.GetTimeT2_B();
	
	int ret = ComposeCCA(bizMsg);

	//bizMsg->m_perf.GetTimeT2_E();

	if(RET_CDR == ret || RET_SUCCESS == ret)
	{	

	}
	else
	{	
		bizMsg->m_resultcode= ret;
		return ret;
	}

	return ret;
}

int DCAnsSMS::ComposeCCA(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

