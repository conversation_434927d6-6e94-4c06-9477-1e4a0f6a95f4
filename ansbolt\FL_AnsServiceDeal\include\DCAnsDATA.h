/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsDATA.h
*Indentifier：
*
*Description：
*		数据业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_DATA_H__
#define __DC_ANS_DATA_H__
#include "DCAns.h"

class DCAnsDATA : public DCAns
{
	public:

		DCAnsDATA();
		virtual ~DCAnsDATA();

	public:

		int Work(void *data);

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);

};

#endif

