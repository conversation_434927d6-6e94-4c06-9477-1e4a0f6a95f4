/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqDSLTEL.h
*Indentifier：
*
*Description：
*		宽带业务处理类
*Version：
*		V1.0
*Author:
*		ZY.F
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_DSL_TEL_H__
#define __DC_REQ_DSL_TEL_H__
#include "DCReqDSL.h"

class DCReqDSLTEL:public DCReqDSL
{
	public:

		DCReqDSLTEL();
		virtual ~DCReqDSLTEL();

	private:

		virtual int SwitchReqType(STBizMsg* bizMsg);
		int compocRER(SCCRBase* base, SCCRDSL* data,STBizMsg* bizMsg,long &nextCCTime);
		int Init(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg);
		int Update(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg);
		int Term(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg);
		int XdrEvent(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg);
};

#endif

