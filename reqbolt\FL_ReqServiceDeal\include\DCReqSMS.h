/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqSMS.h
*Indentifier：
*
*Description：
*		短信业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_SMS_H__
#define __DC_REQ_SMS_H__
#include "DCReq.h"
#include "DCBizMsgDef.h"
#include "SMParaStruct.h"

class DCReqSMS : public DCReq
{
	public:

		DCReqSMS();
		virtual ~DCReqSMS();

	public:

		virtual int Work(void *data);
		
		int RoamSMS( char* MSCorVLR, AREA_INFO& visit,  int iCCRRoamType, STBizMsg* bizMsg);

		int RoamSMSByRoamingType(char* MSCorVLR,int &roamingtype);


	protected:

		virtual int SwitchReqType(STBizMsg* bizMsg);
};

#endif

