#include "TSMPara.h"
#include "DCLogMacro.h"
#include "ErrorCode.h"

static char HexTable[] = {"0123456789ABCDEF"};
TSMPara::TSMPara()
{
	m_para[0] = new TSMPARA();
	m_para[1] = new TSMPARA();
}

TSMPara::~TSMPara()
{

}

int TSMPara::init(DCDBManer *dbm)
{
	m_sql.m_pSELECT_SPR_ACCESS_NUMBER = dbm->GetSQL("q_spr_access_number");
	m_sql.m_pSELECT_SPZ_COUNTRY = dbm->GetSQL("q_spz_country");
	m_sql.m_pSELECT_SPZ_CITY = dbm->GetSQL("q_spz_city");
	m_sql.m_pSELECT_SM_SERVICE_QUOTA_CONF = dbm->GetSQL("q_sm_service_quota_conf");
	//m_sql.m_pSELECT_HLP_ACCTITEM_MAP = dbm->GetSQL("q_hlp_acctitem_map");
	m_sql.m_pSELECT_SM_SYSTEM_PARAMETER = dbm->GetSQL("q_sm_system_parameter");
	m_sql.m_pSELECT_SPR_MSISDN_AREA_MAP = dbm->GetSQL("q_spr_msisdn_area_map");
	m_sql.m_pSELECT_SPR_URGENT_NUMBER = dbm->GetSQL("q_spr_urgent_number");
	m_sql.m_pSELECT_SM_USER_STATE = dbm->GetSQL("q_sm_user_state");
	m_sql.m_pSELECT_SPR_MSC = dbm->GetSQL("q_spr_msc");
	// m_sql.m_pSELECT_SPR_EDGE_ROAM = dbm->GetSQL("q_spr_edge_roam_01");
	// m_sql.m_pSELECT_SPR_EDGE_ROAM_002 = dbm->GetSQL("q_spr_edge_roam_02");
	// m_sql.m_pSELECT_SPR_EDGE_ROAM_DATA = dbm->GetSQL("q_spr_edge_roam_data");
	//m_sql.m_pSELECT_SPR_NP = dbm->GetSQL("q_spr_np");
	m_sql.m_pSELECT_SPR_PDSN = dbm->GetSQL("q_spr_pdsn");
	m_sql.m_pSELECT_SPR_SGSN = dbm->GetSQL("q_spr_sgsn");
	m_sql.m_pSELECT_SPR_DSL = dbm->GetSQL("q_spr_dsl");
	m_sql.m_pSELECT_SPR_LAC = dbm->GetSQL("q_spr_lac");
	m_sql.m_pSELECT_SPR_CELL = dbm->GetSQL("q_spr_cell");
	m_sql.m_pSELECT_SM_RESULTCODE_MAP = dbm->GetSQL("q_sm_resultcode_map");
	m_sql.m_pSELECT_TB_BIL_SVECONT_ATTR_MAP = dbm->GetSQL("q_svecont_arrt_map");
	m_sql.m_pSELECT_SPR_TAC = dbm->GetSQL("q_spr_tac");
	m_sql.m_pSELECT_PAR_ROAM_BORDERLINE = dbm->GetSQL("q_par_roam_borderline");
	m_sql.m_pSELECT_PAR_COUNTRY_CODE_MAP = dbm->GetSQL("q_country_code");
	m_sql.m_pSELECT_SECTOR_ID_MAP = dbm->GetSQL("q_par_down_carrier_code");
	m_sql.m_pSELECT_PAR_ROAM_BORDERLINE_AAA = dbm->GetSQL("q_par_roam_borderline_aaa");
	m_sql.m_pSELECT_SECTOR_ID_CSM_MAP = dbm->GetSQL("q_sector_id_csm");
	m_sql.m_pSELECT_PAR_GSM_CODE_MAP = dbm->GetSQL("q_par_gsm_code_all");
	m_sql.m_pSELECT_PAR_RANGE_CODE_MAP = dbm->GetSQL("q_par_range_code");
    m_sql.m_pSELECT_SPR_SGSN_IPV6 = dbm->GetSQL("q_spr_sgsn_ipv6");
     m_sql.m_pSELECT_5G_NR = dbm->GetSQL("q_5g_nr");
    m_sql.m_pSELECT_SPR_TAC_5GSA = dbm->GetSQL("q_5gsa_tac");
	m_sql.m_pSELECT_CNPLMN_5G_NR=dbm->GetSQL("q_cnplmn_5g_nr");
	m_dbm = dbm;
	return 0;
}


int TSMPara::work(EBDIDX idx)
{
	m_pSMPARA = m_para[idx] ;

	int ret = 0;
	ret = LoadCommonPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadCommonPara failed [%d]",ret);
		return ret;
	}

	ret = LoadINPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadINPara failed [%d]",ret);
		return ret;
	}

	ret = LoadPSPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadPSPara failed [%d]",ret);
		return ret;
	}
	ret = Load5GPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Load5GPara failed [%d]",ret);
		return ret;
	}

	ret = LoadISMPPara();
	if(ret != 0)
	{

		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadISMPPara failed [%d]",ret);
		return ret;
	}

	ret = LoadP2PSMSPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadP2PSMSPara failed [%d]",ret);
		return ret;
	}

	ret = LoadCallPrefix();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadCallNumberPrefix failed [%d]",ret);
		return ret;
	}

       ret = LoadAccess();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,  "", "LoadAccess failed [%d]",ret);
		return -1;
	}

	ret = LoadCountry();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,	"", "LoadCountry failed [%d]",ret);
		return -1;
	}

	ret = LoadArea();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadArea failed [%d]",ret);
		return -1;
	}

	ret = LoadServiceQuotaConf();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadServiceQuotaConf failed [%d]",ret);
		return -1;
	}

	ret = LoadResultCodeMap();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadResultCodeMap failed [%d]",ret);
		return -1;
	}

	ret = LoadServiceType();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadServiceType failed [%d]",ret);
		return ret;
	}
	/*
	ret = LoadMsisdnMap();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadMsisdnAreaMap failed [%d]",ret);
		return -1;
	}
	*/

	ret = LoadSprUrgentNumber();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadSprUrgentNumber failed [%d]",ret);
		return -1;
	}

	ret = LoadMscVlrCode();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadMscVlrCode failed [%d]",ret);
		return -1;
	}

	ret = LoadSmUserState();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSmUserState failed [%d]",ret);
		return -1;
	}

	/*ret = LoadTariffIDMap();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadTariffID failed [%d]",ret);
		return -1;
	}
	*/

	ret = LoadOfrID2G();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadOfrID2G failed [%d]",ret);
		return ret;
	}
/**
	ret = LoadSprNP();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSprNP failed [%d]",ret);
		return -1;
	}
**/
	ret = LoadSprCELL();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSprCELL failed [%d]",ret);
		return -1;
	}

	ret = LoadSprPDSN();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSprPDSN failed [%d]",ret);
		return -1;
	}

	ret = LoadSprSGSN();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSPRSGSN failed [%d]",ret);
		return -1;
	}

	ret = LoadSprDSL();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSprDSL failed [%d]",ret);
		return -1;
	}

	ret = LoadSprLAC();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSprLAC failed [%d]",ret);
		return -1;
	}

	ret = LoadTacVlrCode();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSprtac failed [%d]",ret);
		return -1;
	}

	// ret = LoadEdgeRoamCellid();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadEDGEROAMCELLID failed [%d]",ret);
		return -1;
	}

	// ret = LoadEdgeRoamData();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadEdgeRoamData failed [%d]",ret);
		return -1;
	}

	// ret = LoadEdgeRoamLac();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadEDGEROAMLAC failed [%d]",ret);
		return -1;
	}

	ret = LoadDSLPara();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadWIFIUSERNAME failed [%d]",ret);
		return -1;
	}

	ret = LoadCutArea();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadCutArea failed [%d]",ret);
		return -1;
	}
	ret = LoadBillAttr();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadBillAttr failed [%d]",ret);
		return -1;
	}
	ret = LoadExpireState();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,	"", "LoadExpireState failed [%d]",ret);
		return -1;
	}
	ret = LoadFiltedStauscd();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,	"", "LoadFiltedStauscd failed [%d]",ret);
		return -1;
	}
	ret = LoadRoamBorder();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadRoamBorder failed [%d]",ret);
		return -1;
	}
	ret = LoadRoamBorderData();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadRoamBorderData failed [%d]",ret);
		return -1;
	}
	ret = LoadMscCountryInfo();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadMscCountryInfo failed [%d]",ret);
		return -1;
	}
	ret = LoadTariffInfo();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadTariffInfo failed [%d]",ret);
		return -1;
	}
	ret = LoadTariffInfoCSM();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadTariffInfoCSM failed [%d]",ret);
		return -1;
	}
	ret = LoadFilterProductId();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1,	"", "LoadFilterProductId failed [%d]",ret);
		return -1;
	}
	ret = LoadSprSGSN_IPV6();
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "LoadSPRSGSN_IPV6 failed [%d]",ret);
		return -1;
	}
	LodeSizeComp();

	return 0;
}

int TSMPara::LoadFilterProductId()
{
	SSystemPara systemPara = {0};
	int ret = 0;
	if((ret=LoadSystemPara("CM.COMMON", "PRODUCT_ID_FOR_FILTER", &systemPara)) != 0)
	{
		return ret;
	}
	string strParaValue = systemPara.PARA_VALUE;
	ParseString(strParaValue,m_pSMPARA->m_mapProductID,",");
	return 0;
}

int TSMPara::LoadCutArea()
{
	SSystemPara systemPara = {0};
	string strParaValue ="";
	int num = 0;
	int ret = 0;
	if((ret=LoadSystemPara("CM.COMMON", "CUT_AREA", &systemPara)) != 0)
	{
		return ret;
	}

	strParaValue = systemPara.PARA_VALUE;

	ParseString(strParaValue,m_pSMPARA->m_vecCutArea,",");
	// DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "load system parameter successful, [%s],[%s],ofr_id size[%ld]", "CM.COMMON", "CUT_AREA",m_pSMPARA->m_vecOfrID2G.size());
	return 0;
}


int TSMPara::LodeSizeComp()
{
	//SPR_ACCESS_NUMBER
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_ACCESS_NUMBER size[%d]",m_pSMPARA->m_access.size());

	//SPZ_COUNTRY
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPZ_COUNTRY size[%d]",m_pSMPARA->m_country.size());

	//SPZ_CITY
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPZ_CITY size[%d]",m_pSMPARA->m_area.size());

	//SM_SERVICE_QUOTA_CONF
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SM_SERVICE_QUOTA_CONF RGsize[%d],PID size[%d]",m_pSMPARA->m_service_quota_conf.size(),m_pSMPARA->m_service_quota_conf_ofr.size());

	//SM_AOC_GLOBAL_CFG
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SM_AOC_GLOBAL_CFG size[%d]",m_pSMPARA->m_aoc_para_map.size());

	//SM_SYSTEM_PARAMETER.CALL_NUMBER_PREFIX
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","CALL_NUMBER_PREFIX size[%d]",m_pSMPARA->m_call_number_prefix.size());

	//SM_RESULTCODE_MAP
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SM_RESULTCODE_MAP size[%d]",m_pSMPARA->m_result_code_map.size());

	//SPR_MSISDN_AREA_MAP
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_MSISDN_AREA_MAP size[%d]",m_pSMPARA->m_msisdn_area_map.size());

	//SPR_URGENT_NUMBER
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_URGENT_NUMBER size[%d]",m_pSMPARA->m_spr_urgent_map.size());

	//SPR_MSC
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_MSC size[%d]",m_pSMPARA->m_spr_msc_vlr_map.size());

	//SM_USER_STATE
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SM_USER_STATE size[%d]",m_pSMPARA->m_user_state_map.size());

	//SPR_NP
	//DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_NP size[%d]",m_pSMPARA->m_spr_np_map.size());

	//SPR_CELL
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_CELL size[%d]",m_pSMPARA->m_spr_cell_map.size());

	//SPR_PDSN
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_PDSN size[%d]",m_pSMPARA->m_spr_pdsn_map.size());

	//SPR_SGSN
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_SGSN size[%d]",m_pSMPARA->m_spr_sgsn_map.size());

	//SPR_DSL
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_DSL size[%d]",m_pSMPARA->m_spr_dsl_map.size());

	//SPR_LAC
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_LAC size[%d]",m_pSMPARA->m_spr_lac_map.size());

	//SPR_EDGE_ROAM
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_EDGE_ROAM size[%d]",m_pSMPARA->m_edge_roam_cellid_map.size());

	//SPR_EDGE_ROAM_DATA
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","SPR_EDGE_ROAM_DATA size[-%d]",m_pSMPARA->m_edge_roam_map_data.size());

	// PAR_ROAM_BORDERLINE
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","Roam_BorderNew size[%d]",m_pSMPARA->m_mapRoamBorderNew.size());

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","Roam_BorderData size[%d]",m_pSMPARA->m_mapRoamBorderData.size());

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","m_sector_id_map size[%d]",m_pSMPARA->m_sector_id_map.size());

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","m_msc_country_map size[%d]",m_pSMPARA->m_msc_country_map.size());

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","m_sector_id_csm_map size[%d]",m_pSMPARA->m_sector_id_csm_map.size());

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"","m_spr_sgsn_ipv6_map size[%d]",m_pSMPARA->m_spr_sgsn_ipv6_map.size());
	return 0;
}
int TSMPara::LoadExpireState()
{
	int ret = 0;
	char tmp[20] = {0};
	char *p = tmp;
	char ch;
	SSystemPara systemPara;
	if((ret=LoadSystemPara("CM.COMMON", "EXPIRE_STATE", &systemPara)) != 0)
	{
		return ret;
	}
	ParseState(systemPara.PARA_VALUE);
	return 0;
}
int TSMPara::LoadFiltedStauscd()
{
	int ret = 0;
	char tmp[20] = {0};
	char *p = tmp;
	char ch;
	SSystemPara systemPara;
	if((ret=LoadSystemPara("CM.COMMON", "FILTED_STAUSCD", &systemPara)) != 0)
	{
		return ret;
	}
	ParseStauscd(systemPara.PARA_VALUE);
	return 0;
}
int TSMPara::LoadMscCountryInfo()
{
	m_sql.m_pSELECT_PAR_COUNTRY_CODE_MAP = m_dbm->GetSQL("q_country_code");
	UDBSQL* query = m_sql.m_pSELECT_PAR_COUNTRY_CODE_MAP;
	char tmp[20] = {0};
	MSC_COUNTRY *pConf = NULL;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			pConf = new MSC_COUNTRY;
			query->GetValue(1, tmp);
			pConf->msc_code= tmp;

			query->GetValue(2, tmp);
			pConf->iccarrier_code = tmp;

			query->GetValue(3, tmp);
			pConf->country_code = tmp;

			query->GetValue(4, tmp);
			pConf->area_code = atoi(tmp);

			pair<map<string, MSC_COUNTRY *>::iterator,bool> ret =
				m_pSMPARA->m_msc_country_map.insert(pair<string, MSC_COUNTRY *>(pConf->msc_code, pConf));
			if(ret.second == false)
				delete pConf;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "load tariffid map failed: [%s]", e.ToString());
		return -1;
	}
	return 0;
}
int TSMPara::LoadTariffInfo()
{
	m_sql.m_pSELECT_SECTOR_ID_MAP = m_dbm->GetSQL("q_par_down_carrier_code");
	UDBSQL* query = m_sql.m_pSELECT_SECTOR_ID_MAP;
	char tmp[20] = {0};
	string sectorid;
	string sponsor_code;
	string carrier_code;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, carrier_code);

			query->GetValue(2, sectorid);

			query->GetValue(3, sponsor_code);

			// DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sponsor_code[%s], carrier_code[%s], sectorid[%s]", sponsor_code.c_str(), carrier_code.c_str(), sectorid.c_str());

			m_pSMPARA->m_sector_id_map.insert(pair<string, string>(sponsor_code+carrier_code, sectorid));
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "load tariffid map failed: [%s]", e.ToString());
		return -1;
	}
	return 0;
}

int TSMPara::LoadTariffInfoCSM()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadTariffInfoCSM");
	m_sql.m_pSELECT_SECTOR_ID_CSM_MAP = m_dbm->GetSQL("q_sector_id_csm");
	UDBSQL* query = m_sql.m_pSELECT_SECTOR_ID_CSM_MAP;
	char tmp[20] = {0};
	string sectorid;
	string sponsor_code;
	string carrier_cd;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, carrier_cd);// 国家码

			query->GetValue(2, sectorid);

			query->GetValue(3, sponsor_code);// 和记/vodafone

			m_pSMPARA->m_sector_id_csm_map.insert(pair<string, string>(sponsor_code+carrier_cd, sectorid));

			// DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sponsor_code[%s], carrier_code[%s], sectorid[%s]", sponsor_code.c_str(), carrier_code.c_str(), sectorid.c_str());
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "load tariffid map failed: [%s]", e.ToString());
		return -1;
	}
	return 0;
}


int TSMPara::ParseState(const char* value)
{
	if(value == NULL || !value[0])
	{
		return -1;
	}

	char tmp[20] = {0};
	char *p = tmp;
	char ch;

	while(1)
	{
		while(((ch = *p++ = *value++) !=',') && (ch != '\0'));

		*(--p) = '\0';
		if(tmp[0])
		{
			m_pSMPARA->m_expirestate.insert(pair<string,int>(tmp,1));
		}
		if(ch == '\0')
		{
			break;
		}
		p = tmp;
	}

	return 0;
}
int TSMPara::ParseStauscd(const char* value)
{
	if(value == NULL || !value[0])
	{
		return -1;
	}

	char tmp[20] = {0};
	char *p = tmp;
	char ch;

	while(1)
	{
		while(((ch = *p++ = *value++) !=',') && (ch != '\0'));

		*(--p) = '\0';
		if(tmp[0])
		{
			m_pSMPARA->m_filtedstauscd.insert(pair<string,int>(tmp,1));
		}
		if(ch == '\0')
		{
			break;
		}
		p = tmp;
	}

	return 0;
}
void TSMPara::clear(EBDIDX idx)
{
	TSMPARA* m_pSMCLEAN = m_para[idx];
	map<int, ACCESS_INFO*>::iterator iter = m_pSMCLEAN->m_access.begin();
	while(iter!=m_pSMCLEAN->m_access.end())
	{
		ACCESS_INFO* pInfo = iter->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter++;
	}
	m_pSMCLEAN->m_access.clear();

	map<int, COUNTRY_INFO*>::iterator iter2 = m_pSMCLEAN->m_country.begin();
	while(iter2!=m_pSMCLEAN->m_country.end())
	{
		COUNTRY_INFO* pInfo = iter2->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter2++;
	}
	m_pSMCLEAN->m_country.clear();

	map<int, AREA_INFO*>::iterator iter3 = m_pSMCLEAN->m_area.begin();
	while(iter3!=m_pSMCLEAN->m_area.end())
	{
		AREA_INFO* pInfo = iter3->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter3++;
	}
	m_pSMCLEAN->m_area.clear();

	multimap<long long, TSERVICE_QUOTA_CONF*>::iterator iter4 = m_pSMCLEAN->m_service_quota_conf.begin();
	while(iter4!=m_pSMCLEAN->m_service_quota_conf.end())
	{
		TSERVICE_QUOTA_CONF* pInfo = iter4->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter4++;
	}
	m_pSMCLEAN->m_service_quota_conf.clear();
	m_pSMCLEAN->m_service_quota_conf_ofr.clear();

	m_pSMCLEAN->m_aoc_para_map.clear();

	m_pSMCLEAN->m_tariffid_map.clear();

	m_pSMCLEAN->m_call_number_prefix.clear();

	multimap<long long, MSISDN_MAP_INFO*>::iterator iter8 = m_pSMCLEAN->m_msisdn_area_map.begin();
	while(iter8!=m_pSMCLEAN->m_msisdn_area_map.end())
	{
		MSISDN_MAP_INFO* pInfo = iter8->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter8++;
	}
	m_pSMCLEAN->m_msisdn_area_map.clear();

	multimap<long long, URGENT_NUMBER_INFO*>::iterator iter9 = m_pSMCLEAN->m_spr_urgent_map.begin();
	while(iter9!=m_pSMCLEAN->m_spr_urgent_map.end())
	{
		URGENT_NUMBER_INFO* pInfo = iter9->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter9++;
	}
	m_pSMCLEAN->m_spr_urgent_map.clear();

	multimap<int, USER_STATE_INFO*>::iterator iter10 = m_pSMCLEAN->m_user_state_map.begin();
	while(iter10!=m_pSMCLEAN->m_user_state_map.end())
	{
		USER_STATE_INFO* pInfo = iter10->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter10++;
	}
	m_pSMCLEAN->m_user_state_map.clear();

	map<string, MSC_INFO*>::iterator iter11 = m_pSMCLEAN->m_spr_msc_vlr_map.begin();
	while(iter11!=m_pSMCLEAN->m_spr_msc_vlr_map.end())
	{
		MSC_INFO *pInfo = iter11->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}

		iter11++;
	}
	m_pSMCLEAN->m_spr_msc_vlr_map.clear();

	multimap<string, EDGE_ROAM_INFO*>::iterator iter12 = m_pSMCLEAN->m_edge_roam_lac_map.begin();
	while(iter12!=m_pSMCLEAN->m_edge_roam_lac_map.end())
	{
		EDGE_ROAM_INFO* pInfo = iter12->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter12++;
	}
	m_pSMCLEAN->m_edge_roam_lac_map.clear();

	multimap<string, EDGE_ROAM_INFO*>::iterator iter13 = m_pSMCLEAN->m_edge_roam_cellid_map.begin();
	while(iter13!=m_pSMCLEAN->m_edge_roam_cellid_map.end())
	{
		EDGE_ROAM_INFO* pInfo = iter13->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter13++;
	}
	m_pSMCLEAN->m_edge_roam_cellid_map.clear();
/**
	map<long, string>::iterator iter14 = m_pSMCLEAN->m_spr_np_map.begin();
	while(iter14!=m_pSMCLEAN->m_spr_np_map.end())
	{
		string pInfo = iter14->second ;
		pInfo.clear();
		iter14++;
	}
	m_pSMCLEAN->m_spr_np_map.clear();
**/
	map<string, PDSN_INFO*>::iterator iter15 = m_pSMCLEAN->m_spr_pdsn_map.begin();
	while(iter15!=m_pSMCLEAN->m_spr_pdsn_map.end())
	{
		PDSN_INFO* pInfo = iter15->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter15++;
	}
	m_pSMCLEAN->m_spr_pdsn_map.clear();

	map<string, SGSN_INFO*>::iterator iter16 = m_pSMCLEAN->m_spr_sgsn_map.begin();
	while(iter16!=m_pSMCLEAN->m_spr_sgsn_map.end())
	{
		SGSN_INFO* pInfo = iter16->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter16++;
	}
	m_pSMCLEAN->m_spr_sgsn_map.clear();

	map<string, DSL_INFO*>::iterator iter17 = m_pSMCLEAN->m_spr_dsl_map.begin();
	while(iter17!=m_pSMCLEAN->m_spr_dsl_map.end())
	{
		DSL_INFO* pInfo = iter17->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter17++;
	}
	m_pSMCLEAN->m_spr_dsl_map.clear();

	m_pSMCLEAN->m_spr_lac_map.clear();

	m_pSMCLEAN->m_spr_cell_map.clear();

	map<int, SResultCode*>::iterator iter20 = m_pSMCLEAN->m_result_code_map.begin();
	while(iter20!=m_pSMCLEAN->m_result_code_map.end())
	{
		SResultCode* pInfo = iter20->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter20++;
	}
	m_pSMCLEAN->m_result_code_map.clear();

	multimap<string, EDGE_ROAM_INFO*>::iterator iter24 = m_pSMCLEAN->m_edge_roam_map_data.begin();
	while(iter24!=m_pSMCLEAN->m_edge_roam_map_data.end())
	{
		EDGE_ROAM_INFO* pInfo = iter24->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter24++;
	}
	m_pSMCLEAN->m_edge_roam_map_data.clear();

        map<string,MSC_COUNTRY*>::iterator iter25 = m_pSMCLEAN->m_msc_country_map.begin();
        while(iter25 != m_pSMCLEAN->m_msc_country_map.end())
        {
                delete  iter25->second;
                iter25++;
        }
       m_pSMCLEAN->m_msc_country_map.clear();

    map<string, SGSN_INFO*>::iterator iter26 = m_pSMCLEAN->m_spr_sgsn_ipv6_map.begin();
	while(iter26!=m_pSMCLEAN->m_spr_sgsn_ipv6_map.end())
	{
		SGSN_INFO* pInfo = iter26->second ;
		if(pInfo)
		{
			delete pInfo;
			pInfo = NULL;
		}
		iter26++;
	}
	m_pSMCLEAN->m_spr_sgsn_ipv6_map.clear();
	m_pSMCLEAN->v_mask.clear();

	m_pSMCLEAN->m_ps_short_cdr.clear();

	m_pSMCLEAN->m_business_type_map.clear();

	m_pSMCLEAN->m_SPCProductId_map.clear();

	m_pSMCLEAN->m_ProductOfferId_map.clear();

	m_pSMCLEAN->m_vecOfrID2G.clear();

	m_pSMCLEAN->m_vecCutArea.clear();

	m_pSMCLEAN->m_mapsubfix.clear();

	m_pSMCLEAN->m_vecCdrByService.clear();

	m_pSMCLEAN->m_mapRoamBorderNew.clear();

	m_pSMCLEAN->m_expirestate.clear();

	m_pSMCLEAN->m_filtedstauscd.clear();

	m_pSMCLEAN->m_svecont_attr_map.clear();

	m_pSMCLEAN->m_spr_tac_vlr_map.clear();

	m_pSMCLEAN->m_vecIsmpRefund.clear();

	m_pSMCLEAN->m_mapRoamBorderData.clear();

	m_pSMCLEAN->m_sector_id_map.clear();

	m_pSMCLEAN->m_sector_id_csm_map.clear();
}

int TSMPara::LoadAccess()
{
	char buf[1024] = {0};
	char tmp[256] = {0};

	//接入号码SPR_ACCESS_NUMBER
	m_sql.m_pSELECT_SPR_ACCESS_NUMBER = m_dbm->GetSQL("q_spr_access_number");
	UDBSQL* query = m_sql.m_pSELECT_SPR_ACCESS_NUMBER;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, tmp);
			m_pSMPARA->m_access.insert(pair<int, ACCESS_INFO *>(atoi(tmp), NULL));
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,  "", "load access information failed. error[%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadCountry()
{
	char tmp[256] = {0};

	//国家号码SPZ_COUNTRY
	m_sql.m_pSELECT_SPZ_COUNTRY = m_dbm->GetSQL("q_spz_country");
	UDBSQL* qCountry = m_sql.m_pSELECT_SPZ_COUNTRY;
	COUNTRY_INFO* pCountry = NULL;
	try
	{
		qCountry->UnBindParam();
		qCountry->Execute();
      	while (qCountry->Next())
      	{
			pCountry = new COUNTRY_INFO;
			//国家号
			qCountry->GetValue(1, tmp);
			pCountry->country = atoi(tmp);

			qCountry->GetValue(3, pCountry->countryCode);

			pair<map<int,COUNTRY_INFO * >::iterator,bool> ret =
			m_pSMPARA->m_country.insert(pair<int, COUNTRY_INFO *>(pCountry->country, pCountry));

			if(ret.second == false)
				delete pCountry;
		}
		qCountry->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,  "", "load country information failed. error[%s]",e.ToString());
		return -1;
	}

	return 0;
}


int TSMPara::LoadArea()
{
	char tmp[256] = {0};

	//区号SPZ_CITY
	m_sql.m_pSELECT_SPZ_CITY = m_dbm->GetSQL("q_spz_city");
	UDBSQL* qArea = m_sql.m_pSELECT_SPZ_CITY;
	AREA_INFO* pArea = NULL;
	try
	{
		qArea->UnBindParam();
		qArea->Execute();
  		while (qArea->Next())
  		{
			pArea = new AREA_INFO;
			//区号
			qArea->GetValue(1, tmp);
			pArea->area = atoi(tmp);

			//省号
			qArea->GetValue(2, tmp);
			pArea->province = atoi(tmp);

			pair<map<int,AREA_INFO * >::iterator,bool> ret =
			m_pSMPARA->m_area.insert(pair<int, AREA_INFO *>(pArea->area, pArea));

			if(ret.second == false)
				delete pArea;
		}
		qArea->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,  "", "load zone information failed. error[%s]",e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadTacVlrCode()
{
	char tmp[256] = {0};
	MSC_INFO *pmscinfo = NULL;

	//区号SPZ_CITY
	m_sql.m_pSELECT_SPR_TAC = m_dbm->GetSQL("q_spr_tac");
	UDBSQL* qQuery = m_sql.m_pSELECT_SPR_TAC;
	try
	{
		qQuery->UnBindParam();
		qQuery->Execute();
  		while (qQuery->Next())
  		{
  			MSC_INFO pmscinfo;
			memset(&pmscinfo,0,sizeof(pmscinfo));
			qQuery->GetValue(1, tmp);
			string str = tmp;

			qQuery->GetValue(2, tmp);
			string area= tmp;
			strncpy(pmscinfo.area_code, tmp,14);

			qQuery->GetValue(3, pmscinfo.carriers);
			m_pSMPARA->m_spr_tac_vlr_map.insert(pair<string, MSC_INFO>(str, pmscinfo));

		}
		qQuery->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,  "", "load zone information failed. error[%s]",e.ToString());
		return -1;
	}
	return 0;
}
int TSMPara::LoadServiceQuotaConf()
{
	char tmp[256] = {0};

	m_sql.m_pSELECT_SM_SERVICE_QUOTA_CONF = m_dbm->GetSQL("q_sm_service_quota_conf");
	UDBSQL* query = m_sql.m_pSELECT_SM_SERVICE_QUOTA_CONF;
	TSERVICE_QUOTA_CONF *pConf = NULL;

	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			pConf = new TSERVICE_QUOTA_CONF;
			query->GetValue(1, tmp);
			pConf->SERVICE_TYPE = atoi(tmp);
			query->GetValue(2, tmp);
			pConf->SUB_SERVICE_TYPE = atoi(tmp);
			query->GetValue(4, tmp);
			pConf->RATING_GROUP = atol(tmp);
			query->GetValue(5, tmp);
			pConf->INPUT_OCTETS = atol(tmp);
			query->GetValue(6, tmp);
			pConf->OUTPUT_OCTETS = atol(tmp);
			query->GetValue(7, tmp);
			pConf->TOTAL_OCTETS = atol(tmp);
			query->GetValue(8, tmp);
			pConf->DURATION = atoi(tmp);
			query->GetValue(9, tmp);
			pConf->TOKEN = atoi(tmp);
			query->GetValue(10, tmp);
			pConf->VALID_TIME = atoi(tmp);
			query->GetValue(11, tmp);
			pConf->QUOTA_HOLDING_TIME = atoi(tmp);
			query->GetValue(12, tmp);
			pConf->TIME_QUOTA_THRESHOLD = atoi(tmp);
			query->GetValue(13, tmp);
			pConf->VOLUME_QUOTA_THRESHOLD = atoi(tmp);
			query->GetValue(14, tmp);
			pConf->VOLUME_QUOTA_THRESHOLD_1 = atoi(tmp);
			query->GetValue(15, tmp);
			pConf->QUOTA_CONSUMPTION_TIME = atoi(tmp);

			query->GetValue(16, pConf->szProductOfferId);//PRODUCT_OFFER_ID

        		query->GetValue(17, tmp);
        		pConf->MinBalance = atol(tmp);
        		query->GetValue(18, tmp);
        		pConf->MaxBalance = atol(tmp);

                      query->GetValue(19, tmp);
                      pConf->MinRatio = atol(tmp);
                      query->GetValue(20, tmp);
                      pConf->MaxRatio = atol(tmp);
                      query->GetValue(21, tmp);
                      pConf->THRESHOLD_RATIO = atoi(tmp);

			if(((pConf->DURATION == 0) && (pConf->INPUT_OCTETS == 0) && (pConf->OUTPUT_OCTETS == 0) && (pConf->TOTAL_OCTETS == 0)) && (pConf->TOKEN == 0))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "invalid rating group value: [%lld]", pConf->RATING_GROUP);
				delete pConf;
				pConf = NULL;
				return -1;
			}

                    //  -7 速率单位换算
        		if(pConf->RATING_GROUP == -7)
        		{
        			pConf->MinRatio = pConf->MinRatio * 1024;
        			pConf->MaxRatio = pConf->MaxRatio * 1024;
        			if(m_pSMPARA->m_PSPara.LLBalance > pConf->THRESHOLD_RATIO)
        			{
        				pConf->THRESHOLD_RATIO = m_pSMPARA->m_PSPara.LLBalance;
        			}
        			if(pConf->THRESHOLD_RATIO > 100)
        			{
        				pConf->THRESHOLD_RATIO = 100;
        			}
        			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,  "", "SM_SERVICE_QUOTA_CONF{RATING_GROUP[%ld],min_bps[%ld],max_bps[%ld],THRESHOLD_RATIO[%d]}",
        				pConf->MinRatio,pConf->MaxRatio,pConf->THRESHOLD_RATIO);
        		}

			m_pSMPARA->m_service_quota_conf.insert(pair<long long, TSERVICE_QUOTA_CONF *>(pConf->RATING_GROUP, pConf));

			if(strlen(pConf->szProductOfferId) != 0)
			{
				m_pSMPARA->m_service_quota_conf_ofr.insert(pair<string, TSERVICE_QUOTA_CONF *>(pConf->szProductOfferId, pConf));
			}
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "load service quota config information failed. error[%s]", e.ToString());
		return -1;
	}

	return 0;
}

/*
int TSMPara::LoadTariffIDMap()
{
	char tmp[256] 	= {0};

	int ocp_tariffid 	= 0;
	int sm_tariffid 	= 0;

	UDBSQL* query = m_sql.m_pSELECT_HLP_ACCTITEM_MAP;

	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, tmp);
			ocp_tariffid= atoi(tmp);

			query->GetValue(2, tmp);
			sm_tariffid= atoi(tmp);

			pair<map<int, int>::iterator, bool> iter;
			iter = m_pSMPARA->m_tariffid_map.insert(pair<int, int>(ocp_tariffid, sm_tariffid));
			if(!(iter.second))
			{

				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "same tariffid map value: [%d][%d]", ocp_tariffid, sm_tariffid);
				return -1;
			}
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "load tariffid map failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

*/
int TSMPara::LoadSystemPara(const char *paraGroup, const char *paraKey, SSystemPara *systemPara)
{
	m_sql.m_pSELECT_SM_SYSTEM_PARAMETER = m_dbm->GetSQL("q_sm_system_parameter");
	UDBSQL* query = m_sql.m_pSELECT_SM_SYSTEM_PARAMETER;
	try
	{
		query->UnBindParam();
		query->BindParam(1, paraGroup);
		query->BindParam(2, paraKey);
		query->Execute();

      	if(query->Next())
      	{
			query->GetValue(1, systemPara->PARA_VALUE);
			sprintf(systemPara->PARA_GROUP, "%s", paraGroup);
			sprintf(systemPara->PARA_KEY, "%s", paraKey);
			// DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "load system parameter :group[%s], key[%s], value[%s]", paraGroup, paraKey, systemPara->PARA_VALUE);
			query->Close();
		}
		else
		{
			query->Close();
			DCBIZLOG(DCLOG_LEVEL_ERROR,-2, "", "load system parameter failed:group[%s], key[%s]", paraGroup, paraKey);
			return -2;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load system parameter failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadOfrID2G()
{
	SSystemPara systemPara;
	string strParaValue;
	int ret = 0;
	if((ret=LoadSystemPara("CM.COMMON", "OFR_ID_2G", &systemPara)) != 0)
	{
		return ret;
	}

	strParaValue = systemPara.PARA_VALUE;
	ParseString(strParaValue,m_pSMPARA->m_vecOfrID2G,",");

	return 0;
}


int TSMPara::LoadCommonPara()
{
	SSystemPara systemPara;
	string strParaValue;
	int ret = 0;
	if((ret=LoadSystemPara("CM.COMMON", "CURRENT_PROV_CODE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.currentProvCode = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CURRENT_AREA_CODE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.currentAreaCode = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("CM.COMMON", "ONLY_SUB_FLAG", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.onlySubFlag = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("CM.COMMON", "ONLINE_SESSION", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.onlineSession = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("CM.COMMON", "PLATFORM_OVER_LOAD", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.platformOverLoad = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("CM.COMMON", "TRANSLATE_DATA_CARD", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.isTranslateDataCard = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "TRACE_NUM_ONFF", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.isTraceNumOnff = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("CM.COMMON", "IN_FORBIDDEN_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.INForbindenSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "PSPSMS_FORBIDDEN_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.P2PSMSForbindenSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "PS_FORBIDDEN_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.PSForbindenSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "ISMP_FORBIDDEN_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.ISMPForbindenSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "DSL_FORBIDDEN_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.DSLForbindenSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "EVENT_ISMP_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.EventISMPSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "EVENT_SMS_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.EventSMSSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "TIME_OUT_VALUE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.TIMEOUTVALUE = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_FENCE", &systemPara)) != 0)
	{
		return ret;
	}
	/*注意字符char 类型*/
	m_pSMPARA->m_commonPara.CdrFence = systemPara.PARA_VALUE[0];

	if((ret=LoadSystemPara("CM.COMMON", "SMS_SEND_NBR", &systemPara)) != 0)
	{
		return ret;
	}
	for(int i=0; i<strlen(systemPara.PARA_VALUE); i++)
	{
		if(!( systemPara.PARA_VALUE[i] >= '0' && systemPara.PARA_VALUE[i] <= '9' ))
		{
			return -1;
		}
	}
	strncpy(m_pSMPARA->m_commonPara.SmsSendNo, systemPara.PARA_VALUE, sizeof(m_pSMPARA->m_commonPara.SmsSendNo));

	if((ret=LoadSystemPara("CM.COMMON", "IN_FREE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.INFreeSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "SMS_FREE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.SMSFreeSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "DATA_FREE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.DATAFreeSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "ISMP_FREE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.ISMPFreeSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "DSL_FREE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.DSLFreeSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_INFO_END_CHAR", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.CdrEndchar = (1 == atoi(systemPara.PARA_VALUE)?';':0);

	if((ret=LoadSystemPara("CM.COMMON", "INVALID_MSISDN_DEFAULT_AREA", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.invalidMsisdnArea = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_TARIFFID", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.CdrTariffid = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "BALANCE_INFO_TYPE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.BalanceInfoType = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.BalanceInfoType = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "INVALID_MSISDN_DEAL", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.invalidMsisdnArea = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.invalidMsisdnDeal = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "AOC_TEMPLATE_TOTAL_BYTE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nAocTemplateTotalByte = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.nAocTemplateTotalByte = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "RSU_USU_CCMONEY_UNITS", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nRsuUsuCCmoneyUnits = 1;
	}
	else
	{
		m_pSMPARA->m_commonPara.nRsuUsuCCmoneyUnits = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "GSU_CCMONEY_EXPONENT", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nGsuCCmoneyExponent = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nGsuCCmoneyExponent = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "GSU_COST_INFORMATION_EXPONENT", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nGsuCostInformationExponent = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nGsuCostInformationExponent = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "ASR_MAX_SECOND", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.SessionTimeOutMaxMessageForEachSecond = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "RELEASE_SESSION_FLAG", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nReleaseFlag = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nReleaseFlag = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "TB_NBR_LATN_REL_QUERY_CONTROL", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iLatnRelQueryControl = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "SEND_EPT_RBR_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iSendEPTRBROnOff = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "CDR_VERSION_TYPE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iCdrVersionType = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "IN_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.INAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.INAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "P2PSMS_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.P2PSMSAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.P2PSMSAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "PS_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.PSAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.PSAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "5G_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara._5GAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara._5GAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "ISMP_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.ISMPAuthUserResultCode= 0;	//默认为0
	}
	else
	{
		m_pSMPARA->m_commonPara.ISMPAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "DSL_AUTH_USER_RESULTCODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.DSLAuthUserResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.DSLAuthUserResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "FREE_ADD_CDR", &systemPara)) != 0)
	{
		m_pSMPARA->m_commonPara.nFreeAddCdr = 0;
	}
	else
	{
		m_pSMPARA->m_commonPara.nFreeAddCdr = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("CM.COMMON", "DSL_MONTH_CDR_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iDslMonthCDRSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "UPDATE_USER_STATE_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iUpdateUserStateSwitch= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "SUB_AREA_CONTROL", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.isubAreaControl= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "PRICE_PLAN_ID_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iPricePlanID = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "COMMON_CONTEXTID", &systemPara)) != 0)
	{
		return ret;
	}
	std::string strTmpServContextId = systemPara.PARA_VALUE;
	ParseString(strTmpServContextId, m_pSMPARA->m_commonPara.vecServContextId, ",");

	if((ret=LoadSystemPara("CM.COMMON", "SEND_EPT_RBR_SWITCH_PGW", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iSendEPTRBROnOffPGW = atoi(systemPara.PARA_VALUE);

    if((ret=LoadSystemPara("CM.COMMON", "SEND_EPT_RBR_SWITCH_5G", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iSendEPTRBROnOff5G = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "OFFLINE_CDR_FLAG", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iOfflineCdrFlag = atoi(systemPara.PARA_VALUE);
	if ( m_pSMPARA->m_commonPara.iOfflineCdrFlag < 0 )
	{
	    m_pSMPARA->m_commonPara.iOfflineCdrFlag = 0;
	}

	if((ret=LoadSystemPara("CM.COMMON", "AAA_MIN_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iAAAMinTime= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("CM.COMMON", "BATCH_ID_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_commonPara.iBatchIdTime= atoi(systemPara.PARA_VALUE);
	return 0;
}


int TSMPara::LoadINPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_LONG_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.longCDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_IMSI_CODE", &systemPara)) != 0)
	{
	     return ret;
	}
	m_pSMPARA->m_INPara.iSMIfalg= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_TIME_CITY", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRTimeCity = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_TIME_ROAM", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRTimeRoam = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_FAV_CELLID_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.favCellIDSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "EXPIRE_REMIND_IN", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.remindDay = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROAM_MSC_VLR", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.roamMSCorVLR = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROAM_CELLID_LAC", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.roamCELLIDorLAC = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROAM_COLLIGATE_MSC", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_INPara.roamColligateMSC, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "DEFAULT_ROAM_PROVINCE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.defaultProv = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "DEFAULT_ROAM_AREA", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.defaultArea = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "EDGE_ROAM_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.edgeRoamSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_MAX_STEP_LTH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.INMaxStepLth = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "AOC_BALANCE_GSU", &systemPara)) != 0)
	{
		return ret;
	}
	else
	{
		m_pSMPARA->m_INPara.balanceGsu = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_SHORT_CDR_COUNTRY", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.shortCDRCountry = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_PREFERENTIAL_VILLAGE_RESULT_CODE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_INPara.resultCode = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "OUT_ORG_CELLID_OR_SAI", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.isOrgCellIdOrSai = 0;
	}
	else
	{
		m_pSMPARA->m_INPara.isOrgCellIdOrSai = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "INIT_ROMAN_CALLED", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.initRoma = 0;
	}
	else
	{
		m_pSMPARA->m_INPara.initRoma = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "ROMAN_LONG_RELATION", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.nRomaLongRelation = 1;
	}
	else
	{
		m_pSMPARA->m_INPara.nRomaLongRelation = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "INIT_ACTIVE_SWITCH", &systemPara)) != 0)
	{
		return -1;
	}
	m_pSMPARA->m_INPara.iInitActiveSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.IN.CONFIG", "CELLID_CONVERT", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.nCellidConvert = 0;;
	}
	else
	{
		m_pSMPARA->m_INPara.nCellidConvert = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.IN.CONFIG", "MONTH_IN_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	else
	{
		m_pSMPARA->m_INPara.iMonthCDRTime = atoi(systemPara.PARA_VALUE);
	}

       if((ret=LoadSystemPara("SM.IN.CONFIG", "IN_DENY_HBUSER", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.iDenyHBUser = 0;
	}
       else
       {
            m_pSMPARA->m_INPara.iDenyHBUser = atoi(systemPara.PARA_VALUE);
       }
    if((ret = LoadSystemPara("SM.IN.CONFIG", "IN_VOLTE_CDR_SWITCH", &systemPara)) != 0)
	{
		m_pSMPARA->m_INPara.inVolteCdrSwitch= 0;
	}
	else
	{
		m_pSMPARA->m_INPara.inVolteCdrSwitch = atoi(systemPara.PARA_VALUE);
	}
	return 0;
}

int TSMPara::Load5GPara()
{

	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_LONG_CDR_VOLUMN", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_5GPara.longCDRVolumn5G = atoi(systemPara.PARA_VALUE);

    if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SERVICE_CONTEX_ID", &systemPara)) != 0)
    {
        return ret;
    }
    strncpy(m_pSMPARA->m_5GPara.szServiceContextId5G,systemPara.PARA_VALUE,128);

	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SHORT_CDR_OCTET", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.shortCDROctet5G = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.shortCDROctet5G = atoi(systemPara.PARA_VALUE);
	}

    if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_DENY_HBUSER", &systemPara)) != 0)
    {
        m_pSMPARA->m_5GPara.iDenyHBUser = 0;
    }
    else
    {
        m_pSMPARA->m_5GPara.iDenyHBUser = atoi(systemPara.PARA_VALUE);
    }

	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SHORT_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_5GPara.shortCDRTime5G = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_NO_OFFLINE_LATN", &systemPara)) != 0)
	{

	}
	else
	{
		std::string value = systemPara.PARA_VALUE;
	    ParseString(value,m_pSMPARA->m_5GPara.v_onlineLatn,",");
	}
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_IDX_TIMEOUT_SEC", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.index_timeout_sec = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.index_timeout_sec = atoi(systemPara.PARA_VALUE);
	}
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_DYN_USE_TABLE", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.iDynUseTable = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.iDynUseTable = atoi(systemPara.PARA_VALUE);
	}
	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_CNPLMN", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.str5GCNPLMN.clear();
	}
	else
	{
		m_pSMPARA->m_5GPara.str5GCNPLMN = systemPara.PARA_VALUE;
	}
	if ((ret = LoadSystemPara("SM.5G.CONFIG", "5G_MULTI_COND_SHORT_CDR", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.lnMCondShortTime5G = 0;
		m_pSMPARA->m_5GPara.lnMCondShortOct5G = 0;
	}
	else
	{
		string strMCondShortCdr = systemPara.PARA_VALUE;
		vector<string> vecCond;
		string strSplit = ",";
		ParseString(strMCondShortCdr, vecCond, strSplit.c_str());
		if (vecCond.size() != 2)
		{
			m_pSMPARA->m_5GPara.lnMCondShortTime5G = 0;
			m_pSMPARA->m_5GPara.lnMCondShortOct5G = 0;
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "LoadSystemPara failed paraGrop[SM.5G.CONFIG] paraKey[5G_MULTI_COND_SHORT_CDR] ret=[%d]", ret);
		}
		else
		{
			m_pSMPARA->m_5GPara.lnMCondShortTime5G = atol(vecCond[0].c_str());
			m_pSMPARA->m_5GPara.lnMCondShortOct5G = atol(vecCond[1].c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadSystemPara Multi Cond Dur5G[%ld], Oct5G[%ld]", 
				m_pSMPARA->m_5GPara.lnMCondShortTime5G, m_pSMPARA->m_5GPara.lnMCondShortOct5G);
		}
	}

	if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_XDR_IDX_TIMEOUT_SEC", &systemPara)) != 0)
	{
		m_pSMPARA->m_5GPara.index_SeqTimeOut_sec = 0;
	}
	else
	{
		m_pSMPARA->m_5GPara.index_SeqTimeOut_sec = atoi(systemPara.PARA_VALUE);
	}
	return 0;
}

int TSMPara::LoadPSPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_LONG_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.longCDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_LONG_CDR_VOLUMN", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.longCDRVolumn = atoi(systemPara.PARA_VALUE);


	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_SHORT_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.shortCDRTime, systemPara.PARA_VALUE);
	ParseShortCDR(m_pSMPARA->m_PSPara.shortCDRTime);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_SHORT_CDR_OCTET", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.shortCDROctet = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "FINAL_UNIT_ACTION", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.finalUnitAction = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_ADDRESS_TYPE", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.redirectType = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_MAX_STEP_AMOUNT", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.PSMaxStepAmount = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_MAX_STEP_DURATION", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.PSMaxStepDuration = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_SERVER_ADDRESS", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.redirectServer, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "ACCUMULATE_INFO", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.accumulateinfo, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_SERVER_ADDRESS_RECHARGE", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szRedirectServerRecharge, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "REDIRECT_SERVER_ADDRESS_LOCK", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szRedirectServerLock, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "RECHARGE_PAGE_RG", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.lRechargePageRG = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "BASIC_STATE_RECHARGE", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szBasicStateRecharge, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "BASIC_STATE_LOCK", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_PSPara.szBasicStateLock, systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_RATING_GROUP", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.cdrRatingGroupSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "OFFLINE_ALL_USU", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.nOfflineAllUsu = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_RG_UNIT_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iRGUNITSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_ACCU_UNIT_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iAccuUNITSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "RAT_TYPE_SWITCH", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.nRatTypeSwitch = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.nRatTypeSwitch = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.PS.CONFIG", "FINAL_UNIT_RESULT_CODE", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.nFinalUintResultCode = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.nFinalUintResultCode = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.PS.CONFIG", "USU_OVERLOAD_REFUSE", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.nUsuOverloadRefuse = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.nUsuOverloadRefuse = atoi(systemPara.PARA_VALUE);
	}

	if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_VISIT_AREA", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iPsVisitArea= atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "LTE_SERVICE_CONTEX_ID", &systemPara)) != 0)
	{
		return ret;
	}
	strncpy(m_pSMPARA->m_PSPara.szServiceContextId,systemPara.PARA_VALUE,128);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "LTE_SERVICE_CONTEX_ID", &systemPara)) != 0)
	{
		return ret;
	}
	strncpy(m_pSMPARA->m_PSPara.szServiceContextId,systemPara.PARA_VALUE,128);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "CDR_RATING_SWITCH", &systemPara)) != 0)
	{
		return ret;
	}
   	m_pSMPARA->m_PSPara.iCdrRatingSwitch = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "MONTH_AAA_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iMonthAAACDRTime = atoi(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.PS.CONFIG", "MONTH_PGW_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_PSPara.iMonthPGWCDRTime = atoi(systemPara.PARA_VALUE);

       if((ret=LoadSystemPara("CM.COMMON", "DYNAMIC_CAMP_SWITCH", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.dynamic_camp_on = 0;
	}
	else
	{
		m_pSMPARA->m_PSPara.dynamic_camp_on = atoi(systemPara.PARA_VALUE);
	}

       if((ret=LoadSystemPara("CM.COMMON", "LOWLIMITBALANCE", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.LLBalance = 20;
	}
	else
	{
		m_pSMPARA->m_PSPara.LLBalance = atoi(systemPara.PARA_VALUE);
	}

       if((ret=LoadSystemPara("SM.PS.CONFIG", "PS_DENY_HBUSER", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.iDenyHBUser = 0;
	}
       else
       {
            m_pSMPARA->m_PSPara.iDenyHBUser = atoi(systemPara.PARA_VALUE);
       }

	   if((ret=LoadSystemPara("SM.PS.CONFIG", "PGW_LOC_CHANGE_CELLID", &systemPara)) != 0)
	{
		m_pSMPARA->m_PSPara.iLocChCellid = 1;
	}
       else
       {
            m_pSMPARA->m_PSPara.iLocChCellid = atoi(systemPara.PARA_VALUE);
       }
	return 0;
}

int TSMPara::LoadISMPPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.ISMP.CONFIG", "ISMP_DEBIT_CONTROL", &systemPara)) != 0)
	{
		return ret;
	}
	m_pSMPARA->m_ISMPPara.debitControl = atoi(systemPara.PARA_VALUE);
	if(m_pSMPARA->m_ISMPPara.debitControl<0 || m_pSMPARA->m_ISMPPara.debitControl>3 )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", , "error ISMP_DEBIT_CONTROL[%d]", m_pSMPARA->m_ISMPPara.debitControl);
		return ret;
	}

	if((ret=LoadSystemPara("SM.ISMP.CONFIG", "ISMP_DEBIT_SPC_PRODUCTID", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_ISMPPara.debitSPCPorductId, systemPara.PARA_VALUE);
	ParseSPCProductId(m_pSMPARA->m_ISMPPara.debitSPCPorductId);

	//完成加载处理特殊spid的值
	if((ret=LoadSystemPara("SM.ISMP.CONFIG", "ISMP_DEBIT_SPC_SPID", &systemPara)) != 0)
	{
		return ret;
	}
	strcpy(m_pSMPARA->m_ISMPPara.sp_id, systemPara.PARA_VALUE);

       if((ret=LoadSystemPara("SM.ISMP.CONFIG", "ISMP_DENY_HBUSER", &systemPara)) != 0)
	{
		m_pSMPARA->m_ISMPPara.iDenyHBUser = 0;
	}
       else
       {
            m_pSMPARA->m_ISMPPara.iDenyHBUser = atoi(systemPara.PARA_VALUE);
       }

	if((ret = LoadSystemPara("SM.ISMP.CONFIG", "ISMP_NOT_MOD_TIME_OFFERID", &systemPara)) == 0)
	{
              //R503，若不在列表中则开始时间修改为当前系统交互时间
              if(systemPara.PARA_VALUE[0])
              {
                    ParseProductOfferId(systemPara.PARA_VALUE);
              }
	}

	return 0;
}

int TSMPara::LoadP2PSMSPara()
{
	SSystemPara	systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.P2PSMS.CONFIG", "P2PSMS_DEBIT_CONTROL", &systemPara)) != 0)
	{
		return -1;
	}
	m_pSMPARA->m_P2PSMSPara.debitflag = atoi(systemPara.PARA_VALUE);

       if((ret=LoadSystemPara("SM.P2PSMS.CONFIG", "P2PSMS_DENY_HBUSER", &systemPara)) != 0)
	{
		m_pSMPARA->m_P2PSMSPara.iDenyHBUser = 0;
	}
       else
       {
            m_pSMPARA->m_P2PSMSPara.iDenyHBUser = atoi(systemPara.PARA_VALUE);
       }

	return 0;
}

int TSMPara::LoadDSLPara()
{
	SSystemPara systemPara;
	int ret = 0;
	if((ret=LoadSystemPara("SM.DSL.CONFIG", "WIFI_USER_NAME", &systemPara)) != 0)
	{
		return ret;
	}
	ParseAreaSubfix(systemPara.PARA_VALUE);

	if((ret=LoadSystemPara("SM.DSL.CONFIG", "MONTH_DSL_CDR_TIME", &systemPara)) != 0)
	{
		return ret;
	}
	else
	{
		m_pSMPARA->m_DSLPara.iMonthDSLCDRTime = atoi(systemPara.PARA_VALUE);
	}

       if((ret=LoadSystemPara("SM.DSL.CONFIG", "DSL_DENY_HBUSER", &systemPara)) != 0)
	{
		m_pSMPARA->m_DSLPara.iDenyHBUser = 0;
	}
       else
       {
            m_pSMPARA->m_DSLPara.iDenyHBUser = atoi(systemPara.PARA_VALUE);
       }
	return 0;
}

int TSMPara::LoadCallPrefix()
{
	char prefix[8] = {0};
	SSystemPara systemPara;
	char *value = NULL;
	int num = 0;
	int ret = 0;
	if((ret=LoadSystemPara("CM.COMMON", "CALL_NUMBER_PREFIX", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	DCBIZLOG(DCLOG_LEVEL_TRACE,0, "", "call number prefix value: [%s]", value);

	while(1)
	{
		for(int i=0; i<8; i++)
		{
			if((*value ==',') || (*value =='\0'))
			{
				prefix[i] = '\0';
				break;
			}
			prefix[i] = *value;
			value++;
		}

		num = atoi(prefix);
		if((num <100) || (num>999))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "covert CALL_NUMBER_PREFIX error [%d]", num);
		}
		pair<map<int, int>::iterator, bool> iter;
		iter = m_pSMPARA->m_call_number_prefix.insert(pair<int, int>(num, num));
		if(!(iter.second))
		{
			// DCBIZLOG(DCLOG_LEVEL_WARN,0, "", "same call number prefix value: [%d]", num);
		}
		if(*value == '\0')
		{
			break;
		}

		value++;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","m_prefixMap size %d",m_pSMPARA->m_call_number_prefix.size());
	return 0;
}
/*
int TSMPara::LoadMsisdnMap()
{
	char value[256] = {0};
	long long beginNum = 0;
	MSISDN_MAP_INFO *msisdn_info = NULL;
	UDBSQL* query = m_sql.m_pSELECT_SPR_MSISDN_AREA_MAP;
	try
	{
		query->UnBindParam();
		query->Execute();
		while(query->Next())
		{
			msisdn_info = new MSISDN_MAP_INFO();

			query->GetValue(1, value);
			beginNum = atol(value);
			query->GetValue(2, value);
			msisdn_info->endMsisdn = atol(value);
			query->GetValue(3, value);
			msisdn_info->homeProv = atoi(value);
			query->GetValue(4, value);
			msisdn_info->areaCode = atoi(value);
			query->GetValue(5, value);
			msisdn_info->userType = atoi(value);
			query->GetValue(6, value);
			msisdn_info->bizType = atoi(value);
			query->GetValue(7, value);
			msisdn_info->cspid = atoi(value);
			query->GetValue(8, value);
			msisdn_info->networkType = atoi(value);

			m_pSMPARA->m_msisdn_area_map.insert(pair<long long, MSISDN_MAP_INFO*>(beginNum, msisdn_info));

			// DCBIZLOG(DCLOG_LEVEL_WARN,0, "", "same msisdn value: [%ld]", beginNum);
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load spr_msisdn_area_map failed: [%s]", e.ToString());
		return -1;
	}

	if(m_pSMPARA->m_msisdn_area_map.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "result spr_msisdn_area_map map size is :[0]");
		return -1;
	}

	return 0;
}
*/
int TSMPara::LoadSprUrgentNumber()
{
	char value[256] = {0};
	long long beginNum = 0;
	URGENT_NUMBER_INFO *data = NULL;
	m_sql.m_pSELECT_SPR_URGENT_NUMBER = m_dbm->GetSQL("q_spr_urgent_number");
	UDBSQL* query = m_sql.m_pSELECT_SPR_URGENT_NUMBER;
	try
	{
		query->UnBindParam();
		query->Execute();
		while(query->Next())
		{
			data = new URGENT_NUMBER_INFO();

			query->GetValue(1, value);
			beginNum = atol(value);
			query->GetValue(2, data->serviceType);
			query->GetValue(3, data->provCode);
			query->GetValue(4, data->areaCode);

			m_pSMPARA->m_spr_urgent_map.insert(pair<long long, URGENT_NUMBER_INFO*>(beginNum, data));

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load spr_msisdn_area_map failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadSmUserState()
{
	char tmp[256] = {0};
	int bizType = 0;
	USER_STATE_INFO * userInfo = NULL;

	m_sql.m_pSELECT_SM_USER_STATE = m_dbm->GetSQL("q_sm_user_state");
	UDBSQL* query = m_sql.m_pSELECT_SM_USER_STATE;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			userInfo = new USER_STATE_INFO();
			query->GetValue(1, tmp);
			bizType = atoi(tmp);

			query->GetValue(2, tmp);
			userInfo->resultCode = atoi(tmp);

			query->GetValue(3, userInfo->basicState);

			query->GetValue(4, userInfo->extState);

			query->GetValue(5, userInfo->stausCd);

			query->GetValue(6, userInfo->stopType);

			// DCBIZLOG(DCLOG_LEVEL_TRACE,0, "", "load SM_USER_STATE biztype[%d], resultCode[%d], basic_state[%s],extState[%s]",
			//	bizType, userInfo->resultCode, userInfo->basicState, userInfo->extState);

			m_pSMPARA->m_user_state_map.insert(pair<int, USER_STATE_INFO*>(bizType, userInfo));

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SM_USER_STATE failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadMscVlrCode()
{
	char tmp[256] = {0};
	MSC_INFO *pmscinfo = NULL;

	m_sql.m_pSELECT_SPR_MSC = m_dbm->GetSQL("q_spr_msc");
	UDBSQL* query = m_sql.m_pSELECT_SPR_MSC;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			pmscinfo = new MSC_INFO();
			query->GetValue(1, tmp);
			string str = tmp;

			query->GetValue(2, tmp);
			strncpy(pmscinfo->area_code, tmp,14);

			query->GetValue(3, pmscinfo->carriers);

			pair<map<string,MSC_INFO* >::iterator,bool> ret =
				m_pSMPARA->m_spr_msc_vlr_map.insert(pair<string, MSC_INFO*>(str, pmscinfo));
			if(ret.second == false)
				delete pmscinfo;

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load MSCorVLR failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadEdgeRoamLac()
{
	char tmp[256] = {0};
	EDGE_ROAM_INFO *edgeRoamInfo = NULL;

	UDBSQL* query = m_sql.m_pSELECT_SPR_EDGE_ROAM;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			edgeRoamInfo = new EDGE_ROAM_INFO();

			query->GetValue(1, tmp);
			string str = tmp;

			query->GetValue(2, edgeRoamInfo->areaCode);

			query->GetValue(3, edgeRoamInfo->ecellAreaCode);

			query->GetValue(4, tmp);
			edgeRoamInfo->isProvEdge = tmp[0];

			m_pSMPARA->m_edge_roam_lac_map.insert(pair<string, EDGE_ROAM_INFO*>(str, edgeRoamInfo));


		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load MSCorVLR failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadEdgeRoamCellid()
{
	char tmp[256] = {0};
	EDGE_ROAM_INFO *edgeRoamInfo = NULL;


	UDBSQL* query = m_sql.m_pSELECT_SPR_EDGE_ROAM_002;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			edgeRoamInfo = new EDGE_ROAM_INFO();

			query->GetValue(1, tmp);
			string str = tmp;

			query->GetValue(2, edgeRoamInfo->areaCode);

			query->GetValue(3, edgeRoamInfo->ecellAreaCode);

			query->GetValue(4, tmp);
			edgeRoamInfo->isProvEdge = tmp[0];

			m_pSMPARA->m_edge_roam_cellid_map.insert(pair<string, EDGE_ROAM_INFO*>(str, edgeRoamInfo));


		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load MSCorVLR failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadEdgeRoamData()
{
	char tmp[256] = {0};
	EDGE_ROAM_INFO *edgeRoamInfo = NULL;


	UDBSQL* query = m_sql.m_pSELECT_SPR_EDGE_ROAM_DATA;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			edgeRoamInfo = new EDGE_ROAM_INFO();

			query->GetValue(1, tmp);
			string str = tmp;

			query->GetValue(2, edgeRoamInfo->areaCode);

			query->GetValue(3, edgeRoamInfo->szmsc);

			query->GetValue(4, edgeRoamInfo->ecellAreaCode);

			query->GetValue(5, tmp);
			edgeRoamInfo->isProvEdge = tmp[0];

			m_pSMPARA->m_edge_roam_map_data.insert(pair<string, EDGE_ROAM_INFO*>(str, edgeRoamInfo));

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load spr_edge_roam_data failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}
/**
int TSMPara::LoadSprNP()
{
	char tmp[256] = {0};
	long NpCode = 0;

	UDBSQL* query = m_sql.m_pSELECT_SPR_NP;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, tmp);
			NpCode = atol(tmp);
			query->GetValue(2, tmp);
			string str = tmp;

			m_pSMPARA->m_spr_np_map.insert(pair<long, string>(NpCode, str));
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_NP failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}
**/
int TSMPara::LoadSprPDSN()
{
	char tmp[256] = {0};
	PDSN_INFO *pdsninfo = NULL;

	m_sql.m_pSELECT_SPR_PDSN = m_dbm->GetSQL("q_spr_pdsn");
	UDBSQL* query = m_sql.m_pSELECT_SPR_PDSN;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			pdsninfo= new PDSN_INFO();
			query->GetValue(1, tmp);
			string str = tmp;
			query->GetValue(2, tmp);
			pdsninfo->prov_code = atoi(tmp);
			query->GetValue(3, tmp);
			pdsninfo->area_code = atoi(tmp);

			pair<map<string,PDSN_INFO* >::iterator,bool> ret =
				m_pSMPARA->m_spr_pdsn_map.insert(pair<string, PDSN_INFO*>(str, pdsninfo));
			if(ret.second == false)
				delete pdsninfo;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_PDSN failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadSprSGSN()
{
	char tmp[256] = {0};
	SGSN_INFO *sgsninfo = NULL;

	m_sql.m_pSELECT_SPR_SGSN = m_dbm->GetSQL("q_spr_sgsn");
	UDBSQL* query = m_sql.m_pSELECT_SPR_SGSN;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			sgsninfo = new SGSN_INFO();
			query->GetValue(1, tmp);
			string str = tmp;
			query->GetValue(2, sgsninfo->prov_code);
			query->GetValue(3, sgsninfo->area_code);

			pair<map<string,SGSN_INFO* >::iterator,bool> ret =
				m_pSMPARA->m_spr_sgsn_map.insert(pair<string, SGSN_INFO*>(str, sgsninfo));
			if(ret.second == false)
				delete sgsninfo;

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_DSL failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadSprDSL()
{
	char tmp[256] = {0};
	DSL_INFO *dslinfo = NULL;

	m_sql.m_pSELECT_SPR_DSL = m_dbm->GetSQL("q_spr_dsl");
	UDBSQL* query = m_sql.m_pSELECT_SPR_DSL;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
  			dslinfo = new DSL_INFO();
			query->GetValue(1, tmp);
			string str = tmp;
			query->GetValue(2, tmp);
			dslinfo->prov_code = atoi(tmp);
			query->GetValue(3, tmp);
			dslinfo->area_code = atoi(tmp);

			pair<map<string,DSL_INFO* >::iterator,bool> ret =
				m_pSMPARA->m_spr_dsl_map.insert(pair<string, DSL_INFO*>(str, dslinfo));
			if(ret.second == false)
				delete dslinfo;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_NP failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadSprLAC()
{
	char tmp[256] = {0};
	int area_code = 0;

	m_sql.m_pSELECT_SPR_LAC = m_dbm->GetSQL("q_spr_lac");
	UDBSQL* query = m_sql.m_pSELECT_SPR_LAC;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, tmp);
			string str = tmp;
			query->GetValue(2, tmp);
			area_code = atoi(tmp);

			m_pSMPARA->m_spr_lac_map.insert(pair<string, int>(str, area_code));

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_LAC failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadSprCELL()
{
	char tmp[256] = {0};
	int area_code = 0;

	m_sql.m_pSELECT_SPR_CELL = m_dbm->GetSQL("q_spr_cell");
	UDBSQL* query = m_sql.m_pSELECT_SPR_CELL;
	try
	{
		query->UnBindParam();
		query->Execute();
  		while (query->Next())
  		{
			query->GetValue(1, tmp);
			string str = tmp;
			query->GetValue(2, tmp);
			area_code = atoi(tmp);

			m_pSMPARA->m_spr_cell_map.insert(pair<string, int>(str, area_code));
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_CELL failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::LoadResultCodeMap()
{
	char tmp[256] = {0};
	long curData	= 0;
	SResultCode *resultCode = NULL;
	m_sql.m_pSELECT_SM_RESULTCODE_MAP = m_dbm->GetSQL("q_sm_resultcode_map");
	UDBSQL* query = m_sql.m_pSELECT_SM_RESULTCODE_MAP;

	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;

	try
	{
		query->UnBindParam();
		query->Execute();

  		while(query->Next())
  		{
			resultCode = new SResultCode;

			query->GetValue(1, tmp);
			resultCode->resultCode = atoi(tmp);

			query->GetValue(2, tmp);
			resultCode->OCPResultCode = atoi(tmp);

			query->GetValue(3, tmp);
			resultCode->codeType = atoi(tmp);

			pair<map<int, SResultCode*>::iterator, bool> iter;

			iter = m_pSMPARA->m_result_code_map.insert(pair<int, SResultCode*>(resultCode->resultCode, resultCode));
			if(!(iter.second))
			{
				delete resultCode;
				// DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "same result code value: [%d]", resultCode->resultCode);
			}
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load result code map failed: [%s]", e.ToString());
		return -1;
	}

	if(m_pSMPARA->m_result_code_map.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "result code map size is :[0]");
		return -1;
	}

	return 0;
}

int TSMPara::LoadBillAttr()
{
	char value[256] = {0};
	int  attrvalue	= 0;
	string sessionAttrCode;
	m_sql.m_pSELECT_TB_BIL_SVECONT_ATTR_MAP = m_dbm->GetSQL("q_svecont_arrt_map");
	UDBSQL* query = m_sql.m_pSELECT_TB_BIL_SVECONT_ATTR_MAP;

	try
	{
		query->UnBindParam();
		query->Execute();

  		while(query->Next())
  		{
			query->GetValue(1, value);
			sessionAttrCode = value;

			query->GetValue(2, value);
			sessionAttrCode += value;

			query->GetValue(3, attrvalue);


			m_pSMPARA->m_svecont_attr_map.insert(pair<string, int>(sessionAttrCode, attrvalue));
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load TB_BIL_SVECONT_ATTR_MAP failed: [%s]", e.ToString());
		return -1;
	}

	if(m_pSMPARA->m_result_code_map.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "result TB_BIL_SVECONT_ATTR_MAP size is :[0]");
		return -1;
	}

	return 0;
}


int TSMPara::LoadServiceType()
{
	SSystemPara systemPara;
	char *value = NULL;
	int num = 0;
	int ret = 0;
	if((ret=LoadSystemPara("CM.COMMON", "IN_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(VOICE, value);

	if((ret=LoadSystemPara("CM.COMMON", "P2PSMS_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(SMS, value);

	if((ret=LoadSystemPara("CM.COMMON", "PS_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(DATA, value);

	if((ret=LoadSystemPara("CM.COMMON", "ISMP_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(ISMP, value);

	if((ret=LoadSystemPara("CM.COMMON", "ISMP_SERVICE_CONTEXT_REFUND_FREE", &systemPara)) != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "",  "load system parameter fail, [%s],[%s]", "CM.COMMON", "ISMP_SERVICE_CONTEXT_REFUND_FREE");
	}
	string strParaValue = systemPara.PARA_VALUE;
	ParseString(strParaValue,m_pSMPARA->m_vecIsmpRefund,",");


	if((ret=LoadSystemPara("CM.COMMON", "DSL_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(DSL, value);

	if((ret=LoadSystemPara("CM.COMMON", "RATABLE_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(RATA, value);

	if((ret=LoadSystemPara("CM.COMMON", "HIGH_RISK_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(HRS, value);

	if((ret=LoadSystemPara("CM.COMMON", "CCG_SERVICE_CONTEXT_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(CCG, value);


	if((ret=LoadSystemPara("CM.COMMON", "CDR_RATING_BY_SERVICE", &systemPara)) != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "",  "load system parameter fail, [%s],[%s]", "CM.COMMON", "CDR_RATING_BY_SERVICE");
	}
	strParaValue = systemPara.PARA_VALUE;

	ParseString(strParaValue,m_pSMPARA->m_vecCdrByService,",");

	if((ret=LoadSystemPara("SM.PS.CONFIG", "LTE_SERVICE_CONTEX_ID", &systemPara)) != 0)
	{
		return ret;
	}
	value = systemPara.PARA_VALUE;
	ParseServiceType(PGW, value);

    if((ret=LoadSystemPara("SM.5G.CONFIG", "5G_SERVICE_CONTEX_ID", &systemPara)) != 0)
    {
        return ret;
    }
    value = systemPara.PARA_VALUE;
    ParseServiceType(DATA_5G, value);

	return 0;

}

int TSMPara::LoadRoamBorder()
{
	char value[80];
	sRoamBorderLine m_stRoamBorderLine;
	// m_iMscLacFlag = 2;

	m_sql.m_pSELECT_PAR_ROAM_BORDERLINE = m_dbm->GetSQL("q_par_roam_borderline");
	UDBSQL* query = m_sql.m_pSELECT_PAR_ROAM_BORDERLINE;

	try
	{
		query->UnBindParam();
		query->Execute();
		char sKey[128];
		while (query->Next())
		{
			memset(&m_stRoamBorderLine,0,sizeof(m_stRoamBorderLine));

			query->GetValue(1, value);
			strcpy(m_stRoamBorderLine.m_sVisitMscId, value);

			query->GetValue(2, value);
			strcpy(m_stRoamBorderLine.m_sVisitLacId, value);

			query->GetValue(3, value);
			strcpy(m_stRoamBorderLine.m_sVisitCellId, value);

			query->GetValue(4, value);
			strcpy(m_stRoamBorderLine.m_sHomeAreaCode, value);

			query->GetValue(5, value);
			strcpy(m_stRoamBorderLine.m_sEffDate, value);

			query->GetValue(6, value);
			strcpy(m_stRoamBorderLine.m_sExpDate, value);

			query->GetValue(7, value);
			strcpy(m_stRoamBorderLine.m_sMsisdn, value);

			query->GetValue(8, value);
			strcpy(m_stRoamBorderLine.m_sBorderType, value);

			query->GetValue(9, value);
			strcpy(m_stRoamBorderLine.m_sVisitAreaCode, value);

			/*
			stringTrim(OCI_sVisitMscId);
			stringTrim(OCI_sVisitLacId);
			stringTrim(OCI_sVisitCellId);
			stringTrim(OCI_sHomeAreaCode);
			stringTrim(OCI_sEffDate);
			stringTrim(OCI_sExpDate);
			stringTrim(OCI_sMsisdn);

			stringTrim(OCI_sBorderType);
			stringTrim(OCI_sVisitAreaCode);
			*/

			sprintf(sKey,"%d|%s",atoi(m_stRoamBorderLine.m_sHomeAreaCode), m_stRoamBorderLine.m_sVisitLacId);
			m_pSMPARA->m_mapRoamBorderNew.insert(multimap<string,sRoamBorderLine>::value_type(sKey,m_stRoamBorderLine)) ;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load PAR_ROAM_BORDERLINE failed: [%s]", e.ToString());
		return -1;
	}
	return 0;
}

int TSMPara::LoadRoamBorderData()
{
	char value[80];
	sRoamBorderLineData m_stRoamBorderLineData;
	// m_iMscLacFlag = 2;

	m_sql.m_pSELECT_PAR_ROAM_BORDERLINE_AAA = m_dbm->GetSQL("q_par_roam_borderline_aaa");
	UDBSQL* query = m_sql.m_pSELECT_PAR_ROAM_BORDERLINE_AAA;

	if(!query)
	{
		return 0;
	}

	try
	{
		query->UnBindParam();
		query->Execute();
		while (query->Next())
		{
			memset(&m_stRoamBorderLineData,0,sizeof(m_stRoamBorderLineData));

			query->GetValue(1, value);
			strcpy(m_stRoamBorderLineData.home_msc_id, value);

			query->GetValue(2, value);
			strcpy(m_stRoamBorderLineData.visit_lac_id, value);

			query->GetValue(3, value);
			strcpy(m_stRoamBorderLineData.home_latn_id, value);

			query->GetValue(4, value);
			strcpy(m_stRoamBorderLineData.event_type_id, value);

			query->GetValue(5, value);
			strcpy(m_stRoamBorderLineData.bsid, value);

			query->GetValue(6, value);
			strcpy(m_stRoamBorderLineData.eff_date, value);

			query->GetValue(7, value);
			strcpy(m_stRoamBorderLineData.exp_date, value);

			query->GetValue(8, value);
			strcpy(m_stRoamBorderLineData.billing_object, value);

			m_pSMPARA->m_mapRoamBorderData.insert(multimap<string,sRoamBorderLineData>::value_type(m_stRoamBorderLineData.bsid,m_stRoamBorderLineData)) ;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load PAR_ROAM_BORDERLINE_AAA failed: [%s]", e.ToString());
		return -1;
	}
	return 0;
}
/*
int TSMPara::GetTariffIDMap(int ocp_tariffid)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<int, int>::iterator iter = pSMPARA->m_tariffid_map.find(ocp_tariffid);
	if(iter == pSMPARA->m_tariffid_map.end())
	{
		return 0;
	}
	else
	{
		return iter->second;
	}
}
*/

int TSMPara::LoadSprSGSN_IPV6()
{
	char tmp[256] = {0};
	SGSN_INFO *sgsninfo = NULL;

	m_sql.m_pSELECT_SPR_SGSN_IPV6 = m_dbm->GetSQL("q_spr_sgsn_ipv6");
	UDBSQL* query = m_sql.m_pSELECT_SPR_SGSN_IPV6;
	if(NULL == query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetSQL q_spr_sgsn_ipv6  failed.");
		return -1;
	}

	try
	{
		query->UnBindParam();
		query->Execute();
		while (query->Next())
		{
			sgsninfo = new SGSN_INFO();
			query->GetValue(1, tmp);
			string str = tmp;
			query->GetValue(2, sgsninfo->prov_code);
			query->GetValue(3, sgsninfo->area_code);

			PushAddress(str,sgsninfo);

		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load SPR_DSL failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}
void TSMPara::PushAddress(string ipv6Address,SGSN_INFO* sgsninfo)
{
    size_t npos = ipv6Address.find("/");
    int mask = 128;
    if(npos != string::npos)
    {
        mask = atoi(ipv6Address.substr(npos+1).c_str());
        ipv6Address = ipv6Address.substr(0,npos);
        PushMask(mask);
    }
    npos = ipv6Address.find("::");
    if(npos != string::npos)
    {
        string beginAddr = ipv6Address.substr(0,npos);
        string endAddr = ipv6Address.substr(npos+2);
        ipv6Address = RegularAddr(beginAddr,endAddr);
    }
    else
    {
        ipv6Address = RegularAddr(ipv6Address,"");
    }
    ipv6Address =  MaskAddress(ipv6Address,mask);
    pair<map<string, SGSN_INFO* >::iterator,bool> ret = m_pSMPARA->m_spr_sgsn_ipv6_map.insert(pair<string, SGSN_INFO*>(ipv6Address, sgsninfo));
	if(ret.second == false)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "ipv6Address [%s] multi define", ipv6Address.c_str());
		delete sgsninfo;
	}
    return;

}
string TSMPara::MaskAddress(string ipv6Address,int mask)
{
    int tp = mask % 4;
    int pos = (mask-1) / 4;
    if(tp)
    {
        char ch = ipv6Address[pos];
        if(ch >= 'A' && ch <= 'F')
            ch = ch - 'A' + 10;
        else
            ch = ch - '0';
        char t = 0x8;    //1000
        if(tp == 2)
            t = 0xC;    //1100
        else if(tp == 3)
            t = 0xE;  //1110
        ch = ch & t;
        ipv6Address[pos] = HexTable[ch];
    }
    for(int i = pos+1; i < ipv6Address.size();i++)
    {
        ipv6Address[i] = '0';
    }
    return ipv6Address;
}
string TSMPara::RegularAddr(string beginAddr,string endAddr)
{
    size_t npos;
    size_t nposLast = 0;
    while((npos = beginAddr.find(':',nposLast))!= string::npos)
    {
        if(npos-nposLast < 4)
        {
            beginAddr.insert(nposLast,4 - (npos-nposLast) ,'0');
            npos = beginAddr.find(':',nposLast);
        }
        nposLast = npos + 1;
    }
    if(beginAddr.size() - nposLast < 4)
    {
        beginAddr.insert(nposLast,4 - (beginAddr.size()-nposLast) ,'0');
    }
    nposLast = 0;
    while((npos = endAddr.find(':',nposLast))!= string::npos)
    {
        if(npos-nposLast < 4)
        {
            endAddr.insert(nposLast,4 - (npos-nposLast) ,'0');
            npos = endAddr.find(':',nposLast);
        }
        nposLast = npos + 1;
    }
    if(endAddr.size() - nposLast < 4 && endAddr.size() > 0)
    {
        endAddr.insert(nposLast,4 - (endAddr.size()-nposLast) ,'0');
    }
    string tmp;
    for(unsigned int i = 0;i < beginAddr.size(); i++)
    {
        if(beginAddr[i] == ':')
            continue;
        if(beginAddr[i] >= 'a' && beginAddr[i] <= 'f')
        {
            tmp += (beginAddr[i]-'a'+'A');
        }
        else if(beginAddr[i] >= 'A' && beginAddr[i] <= 'F')
            tmp += beginAddr[i];
        else if(beginAddr[i] >= '0' && beginAddr[i] <= '9')
            tmp += beginAddr[i];
    }
    string tmp2;
    for(unsigned int i = 0;i < endAddr.size(); i++)
    {
        if(endAddr[i] == ':')
            continue;
        if(endAddr[i] >= 'a' && endAddr[i] <= 'f')
        {
            tmp2 += (endAddr[i]-'a'+'A');
        }
        else if(endAddr[i] >= 'A' && endAddr[i] <= 'F')
            tmp2 += endAddr[i];
        else if(endAddr[i] >= '0' && endAddr[i] <= '9')
            tmp2 += endAddr[i];
    }
    int len = 32 - tmp.size() - tmp2.size();
    if(len > 0)
        tmp.insert(tmp.end(),len,'0');
    tmp += tmp2;
    return tmp;
}
void TSMPara::PushMask(int mask)
{
    for(std::vector<int>::iterator itv = m_pSMPARA->v_mask.begin(); itv != m_pSMPARA->v_mask.end(); itv++)
    {
        if(mask > *itv)
        {
            m_pSMPARA->v_mask.insert(itv,mask);
            return;
        }
        else if(mask == *itv)
            return;
    }
    m_pSMPARA->v_mask.push_back(mask);
    return;
}

int  TSMPara::GetGsmCodeMap(long long beginMsisdn, MSISDN_MAP_INFO& msisdn_info)
{
	msisdn_info.flag = 0;
	long long beginNum = 0;
	char value[64] = {0};
	m_sql.m_pSELECT_PAR_GSM_CODE_MAP = m_dbm->GetSQL("q_par_gsm_code_all");
	UDBSQL* query = m_sql.m_pSELECT_PAR_GSM_CODE_MAP;
	if(NULL == query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "get PAR_GSM_CODE_MAP failed");
		return -1;
	}
	try
	{
		query->UnBindParam();
		query->BindParam(1, beginMsisdn);
		query->Execute();
		if(query->Next())
		{
			query->GetValue(1, value);
			msisdn_info.homeProv = atoi(value);
			query->GetValue(2, value);
			msisdn_info.areaCode = atoi(value);
			query->GetValue(3, value);
			msisdn_info.cspid = atoi(value);
			query->GetValue(4, value);
			msisdn_info.networkType = atoi(value);

			msisdn_info.flag = 1;

			// DCBIZLOG(DCLOG_LEVEL_WARN,0, "", "same msisdn value: [%ld]", beginNum);
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load PAR_GSM_CODE_MAP failed: [%s]", e.ToString());
		return -1;
	}

	return 0;
}

int TSMPara::GetRangeCodeMap(long long beginMsisdn, MSISDN_MAP_INFO& msisdn_info)
{
	msisdn_info.flag = 0;
	long long beginNum = 0;
	char value[64] = {0};
	m_sql.m_pSELECT_PAR_RANGE_CODE_MAP = m_dbm->GetSQL("q_par_range_code");
	UDBSQL* query = m_sql.m_pSELECT_PAR_RANGE_CODE_MAP;
	if(NULL == query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "get PAR_RANGE_CODE_MAP failed");
		return -1;
	}
	try
	{
		query->UnBindParam();
		query->BindParam(1, beginMsisdn);
		query->Execute();
		if(query->Next())
		{
			query->GetValue(1, value);
			msisdn_info.homeProv = atoi(value);
			query->GetValue(2, value);
			msisdn_info.areaCode = atoi(value);
			query->GetValue(3, value);
			msisdn_info.cspid = atoi(value);
			query->GetValue(4, value);
			msisdn_info.networkType = atoi(value);

			msisdn_info.flag = 1;

			// DCBIZLOG(DCLOG_LEVEL_WARN,0, "", "same msisdn value: [%ld]", beginNum);
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load PAR_GSM_CODE_MAP failed: [%s]", e.ToString());
		return -1;
	}
	return 0;
}


/*
MSISDN_MAP_INFO * TSMPara::GetMsisdnAreaMap(long long beginMsisdn,int nfind)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	multimap<long long, MSISDN_MAP_INFO*>::iterator iter = pSMPARA->m_msisdn_area_map.lower_bound(beginMsisdn);
	while(iter != pSMPARA->m_msisdn_area_map.upper_bound(beginMsisdn))
	{
		if(nfind)
		{
			if(beginMsisdn== iter->second->endMsisdn)
			{
				return iter->second;
			}
		}
		else
		{
			if(beginMsisdn < iter->second->endMsisdn )
			{
				return iter->second;
			}
		}
		iter++;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", "find no msisdn number[%lld]", beginMsisdn);
	return NULL;
}
*/
int TSMPara::GetCutArea(int area)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	vector<int>::iterator it = find(pSMPARA->m_vecCutArea.begin(),pSMPARA->m_vecCutArea.end(),area);
	if(it != pSMPARA->m_vecCutArea.end())
	{
		return 1;
	}
	return 0;
}

string TSMPara::GetRoamBorderData(string bsid, int ilatn)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "bsid[%s], ilatn[%d]",bsid.c_str(), ilatn);

	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	multimap<string,sRoamBorderLineData>::iterator itr_border = pSMPARA->m_mapRoamBorderData.lower_bound(bsid);
       multimap<string,sRoamBorderLineData>::iterator iter_end = pSMPARA->m_mapRoamBorderData.upper_bound(bsid);
	for(;itr_border != iter_end; itr_border++)
	{
               if(atoi(itr_border->second.home_latn_id) == ilatn)
               {
                    return itr_border->second.event_type_id;
               }
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "not find bsid[%s]",bsid.c_str());
	return "";
}


int TSMPara::GetRoamBorderType(const char *vi_sMsisdn,const char *vi_sMscId, const char *vi_LacId,const char *vi_CellId, const SPhone& home, AREA_INFO* visit)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
    	char sKey[128];
	multimap<string,sRoamBorderLine>::iterator itr_border;
    	sprintf(sKey,"%d|%s",home.area,vi_CellId);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,SM_OTHER_TYPE, "", "Key[%s], MscId[%s], CellId[%s], VisitAreaCode[%d]", sKey, vi_sMscId, vi_CellId, visit->area);
    	itr_border = pSMPARA->m_mapRoamBorderNew.find(sKey);
    	if(pSMPARA->m_mapRoamBorderNew.end() == itr_border )
    	{
    		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "no find BorderType");
       		 return -1;//不是边界漫游
    	}
	else
    	{
       		for(;itr_border != pSMPARA->m_mapRoamBorderNew.end();++itr_border)
        	{
        		// DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "Key[%s], MscId[%s], CellId[%s], VisitAreaCode[%d]",itr_border->first.c_str(),  itr_border->second.m_sVisitMscId, itr_border->second.m_sVisitCellId, atoi(itr_border->second.m_sVisitAreaCode));
			if ((strcmp(sKey,itr_border->first.c_str()) == 0)) // &&
			     //(strcmp(itr_border->second.m_sVisitMscId,vi_sMscId) == 0) &&
			     // (strcmp(itr_border->second.m_sVisitCellId,vi_CellId) == 0)) // &&
	                   // (strcmp(itr_border->second.m_sEffDate,vi_sStartTime) > 0 && strcmp(itr_border->second.m_sExpDate,vi_sStartTime) < 0) &&
	                  // (strcmp(itr_border->second.m_sMsisdn,vi_sMsisdn) != 0))
            		{
            			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "","home province[%d], visit province[%d]",home.province, visit->province );
				//是否是省内相邻处
	            		if(itr_border->second.m_sBorderType[0] == '1' && strlen(itr_border->second.m_sVisitAreaCode) > 1)
	            		{
					if(atoi(itr_border->second.m_sVisitAreaCode) == visit->area)
					{
                       			return 7; // 省内边界漫游
                    			}
	            		}
				else if(home.province != visit->province)
				{
					return 8; // 省际边界漫游
				}
			}
            		/*
            		//是否是省内市话
            		if(itr_border->second.m_sBorderType[0] == '1' && vi_iCallType == 2)//被叫
            		{
                		ret = 10;
                		break;
            		}

            		if(itr_border->second.m_sBorderType[0] == '1' && vi_iCallType == 1)//主叫
            		{
                		if(strcmp(vi_sVisitAreaCode,vi_sOtherHomeCode) == 0)
                		{
                    			ret = 10;
                    			break;
                		}
            		}
            */
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "","not RoamBorder");
	}
	return -1;
}



int TSMPara::GetEdgeRoamLac(const char * lac, int homeArea,AREA_INFO* visit)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	multimap<string, EDGE_ROAM_INFO*>::iterator iter = pSMPARA->m_edge_roam_lac_map.lower_bound(lac);
	while(iter != pSMPARA->m_edge_roam_lac_map.upper_bound(lac))
	{
		/*安徽 0553的用户到了0551，0551和0553不存在边界基站，漫游类型判断为未漫游。
	      日志分析：
	     1. 程序先根据msc判断出用户的漫游类型为1，即省内漫游
	     2.接着根据cellid做进一步的漫游判断，边漫判断开关为开，走边漫判断流程，
	       目前边漫的判断流程是：根据用户的cellid到边界漫游表找记录，
	       如果用户的归属区号和基站覆盖区相等，为省际或者省内边漫
	       如果用户的归属区和基站的归属区相等，判断为未漫游。
	       程序找到了
	       IN|D|19:11:59.583736|12|3277|GetEdgeRoamCellid|TSMPara.cpp|cellid[3041],belong area[0553],cover area[0563],home area[553],province flag[78]
	      记录，满足规则，判断为了未漫游。


	     问题解决：
	     该规则存在问题，应该是判断用户的归属地和拜访地是否存在基站，存在的前提下，做边漫判断。
		即加上条件(visit.area==atoi(iter->second->areaCode)*/
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cellid[%s],belong area[%s],cover area[%s],home area[%d],province flag[%d],visit area[%d]", lac, iter->second->areaCode, iter->second->ecellAreaCode, homeArea,iter->second->isProvEdge,visit->area);
		if(visit->area==atoi(iter->second->areaCode))
		{
			if(atoi(iter->second->ecellAreaCode) == homeArea)//覆盖区AREA_CODE
			{
				if('Y' == iter->second->isProvEdge || 'y' == iter->second->isProvEdge || '1' == iter->second->isProvEdge )
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "roma=8:spr_edge_roam.earea==home.area,is edge prov", "");
					return 8;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=7:spr_edge_roam.earea==home.area,not edge prov", "");
					return 7;
				}
			}
			else if(atoi(iter->second->areaCode) == homeArea)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=0:spr_edge_roam.area==home.area", "");
				return 0;
			}
		}
		iter++;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no edge roma, lac[%s]", lac);
	return -1;
}

int TSMPara::GetEdgeRoamCellid(const char * cellid, int homeArea,AREA_INFO* visit)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	multimap<string, EDGE_ROAM_INFO*>::iterator iter = pSMPARA->m_edge_roam_cellid_map.lower_bound(cellid);
	while(iter != pSMPARA->m_edge_roam_cellid_map.upper_bound(cellid))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "cellid[%s],belong area[%s],cover area[%s],home area[%d],province flag[%d],visit area[%d]", cellid, iter->second->areaCode, iter->second->ecellAreaCode, homeArea,iter->second->isProvEdge,visit->area);
		/*安徽 0553的用户到了0551，0551和0553不存在边界基站，漫游类型判断为未漫游。
	      日志分析：
	     1. 程序先根据msc判断出用户的漫游类型为1，即省内漫游
	     2.接着根据cellid做进一步的漫游判断，边漫判断开关为开，走边漫判断流程，
	       目前边漫的判断流程是：根据用户的cellid到边界漫游表找记录，
	       如果用户的归属区号和基站覆盖区相等，为省际或者省内边漫
	       如果用户的归属区和基站的归属区相等，判断为未漫游。
	       程序找到了
	       IN|D|19:11:59.583736|12|3277|GetEdgeRoamCellid|TSMPara.cpp|cellid[3041],belong area[0553],cover area[0563],home area[553],province flag[78]
	      记录，满足规则，判断为了未漫游。


	     问题解决：
	     该规则存在问题，应该是判断用户的归属地和拜访地是否存在基站，存在的前提下，做边漫判断。
		即加上条件(visit.area==atoi(iter->second->areaCode)*/
		if(visit->area==atoi(iter->second->areaCode))
		{
			if(atoi(iter->second->ecellAreaCode) == homeArea)//覆盖区AREA_CODE
			{
				if('Y' == iter->second->isProvEdge || 'y' == iter->second->isProvEdge || '1' == iter->second->isProvEdge )
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=8:spr_edge_roam.earea==home.area,is edge prov", "");
					return 8;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=7:spr_edge_roam.earea==home.area,not edge prov", "");
					return 7;
				}
			}
			else if(atoi(iter->second->areaCode) == homeArea)//归属区ECELL_AREA_CODE
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=0:spr_edge_roam.area==home.area,not edge roman", "");
				return 0;
			}
		}
		iter++;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no edge roma,cellid[%s]", cellid);
	return -1;
}


int TSMPara::GetEdgeRoamDataCellid(const char * cellid, const char* mscinfo,int homeArea,AREA_INFO* visit,int roamtype)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	multimap<string, EDGE_ROAM_INFO*>::iterator iter = pSMPARA->m_edge_roam_map_data.lower_bound(cellid);
	while(iter != pSMPARA->m_edge_roam_map_data.upper_bound(cellid))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "visitcode[%d],cellid[%s],msc[%s],msccode[%s],belong area[%s],cover area[%s],home area[%d],province flag[%d]",visit->area, cellid,mscinfo, iter->second->szmsc,iter->second->areaCode, iter->second->ecellAreaCode, homeArea,iter->second->isProvEdge);
		if(0==strcmp(iter->second->szmsc,mscinfo) && visit->area==atoi(iter->second->areaCode))
		{
			if(4==roamtype && ('Y' == iter->second->isProvEdge || 'y' == iter->second->isProvEdge || '1' == iter->second->isProvEdge ) )
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=8:spr_edge_roam.earea==home.area and roamtype==4,is edge prov[%d]", iter->second->isProvEdge);
				return 8;
			}
			else if(atoi(iter->second->ecellAreaCode) == homeArea)
			{
				if('Y' == iter->second->isProvEdge || 'y' == iter->second->isProvEdge || '1' == iter->second->isProvEdge )
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=8:spr_edge_roam.earea==home.area,is edge prov", "");
					return 8;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=7:spr_edge_roam.earea==home.area,is edge roam", "");
					return 7;
				}
			}
			else if(atoi(iter->second->areaCode) == homeArea)//归属区ECELL_AREA_CODE
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=0:spr_edge_roam.area==home.area,not edge roman", "");
				return 0;
			}
		}
		iter++;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no edge roma,cellid[%s]", cellid);
	return -1;
}


//msc方式判断省内漫游
int TSMPara::GetRoamMsc(const char * msc, int homeArea,AREA_INFO* visit)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	multimap<string, MSC_INFO*>::iterator iter = pSMPARA->m_spr_msc_vlr_map.lower_bound(msc);
	while(iter != pSMPARA->m_spr_msc_vlr_map.upper_bound(msc))
	{

		visit->area=atoi(iter->second->area_code);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "msc[%s],home area[%d],visit area[%d]", msc,homeArea,visit->area);

		if(visit->area != homeArea)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "visit area[%d],roam type:1", visit->area);
             return 1;//省内漫游
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "roma=0:spr_cell.area==home.area,not roam", "");
			return 0;
		}

		iter++;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no  roam,msc[%s]", msc);
	return -1;
}

MSC_INFO* TSMPara::GetMscVlrCodeInfo(const char* msc)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string str = msc;
	map<string, MSC_INFO*>::iterator iter = pSMPARA->m_spr_msc_vlr_map.find(str);
	if(iter!=pSMPARA->m_spr_msc_vlr_map.end())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find area_code[%s],MSCorVLR [%s]", iter->second->area_code,msc);
		return iter->second;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "no find MSCorVLR code[%s] in spr_msc", msc);
	return NULL;
}
int TSMPara::GetTac_5GSA(const char* tac,int provCode, MSC_INFO &tacinfo)
{
	//区号SPZ_CITY
	int ret = 0;
	m_sql.m_pSELECT_SPR_TAC_5GSA = m_dbm->GetSQL("q_5gsa_tac");
	UDBSQL* qQuery = m_sql.m_pSELECT_SPR_TAC_5GSA;
	if(!qQuery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "no find sql[q_5gsa_tac]");
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "sql[q_5gsa_tac] params:TAC[%s],provcode[%d]",tac,provCode);
	try
	{
		qQuery->UnBindParam();
		qQuery->BindParam(1,tac);
		qQuery->BindParam(2,provCode);
		qQuery->Execute();
  		if (qQuery->Next())
  		{
			qQuery->GetValue(1, tacinfo.area_code);
			qQuery->GetValue(2, tacinfo.carriers);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "find area_code[%s]", tacinfo.area_code);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "no find area_code");
			ret = -1;
		}
		qQuery->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,  "", "load zone information failed. error[%s]",e.ToString());
		return -1;
	}
	return ret;
}
int TSMPara::GetNrCellID(string nrcell,SGSN_INFO &sgsn)
{
	char tmp[256] = {0};
	int ret = 0;
	for(unsigned int i = 0; i < nrcell.size(); i++)
	{
		if(nrcell[i] >= 'A' && nrcell[i] <= 'Z')
		{
			nrcell[i] = nrcell[i] - 'A' + 'a';
		}
	}
	m_sql.m_pSELECT_5G_NR = m_dbm->GetSQL("q_5g_nr");
	UDBSQL* query = m_sql.m_pSELECT_5G_NR;
	if(!query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "no find sql[q_5g_nr]");
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "sql[q_5g_nr] params:nrcell[%s]",nrcell.c_str());
	try
	{
		query->UnBindParam();
		query->BindParam(1,nrcell);
		query->Execute();
  		if (query->Next())
  		{
			query->GetValue(1, sgsn.prov_code);
			query->GetValue(2, sgsn.area_code);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "find area_code[%s],prov_code[%s]", sgsn.area_code,sgsn.prov_code);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "no find area_code");
			ret = -1;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load q_5g_nr failed: [%s]", e.ToString());
		return -1;
	}

	return ret;
}

int TSMPara::GetNrByCellIDAndCnplmn(string strCNPlmn,string nrcell,SGSN_INFO &sgsn)
{
	int ret = 0;
	int nprov_code = 0;

	/*
	for(auto &c : nrcell)
	{
		if(c >= 'A' && c <= 'Z')
		{
			c = c - 'A' + 'a';
		}
	}
	*/

    /*
	for (unsigned i=0; i<nrcell.length(); ++i)
	{
		nrcell.at(i) = tolower(nrcell.at(i));
	}
	*/
	m_sql.m_pSELECT_CNPLMN_5G_NR=m_dbm->GetSQL("q_cnplmn_5g_nr");
	UDBSQL* query = m_sql.m_pSELECT_CNPLMN_5G_NR;
	if(!query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "no find sql[q_cnplmn_5g_nr]");
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "sql[q_cnplmn_5g_nr] params:cnplmn[%s] nrcell[%s]",strCNPlmn.c_str(),nrcell.c_str());
	try
	{
		query->UnBindParam();
		query->BindParam(1,nrcell);
		query->BindParam(2,strCNPlmn);
		query->Execute();
  		if (query->Next())
  		{
  		    query->GetValue(1, nprov_code);
			sprintf(sgsn.prov_code,"%d", nprov_code);
			query->GetValue(2, sgsn.area_code);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "find area_code[%s],prov_code[%s]", sgsn.area_code,sgsn.prov_code);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "no find area_code");
			ret = -1;
		}
		query->Close();
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "load q_cnplmn_5g_nr failed: [%s]", e.ToString());
		return -1;
	}

	return ret;
}



int TSMPara::checkUrgentNumberInfo(long long subnumber, long long calling, long long called, int prov, int area, int servicetype)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	multimap<long long, URGENT_NUMBER_INFO*>::iterator iter = pSMPARA->m_spr_urgent_map.lower_bound(subnumber);
	while(iter != pSMPARA->m_spr_urgent_map.upper_bound(subnumber))
	{
		if((atoi(iter->second->serviceType) == servicetype || 0 == strcmp(iter->second->serviceType, "#"))&&
			(atoi(iter->second->provCode) == prov || 0 == strcmp(iter->second->provCode, "#")) &&
			(atoi(iter->second->areaCode) == area || 0 == strcmp(iter->second->areaCode, "#")))
		{
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find urgent number[%lld]", subnumber);
			return 1;
		}
		iter++;
	}

	iter = pSMPARA->m_spr_urgent_map.lower_bound(calling);
	while(iter != pSMPARA->m_spr_urgent_map.upper_bound(calling))
	{
		if((atoi(iter->second->serviceType) == servicetype || 0 == strcmp(iter->second->serviceType, "#"))&&
			(atoi(iter->second->provCode) == prov || 0 == strcmp(iter->second->provCode, "#")) &&
			(atoi(iter->second->areaCode)== area  || 0 == strcmp(iter->second->areaCode, "#")))
		{
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find urgent number[%lld]", calling);
			return 1;
		}
		iter++;
	}

	iter = pSMPARA->m_spr_urgent_map.lower_bound(called);
	while(iter != pSMPARA->m_spr_urgent_map.upper_bound(called))
	{
		if((atoi(iter->second->serviceType) == servicetype ||0 == strcmp(iter->second->serviceType, "#"))&&
			(atoi(iter->second->provCode) == prov || 0 == strcmp(iter->second->provCode, "#")) &&
			(atoi(iter->second->areaCode) == area || 0 == strcmp(iter->second->areaCode, "#")))
		{
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find urgent number[%lld]", called);
			return 1;
		}
		iter++;
	}

	 DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "find no urgent subNumber[%lld], calling[%lld], called[%lld],servicetype[%d],prov[%d],area[%d]", subnumber, calling, called,servicetype,prov,area);

	return 0;
}

int TSMPara::checkUserStateInfo(int bizType, char* basicState, char* extState, char* stausCd, char* stopType)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	int ret = SM_OCP_USER_UNKNOWN;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "bizType[%d], basicState[%s],extState[%s],stausCd[%s],stopType[%s]", bizType, basicState, extState,stausCd,stopType);
	multimap<int, USER_STATE_INFO*>::iterator iter = pSMPARA->m_user_state_map.lower_bound(bizType);
	while(iter != pSMPARA->m_user_state_map.upper_bound(bizType))
	{
		if ((!strncmp(iter->second->basicState, "#", 1)||!strcmp(iter->second->basicState, basicState))
		  &&(!strncmp(iter->second->extState, "#", 1)||!strcmp(iter->second->extState, extState))
		  &&(!strncmp(iter->second->stausCd, "#", 1)||!strcmp(iter->second->stausCd, stausCd))
		  &&(!strncmp(iter->second->stopType, "#", 1)||!strcmp(iter->second->stopType, stopType))
		)
		{
			return iter->second->resultCode;
		}
		/*
		if(0==strncmp(iter->second->stausCd, "#", 1) &&
			0==strcmp(iter->second->basicState, basicState) &&
			0==strcmp(iter->second->extState, extState))
		{
			return iter->second->resultCode;
		}
		else if(0==strncmp(iter->second->stausCd, "#", 1) &&
			0==strcmp(iter->second->basicState, basicState) &&
			0==strncmp(iter->second->extState, "#", 1))
		{
			return iter->second->resultCode;
		}
		else if(0==strncmp(iter->second->stausCd, "#", 1) &&
			0==strncmp(iter->second->basicState, "#", 1) &&
			0==strcmp(iter->second->extState, extState))
		{
			return iter->second->resultCode;
		}
		else if(0==strncmp(iter->second->stausCd, "#", 1) &&
			0==strncmp(iter->second->basicState, "#", 1) &&
			0==strncmp(iter->second->extState, "#", 1))
		{
			return iter->second->resultCode;
		}
		else if(0==strncmp(iter->second->basicState, "#", 1) &&
			0==strncmp(iter->second->extState, "#", 1) &&
			0==strcmp(iter->second->stausCd, stausCd))
		{
			return iter->second->resultCode;
		}
		*/
		iter++;
	}

	return ret;
}

int TSMPara::getsuffix(char* postfix)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

   if(NULL == postfix)
   {
     return -1;
   }
   char *p = NULL;
   p = strtok(postfix, "@");
   if(NULL == p)
   {
     return -1;
   }
   p = strtok(NULL, "@");
   if(NULL == p)
   {
     return -1;
   }

   map<string,bool>::iterator it = pSMPARA->m_mapsubfix.find(p);
   if(it != pSMPARA->m_mapsubfix.end())
   {
		return 0;
   }
   else
   {
		return -1;
   }

   return 0;
}

map<int, ACCESS_INFO*> * TSMPara::GetAccess()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_access;
}


map<int, COUNTRY_INFO*> * TSMPara::GetCountry()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_country;
}

const COUNTRY_INFO * TSMPara::GetCountryInfo(int country)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<int, COUNTRY_INFO*>::iterator countryIter = pSMPARA->m_country.find(country);
	if(countryIter == pSMPARA->m_country.end())
	{
		return NULL;
	}
	else
	{
		return countryIter->second;
	}
}


map<int, AREA_INFO*> * TSMPara::GetArea()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_area;
}

const AREA_INFO 	* TSMPara::GetAreaInfo(int area)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<int, AREA_INFO*>::iterator areaIter = pSMPARA->m_area.find(area);
	if(areaIter == pSMPARA->m_area.end())
	{
		return NULL;
	}
	else
	{
		return areaIter->second;
	}
}


map<int, int> * TSMPara::GetCallPrefix()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_call_number_prefix;
}

int TSMPara::IsTranslateDataCard()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return pSMPARA->m_commonPara.isTranslateDataCard;
}

int TSMPara::getPlatformOverLoad()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return pSMPARA->m_commonPara.platformOverLoad;
}


SCommonPara *TSMPara::GetCommonPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_commonPara;
}


SINPara * TSMPara::GetINPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_INPara;
}

SDSLPara * TSMPara::GetDSLPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_DSLPara;
}

S5GPara * TSMPara::Get5GPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_5GPara;
}

SPSPara * TSMPara::GetPSPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_PSPara;
}

SISMPPara* TSMPara::GetISMPPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_ISMPPara;
}

SP2PSMSPara * TSMPara::GetP2PSMSPara()
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	return &pSMPARA->m_P2PSMSPara;
}

int TSMPara::GetISMPSPCDebit(char* SPCProductId)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<string, int>::iterator iter = pSMPARA->m_SPCProductId_map.find(SPCProductId);

	if(iter != pSMPARA->m_SPCProductId_map.end())
	{
		return 0;
	}
	else
	{
		return 2;
	}
}

int TSMPara::JudgeISMPModTime(const char * productOfferID)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
       if(pSMPARA->m_ProductOfferId_map.empty())
       {
            // 不修改时间
            return 0;
       }
	map<string, int>::iterator iter = pSMPARA->m_ProductOfferId_map.find(productOfferID);
	if(iter != pSMPARA->m_ProductOfferId_map.end())
	{
		return 0;
	}
	else
	{
              //修改时间
		return 1;
	}
}

int TSMPara::GetOCPResultCode(int resultCode)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<int, SResultCode*>::iterator iter = pSMPARA->m_result_code_map.find(resultCode);

	if(iter != pSMPARA->m_result_code_map.end())
	{
		return (iter->second)->OCPResultCode;
	}
	else
	{
		return -1;
	}
}

int TSMPara::GetBillAttr(string serviceAttr)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<string, int>::iterator iter = pSMPARA->m_svecont_attr_map.find(serviceAttr);

	if(iter != pSMPARA->m_svecont_attr_map.end())
	{
		return iter->second;
	}
	else
	{
		return -1;
	}
}
int TSMPara::IsExpire(string basic_state)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	if(pSMPARA->m_expirestate.find(basic_state) != pSMPARA->m_expirestate.end())
		return 1;
	return 0;
}
int TSMPara::IsFiltedByStauscd(string staus_cd)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	if(pSMPARA->m_filtedstauscd.find(staus_cd) != pSMPARA->m_filtedstauscd.end())
		return 1;
	return 0;
}
int TSMPara::IsFilted(long productid)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	if(pSMPARA->m_mapProductID.find(productid) != pSMPARA->m_mapProductID.end())
		return 1;
	return 0;
}
MSC_COUNTRY * TSMPara::GetMscCountryInfo(string msc_code)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "msc_code[%s]",msc_code.c_str());

	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	map<string,MSC_COUNTRY*>::iterator it = pSMPARA->m_msc_country_map.find(msc_code);
	if(it != pSMPARA->m_msc_country_map.end())
		return it->second;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "not find msc_code[%s]",msc_code.c_str());
	return NULL;
}

string TSMPara::GetSectorId(string visited_carrier_code, string sponsor_code)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "visited_carrier_code[%s],sponsor_code[%s]",
		visited_carrier_code.c_str(),sponsor_code.c_str());
	visited_carrier_code = visited_carrier_code + sponsor_code;
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	map<string,string>::iterator it = pSMPARA->m_sector_id_map.find(visited_carrier_code);
	if(it != pSMPARA->m_sector_id_map.end())
	{
		return it->second;
	}
	return "";
}

string TSMPara::GetTariffInfoCSM(string visited_carrier_code, int sponsor_code)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "visited_carrier_code[%s],sponsor_code[%d]",
		visited_carrier_code.c_str(),sponsor_code);
	char tmp[8];
	sprintf(tmp, "%d", sponsor_code);
	visited_carrier_code = visited_carrier_code + tmp;
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	map<string,string>::iterator it = pSMPARA->m_sector_id_csm_map.find(visited_carrier_code);
	if(it != pSMPARA->m_sector_id_csm_map.end())
	{
		return it->second;
	}
	return "";
}


TSERVICE_QUOTA_CONF * TSMPara::GetServiceQuotaConf(long long ratingGroup,long Balance)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "ratingGroup[%lld],Balance[%ld]", ratingGroup,Balance);

	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	multimap<long long, TSERVICE_QUOTA_CONF*>::iterator confIter;
	multimap<long long, TSERVICE_QUOTA_CONF*>::iterator	begin;
	multimap<long long, TSERVICE_QUOTA_CONF*>::iterator	end;

	begin  = pSMPARA->m_service_quota_conf.lower_bound(ratingGroup);
	end    = pSMPARA->m_service_quota_conf.upper_bound(ratingGroup);

	if(ratingGroup == -7)
	{
		// 读取速率信息
		for(confIter = begin; confIter != end; confIter++)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "MinRatio[%ld], MaxRatio[%ld]",confIter->second->MinRatio, confIter->second->MaxRatio);
			if((confIter->second->MinRatio <= Balance) && (confIter->second->MaxRatio > Balance))
			{
				return confIter->second;
			}
	 	}
	}
	else
	{
		if(Balance != -1)
		{

			for(confIter = begin; confIter != end; confIter++)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "MinBalance[%ld], MaxBalance[%ld]",confIter->second->MinBalance, confIter->second->	MaxBalance);
				if((confIter->second->MinBalance <= Balance) && (confIter->second->MaxBalance > Balance))
				{
					return confIter->second;
				}
			}
	 	}

		for(confIter = begin; confIter != end; confIter++)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "MinBalance[%ld], MaxBalance[%ld]",confIter->second->MinBalance, confIter->second->	MaxBalance);
			if((confIter->second->MinBalance <=0) && (confIter->second->MaxBalance <=0))
			{
				return confIter->second;
			}
		}
	}
	return  NULL;
}


TSERVICE_QUOTA_CONF * TSMPara::GetServiceQuotaByOfr(const char * OfferId,long Balance)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "","OfferId[%s],Balance[%ld]", OfferId,Balance);

	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	multimap<string, TSERVICE_QUOTA_CONF*>::iterator confIter;
	multimap<string, TSERVICE_QUOTA_CONF*>::iterator begin;
	multimap<string, TSERVICE_QUOTA_CONF*>::iterator end;

	begin = pSMPARA->m_service_quota_conf_ofr.lower_bound(OfferId);
	end = pSMPARA->m_service_quota_conf_ofr.upper_bound(OfferId);

	if(Balance != -1)
	{
	    	for(confIter = begin; confIter != end; confIter++)
	    	{
	            if(confIter->second->MinBalance <= Balance &&   Balance  < confIter->second->MaxBalance)
	    	    {
	    	        return confIter->second;
	    	    }
	    	}
	}

	for(confIter = begin; confIter != end; confIter++)
	{
	        if(confIter->second->MinBalance <=0 && confIter->second->MaxBalance <=0)
	        {
	            return confIter->second;
	        }
	}

	return NULL;


}


int TSMPara::GetServiceType(const char* str)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<string, int>::iterator iter;
	iter = pSMPARA->m_business_type_map.find(str);
	if(iter != pSMPARA->m_business_type_map.end())
	{
		return iter->second;
	}

	return -1;
}

int TSMPara::GetSprNp(long sub, char* rcarrier, long time, int iRcarrierLen)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	UDBSQL* query = m_dbm->GetSQL("q_spr_np");
	char value[128] = {0};
	long lEff_date = 0;
	long lExp_date = 0;
	int year = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "find np_inst by nbr[%ld]", sub);
	int ret = -1;
	if(!query)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "get sql[q_spr_np] failed");
		return ret;
	}

	try
	{
		query->UnBindParam();
		query->BindParam(1,sub);
		query->Execute();
  		while (query->Next())
  		{
  			query->GetValue(1, value);
			strncpy(rcarrier, value, iRcarrierLen-1);
  			query->GetValue(2, value);
			if(strlen(value) == 8) //表里取到时间8位补6个0 
			{
 				sprintf(value, "%s%s", value, "000000");
 			}
			lEff_date = atol(value);
			query->GetValue(3, value);
			if(strlen(value) == 8)
			{
 				sprintf(value, "%s%s", value, "000000");
 			}
			lExp_date = atol(value);
			if(lEff_date <= time && time <= lExp_date)
			{
				ret = 0;
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "get in_network[%s]", rcarrier);
				break;
			}
  		}
		
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "get np_inst failed: [%s]", e.ToString());
		return ret;
	}
	return ret;
}


PDSN_INFO * TSMPara::GetSprPDSN(const char* pdsn)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string str = pdsn;
	map<string, PDSN_INFO*>::iterator iter = pSMPARA->m_spr_pdsn_map.find(str);
	if(iter == pSMPARA->m_spr_pdsn_map.end())
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "find no pdsn number[%s]", pdsn);
		return NULL;
	}
	else
	{
		return iter->second;
	}

	return NULL;
}

SGSN_INFO * TSMPara::GetSprSGSN(const char* sgsn)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	string str = sgsn;
	map<string, SGSN_INFO*>::iterator iter = pSMPARA->m_spr_sgsn_map.find(str);
	if(iter == pSMPARA->m_spr_sgsn_map.end())
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "find no sgsn number[%s]", sgsn);
		return NULL;
	}
	else
	{
		return iter->second;
	}

	return NULL;
}

DSL_INFO * TSMPara::GetSprDSL(const char* nasIP)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string str = nasIP;
	map<string, DSL_INFO*>::iterator iter = pSMPARA->m_spr_dsl_map.find(str);
	if(iter == pSMPARA->m_spr_dsl_map.end())
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, 0,"", "find no dsl number[%d]", nasIP);
		return NULL;
	}
	else
	{
		return iter->second;
	}

	return NULL;
}

int TSMPara::GetSprLAC(const char* lac)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string str = lac;
	map<string, int>::iterator iter;
	iter = pSMPARA->m_spr_lac_map.find(str);
	if(iter != pSMPARA->m_spr_lac_map.end())
	{
		return iter->second;
	}

	return -1;
}

int TSMPara::GetSprCELL(const char* cellid)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string str = cellid;
	map<string, int>::iterator iter;
	iter = pSMPARA->m_spr_cell_map.find(str);
	if(iter != pSMPARA->m_spr_cell_map.end())
	{
		return iter->second;
	}

	return -1;
}

int TSMPara::GetOfrID2G(char * ofrid)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string strOfrID = ofrid;
	vector<string>::iterator it = find(pSMPARA->m_vecOfrID2G.begin(),pSMPARA->m_vecOfrID2G.end(),strOfrID);
	if(it != pSMPARA->m_vecOfrID2G.end())
	{
		return 1;
	}
	return 0;
}

int TSMPara::GetIsmpRefund(char * ServiceContext)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string strSerivce = ServiceContext;
	vector<string>::iterator it = find(pSMPARA->m_vecIsmpRefund.begin(),pSMPARA->m_vecIsmpRefund.end(),strSerivce);
	if(it != pSMPARA->m_vecIsmpRefund.end())
	{
		return 1;
	}
	return 0;
}

int TSMPara::GetPSShortCdr(long long  ratinggroup)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	map<long long, int>::iterator iter;
	iter = pSMPARA->m_ps_short_cdr.find(ratinggroup);
	if(iter != pSMPARA->m_ps_short_cdr.end())
	{
		return iter->second;
	}
	return 0;
}

int TSMPara::getCdrService(char* ServiceContext)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string strSerivce = ServiceContext;
	vector<string>::iterator it = find(pSMPARA->m_vecCdrByService.begin(),pSMPARA->m_vecCdrByService.end(),strSerivce);
	if(it != pSMPARA->m_vecCdrByService.end())
	{
		return 1;
	}
	return 0;
}


 int TSMPara::GetTacVlrCodeInfo(const char* tac,MSC_INFO &tacinfo)
{
	 TSMPARA *pSMPARA = (TSMPARA*)BData::data();

	string str = tac;
	map<string, MSC_INFO>::iterator iter = pSMPARA->m_spr_tac_vlr_map.find(str);
	if(iter!=pSMPARA->m_spr_tac_vlr_map.end())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "find area_code[%s],TACorVLR [%s]", iter->second.area_code,tac);
		tacinfo=iter->second;
		return 0;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "no find TACorVLR code[%s] in spr_tac", tac);
	return -1;
}

SGSN_INFO * TSMPara::GetSprSGSN_IPV6(const char* sgsn)
{
	TSMPARA *pSMPARA = (TSMPARA*)BData::data();
	string str = sgsn;
	str = RegularAddr(str,"");
	map<string, SGSN_INFO*>::iterator iter = pSMPARA->m_spr_sgsn_ipv6_map.find(str);
	if(iter != pSMPARA->m_spr_sgsn_ipv6_map.end())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get ipv6address[%s]", iter->first.c_str());
		return iter->second;
	}
	for(int i = 0; i < m_pSMPARA->v_mask.size(); i++)
	{
		string tpAddr = MaskAddress(str, m_pSMPARA->v_mask[i]);
		iter = pSMPARA->m_spr_sgsn_ipv6_map.find(tpAddr);
		if(iter != pSMPARA->m_spr_sgsn_ipv6_map.end())
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "get ipv6address[%s]", iter->first.c_str());
			return iter->second;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "can not get ipv6address[%s]", str.c_str());
	return NULL;
}

int TSMPara::ParseServiceType(int busineseType, const char* value)
{
	if(value == NULL)
	{
		return -1;
	}

	char tmp[20] = {0};
	char *p = tmp;
	char ch;

	while(1)
	{
		while(((ch = *p++ = *value++) !=',') && (ch != '\0'));

		*(--p) = '\0';
		m_pSMPARA->m_business_type_map.insert(pair<string,int>(tmp,busineseType));
		if(ch == '\0')
		{
			break;
		}
		p = tmp;
	}

	return 0;
}


int TSMPara::ParseSPCProductId(const char* value)
{
	if(value == NULL)
	{
		return -1;
	}

	char tmp[20] = {0};
	char *p = tmp;
	char ch;

	while(1)
	{
		while(((ch = *p++ = *value++) !=',') && (ch != '\0'));

		*(--p) = '\0';

		m_pSMPARA->m_SPCProductId_map.insert(pair<string,int>(tmp,1));
		if(ch == '\0')
		{
			break;
		}
		p = tmp;
	}

	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "SPCProdyctId size is[%ld]", m_pSMPARA->m_SPCProductId_map.size());

	return 0;
}
int TSMPara::ParseProductOfferId(const char* value)
{
	if(value == NULL)
	{
		return -1;
	}

	char tmp[30] = {0};
	char *p = tmp;
	char ch;

	while(1)
	{
		while(((ch = *p++ = *value++) !=',') && (ch != '\0'));

		*(--p) = '\0';

		m_pSMPARA->m_ProductOfferId_map.insert(pair<string,int>(tmp,1));
		if(ch == '\0')
		{
			break;
		}
		p = tmp;
	}

	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "ProductOfferId_map size is[%ld]", m_pSMPARA->m_ProductOfferId_map.size());

	return 0;
}
int TSMPara::ParseAreaSubfix(const char* value)
{
	if(value == NULL)
	{
		return -1;
	}

	char tmp[20] = {0};
	char *p = tmp;
	char ch;

	while(1)
	{
		while(((ch = *p++ = *value++) !=',') && (ch != '\0'));

		*(--p) = '\0';
		m_pSMPARA->m_mapsubfix.insert(pair<string,bool>(tmp,1));
		if(ch == '\0')
		{
			break;
		}
		p = tmp;
	}
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "WIFI_USER_NAME size is[%ld]", m_pSMPARA->m_mapsubfix.size());

	return 0;
}

void TSMPara::ParseString(string& szSourString,vector<string> &vecDestString,const char* szSeparator)
{
	string::size_type pos = 0, prev_pos = 0;
	int nCounter = 1;

	vecDestString.clear();

	while (( pos = szSourString.find(szSeparator , pos ))
		!= string::npos)
	{
		vecDestString.push_back( szSourString.substr( prev_pos, pos - prev_pos ) );

		prev_pos = ++pos;
		++nCounter;
	}

	vecDestString.push_back( szSourString.substr( prev_pos, pos - prev_pos ) );

	return ;
}

void TSMPara::ParseString(string& szSourString,vector<int> &vecDest,const char* szSeparator)
{
	string::size_type pos = 0, prev_pos = 0;
	int nCounter = 1;

	vecDest.clear();

	while (( pos = szSourString.find(szSeparator , pos ))
		!= string::npos)
	{
		if(pos!=prev_pos)
		{
			vecDest.push_back( atoi(szSourString.substr( prev_pos, pos - prev_pos ).c_str()) );
		}

		prev_pos = ++pos;
		++nCounter;
	}
	if(pos != prev_pos)
	{
		vecDest.push_back( atoi(szSourString.substr( prev_pos, pos - prev_pos ).c_str()) );
	}
}

void TSMPara::ParseString(string& szSourString,map<long,int> &mapDest,const char* szSeparator)
{
	string::size_type pos = 0, prev_pos = 0;
	int nCounter = 1;

	mapDest.clear();

	while (( pos = szSourString.find(szSeparator , pos ))
		!= string::npos)
	{
		if(pos!=prev_pos)
		{
			mapDest[atoi(szSourString.substr( prev_pos, pos - prev_pos ).c_str())] = 1;
		}

		prev_pos = ++pos;
		++nCounter;
	}
	if(pos != prev_pos)
	{
		mapDest[atoi(szSourString.substr( prev_pos, pos - prev_pos ).c_str())] = 1;
	}
}

void TSMPara::ParseString(string& szSourString,map<string,int> &mapDest,const char* szSeparatorOne, const char* szSeparatorTwo)
{
	string::size_type pos = 0, prev_pos = 0;
	int nCounter = 1;

	vector<string> vecDestString;

	while (( pos = szSourString.find(szSeparatorOne , pos ))
		!= string::npos)
	{
		vecDestString.push_back( szSourString.substr( prev_pos, pos - prev_pos ) );

		prev_pos = ++pos;
		++nCounter;
	}

    if(pos != prev_pos)
	{
        vecDestString.push_back( szSourString.substr( prev_pos, pos - prev_pos ) );
	}
	
	mapDest.clear();
    vector<string >::iterator it;
	for (it = vecDestString.begin(); it != vecDestString.end(); ++it)
	{
	    pos = 0, prev_pos = 0;
		
	     while (( pos = (*it).find(szSeparatorTwo , pos ))
		!= string::npos)
		{
		    string left = (*it).substr( prev_pos, pos - prev_pos );
            ++pos;
			int right = atoi((*it).substr( pos ).c_str());
			
			mapDest[left] = right;
		}
	
	}
	
}


int TSMPara::ParseShortCDR(const char* value)
{
	if(value == NULL)
	{
		return -1;
	}

	string str = value;
	int m = 0;
	int n = 0;
	int k = 0;
    while(true)
    {
    	int time = 0;
   		long rating = 0;
		m = str.find(",", k);
		if(-1!=m)
		{
        	time = atoi(str.substr(k,m-k).c_str());
		}
    	else
   		{
        	break;
    	}

    	n = str.find(";", m+1);
		if(-1!=n)
		{
			rating = atol(str.substr(m+1,n-m-1).c_str());
        	m = n;
			k = n+1;
		}
		else
		{
			break;
		}

		m_pSMPARA->m_ps_short_cdr.insert(pair<long long,int>(rating,time));
		DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "PSShortCDRInfo time[%d] rg[%ld]", time, rating);

    }
	return 0;
}


