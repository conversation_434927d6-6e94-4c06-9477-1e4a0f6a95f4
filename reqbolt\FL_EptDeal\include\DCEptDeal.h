/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCEptDeal.h
*Indentifier：
*
*Description：
*		异常消息组装类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_EPTDEAL_H__
#define __DC_EPTDEAL_H__
#include "DCBasePlugin.h"
#include "DCBizMsgDef.h"
#include "DCSeriaOp.h"

#define RE_MSG_ID_EPT 99

class DCEptDeal : public DCBasePlugin
{
	public:	
		DCEptDeal(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)         :DCBasePlugin(category,func,version),m_en(ESeriaBinString)
		{
		
		}
		
		virtual ~DCEptDeal()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		virtual const char* desc();
	private:
		int composeEptMsg(STBizMsg* bizMsg);

	private:
		DCSeriaEncoder m_en;		
		DCSeriaPrinter m_print;
};
#endif

