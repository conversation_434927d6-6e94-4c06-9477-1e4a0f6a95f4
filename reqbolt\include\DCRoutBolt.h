/*******************************************
*Copyrights   2018，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCRoutBolt.h
*Indentifier：
*
*Description：
*      SM统一消息服务
*Version：
*       V1.0
*Author:
*       FangDa
*Finished：
*       2018-11-22
*History:
*
********************************************/

#ifndef DC_ROUTBOLT_H_
#define DC_ROUTBOLT_H_

#include <DCBolt.h>
#include <stdio.h>
#include <time.h>
#include "DCPluginManer.h"


class DCPerfTimeStats;
class DCSeriaDecoder;
class DCRoutBolt: public tydic::storm::DCBolt {
public:
	int Initialize(const tydic::storm::DCStormConfig& config);
	int Process(tydic::storm::Tuple &tuple);
	DCRoutBolt();
	virtual ~DCRoutBolt();
	int Refresh(const char * path);
	int SetWacther();

private:
	DCPluginManer m_pm;
	DCPerfTimeStats*  m_tstat;
	pthread_t			m_tid;
	time_t m_checktime;
	char m_Topic[50];
	char m_payflagTopic[50];
	char m_testTopic[50];
	DCSeriaDecoder* m_de;
};


extern "C" {

tydic::storm::DCStormBase* CreateInstance()
{
	return new DCRoutBolt();
}

void ReleaseInstance(tydic::storm::DCStormBase* pStorm)
{
	if (pStorm != NULL)
		delete pStorm;
}

}

#endif /* DC_ROUTBOLT_H_ */


