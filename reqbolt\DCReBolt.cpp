#include "DCReBolt.h"
#include "DCStormProtocol.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "DCRbMsgDef.h"
#include "tinyxml.h"
#include "DCParseXml.h"
#include "DCEptMsgDef.h"
#include "UHead.h"

using namespace std;

DCReBolt::DCReBolt() 
{

}

DCReBolt::~DCReBolt() 
{

}


int DCReBolt::Refresh(const char * path)
{
	return 0;
}

int DCReBolt::SetWacther()
{
	//监听	 
	return 0;
}

int DCReBolt::Initialize(const tydic::storm::DCStormConfig& config)
{
	int ret = 0;
	char buf[512]={0};
	char *szconfig =getenv("OCS_CONFIG");
	if(NULL==szconfig)
	{
	  return -1;
	}

	sprintf(buf,"%s/sm_cfg.xml",szconfig);
	ret = DCParseXml::Instance()->Init("SM",buf);
	if(ret)
	{
		return -1;
	}

	const char* logpath = DCParseXml::Instance()->GetParam("logAddr","Common/log");
	int loglevel  = atoi(DCParseXml::Instance()->GetParam("level","Common/log"));

	//--------------------------------性能日志级别、阈值设置-------------------------------------//
	int perf_level = 0;//性能日志级别设置
	const char* perf_level_param = DCParseXml::Instance()->GetParam("perf","Commom/log");
	if(perf_level)
	{
		perf_level = atoi(perf_level_param);
	}
	int perf_threshold = 50;//性能日志阈值获得
	const char* perf_threshold_param = DCParseXml::Instance()->GetParam("perf.ms","Commom/log");
	if(perf_level)
	{
		perf_threshold = atoi(perf_threshold_param);
	}		
	//日志初始化
	ret = DCLOGINIT("ocs","re_bolt",loglevel,logpath);
	if(ret)
	{
	  return -1;
	}
	DCLOG_SETLEVEL(DCLOG_CLASS_PERF,perf_level);
	DCLOG_SETCTL(DCLOG_MASK_PERF,perf_threshold*1000);//单位转化为微秒

	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d, module:%s",logpath, loglevel, "re_bolt");
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init log successful");

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init rebolt successful");
	return 0;
}

int DCReBolt::LoadRBAMsg(ocs::rbresult& rba)
{
	char *szconfig =getenv("RB_TEST");
	if(NULL==szconfig)
	{
	  return -1;
	}
	TiXmlDocument config(szconfig);
	if(!config.LoadFile())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "read config file error:%s", szconfig);
		return -1;
	}
	TiXmlHandle pHandle(&config);

	TiXmlElement * element1 = NULL;
	TiXmlElement * element2 = NULL;
	TiXmlElement * element3 = NULL;
	TiXmlElement * element4 = NULL;
	
	element1 = pHandle.FirstChildElement("rba").Element();
	if(element1 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "read rba error");
		return -1;
	}
	
	element2 = element1->FirstChildElement("resultcode");
	m_resultCode = atoi(element2->Attribute("value"));

	element2 = element1->FirstChildElement("fnf_flag");
	rba.fnf_flag = atoi(element2->Attribute("value"));
	
	element2 = element1->FirstChildElement("tariff_change_time");
	rba.tariff_change_time = atol(element2->Attribute("value"));

	element2 = element1->FirstChildElement("final_flag");
	rba.final_flag = atoi(element2->Attribute("value"));

	element2 = element1->FirstChildElement("test_info");
	rba.test_info = atoi(element2->Attribute("value"));

	element2 = element1->FirstChildElement("fav_flag");
	rba.fav_flag = atoi(element2->Attribute("value"));

	element2 = element1->FirstChildElement("evt_id");
	rba.evt_id = atol(element2->Attribute("value"));

	element2 = element1->FirstChildElement("plan_id");
	rba.plan_id = element2->Attribute("value");

	element2 = element1->FirstChildElement("dist_fee");
	rba.dist_fee = atol(element2->Attribute("value"));
	

	// B01
	reserve_item_t rsv;
	element2 = element1->FirstChildElement("rsv");
	while(element2)
	{
		element3 = element2->FirstChildElement("rating_group");	
		rsv.rating_group = element3->Attribute("value");

		element3 = element2->FirstChildElement("acct_item_id");	
		rsv.acct_item_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");	
		rsv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");	
		rsv.amount = atol(element3->Attribute("value"));
			
		rba.rsv.push_back(rsv);
		
		element2 = element1->NextSiblingElement("rsv");
	}
	
	// B10
	gsu_item_t gsv;
	element2 = element1->FirstChildElement("gsv");
	while(element2)
	{
		element3 = element2->FirstChildElement("rating_group");
		gsv.rating_group = element3->Attribute("value");

		element3 = element2->FirstChildElement("acct_item_id");	
		gsv.acct_item_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");	
		gsv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");	
		gsv.amount = atol(element3->Attribute("value"));

		rba.gsv.push_back(gsv);
		
		element2 = element1->NextSiblingElement("gsv");	
	}
	// B03
	debit_item_t dbv;
	element2 = element1->FirstChildElement("dbv");
	while(element2)
	{
		element3 = element2->FirstChildElement("rating_group");
		dbv.rating_group = element3->Attribute("value");

		element3 = element2->FirstChildElement("acct_item_id");	
		dbv.acct_item_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");	
		dbv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");	
		dbv.amount = atol(element3->Attribute("value"));

		rba.dbv.push_back(dbv);
		
		element2 = element1->NextSiblingElement("dbv");	
	
	}
	// B04
	refund_item_t rfv;
	element2 = element1->FirstChildElement("rfv");
	while(element2)
	{
		element3 = element2->FirstChildElement("rating_group");
		rfv.rating_group = element3->Attribute("value");

		element3 = element2->FirstChildElement("acct_item_id");	
		rfv.acct_item_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");	
		rfv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");	
		rfv.amount = atol(element3->Attribute("value"));

		rba.rfv.push_back(rfv);
		
		element2 = element1->NextSiblingElement("rfv");	
	
	}
	
	// B06
	ocs::accum_item_t accv;
	element2 = element1->FirstChildElement("accv");
	while(element2)
	{
		element3 = element2->FirstChildElement("accu_id");
		accv.accu_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("accu_type_id");
		accv.accu_type_id = atoi(element3->Attribute("value"));
		
		element3 = element2->FirstChildElement("AccuDirection");
		accv.AccuDirection = atol(element3->Attribute("value"));
		
		element3 = element2->FirstChildElement("amount");
		accv.amount= atol(element3->Attribute("value"));

		rba.accv.push_back(accv);
		
		element2 = element1->NextSiblingElement("accv");	
	}
	
	// B07
	tariff_t trv;
	element2 = element1->FirstChildElement("trv");
	while(element2)
	{
		element3 = element2->FirstChildElement("ofr_id");
		trv.ofr_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");
		trv.amount = atol(element3->Attribute("value"));

		rba.trv.push_back(trv);
		
		element2 = element1->NextSiblingElement("trv");
	}
	// B08
	acctchangeitem_t actv;
	element2 = element1->FirstChildElement("actv");
	while(element2)
	{
		element3 = element2->FirstChildElement("acct_item_id");
		actv.acct_item_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");
		actv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");
		actv.amount = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("balance");
		actv.balance = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("acct_code_id");
		actv.acct_code_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("fee_item_id");
		actv.fee_item_id = atol(element3->Attribute("value"));

		rba.actv.push_back(actv);

		element2 = element1->NextSiblingElement("actv");
	
	}
	// B20
	ratingitem_t rgv;
	element2 = element1->FirstChildElement("rgv");
	while(element2)
	{
		element3 = element2->FirstChildElement("tariff_start_time");
		rgv.tariff_start_time = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");
		rgv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("step");
		rgv.step = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("price");
		rgv.price = atoi(element3->Attribute("value"));
		
		rba.rgv.push_back(rgv);
		element2 = element1->NextSiblingElement("rgv");
	}
	// B21
	acctitem_t bal;
	element2 = element1->FirstChildElement("bal");
	while(element2)
	{
		element3 = element2->FirstChildElement("acct_item_id");
		bal.acct_item_id = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");
		bal.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");
		bal.amount = atoi(element3->Attribute("value"));
		
		rba.bal.push_back(bal);
		element2 = element1->NextSiblingElement("bal");
	}
	// B24
	ratableitem_t ratv;
	element2 = element1->FirstChildElement("ratv");
	while(element2)
	{
		element3 = element2->FirstChildElement("ofr_id");
		ratv.ofr_id = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("owner_type");
		ratv.owner_type = element3->Attribute("value");

		element3 = element2->FirstChildElement("owner_id");
		ratv.owner_id = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("ratable_res_id");
		ratv.ratable_res_id = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("ratable_begin_time");
		ratv.ratable_begin_time = element3->Attribute("value");

		element3 = element2->FirstChildElement("ratable_end_time");
		ratv.ratable_end_time = element3->Attribute("value");

		element3 = element2->FirstChildElement("amount");
		ratv.amount = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");
		ratv.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("result");
		ratv.result = atoi(element3->Attribute("value"));

		rba.ratv.push_back(ratv);
		element2 = element1->NextSiblingElement("ratv");
	
	}
	freeinfo_t freev;
	element2 = element1->FirstChildElement("freev");
	while(element2)
	{
		element3 = element2->FirstChildElement("ofr_id");
		freev.ofr_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("ratable_res_id");
		freev.ratable_res_id = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("unit");
		freev.unit = atoi(element3->Attribute("value"));

		element3 = element2->FirstChildElement("resource_name");
		freev.resource_name = element3->Attribute("value");

		element3 = element2->FirstChildElement("present_amount");
		freev.present_amount = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("amount");
		freev.amount = atol(element3->Attribute("value"));

		element3 = element2->FirstChildElement("accum_amount");
		freev.accum_amount = atol(element3->Attribute("value"));

		rba.freev.push_back(freev);
		element2 = element1->NextSiblingElement("freev");
	}
	return 0;
}


int DCReBolt::Process(tydic::storm::Tuple &tuple) 
{	
	std::string recvRBRRMsg = tuple.GetValues(1);
	std::string sendmsg;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","recv MSG[%s]", recvRBRRMsg.c_str());

	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdata data;
	ocs::rbvoice voice;
	ocs::rbsms sms;
	ocs::rbdsl dsl;
	ocs::rbismp ismp;
	ocs::rbext ext;
	ocs::rbquery rbq;
	
	// 反序列化
	DCSeriaDecoder de(ESeriaBinString);
	DCSeriaPrinter m_print;
	vector<uint8_t> vectorMsg;
	try
	{
		vectorMsg = HexDecode(recvRBRRMsg.c_str() + 16,recvRBRRMsg.size() -16);
		de.set(vectorMsg);
		de.decode(uhd);
		de.decode(head);
		if(head.type == 130)
		{
			de.decode(rbq);
			de.decode(ext);
			m_print.clear();
			m_print.print(head);
			m_print.print(rbq);
			m_print.print(ext);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "recv query MSG[%s]", m_print.data());
		}
		else{
			de.decode(domain);
			m_print.clear();
			m_print.print(head);
			m_print.print(domain);
			switch(head.type)
			{
				case 14:
				case 60:
					de.decode(voice);
					m_print.print(voice);
					break;
				case 70:
					de.decode(sms);
					m_print.print(sms);
					break;
				case 34:
				case 80:
				case 40:
					de.decode(data);
					m_print.print(data);
					break;
				case 44:
				case 90:
					de.decode(ismp);
					m_print.print(ismp);
					break;
				case 54:
				case 120:
					de.decode(dsl);
					m_print.print(dsl);
					break;
			}
			de.decode(ext);
			m_print.print(ext);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "recv MSG[%s]", m_print.data());
		}
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","decode failed");
		return -1;
	}
	// 组装RBA
	ocs::rbhead rbahead;
	ocs::rbresult rba;
	ocs::gsu_item_t gsv;

	LoadRBAMsg(rba);

    sendmsg ="";
	DCSeriaEncoder en(ESeriaBinString);

       struct timeval tmv;
        char buf[20];
    
//	if(head.sreq == 3)
//		m_resultCode = 0;
	if(m_resultCode<0)
	{
		ocs::SEPTMsg msg;
		msg.type = 2;
		msg.sessionID = head.session;	
		msg.result =  m_resultCode;
		msg.sreq = head.sreq;
		msg.servicecontextid = head.type+1;	
		msg.serial =head.serial;
		msg.stamp = head.stamp;
		msg.szServiceContextIDStr = ext.kv["strID"];
		msg.anstopic = ext.kv["anstopic"];
		uhd.car = "";

		try
		{
			en.encode(uhd);
			en.encode(msg); 
			sendmsg = HexEncode(en.data(), en.size());
			
			m_print.clear();
			m_print.print(uhd);
			m_print.print(msg); 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "exception msg[%s]", m_print.data());
		}
		catch(exception& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","encode failed");
			return -1;
		}

              // 头部加固定16位微妙时间戳
             gettimeofday(&tmv, NULL);
             sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
             sendmsg.insert(0, buf);
        
		tydic::storm::Tuple tRnd;
		tRnd.SetValues("1");
		tRnd.SetValues(sendmsg);
		tydic::storm::EmitDirect(-1,tRnd,"eptbolt");	
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","result code:%d,send to EptBolt\n",m_resultCode);
	}
	else
	{	
		uhd.car="";
		rbahead.type = head.type+1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","servType[%d]", rbahead.type);
		rbahead.sreq = head.sreq;
		rbahead.stamp = head.stamp;
		rbahead.session = head.session;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","sessionID[%s]", rbahead.session.c_str());
		rbahead.serial = head.serial;
		rbahead.trace = head.trace;
		rbahead.result = m_resultCode;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","resultCode[%d]", rbahead.result);

		try
		{
			// 序列化	
			en.encode(uhd);
			en.encode(rbahead);
			en.encode(rba);
			en.encode(ext);
			
			sendmsg = HexEncode(en.data(), en.size());
		}
		catch(exception& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, 0,"encode failed");
			return -1;
		}

               // 头部加固定16位微妙时间戳
             gettimeofday(&tmv, NULL);
             sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
             sendmsg.insert(0, buf);
              
		tydic::storm::Tuple tRnd;
		tRnd.SetValues("1");
		tRnd.SetValues(sendmsg);
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","send MSG[%s]", sendmsg.c_str());
		tydic::storm::EmitDirect(-1,tRnd,"DCAnsBoltStream");

	}

	return 0;
}

