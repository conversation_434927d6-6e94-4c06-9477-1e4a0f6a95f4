/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCUserAuth.h
*Indentifier：
*
*Description：
*		消息解析类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_USERAUTH_H__
#define __DC_USERAUTH_H__
#include "DCBasePlugin.h"
#include "DCBizMsgDef.h"
#include "DCOcpMsgDef.h"
using namespace ocs;


class DCUserAuth : public DCBasePlugin
{
	public:	
		DCUserAuth(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCUserAuth()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		virtual const char* desc();

		int GetAreaCode(SPhone & phone, STBizMsg * bizMsg);
	private:
		SUserInfo *m_userInfo;
};
#endif

