#include "DCReqISMPTEL.h"
#include "DCRbMsgDef.h"
#include "BizCdrDefTEL.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "BizCdrDefTEL.h"
#include "BizDataDef.h"
#include "DCLogMacro.h"
#include "REMsgTypeDef.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCUDB.h"
#include "DCDBManer.h"
#include "TConfig.h"
#include "UHead.h"
#include "DCCommonIF.h"

DCReqISMPTEL::DCReqISMPTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_ISMP_TYPE, "", "", "");
}

DCReqISMPTEL::~DCReqISMPTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,  SM_ISMP_TYPE, "", "", "");
}


int DCReqISMPTEL::SwitchReqType(STBizMsg* bizMsg)
{
	if(!bizMsg->m_base)
	{
		return RET_ERROR;
	}
	int ret = RET_SUCCESS;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	SCCRISMP* data =(SCCRISMP*)bizMsg->m_extend;

	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				ret = Init(base, data, bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				ret = Update(base, data, bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				ret = Term(base, data, bizMsg);
			}
			break;
		case SM_SESSION_EVENT_CODE:
			{
				ret = Event(base, data, bizMsg);
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				ret = XDR(base, data, bizMsg);
			}
			break;
		default:
			{
				ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "", "invalid request type[%s]", bizMsg->m_requestType);
			}
			break;
	}

	return ret;
}

int DCReqISMPTEL::Init(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int payFlag 							= 1;
	int  traceNumOnff						= 0;
	long nextCCTime							= 0;
	int iUserMessFlag						= 0;	//用户类型标示:0,非高风险业务;1，预付费高风险业务;2，后付费高风险业务

	TSERVICE_QUOTA_CONF *conf				= NULL;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]				= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};

	TSMPara* m_smpara 						= (TSMPara*)bizMsg->m_smpara;
	SUserInfo *userInfo						= (SUserInfo*)bizMsg->m_userinfo;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;
	if(bizMsg->m_serviceContextID == HRS)
		if( bizMsg->m_userType == 1)
		iUserMessFlag = 2;
		else
		iUserMessFlag = 1;

	//获取配额信息
	if((conf = m_smpara->GetServiceQuotaConf(ISMP_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "no find service quota config[%d]",ISMP_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + conf->VALID_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "nextCCTime[%ld]", nextCCTime);
    sprintf(timeStamp,"%ld",base->timestamp);

	//计算计费方类型
	if(data->chargingPartyType >= 3)
	{
		payFlag = 3;
	}
	else
	{
		payFlag = data->chargingPartyType;
	}

	//判断网元是否有上传预占
	if(!base->RSU.money && !base->RSU.duration && !base->RSU.unitInput && !base->RSU.unitOutput && !base->RSU.unitTotal)
	{
		base->RSU.duration = conf->DURATION;
		base->RSU.unitInput = conf->INPUT_OCTETS;
		base->RSU.unitOutput = conf->OUTPUT_OCTETS;
		base->RSU.unitTotal = conf->TOTAL_OCTETS;
	}

	if(base->subscription.phone != data->calling.phone)
	{
		data->calling.area=base->subscription.area;
	}
	else if(base->subscription.phone != data->called.phone )
	{
		data->called.area=base->subscription.area;
	}


	//组装RER	消息
	ocs::UHead uhd;
	ocs::rbismp rbr;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rsu_t rsu;
	ocs::rbext ext;

	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	head.topology = base->topology;
	//100   MsgType
	head.type = RE_SERVICE_TYPE_INT_ISMP_REQ;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MsgType[%d]", RE_SERVICE_TYPE_INT_ISMP_REQ);

	//000   会话ID
	head.session = bizMsg->m_sessionID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionID[%s]", bizMsg->m_sessionID);

	//101   EventTimeStamp
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose EventTimeStamp[%ld]", bizMsg->timestampCCR);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Serial[%ld]", bizMsg->m_serial);

	//设置信令跟踪标志，通知RB需要信令跟踪
	head.trace = bizMsg->m_trace_flag;
	uhd.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose TracFlag[%d]", bizMsg->m_trace_flag);

	head.sreq = SM_SESSION_INITIAL_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", SM_SESSION_INITIAL_CODE);

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "","compose Latn ID[%d]", userInfo->ilatnid);

	//R85   用户付费属性标识 1
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PayType[1]");

	//R71   会话开始时间
	rbr.sess_start_time = "0";
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionStartTime[%s]", "0");

	//R01   付费号码
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Charge[%s]", base->subscription.phone.c_str());

	//R02   主叫号码
	rbr.calling_nbr = data->calling.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Calling[%s]", data->calling.phone.c_str());

	//R03   被叫号码
	rbr.called_nbr = data->called.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Called[%s]", data->called.phone.c_str());

	//R401 产品
	rbr.product_id = data->productID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductID[%s]", data->productID.c_str());

	//R402 业务类型
	rbr.service_type = data->serviceEnableType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ServiceEnableType[%ld]", data->serviceEnableType);

	//R405 内容代码
	rbr.content_id = data->contentID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ContentID[%s]", data->contentID.c_str());

	//R406 SP代码
	rbr.sp_id = data->SPID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SPID[%s]", data->SPID.c_str());

	//R407媒体类型
	rbr.media_type = data->mediaType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MediaID[%s]", data->mediaType.c_str());

	//R408 客户端IP
	rbr.client_addr = data->clientIP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ClientIP[%s]", data->clientIP.c_str());

	//R409 消息ID
	rbr.msg_id = data->messageID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MessageID[%s]", data->messageID.c_str());

	//R503  销售品ID
	rbr.product_offer_id = data->productOfferID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductOfferID[%s]", data->productOfferID.c_str());

	//R504 主叫号码归属费率区
	if (86 != data->calling.country)
	{
		sprintf(value, "00%d", data->calling.country);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", data->calling.area);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);
	}

	//R506  主叫号码归属运营商
	rbr.calling_hcarrier = data->calling.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingOperator[%d]", data->calling.carriers);

	//R507  被叫号码归属费率区
	if (86 != data->called.country)
	{
		sprintf(value, "00%d", data->called.country);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", data->called.area);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]",  value);
	}

	//R509 被叫号码归属运营商
	rbr.called_hcarrier = data->called.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledOperator[%d]", data->called.carriers);

	//R5012 付费号码归属费率区
	if (86 != base->subscription.country)
	{
		sprintf(value, "00%d", base->subscription.country);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CahrgingArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", base->subscription.area);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CahrgingArea[%s]", value);
	}

	//R601 重发标记
	// pREMsg->set(RB_CODE_R_REPEAT_FLAG, "0");
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RepeatFlag[%s]", "0");

	//R602 计费类型
	rbr.sreq = SM_SESSION_INITIAL_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", SM_SESSION_INITIAL_CODE);

	//R603  会话上次扣费开始时间
	rbr.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitTime[%s]", "0");

	//R604  本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CurDebitTime[%ld]", bizMsg->m_sessionID);

	//R605  是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RatableTime[%s]", "0");

	//R606  激活用户
	if(userInfo->isActive)
	{
		rbr.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ActiveFlag[%s]", "1");
	}
	else
	{
		// pREMsg->del(RB_CODE_R_ACTIVE_FLAG);
		rbr.active_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "delete ActiveFlag", "");
	}

	int numUnit = 0;
	//金额
	if(base->RSU.RSUflag.moneyFlag ||base->RSU.money)
	{
		rsu.unit = RB_UNIT_CODE_CENT;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestUnit[%d]", RB_UNIT_CODE_CENT);

		rsu.amount = (long)base->RSU.money;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestAmount[%d]", base->RSU.money);
		numUnit++;
		domain.rsv.push_back(rsu);
	}

	//时长
	if (base->RSU.RSUflag.durationFlag||base->RSU.duration)
	{
		rsu.unit = RB_UNIT_CODE_SECOND;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestUnit[%d]", RB_UNIT_CODE_SECOND);

		rsu.amount = (long)base->RSU.duration;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestAmount[%d]", base->RSU.duration);
		numUnit++;
		domain.rsv.push_back(rsu);
	}

	//总流量
	if (base->RSU.RSUflag.totalunitFlag||base->RSU.unitTotal)
	{
		rsu.unit = RB_UNIT_CODE_TOTAL_BYTES;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestUnit[%d]", RB_UNIT_CODE_TOTAL_BYTES);

		rsu.amount = (long)base->RSU.unitTotal;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestAmount[%d]", base->RSU.unitTotal);
		numUnit++;
		domain.rsv.push_back(rsu);
	}

	//上行流量
	if (base->RSU.RSUflag.inputFlag||base->RSU.unitInput)
	{
		rsu.unit = RB_UNIT_CODE_UP_BYTES;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestUnit[%d]", RB_UNIT_CODE_UP_BYTES);

		rsu.amount = (long)base->RSU.unitInput;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestAmount[%d]", base->RSU.unitInput);
		numUnit++;
		domain.rsv.push_back(rsu);
	}

	//下行流量
	if (base->RSU.RSUflag.outputFlag||base->RSU.unitOutput)
	{
		rsu.unit = RB_UNIT_CODE_DOWN_BYTES;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestUnit[%d]", RB_UNIT_CODE_DOWN_BYTES);

		sprintf(value, "%d", base->RSU.unitOutput);
		rsu.amount = (long)base->RSU.unitOutput;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestAmount[%d]", base->RSU.unitOutput);
		numUnit++;
		domain.rsv.push_back(rsu);
	}

	if(numUnit == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "no rsu unit", "");
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	rbr.accumlator_info 	= 1;    	//B06	累积量信息
	rbr.tariff_info 		= 1;		//B07	资费信息
	rbr.balance_info 		= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 		= 1;		//B20	费率信息查询命令
	rbr.balance_query 		= 1;		//B21	余额查询命令
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		bizMsg->data = (char*)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	UDBSQL* pExec= dbm->GetSQL(ISMP_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID);
		pExec->BindParam(2, SM_CDR_SEQ_NUM);
		pExec->BindParam(3, SM_CDR_VERSION);
		pExec->BindParam(4, SM_CDR_TICKETTYPE);
		pExec->BindParam(5, timeStamp);
		pExec->BindParam(6, base->topology);
		pExec->BindParam(7, 0);
		pExec->BindParam(8, 0);
		if(2 == iUserMessFlag)
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP_HRS_A);
		}
		else
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP);
		}
		pExec->BindParam(10, payFlag);
		pExec->BindParam(11, userInfo->servID);
		pExec->BindParam(12, userInfo->custID);
		pExec->BindParam(13, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(14, 0);
		pExec->BindParam(15, TORB_ACTION);
		pExec->BindParam(16, (int)data->chargingType);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->originHost);
		pExec->BindParam(19, bizMsg->m_serial);
		pExec->BindParam(20, (long)bizMsg->timestampCCR);
		pExec->BindParam(21, (long)bizMsg->timestampCCR);
		pExec->BindParam(22, SM_SESSION_INITIAL_CODE);
		pExec->BindParam(23, (int)bizMsg->m_requestNumber);
		pExec->BindParam(24, (long)conf->VALID_TIME);
		pExec->BindParam(25, data->messageID);
		pExec->BindParam(26, (int)data->chargingPartyType);
		pExec->BindParam(27, data->SPID);
		pExec->BindParam(28, (int)data->serviceEnableType);
		pExec->BindParam(29, (int)data->chargingType);
		pExec->BindParam(30, data->productID);
		pExec->BindParam(31, data->productOfferID);
		pExec->BindParam(32, (int)data->serviceType);
		pExec->BindParam(33, data->contentID);
		pExec->BindParam(34, data->mediaType);
		pExec->BindParam(35, data->clientIP);
		pExec->BindParam(36, base->subscriptionType);
		pExec->BindParam(37, base->subscription.phone);
		pExec->BindParam(38, data->calling.phone);
		pExec->BindParam(39, data->called.phone);
		pExec->BindParam(40, "");

		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(41, value);

		if(86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else
		{
			if (data->calling.area)
			{
				sprintf(value, "0%d", data->calling.area);
			}
			else
			{
				value[0] = '\0';
			}
		}
		pExec->BindParam(42, value);

		if(86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		else
		{
			if (data->called.area)
			{
				sprintf(value, "0%d", data->called.area);
			}
			else
			{
				value[0] = '\0';
			}
		}
		pExec->BindParam(43, value);
		pExec->BindParam(44, "");
		pExec->BindParam(45, data->callingNumber);
		pExec->BindParam(46, data->calledNumber);
		pExec->BindParam(47, base->subscription.carriers);
		pExec->BindParam(48, data->calling.carriers);
		pExec->BindParam(49, data->called.carriers);
		pExec->BindParam(50, 1);

		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(51, value);

		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(52, value);

		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
		}
		else
		{
			value[0] = '\0';
		}
		pExec->BindParam(53, value);
		pExec->BindParam(54, "");
		pExec->BindParam(55, (long)conf->QUOTA_CONSUMPTION_TIME);
		pExec->BindParam(56, (long)conf->VOLUME_QUOTA_THRESHOLD);
		pExec->BindParam(57, (long)conf->TIME_QUOTA_THRESHOLD);
		pExec->BindParam(58, (long)conf->QUOTA_HOLDING_TIME);
		pExec->BindParam(59, base->subUnified);
		pExec->BindParam(60, data->callingUnified);
		pExec->BindParam(61, data->calledUnified);
		pExec->BindParam(62, userInfo->aocType);
		pExec->BindParam(63, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(64, traceNumOnff);

		pExec->BindParam(65, "");
		pExec->BindParam(66, "");
		pExec->BindParam(67, "");
		pExec->BindParam(68, "");
		pExec->BindParam(69, "");
		pExec->BindParam(70, (long)base->timestamp);

		pExec->BindParam(71, userInfo->userType);
		pExec->BindParam(72, userInfo->mvnoID);
		pExec->BindParam(73, base->topology);
		pExec->BindParam(74, userInfo->ilatnid);
		pExec->BindParam(75, userInfo->lnAcctID);
		pExec->BindParam(76, bizMsg->m_szServiceContextIDStr);
		if( 1 == bizMsg->m_version)
		{
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(77, switchId);
		}
		else
		{
			pExec->BindParam(77, (long)0);
		}
		pExec->BindParam(78, ""); // CDR_STR_BATCH_ID
		pExec->BindParam(79, ""); // CDR_STR_CREATFLAG

		pExec->Execute();
		pExec->Connection()->Commit();

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "insert ok", "");
	}
	catch(UDBException& e)
	{
		string sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	return RET_SUCCESS;
}

int DCReqISMPTEL::Update(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	long nextCCTime							= 0;
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	ocs::SCCRDataUnit* USU						= NULL;
	SSessionCache cacheData 				;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;

	if(base->usued)
	{
		USU = &(base->USU);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_MISSING_AVP,"", "missing USU", "");
		return SM_OCP_MISSING_AVP;
	}

	// 组装RBR
	ret = composeRER(cacheData, bizMsg, USU);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "" ,"ComposeRER failed!");
		return ret;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + cacheData.valid_time;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "nextCCTime[%ld]", nextCCTime);
    sprintf(timeStamp,"%ld",base->timestamp);

	UDBSQL* pUpdate= dbm->GetSQL(ISMP_UpdateSessionReq);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pUpdate->DivTable(bizMsg->m_sessionID);
			pUpdate->UnBindParam();
			pUpdate->BindParam(1, (int)bizMsg->m_requestType);
			pUpdate->BindParam(2, (int)bizMsg->m_requestNumber);
			pUpdate->BindParam(3, TORB_ACTION);
			pUpdate->BindParam(4, bizMsg->m_serial);
			pUpdate->BindParam(5, nextCCTime);
			pUpdate->BindParam(6, (long)bizMsg->timestampCCR);
			pUpdate->BindParam(7, (long)USU->duration);
			pUpdate->BindParam(8, (long)USU->money);
			pUpdate->BindParam(9, (long)USU->unitTotal);
			pUpdate->BindParam(10, (long)USU->unitInput);
			pUpdate->BindParam(11, (long)USU->unitOutput);
			pUpdate->BindParam(12, (long)base->timestamp);
			pUpdate->BindParam(13, bizMsg->m_sessionID);
			pUpdate->Execute();
			pUpdate->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "update ok");
		}
		catch (UDBException &e)
		{
			string sql;
			pUpdate->Connection()->Rollback();
			pUpdate->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "insert execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_ISMP_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	return RET_SUCCESS;
}

int DCReqISMPTEL::Term(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	long nextCCTime							= 0;
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	ocs::SCCRDataUnit* USU						= NULL;
	SSessionCache cacheData 				;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;

	if(base->usued)
	{
		USU = &(base->USU);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_MISSING_AVP,"", "missing USU", "");
		return SM_OCP_MISSING_AVP;
	}


	// 组装RBR
	ret = composeRER(cacheData, bizMsg, USU);
	if (ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "" ,"ComposeRER failed!");
		return ret;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + cacheData.valid_time;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "nextCCTime[%ld]", nextCCTime);
    sprintf(timeStamp,"%ld",base->timestamp);

	UDBSQL *pUpdate = dbm->GetSQL(ISMP_UpdateSessionReq);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pUpdate->DivTable(bizMsg->m_sessionID);
			pUpdate->UnBindParam();
			pUpdate->BindParam(1, (int)bizMsg->m_requestType);
			pUpdate->BindParam(2, (int)bizMsg->m_requestNumber);
			pUpdate->BindParam(3, TORB_ACTION);
			pUpdate->BindParam(4, bizMsg->m_serial);
			pUpdate->BindParam(5, nextCCTime);
			pUpdate->BindParam(6, (long)bizMsg->timestampCCR);
			pUpdate->BindParam(7, (long)USU->duration);
			pUpdate->BindParam(8, (long)USU->money);
			pUpdate->BindParam(9, (long)USU->unitTotal);
			pUpdate->BindParam(10, (long)USU->unitInput);
			pUpdate->BindParam(11, (long)USU->unitOutput);
			pUpdate->BindParam(12, (long)base->timestamp);
			pUpdate->BindParam(13, bizMsg->m_sessionID);
			pUpdate->Execute();
			pUpdate->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "update ok");
		}
		catch (UDBException &e)
		{
			string sql;
			pUpdate->Connection()->Rollback();
			pUpdate->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "insert execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_ISMP_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	return RET_SUCCESS;
}

int DCReqISMPTEL::Event(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret = RET_SUCCESS;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	switch(base->requestAction)
	{
		case SM_REQUESTED_ACTION_DEBIT:
			{

				ret = Debit(base, data, bizMsg);
			}
			break;
		case SM_REQUESTED_ACTION_REFUND:
			{
				//根据配置决定是否补款
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "service context id original[%s]", bizMsg->m_szServiceContextIDStr);
				ret = m_smpara->GetIsmpRefund(bizMsg->m_szServiceContextIDStr);
				if(ret)
				{
					ret = RefundFreeCCA(bizMsg);//直接返回给网元，防止网元下发不正确补多的情况
				}
				else
				{
					ret = Refund(base, data, bizMsg);
				}
			}
			break;
		case SM_REQUESTED_ACTION_CHECK:
			{
				ret = Check(base, bizMsg);
			}
			break;
		case SM_REQUESTED_ACTION_QUERY_RATABLE_1:
		case SM_REQUESTED_ACTION_QUERY_RATABLE_2:
			{
				ret = QueryRatable(base, data,bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE, "", "invalid action type[%s]", base->requestAction);
				ret = SM_OCP_INVALID_AVP_VALUE;
			}
			break;
	}

	return ret;
}

int DCReqISMPTEL::XDR(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret = RET_SUCCESS;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	//目前xdr转换消息只针对扣负
	if( SM_REQUESTED_ACTION_DEBIT == base->requestAction)
	{
		ret = Debit(base, data, bizMsg);

	}
	else if(SM_REQUESTED_ACTION_REFUND == base->requestAction)
	{
		//根据配置决定是否补款
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "service context id original[%s]", bizMsg->m_szServiceContextIDStr);
		ret = m_smpara->GetIsmpRefund(bizMsg->m_szServiceContextIDStr);
		if(ret)
		{
			ret = RefundFreeCCA(bizMsg);//直接返回给网元，防止网元下发不正确补多的情况
		}
		else
		{
			ret = Refund(base, data, bizMsg);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_INVALID_AVP_VALUE, "", "invalid action type[%d]", base->requestAction);
        ret = SM_OCP_INVALID_AVP_VALUE;
	}

	return ret;
}


int DCReqISMPTEL::Debit(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int payFlag 							= 1;
	string iCreatFlag;
	int  traceNumOnff						= 0;
	long nextCCTime							= 0;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;
	TSERVICE_QUOTA_CONF *conf				= NULL;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]				= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	//用户类型标示:0,非高风险业务;1，预付费高风险业务;2，后付费高风险业务
	int iUserMessFlag						= 0;
	if(bizMsg->m_serviceContextID == HRS)
	{
		if( bizMsg->m_userType == 1)
		{
			iUserMessFlag = 1;
		}
		else if(2 == bizMsg->m_userType)
		{
			iUserMessFlag = 2;
		}
	}

	SUserInfo* userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;


	//获取配额信息
	if((conf = m_smpara->GetServiceQuotaConf(ISMP_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "no find service quota config[%d]",ISMP_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//是否允许扣负
	int negat =0;
	negat = m_smpara->GetCommonPara()->EventISMPSwitch;


	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "nextCCTime[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	//计算计费方类型
	if(data->chargingPartyType >= 3)
	{
		payFlag = 3;
	}
	else
	{
		payFlag = data->chargingPartyType;
	}

	data->calling.area=base->subscription.area;

	//组装RER	消息
	ocs::UHead uhd;
	ocs::rbismp rbr;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbext ext;
	if(bizMsg->m_requestType == 5)
	{
		 //R07离线批价扣费标识
		string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
		int ratingflag= m_smpara->GetBillAttr(servAttr);
		sprintf(value,"%d",ratingflag);
		if(ratingflag>=0)
		{
			ext.kv["ratingflag"] = value;
		}
	}
	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	head.topology = base->topology;
	//100	MsgType
	head.type = RE_SERVICE_TYPE_INT_ISMP_REQ;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MsgType[%d]", RE_SERVICE_TYPE_INT_ISMP_REQ);
	if(bizMsg->m_requestType == 5)
	head.version = 1;
	else
	head.version = 2;
	//000	会话ID
	head.session = bizMsg->m_sessionID;
	if(bizMsg->m_requestType == 5)
		head.session.erase(0,3);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionID[%s]", bizMsg->m_sessionID);

	//101	EventTimeStamp
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose EventTimeStamp[%ld]", bizMsg->timestampCCR);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Serial[%ld]", bizMsg->m_serial);

	//设置信令跟踪标志，通知RB需要信令跟踪
	head.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose TracFlag[%d]", bizMsg->m_trace_flag);

	head.sreq = SM_SESSION_EVENT_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	head.creditCtlFlag = atoi(iCreatFlag.c_str());

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "","compose Latn ID[%d]", userInfo->ilatnid);
	bizMsg->m_ilatnId = userInfo->ilatnid;

	//R85	用户付费属性标识
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PayType[1]");

	//R01   付费号码
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Charge[%s]", base->subscription.phone.c_str());

	//R02   主叫号码
	rbr.calling_nbr = data->calling.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Calling[%s]", data->calling.phone.c_str());

	//R03   被叫号码
	rbr.called_nbr = data->called.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Called[%s]", data->called.phone.c_str());

	//R401 产品
	rbr.product_id = data->productID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductID[%s]", data->productID.c_str());

	//R402 业务类型
	rbr.service_type = data->serviceEnableType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ServiceEnableType[%ld]", data->serviceEnableType);

	//R405 内容代码
	rbr.content_id = data->contentID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ContentID[%s]", data->contentID.c_str());

	//R406 SP代码
	rbr.sp_id = data->SPID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SPID[%s]", data->SPID.c_str());

	//R407媒体类型
	rbr.media_type = data->mediaType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MediaID[%s]", data->mediaType.c_str());

	//R408 客户端IP
	rbr.client_addr = data->clientIP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ClientIP[%s]", data->clientIP.c_str());

	//R409 消息ID
	rbr.msg_id = data->messageID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MessageID[%s]", data->messageID.c_str());

	//R410 计费类型
	if (data->chargingType)
	{
		rbr.charging_type = data->chargingType;
	}
	else
	{
		rbr.charging_type = 0;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingType[%u]", data->chargingType);

	//R503  销售品ID
	rbr.product_offer_id = data->productOfferID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductOfferID[%s]", data->productOfferID.c_str());

	//R504 主叫号码归属费率区
	if (86 != data->calling.country)
	{
		sprintf(value, "00%d", data->calling.country);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", data->calling.area);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);
	}

	//R506  主叫号码归属运营商
	rbr.calling_hcarrier = data->calling.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingOperator[%d]", data->calling.carriers);

	//R507  被叫号码归属费率区
	if (86 != data->called.country)
	{
		sprintf(value, "00%d", data->called.country);
		rbr.called_harea =  value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", data->called.area);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]", value);
	}

	//R509 被叫号码归属运营商
	rbr.called_hcarrier = data->called.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledOperator[%d]", data->called.carriers);

	//R5012 付费号码归属费率区
	if (86 != base->subscription.country)
	{
		sprintf(value, "00%d", base->subscription.country);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", base->subscription.area);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingArea[%s]", value);
	}

	//R601 重发标记
	// pREMsg->set(RB_CODE_R_REPEAT_FLAG, "0");
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RepeatFlag[%s]", "0");

	//R602 计费类型
	rbr.sreq = SM_SESSION_EVENT_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	//R603  会话上次扣费开始时间
	rbr.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PREDebitTime[%s]", "0");

	//R604  本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CURDebitTime[%ld]", bizMsg->timestampCCR);

	//R605  是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RatableTime[%s]", "0");

	//R606  激活用户
	if(userInfo->isActive)
	{
		rbr.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ActiveFlag[%s]", "1");
	}
	else
	{
		// pREMsg->del(RB_CODE_R_ACTIVE_FLAG);
		rbr.active_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "delete ActiveFlag", "");
	}


	//高风险标识
	rbr.highrisk_flag = iUserMessFlag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose HighRiskService[%d]", iUserMessFlag);

       int debitControl = 0;
	debitControl = m_smpara->GetISMPPara()->debitControl;
	if(debitControl)
	{
		if(debitControl>2)
		{
			debitControl = 2;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "DebitType[%d]", debitControl);
	}

	//B038	事件是否允许扣负
	if( SM_SESSION_XDR_CODE == bizMsg->m_requestType )
	{
            //xdr 取离线话单扣负标识配置值
			//B038
		string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectArrear");
		rbr.neg_debit= m_smpara->GetBillAttr(servAttr);
		if(rbr.neg_debit == -1)
			rbr.neg_debit = 1;
	}
	else
	{
		rbr.neg_debit = negat;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitResult[%s]", value);

	ocs::debit dbv;
	//B036 次数
	dbv.unit = atoi(RB_UNIT_STR_TIME);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_TIME);

	// B037
	dbv.amount = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%s]", "1");

       // B0391
	dbv.debit_type = debitControl;
       DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

       domain.dbv.push_back(dbv);

       // 彩铃业务，金钱从productID中获取
	if(!strncmp(data->productID.c_str(), "CRBT", 4))
	{
               // B036 金钱
               dbv.unit = RB_UNIT_CODE_CENT;
               DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_CENT);

               // B037
               dbv.amount = atol(data->productID.c_str()+4);
               DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", dbv.amount);

               // B0391
               dbv.debit_type = debitControl;
               DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

               domain.dbv.push_back(dbv);
	}

	//金额
	if(base->rsued)
	{
		if(base->RSU.RSUflag.inputFlag||base->RSU.unitInput)
		{
			// B036
			dbv.unit = RB_UNIT_CODE_DOWN_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%d]", RB_UNIT_CODE_DOWN_BYTES);

			// B037
			dbv.amount = base->RSU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", base->RSU.unitInput);

			// B0391
			dbv.debit_type = debitControl;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

			domain.dbv.push_back(dbv);
		}

		if(base->RSU.RSUflag.outputFlag||base->RSU.unitOutput)
		{
			dbv.unit = RB_UNIT_CODE_UP_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%d]",  RB_UNIT_CODE_UP_BYTES);

			dbv.amount = base->RSU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]",  base->RSU.unitOutput);

			dbv.debit_type = debitControl;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

			domain.dbv.push_back(dbv);
		}

		if(base->RSU.RSUflag.totalunitFlag||base->RSU.unitTotal)
		{
			dbv.unit = RB_UNIT_CODE_TOTAL_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_TOTAL_BYTES);

			dbv.amount = base->RSU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", base->RSU.unitTotal);

			dbv.debit_type = debitControl;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

			domain.dbv.push_back(dbv);
		}

		if(strncmp(data->productID.c_str(), "CRBT", 4) && (base->RSU.RSUflag.moneyFlag ||base->RSU.valueDigits))
		{
                     // 非彩铃业务使用RSU 金钱
			dbv.unit = RB_UNIT_CODE_CENT;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_CENT);

			dbv.amount = base->RSU.money;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%d]", base->RSU.money);

			dbv.debit_type = debitControl;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

			domain.dbv.push_back(dbv);
		}

		if(base->RSU.RSUflag.durationFlag||base->RSU.duration)
		{
			dbv.unit = RB_UNIT_CODE_SECOND;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_SECOND);

			dbv.amount = base->RSU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", base->RSU.duration);

                     dbv.debit_type = debitControl;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitType[%d]", debitControl);

			domain.dbv.push_back(dbv);
		}
	}

	rbr.accumlator_info 	= 1;		//B06	累积量信息
	rbr.tariff_info 		= 1;		//B07	资费信息
	rbr.balance_info 		= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 		= 1;		//B20	费率信息查询命令
	rbr.balance_query 		= 1;		//B21	余额查询命令
	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	// 获取批次号
	if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 2);
	}
	else
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 1);
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	try
	{
		// encode
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		bizMsg->data = (char*)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	UDBSQL* pExec= dbm->GetSQL(ISMP_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID);
		pExec->BindParam(2, SM_CDR_SEQ_NUM);
		pExec->BindParam(3, SM_CDR_VERSION);
		pExec->BindParam(4, SM_CDR_TICKETTYPE);
		pExec->BindParam(5, timeStamp);
		pExec->BindParam(6, base->topology.c_str());
		/*
		if( 1 == bizMsg->m_version)
		{
			pExec->BindParam(6, base->smExt.kv["CollectId"].c_str());
		}
		else
		{
			pExec->BindParam(6, base->topology.c_str());
		}
		*/
		pExec->BindParam(7, 0);
		pExec->BindParam(8, 0);
		if(2 == iUserMessFlag)
		{
			pExec->BindParam(9, 701);  //  高风险业务后付费用户
		}
		else if (1 == iUserMessFlag)
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP_HRS_A);
		}
		else
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP);   // 高风险业务预付费用户
		}
		pExec->BindParam(10, payFlag);
		pExec->BindParam(11, userInfo->servID);
		pExec->BindParam(12, userInfo->custID);
		pExec->BindParam(13, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(14, 0);
		pExec->BindParam(15, TORB_ACTION);
		pExec->BindParam(16, (int)data->chargingType);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->originHost);
		pExec->BindParam(19, bizMsg->m_serial);
		pExec->BindParam(20, (long)bizMsg->timestampCCR);
		pExec->BindParam(21, (long)bizMsg->timestampCCR);
		pExec->BindParam(22, (int)bizMsg->m_requestType);
		pExec->BindParam(23, (int)bizMsg->m_requestNumber);
		pExec->BindParam(24, (long)conf->VALID_TIME);
		pExec->BindParam(25, data->messageID);
		pExec->BindParam(26, (int)data->chargingPartyType);
		pExec->BindParam(27, data->SPID);
		pExec->BindParam(28, (int)data->serviceEnableType);
		pExec->BindParam(29, (int)data->chargingType);
		pExec->BindParam(30, data->productID);
		pExec->BindParam(31, data->productOfferID);
		pExec->BindParam(32, (int)data->serviceType);
		pExec->BindParam(33, data->contentID);
		pExec->BindParam(34, data->mediaType);
		pExec->BindParam(35, data->clientIP);
		pExec->BindParam(36, base->subscriptionType);
		pExec->BindParam(37, base->subscription.phone);
		pExec->BindParam(38, data->calling.phone);
		pExec->BindParam(39, data->called.phone);
		pExec->BindParam(40, "");  // RE_STR_THIRD_PARTY_NBR

		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
			pExec->BindParam(41, value);
		}
		else
		{
			pExec->BindParam(41, "");
		}

		if(86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
			pExec->BindParam(42, value);
		}
		else
		{
			if (data->calling.area)
			{
				sprintf(value, "0%d", data->calling.area);
				pExec->BindParam(42, value);
			}
			else
			{
				pExec->BindParam(42, "");
			}
		}

		if(86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
			pExec->BindParam(43, value);
		}
		else
		{
			if (data->called.area)
			{
				sprintf(value, "0%d", data->called.area);
				pExec->BindParam(43, value);
			}
			else
			{
				pExec->BindParam(43, "");
			}
		}

		pExec->BindParam(44, "");  // RE_STR_THIRD_AREA
		pExec->BindParam(45, data->callingNumber);
		pExec->BindParam(46, data->calledNumber);
		pExec->BindParam(47, base->subscription.carriers);
		pExec->BindParam(48, data->calling.carriers);
		pExec->BindParam(49, data->called.carriers);
		pExec->BindParam(50, 0);   // RE_INT_THIRD_OPERATOR

		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
			pExec->BindParam(51, value);
		}
		else
		{
			pExec->BindParam(51, "");
		}

		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
			pExec->BindParam(52, value);
		}
		else
		{
			pExec->BindParam(52, "");
		}

		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
			pExec->BindParam(53, value);
		}
		else
		{
			pExec->BindParam(53, "");
		}

		pExec->BindParam(54, "");  // RE_STR_THIRD_COUNTRY
		pExec->BindParam(55, (long)conf->QUOTA_CONSUMPTION_TIME);
		pExec->BindParam(56, (long)conf->VOLUME_QUOTA_THRESHOLD);
		pExec->BindParam(57, (long)conf->TIME_QUOTA_THRESHOLD);
		pExec->BindParam(58, (long)conf->QUOTA_HOLDING_TIME);
		pExec->BindParam(59, base->subUnified);
		pExec->BindParam(60, data->callingUnified);
		pExec->BindParam(61, data->calledUnified);
		pExec->BindParam(62, userInfo->aocType);
		pExec->BindParam(63, base->routeRecord);
		traceNumOnff = bizMsg->m_trace_flag;	//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(64, traceNumOnff);
		pExec->BindParam(65, "");  // OCP_STR_SPC_PRODUCTID
		pExec->BindParam(66, "");  // OCP_STR_SP_PRODUCTID
		pExec->BindParam(67, "");  // OCP_STR_ORDERMETHODID
		pExec->BindParam(68, "");  // OCP_STR_PUSHID
		pExec->BindParam(69, "");  // OCP_STR_CPID

		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(70, bizMsg->billcycle);
		}
		else
			pExec->BindParam(70, (long)base->timestamp);

		pExec->BindParam(71, userInfo->userType);
		pExec->BindParam(72, userInfo->mvnoID);
		pExec->BindParam(73, "");
		pExec->BindParam(74, userInfo->ilatnid);
		pExec->BindParam(75, userInfo->lnAcctID);
		pExec->BindParam(76, bizMsg->m_szServiceContextIDStr);
		if( 1 == bizMsg->m_version)
		{
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(77, switchId);
		}
		else
		{
			pExec->BindParam(77, (long)0);
		}
		pExec->BindParam(78, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(79, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "insert ok");
	}
	catch(UDBException& e)
	{
		string sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}

int DCReqISMPTEL::Refund(SCCRBase* base, SCCRISMP* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int payFlag 							= 1;
	int  traceNumOnff						= 0;
	string iCreatFlag;
	long nextCCTime							= 0;
	DCDBManer* dbm 							= (DCDBManer*)bizMsg->m_dbm;
	TSERVICE_QUOTA_CONF *conf				= NULL;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]				= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	SUserInfo* userInfo = (SUserInfo*)bizMsg->m_userinfo;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;

	//用户类型标示:0,非高风险业务;1，预付费高风险业务;2，后付费高风险业务
	int iUserMessFlag	= 0;
	if(bizMsg->m_serviceContextID == HRS)
		if( bizMsg->m_userType == 1)
		iUserMessFlag = 2;
		else
		iUserMessFlag = 1;

	//获取配额信息
	if((conf = m_smpara->GetServiceQuotaConf(ISMP_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "no find service quota config[%d]",ISMP_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + 300;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "nextCCTime[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	//计算计费方类型
	if(data->chargingPartyType >= 3)
	{
		payFlag = 3;
	}
	else
	{
		payFlag = data->chargingPartyType;
	}

	data->calling.area=base->subscription.area;


	//组装RER	消息
	ocs::UHead uhd;
	ocs::rbismp rbr;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbext ext;
	//R07离线批价扣费标识
	string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
	int ratingflag= m_smpara->GetBillAttr(servAttr);
	sprintf(value,"%d",ratingflag);
	if(ratingflag>=0)
	{
		ext.kv["ratingflag"] = value;
	}

	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	head.topology = base->topology;
	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	head.creditCtlFlag = atoi(iCreatFlag.c_str());
	//100	MsgType
	head.type = RE_SERVICE_TYPE_INT_ISMP_REQ;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MsgType[%s]", RE_SERVICE_TYPE_STR_ISMP_REQ);

	if(bizMsg->m_requestType == 5)
	head.version = 1;
	else
	head.version = 2;
	//000	会话ID
	head.session = bizMsg->m_sessionID;
	if(bizMsg->m_requestType == 5)
		head.session.erase(0,3);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionID[%s]", bizMsg->m_sessionID);

	//101	EventTimeStamp
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose EventTimeStamp[%ld]", bizMsg->timestampCCR);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Serial[%ld]", bizMsg->m_serial);

	//设置信令跟踪标志，通知RB需要信令跟踪
	head.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose TracFlag[%d]", bizMsg->m_trace_flag);

	head.sreq = SM_SESSION_EVENT_REFUND_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "","compose Latn ID[%d]", userInfo->ilatnid);
	bizMsg->m_ilatnId = userInfo->ilatnid;

	//R85	用户付费属性标识
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PayFlag[1]");

	//R01	付费号码
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Charge[%s]", base->subscription.phone.c_str());

	//R02	主叫号码
	rbr.calling_nbr = data->calling.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Calling[%s]", data->calling.phone.c_str());

	//R03	被叫号码
	rbr.called_nbr = data->called.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Called[%s]", data->called.phone.c_str());

	//R401 产品
	rbr.product_id = data->productID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductID[%s]", data->productID.c_str());

	//R402 业务类型
	rbr.service_type = data->serviceEnableType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ServiceEnableType[%ld]", data->serviceEnableType);

	//R405 内容代码
	rbr.content_id = data->contentID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ContentID[%s]", data->contentID.c_str());

	//R406 SP代码
	rbr.sp_id = data->SPID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SPID[%s]", data->SPID.c_str());

	//R407媒体类型
	rbr.media_type = data->mediaType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MediaID[%s]", data->mediaType.c_str());

	//R408 客户端IP
	rbr.client_addr = data->clientIP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ClientIP[%s]", data->clientIP.c_str());

	//R409 消息ID
	rbr.msg_id = data->messageID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MessageID[%s]", data->messageID.c_str());

	//R410 计费类型
	if (data->chargingType)
	{
		rbr.charging_type = data->chargingType;
	}
	else
	{
		rbr.charging_type = 0;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingType[%u]", data->chargingType);

	//R503	销售品ID
	rbr.product_offer_id = data->productOfferID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductOfferID[%s]", data->productOfferID.c_str());

	//R504 主叫号码归属费率区
	if (86 != data->calling.country)
	{
		sprintf(value, "00%d", data->calling.country);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", data->calling.area);
		rbr.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);
	}

	//R506	主叫号码归属运营商
	rbr.calling_hcarrier = data->calling.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingOperator[%d]", data->calling.carriers);

	//R507	被叫号码归属费率区
	if (86 != data->called.country)
	{
		sprintf(value, "00%d", data->called.country);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", data->called.area);
		rbr.called_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]", value);
	}

	//R509 被叫号码归属运营商
	rbr.called_hcarrier = data->called.carriers;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledOperator[%d]", data->called.carriers);

	//R5012 付费号码归属费率区
	if (86 != base->subscription.country)
	{
		sprintf(value, "00%d", base->subscription.country);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingArea[%s]", value);
	}
	else
	{
		sprintf(value, "0%d", base->subscription.area);
		rbr.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingArea[%s]", value);
	}

	//R601 重发标记
	// pREMsg->set(RB_CODE_R_REPEAT_FLAG, "0");
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RepeatFlag[%s]", "0");

	//R602 计费类型
	rbr.sreq = SM_SESSION_EVENT_REFUND_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", SM_SESSION_EVENT_CODE);

	//R603	会话上次扣费开始时间
	rbr.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PREDebitTime[%s]", "0");

	//R604	本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CURDebitTime[%ld]", bizMsg->timestampCCR);

	//R605	是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RatableTime[%s]", "0");

	//R606	激活用户
	if(userInfo->isActive)
	{
		rbr.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ActiveFlag[%s]", "1");
	}
	else
	{
		// pREMsg->del(RB_CODE_R_ACTIVE_FLAG);
		rbr.active_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "delete ActiveFlag", "");
	}


	memset(value, 0x00, sizeof(value));
	//高风险标识
	rbr.highrisk_flag = iUserMessFlag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose HighRiskService[%d]", iUserMessFlag);

	//B04
	ocs::refund rfv;
	//次数
	// B046
	rfv.unit = RB_UNIT_CODE_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose BackUnit[%d]", RB_UNIT_CODE_TIME);

	// B047
	rfv.amount = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose BackAmount[%s]", "1");
	domain.rfv.push_back(rfv);

		//金额
	if(base->rsued)
	{
		if(base->RSU.RSUflag.moneyFlag ||base->RSU.valueDigits)
		{
			rfv.unit = RB_UNIT_CODE_CENT;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%d]", RB_UNIT_CODE_CENT);

			rfv.amount = base->RSU.money;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%d]", base->RSU.money);
			domain.rfv.push_back(rfv);
		}

		if(base->RSU.RSUflag.outputFlag||base->RSU.unitOutput)
		{
			rfv.unit = RB_UNIT_CODE_UP_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_CODE_UP_BYTES);

			rfv.amount = base->RSU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", base->RSU.unitOutput);
			domain.rfv.push_back(rfv);
		}

		if(base->RSU.RSUflag.totalunitFlag||base->RSU.unitTotal)
		{
			rfv.unit = RB_UNIT_CODE_TOTAL_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_TOTAL_BYTES);

			rfv.amount = base->RSU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", base->RSU.unitTotal);
			domain.rfv.push_back(rfv);
		}

		if(base->RSU.RSUflag.inputFlag||base->RSU.unitInput)
		{
			rfv.unit = RB_UNIT_CODE_DOWN_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_DOWN_BYTES);

			rfv.amount = base->RSU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%ld]", base->RSU.unitInput);
			domain.rfv.push_back(rfv);
		}

		if(base->RSU.RSUflag.durationFlag||base->RSU.duration)
		{
			rfv.unit = RB_UNIT_CODE_SECOND;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_SECOND);

			rfv.amount = base->RSU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%d]", base->RSU.duration);
			domain.rfv.push_back(rfv);
		}

	}

	rbr.accumlator_info 	= 1;		//B06	累积量信息
	rbr.tariff_info 		= 1;		//B07	资费信息
	rbr.balance_info 		= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 		= 1;		//B20	费率信息查询命令
	rbr.balance_query 		= 1;		//B21	余额查询命令
	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	// 获取批次号
	if(SM_SESSION_XDR_CODE == bizMsg->m_requestType)
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 2);
	}
	else
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, m_smpara->GetCommonPara()->iBatchIdTime, 1);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
	ext.kv["batchId"] = bizMsg->m_sBatchId;

	try
	{
		// encode
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		bizMsg->data = (char*)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

    UDBSQL* pExec= dbm->GetSQL(ISMP_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID);
		pExec->BindParam(2, SM_CDR_SEQ_NUM);
		pExec->BindParam(3, SM_CDR_VERSION);
		pExec->BindParam(4, SM_CDR_TICKETTYPE);
		pExec->BindParam(5, timeStamp);
		pExec->BindParam(6, base->topology.c_str());
		/*
		if( 1 == bizMsg->m_version)
		{
			pExec->BindParam(6, base->smExt.kv["CollectId"].c_str());
		}
		else
		{
			pExec->BindParam(6, base->topology.c_str());
		}
		*/
		pExec->BindParam(7, 0);
		pExec->BindParam(8, 0);
		if(2 == iUserMessFlag)
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP_HRS_A);
		}
		else
		{
			pExec->BindParam(9, SERVICES_CENARIOUS_ISMP);
		}
		pExec->BindParam(10, payFlag);
		pExec->BindParam(11, userInfo->servID);
		pExec->BindParam(12, userInfo->custID);
		pExec->BindParam(13, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(14, 0);
		pExec->BindParam(15, TORB_ACTION);
		pExec->BindParam(16, (int)data->chargingType);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->originHost);
		pExec->BindParam(19, (long)bizMsg->m_serial);
		pExec->BindParam(20, (long)bizMsg->timestampCCR);
		pExec->BindParam(21, (long)bizMsg->timestampCCR);
		pExec->BindParam(22, SM_SESSION_EVENT_REFUND_CODE);
		pExec->BindParam(23, (int)bizMsg->m_requestNumber);
		pExec->BindParam(24, (long)conf->VALID_TIME);
		pExec->BindParam(25, data->messageID);
		pExec->BindParam(26, (int)data->chargingPartyType);
		pExec->BindParam(27, data->SPID);
		pExec->BindParam(28, (int)data->serviceEnableType);
		pExec->BindParam(29, (int)data->chargingType);
		pExec->BindParam(30, data->productID);
		pExec->BindParam(31, data->productOfferID);
		pExec->BindParam(32, (int)data->serviceType);
		pExec->BindParam(33, data->contentID);
		pExec->BindParam(34, data->mediaType);
		pExec->BindParam(35, data->clientIP);
		pExec->BindParam(36, base->subscriptionType);
		pExec->BindParam(37, base->subscription.phone);
		pExec->BindParam(38, data->calling.phone);
		pExec->BindParam(39, data->called.phone);
		pExec->BindParam(40, "");

		if (base->subscription.area)
		{
			sprintf(value, "0%d", base->subscription.area);
			pExec->BindParam(41, value);
		}
		else
		{
			pExec->BindParam(41, "");
		}

		if(86 != data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
			pExec->BindParam(42, value);
		}
		else
		{
			if (data->calling.area)
			{
				sprintf(value, "0%d", data->calling.area);
				pExec->BindParam(42, value);
			}
			else
			{
				pExec->BindParam(42, "");
			}
		}

		if(86 != data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
			pExec->BindParam(43, value);
		}
		else
		{
			if (data->called.area)
			{
				sprintf(value, "0%d", data->called.area);
				pExec->BindParam(43, value);
			}
			else
			{
				pExec->BindParam(43, "");
			}
		}

		pExec->BindParam(44, "");
		pExec->BindParam(45, data->callingNumber);
		pExec->BindParam(46, data->calledNumber);
		pExec->BindParam(47, base->subscription.carriers);
		pExec->BindParam(48, data->calling.carriers);
		pExec->BindParam(49, data->called.carriers);
		pExec->BindParam(50, 0);

		if (base->subscription.country)
		{
			sprintf(value, "00%d", base->subscription.country);
			pExec->BindParam(51, value);
		}
		else
		{
			pExec->BindParam(51, "");
		}

		if (data->calling.country)
		{
			sprintf(value, "00%d", data->calling.country);
			pExec->BindParam(52, value);
		}
		else
		{
			pExec->BindParam(52, "");
		}

		if (data->called.country)
		{
			sprintf(value, "00%d", data->called.country);
			pExec->BindParam(53, value);
		}
		else
		{
			pExec->BindParam(53, "");
		}

		pExec->BindParam(54, "");
		pExec->BindParam(55, (long)conf->QUOTA_CONSUMPTION_TIME);
		pExec->BindParam(56, (long)conf->VOLUME_QUOTA_THRESHOLD);
		pExec->BindParam(57, (long)conf->TIME_QUOTA_THRESHOLD);
		pExec->BindParam(58, (long)conf->QUOTA_HOLDING_TIME);
		pExec->BindParam(59, base->subUnified);
		pExec->BindParam(60, data->callingUnified);
		pExec->BindParam(61, data->calledUnified);
		pExec->BindParam(62, userInfo->aocType);
		pExec->BindParam(63, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(64, traceNumOnff);
		pExec->BindParam(65, "");
   		pExec->BindParam(66, "");
   		pExec->BindParam(67, "");
   		pExec->BindParam(68, "");
   		pExec->BindParam(69, "");
		if(bizMsg->m_requestType == 5)
		{
			pExec->BindParam(70, bizMsg->billcycle);
		}
		else
			pExec->BindParam(70, (long)base->timestamp);

		pExec->BindParam(71, userInfo->userType);
		pExec->BindParam(72, userInfo->mvnoID);
		pExec->BindParam(73, "");
		pExec->BindParam(74, userInfo->ilatnid);
		pExec->BindParam(75, userInfo->lnAcctID);
		pExec->BindParam(76, bizMsg->m_szServiceContextIDStr);
		if( 1 == bizMsg->m_version)
		{
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(77, switchId);
		}
		else
		{
			pExec->BindParam(77, (long)0);
		}
		pExec->BindParam(78, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(79, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "insert ok");
	}
	catch(UDBException& e)
	{
		string sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY,"", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}

int DCReqISMPTEL::composeRER(SSessionCache& cacheData, STBizMsg* bizMsg, SCCRDataUnit* USU)
{
	int ret 								= 0;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]				= {0};
	TSERVICE_QUOTA_CONF *conf				= NULL;
	ocs::SCCRDataUnit TUSU;
	ocs::SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	TSMPara* m_smpara = (TSMPara*)bizMsg->m_smpara;
	DCDBManer* dbm 	= (DCDBManer*)bizMsg->m_dbm;
	SUserInfo* userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	//获取配额信息
	if((conf = m_smpara->GetServiceQuotaConf(ISMP_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "no find service quota config[%d]",ISMP_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	cacheData.valid_time = conf->VALID_TIME;
	cacheData.rsu_duration = conf->DURATION;
	cacheData.rsu_totalOCtets = conf->TOTAL_OCTETS;

	//判断网元是否有上传预占
	if(!base->RSU.money && !base->RSU.duration && !base->RSU.unitInput && !base->RSU.unitOutput && !base->RSU.unitTotal)
	{
		base->RSU.duration = conf->DURATION;
		base->RSU.unitInput = conf->INPUT_OCTETS;
		base->RSU.unitOutput = conf->OUTPUT_OCTETS;
		base->RSU.unitTotal = conf->TOTAL_OCTETS;
	}

	//组装RER	消息
	ocs::UHead uhd;
	ocs::rbismp rbr;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbext ext;

    UDBSQL* pQuery = dbm->GetSQL(ISMP_GetSessionRecord);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			head.topology = base->topology;
			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.trace = bizMsg->m_trace_flag;
			uhd.checkKey = bizMsg->m_strCheckKey;
			//100	MsgType
			head.type = RE_SERVICE_TYPE_INT_ISMP_REQ;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MsgType[%s]", RE_SERVICE_TYPE_STR_ISMP_REQ);

			//000	会话ID
			head.session = bizMsg->m_sessionID;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionID[%s]", bizMsg->m_sessionID);

			//101	EventTimeStamp
			head.stamp = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose EventTimeStamp[%ld]", bizMsg->timestampCCR);

			// 001 serial
			head.serial = bizMsg->m_serial;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Serial[%ld]", bizMsg->m_serial);

			head.sreq = bizMsg->m_requestType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", bizMsg->m_requestType);

			head.creditCtlFlag = 3;

			//prod_inst_id
			pQuery->GetValue(48, value);
			head.prodinstid = userInfo->servID = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

                     pQuery->GetValue(47, value);
			rbr.latn_id = userInfo->ilatnid = atoi(value);
			bizMsg->m_ilatnId = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "","compose Latn ID[%d]", userInfo->ilatnid);


			//R85	用户付费属性标识
			rbr.pay = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PayFlag[1]");

			//R601 重发标记
			// pREMsg->set(RB_CODE_R_REPEAT_FLAG, "0");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RepeatFlag[%s]", "0");

			//R602 计费类型
			rbr.sreq = SM_SESSION_UPDATE_CODE;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%s]", SM_SESSION_UPDATE_STRING);

			//R604  本次计费请求开始时间
			rbr.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CURDebitTime[%s]", value);

			//R605  是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RatableTime[%s]", "0");

			pQuery->GetValue(1, value);//SM_INT_SESSION_STATUS
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "sessionStatus[%d]",value);

			//总使用时长
			memset(value, 0, sizeof(value));
			pQuery->GetValue(2, value);// SM_LNG_ALL_USU_TIME
			TUSU.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "duration[%u]", TUSU.duration);

			//总使用金额
			memset(value, 0, sizeof(value));
			pQuery->GetValue(3, value);// SM_LNG_ALL_USU_MONEY
			TUSU.money= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "money[%d]", TUSU.money);

			//总使用总流量
			memset(value, 0, sizeof(value));
			pQuery->GetValue(4, value); // SM_LNG_ALL_USU_TOTAL_OCT
			TUSU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "unitTotal[%d]", TUSU.unitTotal);

			//总使用上行流量
			memset(value, 0, sizeof(value));
			pQuery->GetValue(5, value); // SM_LNG_ALL_USU_INPUT_OCT
			TUSU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "unitInput[%d]", TUSU.unitInput);

			//总使用下行流量
			memset(value, 0, sizeof(value));
			pQuery->GetValue(6, value); // SM_LNG_ALL_USU_OUTPUT_OCT
			TUSU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "unitOutput[%d]", TUSU.unitOutput);

			//信令跟踪
			pQuery->GetValue(7, value);// SM_TRACE_NUM_ONFF
			//traceNumOnff  = atoi(value);
			bizMsg->m_trace_flag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "SM_TRACE_NUM_ONFF[%s]", value);
			//设置信令跟踪标志，通知RB需要信令跟踪
			head.trace = bizMsg->m_trace_flag;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose TracFlag[%d]", bizMsg->m_trace_flag);

			//R71   会话开始时间
			pQuery->GetValue(8, value);// RE_LNG_CALL_START_TIME
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionStartTime[%s]", value);

			//R603  会话上次扣费开始时间
			pQuery->GetValue(9, value);// RE_LNG_CURRENT_CCR_TIME
			rbr.pre_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose PREDebitTime[%s]", value);

			//R01   付费号码
			pQuery->GetValue(10, value);// RE_STR_SUB_NBR
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Charge[%s]", value);

			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value); // RE_STR_SUB_COUNTRY
			if (86 != atoi(value))
			{
				rbr.charged_harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingArea[%s]", value);
			}
			else
			{
				pQuery->GetValue(12, value);// RE_STR_SUB_AREA
				rbr.charged_harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ChargingArea[%s]", value);
			}

			//R02   主叫号码
			pQuery->GetValue(13, value); // RE_STR_OA_SUB_NBR
			rbr.calling_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Calling[%s]", value);

			//R504 主叫号码归属费率区
			pQuery->GetValue(15, value);// RE_STR_CALLING_AREA
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingArea[%s]", value);

			//R506  主叫号码归属运营商
			pQuery->GetValue(16, value); // RE_INT_CALLING_OPERATOR
			rbr.calling_hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CallingOperator[%s]", value);

			//R03   被叫号码
			pQuery->GetValue(17, value);// RE_STR_DA_SUB_NBR
			rbr.called_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose Called[%s]", value);

			//R507  被叫号码归属费率区
			pQuery->GetValue(19, value);// RE_STR_CALLED_AREA
			rbr.called_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledArea[%s]", value);

			//R509 被叫号码归属运营商
			pQuery->GetValue(20, value);// RE_INT_CALLED_OPERATOR
			rbr.called_hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose CalledOperator[%s]", value);

			//R401 产品
			pQuery->GetValue(21, value);// OCP_STR_PRODUCT_ID
			rbr.product_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductID[%s]", value);

			//R402 业务类型
			pQuery->GetValue(22, value);// OCP_INT_SERVICE_ENABLE_TYPE
			rbr.service_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ServiceEnableType[%s]", value);

			//R503  销售品
			pQuery->GetValue(23, value);// OCP_STR_SPC_PRODUCTID
			rbr.product_offer_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ProductOfferID[%s]", value);

			//R405 内容代码
			pQuery->GetValue(24, value);// OCP_STR_CONTENT_ID
			rbr.content_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ContentID[%s]", value);

			//R406 SP代码
			pQuery->GetValue(25, value);// OCP_STR_SP_PRODUCTID
			rbr.sp_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SPID[%s]", value);

			//R407媒体类型
			pQuery->GetValue(26, value);// OCP_STR_MEDIA_TYPE
			rbr.media_type = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MediaID[%s]", value);

			//R408 客户端IP
			pQuery->GetValue(27, value);// OCP_STR_IP_CLIENT_IP
			rbr.client_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ClientIP[%s]", value);

			//R409 消息ID
			pQuery->GetValue(28, value);// OCP_STR_MSG_ID
			rbr.msg_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose MessageID[%s]", value);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNKNOWN_SESSION_ID, "", "not find session", "");
			return  SM_OCP_UNKNOWN_SESSION_ID;
		}
	}
	catch(UDBException& e)
	{
		string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//累计总使用量
	TUSU.duration += USU->duration;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "TUSU druation[%u]", TUSU.duration);

	TUSU.money += USU->money;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "TUSU money[%d]", TUSU.money);

	TUSU.unitTotal += USU->unitTotal;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "TUSU unitTotal[%d]", TUSU.unitTotal);

	TUSU.unitInput += USU->unitInput;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "TUSU unitInput[%d]", TUSU.unitInput);

	TUSU.unitOutput += USU->unitOutput;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "TUSU unitOutput[%d]", TUSU.unitOutput);


	if (SM_SESSION_UPDATE_CODE == bizMsg->m_requestType)
	{
		rsu_t rsu;
		//B01预占
		int numUnit = 0;
		//金额
		if(base->RSU.RSUflag.moneyFlag ||base->RSU.money)
		{
			rsu.unit = RB_UNIT_CODE_CENT;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedUnit[%s]", RB_UNIT_STR_CENT);

			rsu.amount = (long)base->RSU.money;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedAmount[%d]", base->RSU.money);
			numUnit++;
			domain.rsv.push_back(rsu);
		}

		//时长
		if (base->RSU.RSUflag.durationFlag||base->RSU.duration)
		{
			rsu.unit = RB_UNIT_CODE_SECOND;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedUnit[%s]", RB_UNIT_STR_SECOND);

			rsu.amount = (long)base->RSU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedAmount[%d]", base->RSU.duration);
			numUnit++;
			domain.rsv.push_back(rsu);
		}

		//总流量
		if (base->RSU.RSUflag.totalunitFlag||base->RSU.unitTotal)
		{
			rsu.unit = RB_UNIT_CODE_TOTAL_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedUnit[%s]", RB_UNIT_STR_TOTAL_BYTES);

			rsu.amount = (long)base->RSU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedAmount[%d]", base->RSU.unitTotal);
			numUnit++;
			domain.rsv.push_back(rsu);
		}

		//上行流量
		if (base->RSU.RSUflag.inputFlag||base->RSU.unitInput)
		{
			rsu.unit = RB_UNIT_CODE_UP_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedUnit[%s]", RB_UNIT_STR_UP_BYTES);

			rsu.amount = (long)base->RSU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedAmount[%d]", base->RSU.unitInput);
			numUnit++;
			domain.rsv.push_back(rsu);
		}

		//下行流量
		if (base->RSU.RSUflag.outputFlag||base->RSU.unitOutput)
		{
			rsu.unit = RB_UNIT_CODE_DOWN_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedUnit[%s]", RB_UNIT_STR_DOWN_BYTES);

			sprintf(value, "%d", base->RSU.unitOutput);
			rsu.amount = (long)base->RSU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedAmount[%d]", base->RSU.unitOutput);
			numUnit++;
			domain.rsv.push_back(rsu);
		}
		if(numUnit == 0)
		{
			rsu.unit = RB_UNIT_CODE_CENT;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedUnit[%s]", RB_UNIT_STR_CENT);

			rsu.amount = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RequestedAmount[%s]", "0");
			domain.rsv.push_back(rsu);
		}
	}
	else if (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		//B03总使用量
		debit dbv;
		//时长
		if(USU->RSUflag.durationFlag ||TUSU.duration)
		{
			dbv.unit = RB_UNIT_CODE_SECOND;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_SECOND);

			dbv.amount = (long)TUSU.duration;
			cacheData.duration = TUSU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%d]", TUSU.duration);
			domain.dbv.push_back(dbv);
		}

		//金额
		if(USU->RSUflag.moneyFlag||TUSU.money)
		{
			dbv.unit = RB_UNIT_CODE_CENT;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]",  RB_UNIT_STR_CENT);

			dbv.amount = (long)TUSU.money;
			cacheData.money = TUSU.money;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%d]", TUSU.money);
			domain.dbv.push_back(dbv);
		}

		//总使用量
		if(USU->RSUflag.totalunitFlag||TUSU.unitTotal)
		{
			dbv.unit = RB_UNIT_CODE_TOTAL_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_TOTAL_BYTES);

			dbv.amount = (long)TUSU.unitTotal;
			cacheData.unitTotal = TUSU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%d]", TUSU.unitTotal);
			domain.dbv.push_back(dbv);
		}

		//上行流量
		if(USU->RSUflag.inputFlag||TUSU.unitInput)
		{
			dbv.unit = RB_UNIT_CODE_UP_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_UP_BYTES);

			dbv.amount = (long)TUSU.unitInput;
			cacheData.unitInput = TUSU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%lld]", TUSU.unitInput);
			domain.dbv.push_back(dbv);
		}

		//下行流量
		if(USU->RSUflag.outputFlag||TUSU.unitOutput)
		{
			dbv.unit = RB_UNIT_CODE_DOWN_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitUnit[%s]", RB_UNIT_STR_DOWN_BYTES);

			dbv.amount = (long)TUSU.unitOutput;
			cacheData.unitOutput = TUSU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose DebitAmount[%lld]", TUSU.unitOutput);
			domain.dbv.push_back(dbv);
		}
	}

	//B30更新使用量
	if(base->usued)
	{
		usu usv;

		if(USU->RSUflag.durationFlag)
		{
			usv.unit = RB_UNIT_CODE_SECOND;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedUnit[%s]", RB_UNIT_STR_SECOND);

			usv.amount = (long)USU->duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedAmount[%d]", USU->duration);
			domain.usv.push_back(usv);
		}

		//金额
		if(USU->RSUflag.moneyFlag)
		{
			usv.unit = RB_UNIT_CODE_CENT;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedUnit[%s]", RB_UNIT_STR_CENT);

			usv.amount = (long)USU->money;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedAmount[%d]", USU->money);
			domain.usv.push_back(usv);
		}

		//总流量
		if(USU->RSUflag.totalunitFlag)
		{
			usv.unit = RB_UNIT_CODE_TOTAL_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedUnit[%s]", RB_UNIT_STR_TOTAL_BYTES);

			usv.amount = (long)USU->unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedAmount[%d]", USU->unitTotal);
			domain.usv.push_back(usv);
		}

		//上行流量
		if(USU->RSUflag.inputFlag)
		{
			usv.unit = RB_UNIT_CODE_UP_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedUnit[%s]", RB_UNIT_STR_UP_BYTES);

			usv.amount = (long)USU->unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedAmount[%s]", value);
			domain.usv.push_back(usv);
		}

		//下行流量
		if(USU->RSUflag.outputFlag)
		{
			usv.unit = RB_UNIT_CODE_DOWN_BYTES;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedUnit[%s]", RB_UNIT_STR_DOWN_BYTES);

			usv.amount = (long)USU->unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose UsedAmount[%d]", USU->unitOutput);
		}

	}

	rbr.accumlator_info 	= 1;		//B06	累积量信息
	rbr.tariff_info 		= 1;		//B07	资费信息
	rbr.balance_info 		= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 		= 1;		//B20	费率信息查询命令
	rbr.balance_query 		= 1;		//B21	余额查询命令
	// pREMsg->set(RB_CODE_B_RELEASE, "1");				//B02	释放预占
	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;
	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		bizMsg->data = (char*)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	return RET_SUCCESS;
}

