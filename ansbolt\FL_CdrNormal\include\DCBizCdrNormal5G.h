/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdrNormal5G.h
*Indentifier：
*
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*
*History:
********************************************/
#ifndef _DCBIZ_CDR_NORMAL_5G_H_
#define _DCBIZ_CDR_NORMAL_5G_H_

#include "DCBizCdrNormal.h"
#include "TCDRDict.h"


#define RE_5GUSU_MAXSIZE 100

class DCBizCdrNormal5G:public DCBizCdrNormal
{
	public:

		DCBizCdrNormal5G();
		~DCBizCdrNormal5G();

	protected:


		virtual int Compose5G(STBizMsg* bizMsg);
		void TimestampToDate(time_t timestamp, char *pszDate);
		int Compose5GReqCdrMsg(STBizMsg* bizMsg, int iRG, int iUseq, char *szCdrMsg);
		int ComposeFilterCDR(STBizMsg* bizMsg);
		int ComposeAllCdrMsg(STBizMsg* bizMsg);
};

#endif


