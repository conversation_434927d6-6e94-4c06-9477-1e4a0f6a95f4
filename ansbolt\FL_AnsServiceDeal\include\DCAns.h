/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAns.h
*Indentifier：
*
*Description：
*		应答消息处理
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_H__
#define __DC_ANS_H__

#include "DCBizMsgDef.h"
#include "BizDataDef.h"
#include "DCOcpMsgDef.h"
#include "DCSeriaOp.h"
#include "UStaMsg.h"
#include <string>
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "DCAnsPara.h"
#include "func_sqlindex.h"
#include "DCRbMsgDef.h"
#include "DCCommonIF.h"
#include "DCMqProduceServer.h"
#include "UHead.h"
#include <sys/time.h>
#include <stdio.h>
#include "DCCdrIndex.h"
#include <map>
#include <list>
using namespace std;
using namespace ocs;
class DCAns
{
	public:

		DCAns();
		virtual ~DCAns();

		virtual int Work(void *data);

		int ModifyREAMsg(STBizMsg* bizMsg, SREAInfo &REAMsg, int seq = 0);

		int DelSession(STBizMsg* bizMsg);

		/*******************************************************************************
		@Description: 对RBA B08组余额信息按帐本类型累加
		@Input:
			pBuf:原先保存的字段
			bizMsg:公共部分
		@Output:
		@Return: 0 成功
		*******************************************************************************/
		int ModifyBalanceInfo(STBizMsg* bizMsg, char* pBuf, int seq = 0);

		/*******************************************************************************
		@Description: 对RBA B08组余额信息按帐本类型累加
		@Input:
			pBuf:原先保存的字段
			bizMsg:公共部分
		@Output:
		@Return: 0 成功
		*******************************************************************************/
		int ModifyBalanceInfo2(STBizMsg* bizMsg, char* pBuf, int seq = 0);
		/*******************************************************************************
		@Description: 对RBA B07组资费信息按销售品IP及帐本类型累加
		@Input:
			pBuf:原先保存的字段
			bizMsg:公共部分
		@Output:
		@Return: 0 成功
		*******************************************************************************/
		int ModifyTariffIdInfo(STBizMsg* bizMsg, char* pBuf, int iBufCapacity, int seq = 0);


		/*******************************************************************************
		@Description: 对RBA B06组累积量按帐本类型累加
		@Input:
			pBuf:原先保存的字段
			bizMsg:公共部分
		@Output:
		@Return: 0 成功
		*******************************************************************************/
		int ModifyAccumlatorInfo(STBizMsg* bizMsg, char* pBuf, int iBufCapacity, int seq = 0);


		/********************************************************************************
		@Description: 对RBA B21组余额信息累加
		@Input:
		@Output:
		@Return:返回余额总额
		*******************************************************************************/
		int AccumlateBalance(STBizMsg* bizMsg);



		/********************************************************************************
		@Description: 对RBA B01组预占结果
		@Input:
		@Output:
		@Return:返回预占结果
		*******************************************************************************/
		int PreChargeInfo(STBizMsg* bizMsg,int &amount);

		/*******************************************************************************
		@Description: 对RBA B16组一批信息按照一批账目类型累计分组
		@Input:
			pBuf:原先保存的字段
			bizMsg:公共部分
		@Output:
		@Return: 0 成功
		*******************************************************************************/
		int ModifyOriChargeInfo(STBizMsg* bizMsg, char* pBuf, int seq = 0);


		int ModifyChargeInfoRefund(STBizMsg* bizMsg, char* pBuf,int *pInt);

		int ModifyChargeInfo(STBizMsg* bizMsg, char* pBuf,  int *pInt, int seq = 0);

		int ModifyPricinglanID(STBizMsg* bizMsg, char* pBuf);

		int EraseZeroItem(STBizMsg* bizMsg);

		int GetCostInfo(STBizMsg* bizMsg, int& cost_unit, int& cost_value);

		//int ProduceCCA(STBizMsg* bizMsg,ocs::SCCAMsg &ccamsg,const char *topic);
		int ComposeRMQMsg(STBizMsg* bizMsg,ocs::SCCAMsg &ccamsg,const char *topic,string &msg);
        int ComposeRMQMsg(STBizMsg* bizMsg,ocs::SCCA5G &cca5g,const char *topic,string &msg);
		
		int MergeReaMsg(STBizMsg* bizMsg,const char * split,char *merge);
		int MergeReaMsgActv(STBizMsg* bizMsg,const char * split,char *merge);

		int MergeReaMsgPri(STBizMsg* bizMsg,const char * split,char *merge);
		int MergeReaMsgTrv(STBizMsg* bizMsg,const char * split,char *merge);
		int UpsetRatingMsg(STBizMsg* bizMsg);
		void DealAccunumlation(vector<string> &vAccum,STBizMsg* bizMsg,vector<long> &v_key);
		void DealRatableInfo(vector<string> &vRata,STBizMsg* bizMsg,vector<long> &v_key);
		void DealSumInfo(vector<string> &vSum,STBizMsg* bizMsg,vector<long> &v_key);
		void UpsetMsg(STBizMsg* bizMsg,string &value,long key,bool updateFlag,int type);
		void ParserString(string str, vector<string> &vKey, char key);
		int SplitStr(const char *pszStr,const char cSplit,std::vector<int>& ltStr);
		int SplitString(const char* pszStr, const char cSeparator, std::vector<std::string>& vecStr);

	private:
		DCSeriaEncoder m_en;

};

#endif

