#include "DCAnsVOICE.h"
#include "DCLogMacro.h"
#include <sys/time.h>
#include "ErrorCode.h"

DCAnsVOICE::DCAnsVOICE()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "", "");
}

DCAnsVOICE::~DCAnsVOICE()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "", "");
}

int DCAnsVOICE::Work(void *data)
{
	int ret 			= 0;
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "null msg");
		return -1;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;
	
	//cvar->m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));
	
	//PERF LOG
	//bizMsg->m_perf.GetTimeT2_B();
	if(5==bizMsg->m_requestType)
	{	
		ret = XdrEvent(bizMsg);
	}
	else
	{
		ret = ComposeCCA(bizMsg);
	}

	//bizMsg->m_perf.GetTimeT2_E();
	if(RET_CDR == ret || RET_SUCCESS == ret)
	{	

	}
	else
	{	
		bizMsg->m_resultcode= ret;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
		return ret;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
	return ret;
}

int DCAnsVOICE::ComposeCCA(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}
int DCAnsVOICE::XdrEvent(STBizMsg * bizMsg)
{
	return RET_SUCCESS;
}