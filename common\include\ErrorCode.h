/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                   技术平台项目组
*All rights reserved.
*
*Filename：
*       ErrorCode.h
*Indentifier：
*       
*Description：
*       系统 及业务错误码定义
*Version：
*       V1.0
*Author:
*     
*Finished：
*      
*History:
*     
********************************************/

#ifndef _ERROR_CODE_H_
#define _ERROR_CODE_H_

#define RET_SUCCESS				            0	
#define RET_ERROR                           -1
#define RET_CDR                             1
#define RET_AOC                            2
#define RET_OVER				           3	
#define RET_CONTINUE				       4	
#define OFF_LINE                           5
#define RET_NOT_NEED_ANS                   6
#define RET_ALL_CDR                        7

#define SM_OCP_NET_OFFLINE                  300100
#define SM_OCP_XDR_ROLL_USER                300101
#define SM_OCP_ROLL_INTERCEPT				300102 //回退拦截
#define SM_OPC_ROLL_USU						300103 //数据业务回退拦截usu
#define SM_ALL_MSCC_FILTER                  300104 //所有RG均被过滤
#define SM_5G_RG_PAR_SM_FILTER_CONDITION    300105 //rg过滤规则RG均被过滤
#define SM_OCP_UNABLE_TO_DELIVER			300200	//3002
#define SM_OCP_REALM_NOT_SERVED				300300	//3003
#define SM_OCP_TOO_BUSY						300400	//3004
#define SM_OCP_LOOP_DETECTED					300500	//3005
#define SM_OCP_REDIRECT_INDICATION			300600	//3006
#define SM_OCP_APPLICATION_UNSUPPORTED		300700	//3007
#define SM_OCP_INVALID_HDR_BITS				300800	//3008
#define SM_OCP_INVALID_AVP_BITS				300900	//3009
#define SM_OCP_UNKNOWN_PEER					301000	//3010
#define SM_OCP_AUTHENTICATION_REJECTED		400100	//4001
#define SM_ELECTION_LOST						400300	//4003
#define SM_OCP_END_USER_SERVICE_DENIED		401000	//4010
#define SM_OCP_CREDIT_LIMIT_REACHED			401200	//4012
#define SM_USER_NEVER_OPEN					420100	//4201
#define SM_NEVER_USED							420200	//4202
#define SM_USER_FROZEN							420300	//4203
#define SM_USER_MISSING_CLAIM					420400	//4204
#define SM_USER_LOCKED							420500	//4205
#define SM_USER_STOPPED_OUT					420600	//4206
#define SM_USER_EXPIRED						420700	//4207
#define SM_VOICE_SERVICE_STOPED				420803	//4208
#define SM_USER_INVALID						420900	//4209
#define SM_BLACK_LIST							421000	//4210
#define SM_NOT_PPS								421100	//4211
#define SM_BALANCE_IS_ZERO						421200	//4212
#define SM_NO_INDIVIDUAL_PAYMENT				421400	//4214
#define SM_MEMBER_ACCOUNT_FROZEN			421600	//4216
#define SM_MEMBER_MONTHLY_LIMIT				421800	//4218
#define SM_CALLED_MEMBER_FROZEN				422000	//4220
#define SM_CALLED_MEMBER_MONTHLY_LIMIT		422100	//4221
#define SM_USER_SINGLE_EXPIRED				490000	//4900

#define SM_OCP_AVP_UNSUPPORTED				500100	//5001
#define SM_OCP_UNKNOWN_SESSION_ID			500200	//5002
#define SM_OCP_AUTHORIZATION_REJECTED		500300	//5003
#define SM_OCP_INVALID_AVP_VALUE				500400	//5004
#define SM_OCP_INVALID_AVP_VALUE_USELESS_CCR	500401	//5004
#define SM_INVALID_COUNTRY_CODE				500410	//5004
#define SM_INVALID_MOBILE_NUMBER				500411	//5004
#define SM_CSPID_NOT_FOUND					500412	//5004
#define SM_INVALID_INPUT_NUMBER				500413	//5004
#define SM_NUMBER_LENGTH_LIMIT				500414	//5004
#define SM_NUMBER_NOT_DEFINED				500415	//5004
#define SM_NOT_INCLUDE_AREACODE				500416	//5004
#define SM_NUMBER_NON_NUMERIC				500417	//5004
#define SM_NUMBER_PROFIX_NOT_DEFINED		500418	//5004
#define SM_MOBILE_NUMBER_NOT_DEFINED		500419	//5004
#define SM_OCP_MISSING_AVP					500500	//5005
#define SM_OCP_MISSING_AVP_USELESS_CCR		500501	//5005
#define SM_OCP_NULL_MSG						500502	//5005
#define SM_OCP_RESOURCES_EXCEEDED			500600	//5006
#define SM_OCP_CONTRADICTING_AVPS			500700	//5007
#define SM_OCP_AVP_NOT_ALLOWED				500800	//5008
#define SM_OCP_AVP_OCCURS_TOO_MANY_TIMES	500900	//5009
#define SM_OCP_NO_COMMON_APPLICATION		501000	//5010
#define SM_OCP_UNSUPPORTED_VERSION			501100	//5011
#define SM_OCP_UNABLE_TO_COMPLY				501200	//5012
#define SM_OCP_INVALID_BIT_IN_HEADER			501300	//5013
#define SM_OCP_INVALID_AVP_LENGTH			501400	//5014
#define SM_OCP_INVALID_MESSAGE_LENGTH		501500	//5015
#define SM_OCP_INVALID_AVP_BIT_COMBO			501600	//5016
#define SM_OCP_NO_COMMON_SECURITY			501700	//5017
#define SM_OCP_USU_OVERLOAD			             501800	//5018
#define SM_OCP_USER_UNKNOWN					503000	//5030
#define SM_OCP_RATING_FAILED					503100	//5031

#define SM_OCP_REQNUM_REPEATED              504100

#define RB_SM_UNABLE_TO_COMPLY				-5005	//5012
#define RB_RBA_MSG_ERROR						-5004	//5012
#define RB_RBA_TIMEOUT							-5003	//5012
#define RB_USELESS								-5002	//5012
#define RB_TOOBUSY								-20000	//3004

#define NE_USELESS								-7000
#define NE_SESSION_TIMEOUT						-5006	//5012

#define RB_MSG_000_ERROR						600000	//5031
#define RB_MSG_001_ERROR						600001	//5031
#define RB_MSG_003_ERROR						600003	//5031
#define RB_MSG_100_ERROR						600100	//5031
#define RB_MSG_101_ERROR						600101	//5031
#define RB_MSG_R601_ERROR						601601	//5031
#define RB_MSG_B03_ERROR						602003	//5031
#define RB_MSG_B04_ERROR						602004	//5031
#define RB_MSG_B06_ERROR						602006	//5031
#define RB_MSG_B20_ERROR						602020	//5031
#define RB_MSG_B21_ERROR						602021	//5031

#define ERR_DECODE_CORE						-20002
#define ERR_ENCODE_CODE						-20003

#endif
