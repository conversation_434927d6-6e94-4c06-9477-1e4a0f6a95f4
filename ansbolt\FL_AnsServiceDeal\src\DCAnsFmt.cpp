#include "DCAnsFmt.h"
#include "DCAnsPara.h"
#include "ErrorCode.h"
#include <sys/time.h>
#include "DCRbMsgDef.h"
#include "DCLogMacro.h"
#include "DCBizMsgDef.h"
#include "REMsgTypeDef.h"
#include "DCCommonIF.h"
#include "TConfig.h"

using namespace ocs;

DCAnsFmt::DCAnsFmt()
{
	m_nRevErrNum = 0;
	DCBIZLOG(DCLOG_LEVEL_TRACE, SM_OTHER_TYPE,"",  "DCAnsFmt");
}

DCAnsFmt::~DCAnsFmt()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, SM_OTHER_TYPE,"",  "~DCAnsFmt");
}

int DCAnsFmt::Work(void *data)
{
	if (NULL == data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE,  "", "null msg", "");
		return -1;
	}

	int ret = RET_SUCCESS;
	DCOBJSet* pset = (DCOBJSet*)data;
	STBizMsg* bizMsg = pset->get<STBizMsg>();

	//PERF LOG
	//bizMsg->m_perf.GetTimeT1_B();

	ret = FormatCommon(pset);

	//PERF LOG
	//bizMsg->m_perf.GetTimeT1_E();


	if(RET_SUCCESS == ret)
	{

	}
	else
	{
		//bizMsg->m_resultcode = ret;
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "RET[%d]", ret);
		return ret;
	}

	return ret;
}

int DCAnsFmt::FormatCommon(DCOBJSet* pset)
{
	char value[256] = {0};

	STBizMsg* bizMsg = pset->get<STBizMsg>();
	rbhead *head = pset->get<rbhead>();
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	rbresult* base = (rbresult*)bizMsg->m_base;
	rbext* ext = (rbext*)bizMsg->m_extend;
	bizMsg->m_serial = head->serial; 	//序列号
	sprintf(bizMsg->m_eventType,"%ld",base->evt_id);
	// 离线请求类型置为5
	if (1 == head->version)
	{
		strncpy(bizMsg->m_xdrsource,ext->kv["source"].c_str(),sizeof(bizMsg->m_xdrsource));
		if(head->type == RE_SERVICE_TYPE_INT_SMS_ANS || head->type == RE_SERVICE_TYPE_INT_ISMP_ANS)
		{
		    bizMsg->m_requestType = head->sreq;
		}
		else
		{
		    bizMsg->m_requestType = 5;
		}
        bizMsg->m_version = 1;
        sprintf(bizMsg->m_sessionID,"XDR%s",head->session.c_str());
	}
	else
	{
		bizMsg->m_version = 2;
		bizMsg->m_requestType = head->sreq;
		sprintf(bizMsg->m_sessionID,"%s",head->session.c_str());
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "ans [requesttype=%d]", bizMsg->m_requestType);

	bizMsg->m_sourceId = ext->kv["sourceId"];
	bizMsg->m_CreditCtlFlag = ext->kv["CreditCtlFlag"];
	bizMsg->m_ilatnId = atoi(ext->kv["LatnId"].c_str());

	char stmp[6] = {0};
	sprintf(stmp, "%d", bizMsg->m_ilatnId);
	bizMsg->m_pSendMsg->insert(pair<string,string>("LatnId",stmp));
	
	sprintf(bizMsg->m_childsessionID,"%s",bizMsg->m_sessionID);
	if(atoi(ext->kv["spiltflag"].c_str()) > 1)
	{
		sprintf(value,";00%s",ext->kv["spiltflag"].c_str());
		strcat(bizMsg->m_childsessionID,value);
	}
	bizMsg->m_spiltflag = atoi(ext->kv["spiltflag"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "ans [SESSION=%s] [CHILDSESSION=%s]", bizMsg->m_sessionID,bizMsg->m_childsessionID);

	bizMsg->m_trace_flag = head->trace;
	if(bizMsg->m_trace_flag <0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "m_trace_flag,set 0[%d]", bizMsg->m_trace_flag);
		bizMsg->m_trace_flag = 0;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "trace number onff[%d]", bizMsg->m_trace_flag);
	strcpy(bizMsg->m_szServiceContextIDStr,ext->kv["strID"].c_str());


	if(base->test_info == 1)
	{
		bizMsg->m_testFlag = 1;
		DCBIZLOG(DCLOG_LEVEL_TRACE, SERVTYPE(bizMsg->m_serviceContextID), bizMsg->m_sessionID, "testflag[%d]", bizMsg->m_testFlag);
	}

	//业务类型区分
	switch(head->type)
	{
		case RE_SERVICE_TYPE_INT_VOICE_ANS:
			{
				bizMsg->m_serviceContextID = VOICE;
			}
			break;
		case RE_SERVICE_TYPE_INT_SMS_ANS:
			{
				bizMsg->m_serviceContextID = SMS;
			}
			break;
		case RE_SERVICE_TYPE_INT_DATA_ANS:
			{
				bizMsg->m_serviceContextID = DATA;
			}
			break;
		case RE_SERVICE_TYPE_INT_PGW_ANS:
			{
				bizMsg->m_serviceContextID = PGW;
			}
            break;
        case RE_SERVICE_TYPE_INT_5G_ANS:
			{
				bizMsg->m_serviceContextID = DATA_5G;
			}
			break;
		case RE_SERVICE_TYPE_INT_ISMP_ANS:
			{
				bizMsg->m_serviceContextID = ISMP;
			}
			break;
		case RE_SERVICE_TYPE_INT_BALANCE_ANS_ISMP:
		case RE_SERVICE_TYPE_INT_RATA_ANS_ISMP:
			{
				bizMsg->m_serviceContextID = ISMP;
				bizMsg->m_requestType = SM_SESSION_EVENT_BALANCE_CODE;
			}
			break;
		case RE_SERVICE_TYPE_INT_DSL_ANS:
			{
				bizMsg->m_serviceContextID = DSL;
			}
			break;
		case RE_SERVICE_TYPE_INT_BALANCE_ANS:
			{
				bizMsg->m_serviceContextID  = VOICE;
				bizMsg->m_requestType = SM_SESSION_EVENT_BALANCE_CODE;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR,  "", "reject msg:invalid msg type value[%d]", head->type);
				return RB_RBA_MSG_ERROR;
			}

	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "serviceContextID[%u]", bizMsg->m_serviceContextID);


	//SR CCA消息中Service-Flow-Id
	memset(bizMsg->m_ServiceFlowID,0x00,sizeof(bizMsg->m_ServiceFlowID));
	DCCommonIF::GetServiceFlowID(bizMsg->m_ServiceFlowID);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "service flow id[%s]", bizMsg->m_ServiceFlowID);

	bizMsg->m_resultcode = head->result;
	DCBIZLOG(DCLOG_LEVEL_TRACE, SERVTYPE(bizMsg->m_serviceContextID), "", "ans [RESULT=%d]", head->result);


	//数据业务异常流程不走特殊处理
	if((bizMsg->m_serviceContextID != DATA) &&( bizMsg->m_serviceContextID != CCG) && (bizMsg->m_serviceContextID != PGW))
	{
		if(bizMsg->m_resultcode<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "REA result error[%d]",bizMsg->m_resultcode);
			return bizMsg->m_resultcode;
		}
		else if(bizMsg->m_requestType != SM_SESSION_XDR_CODE)
		{
			int ocs_result = smpara->GetOCPResultCode(bizMsg->m_resultcode);
			DCBIZLOG(DCLOG_LEVEL_TRACE, SERVTYPE(bizMsg->m_serviceContextID), "", "ans [ocs_result=%d]", ocs_result);

			if(ocs_result == -1)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "reject msg:invalid resultcode [%d]",ocs_result);
				return ocs_result;
			}
			bizMsg->m_resultcode = ocs_result;
		}
	}
	else
	{
		//判断预扣结果是否成功
		int ocs_result = bizMsg->m_resultcode;
		if(bizMsg->m_requestType != SM_SESSION_XDR_CODE)
		{
			bizMsg->m_resultcode = smpara->GetOCPResultCode(ocs_result);
			DCBIZLOG(DCLOG_LEVEL_TRACE, SERVTYPE(bizMsg->m_serviceContextID), "", "after mapped [RESULT=%d]",bizMsg->m_resultcode);
		}
		if(bizMsg->m_resultcode == -1)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID),  "", "reject msg:invalid resultcode =[%d]",ocs_result);
			bizMsg->m_resultcode = 3004;
			return RB_RBA_MSG_ERROR;
		}
		if(ocs_result<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "RBA message error,result code[%d]", ocs_result);
			return ocs_result;
		}

	}
	return RET_SUCCESS;
}

