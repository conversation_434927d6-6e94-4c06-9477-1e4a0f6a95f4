#include <string.h>
#include "TCDRDict.h"
#include "tinyxml.h"
#include "DCLogMacro.h"
#include "BizDataDef.h"


TCDRDict* TCDRDict::m_instance = NULL;

TCDRDict::TCDRDict()
{
}

TCDRDict::~TCDRDict()
{
}

TCDRDict* TCDRDict::instance()
{
	if(!m_instance)
	{
		m_instance = new TCDRDict();
	}

	return m_instance;
}

int TCDRDict::Destroy()
{
	delete this;
	return 0;
}

int TCDRDict::Load(const char* path)
{
	TiXmlDocument dict(path);
	if(!dict.LoadFile())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load cdr dict error[%s]", path);
		return -1;
	}
	TiXmlHandle pHandle(&dict);


	TiXmlElement * element1 = NULL;
	TiXmlElement * element2 = NULL;
	TiXmlElement * element3 = NULL;
	SCDRField* field;
	SCDRField* fieldChild5G;
	SCDRField* fieldMain5G;

	element1 = pHandle.FirstChildElement("cdrdict").Element();
	if(element1 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load cdrdict error", "");
		return -1;
	}

	//IN字段
	element2 = element1->FirstChildElement("business");
	if(element2 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load business error", "");
		return -1;
	}
	element3 = element2->FirstChildElement("field");
	if(element3 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load field error", "");
		return -1;
	}

	while(element3)
	{
		field = new SCDRField;
		if(element3->Attribute("name"))
		{
			field->flag = 1;
			strcpy(field->value, element3->Attribute("name"));
		}
		else
		{
			field->flag = 0;
			strcpy(field->value, element3->Attribute("default"));
		}

		m_INField.push_back(field);
		field = NULL;

		element3 = element3->NextSiblingElement();
	}


	//P2PSMS字段
	element2 = element2->NextSiblingElement();
	if(element2 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load business error", "");
		return -1;
	}
	element3 = element2->FirstChildElement("field");
	if(element3 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load field error", "");
		return -1;
	}
	while(element3)
	{
		field = new SCDRField;
		if(element3->Attribute("name"))
		{
			field->flag = 1;
			strcpy(field->value, element3->Attribute("name"));
		}
		else
		{
			field->flag = 0;
			strcpy(field->value, element3->Attribute("default"));
		}

		m_P2PSMSField.push_back(field);
		field = NULL;

		element3 = element3->NextSiblingElement();
	}


	//PS字段
	element2 = element2->NextSiblingElement();
	if(element2 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load business error", "");
		return -1;
	}
	element3 = element2->FirstChildElement("field");
	if(element3 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load field error", "");
		return -1;
	}
	while(element3)
	{
		field = new SCDRField;
		if(element3->Attribute("name"))
		{
			field->flag = 1;
			strcpy(field->value, element3->Attribute("name"));
		}
		else
		{
			field->flag = 0;
			strcpy(field->value, element3->Attribute("default"));
		}

		m_DATAField.push_back(field);
		field = NULL;

		element3 = element3->NextSiblingElement();
	}


	//ISMP字段
	element2 = element2->NextSiblingElement();
	if(element2 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load business error", "");
		return -1;
	}
	element3 = element2->FirstChildElement("field");
	if(element3 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load field error", "");
		return -1;
	}
	while(element3)
	{
		field = new SCDRField;
		if(element3->Attribute("name"))
		{
			field->flag = 1;
			strcpy(field->value, element3->Attribute("name"));
		}
		else
		{
			field->flag = 0;
			strcpy(field->value, element3->Attribute("default"));
		}

		m_ISMPField.push_back(field);
		field = NULL;

		element3 = element3->NextSiblingElement();
	}


	//DSL字段
	element2 = element2->NextSiblingElement();
	if(element2 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load business error", "");
		return -1;
	}
	element3 = element2->FirstChildElement("field");
	if(element3 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load field error", "");
		return -1;
	}
	while(element3)
	{
		field = new SCDRField;
		if(element3->Attribute("name"))
		{
			field->flag = 1;
			strcpy(field->value, element3->Attribute("name"));
		}
		else
		{
			field->flag = 0;
			strcpy(field->value, element3->Attribute("default"));
		}

		m_DSLField.push_back(field);
		field = NULL;

		element3 = element3->NextSiblingElement();
	}

    //5G 字段
    element2 = element2->NextSiblingElement();
    if(element2 == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load business error", "");
        return -1;
    }
    element3 = element2->FirstChildElement("field");
    if(element3 == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "load field error", "");
        return -1;
    }

    while(element3)
    {
        field = new SCDRField;
		fieldChild5G = new SCDRField;
		fieldMain5G = new SCDRField;
		
		if(element3->Attribute("tablecode") && strlen(element3->Attribute("tablecode")))
		{
			if(!strcmp(element3->Attribute("tablecode"), "CHILD"))
	        {
	        	fieldChild5G->flag = 1;
				if(element3->Attribute("name"))
				{
					strcpy(fieldChild5G->value, element3->Attribute("name"));
					m_DATA_5GField.push_back(fieldChild5G);
				}
	            else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "ERROR: tablecode = CHILD, TYPE is not name");
					return -1;
				}
	        }
			else if(!strcmp(element3->Attribute("tablecode"), "MAIN"))
			{
				fieldMain5G->flag = 1;
				if(element3->Attribute("name"))
				{
					strcpy(fieldMain5G->value, element3->Attribute("name"));
					m_DATA_5GField.push_back(fieldMain5G);
				} 
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "ERROR: tablecode = MAIN, TYPE is not name");
					return -1;
				}    
			}
		}
        else
        {
        	if(element3->Attribute("name"))
		    {
		        field->flag = 1;
		        strcpy(field->value, element3->Attribute("name"));
		    }
			else
			{
	            field->flag = 0;
	            strcpy(field->value, element3->Attribute("default"));
			}
			m_DATA_5GField.push_back(field);
        }
        field = NULL;
		fieldChild5G = NULL;
		fieldMain5G = NULL;
        element3 = element3->NextSiblingElement();
    }
/*
	//»ñÈ¡ismpHB ×Ö¶Î
	element2 = element2->NextSiblingElement();
	if(element2 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "cannot load ismpHB ,ignored", "");
		return -1;
	}
	element3 = element2->FirstChildElement("field");
	if(element3 == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "load field error", "");
		return -1;
	}
	while(element3)
	{
		field = new SCDRField;
		if(element3->Attribute("name"))
		{
			field->flag = 1;
			strcpy(field->value, element3->Attribute("name"));
		}
		else
		{
			field->flag = 0;
			strcpy(field->value, element3->Attribute("default"));
		}

		m_ISMPFieldHB.push_back(field);
		field = NULL;

		element3 = element3->NextSiblingElement();
	}
	*/
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "load field successful", "");

	return 0;
}

vector<SCDRField*>* TCDRDict::GetINField()
{
	return &m_INField;
}

vector<SCDRField*>* TCDRDict::GetP2PSMSField()
{
	return &m_P2PSMSField;
}

vector<SCDRField*>* TCDRDict::GetDATAField()
{
	return &m_DATAField;
}

vector<SCDRField*>* TCDRDict::GetISMPField()
{
	return &m_ISMPField;
}

vector<SCDRField*>* TCDRDict::GetISMPFieldHB()
{
	return &m_ISMPFieldHB;
}
vector<SCDRField*>* TCDRDict::GetDSLField()
{
	return &m_DSLField;
}
vector<SCDRField*>* TCDRDict::GetDATA5GField()
{
    return &m_DATA_5GField;
}

