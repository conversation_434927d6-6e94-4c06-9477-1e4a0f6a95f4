/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCUserManaFlow.h
*Indentifier：
*
*Description：
*		用户管理类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_USERMANAFLOW_H__
#define __DC_USERMANAFLOW_H__
#include "DCBasePlugin.h"

class DCUserManaFlow : public DCBasePlugin
{
	public:	
		DCUserManaFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)         :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCUserManaFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		virtual const char* desc();		
		
};
#endif

