#ifndef __CDR_H__
#define __CDR_H__
#include <stdio.h>
#include <string>
#include <map>
using namespace std;
//连接队列时，队列不存在创建
#define QUEUE_OPEN_TYPE IPC_CREAT|0600
#define SM_CDR_MSG_LEN_MAX		3072

#define SM_CDR_FLAG_NORMAL		0
//#define SM_CDR_FLAG_ABNORMAL		1
//#define SM_CDR_FLAG_REFUND		2
//#define SM_CDR_FLAG_ERROR			3
#define SM_CDR_FLAG_OFFLINE		1
#define SM_CDR_TYPE_IN				100
#define SM_CDR_TYPE_PS				200
#define SM_CDR_TYPE_SMS			300
#define SM_CDR_TYPE_ISMP			400
#define SM_CDR_TYPE_DSL			500
//#define SM_CDR_TYPE_ISMP_HB			600 //出一份清单、按照HB规范
#define SM_CDR_TYPE_HRS			600		//预 付费用户
#define SM_CDR_TYPE_HRS_AFTER			701		//后付费用户
#define SM_CDR_TYPE_IMS			800		//IMS
#define SM_CDR_TYPE_5G			900

#define SM_CFG_CDR_QUEUE_IN			"IN"
#define SM_CFG_CDR_QUEUE_SMS			"SMS"
#define SM_CFG_CDR_QUEUE_ISMP			"ISMP"
#define SM_CFG_CDR_QUEUE_PS			"PS"
#define SM_CFG_CDR_QUEUE_DSL			"DSL"
//配置文件中的元素名
#define SM_CDR_STATUS_FILE			".cdr_status"

#define SM_CDR_SERVICE_MAX		6
#define SM_CDR_TYPE_MAX			2
#define SM_CDR_PATH_LEN			256
#define SM_CDR_NAME_LEN			128
#define SM_CDR_LEN_POSTFIX			6
#define SM_CDR_DIR_POPEDOM				0755
#define SM_SLEEP_TIME_SEC			999999


#define SM_SUBDIR_LEN_MAX			"30"

#define SM_THREAD_COUNT 6

#define SM_CDR_NAME_SERIAL		0

#define SM_CDR_PATH_TYPE_ISMPUPLOAD			5	//HB规范清单目录

#define SM_CDR_PATH_TYPE_HRS		6

#define SM_CDR_ISMP_HB_NAME			"IISMP"		//文件名标识,如IISMP_20100920_0001


struct SCDRFileInfo
{
	char			sign[16];					//业务标识100  101  102
	unsigned int	serial;					//序列号
	unsigned int	size;					//文件大小
	unsigned int	time;					//文件创建时间
	unsigned int    type;
	unsigned int	unused;

	FILE*		fp;
	char 		name[SM_CDR_NAME_LEN];			//文件名
	SCDRFileInfo()
	{
		sign[0] = 0;
		serial = 0;
		size = 0;
		time = 0;
		unused = 0;
		fp = NULL;
		type = 0;
		name[0] = 0;
	}
};

typedef struct
{
	map<string,SCDRFileInfo> file;		//业务标识分类
}SCDRTypeInfo;

struct SCDRInfo
{
	long				key;							//消息队列Key
	unsigned int		id;								//消息队列ID
	unsigned int		onff:8;							//话单开关
	unsigned int		flag:8;							//备份标志
	unsigned int		service:16;						//业务类型
	unsigned int		last;							//(备份，当前)时间
	SCDRTypeInfo		cdr[SM_CDR_TYPE_MAX];
	SCDRInfo()
	{
		key= 0;
		id = 0;
		onff = 0;
		flag = 0;
		service = 0;
		last = 0;
	}
};

typedef struct
{
	SCDRInfo info[SM_CDR_SERVICE_MAX];					//按业务分类信息
}SCDRRootInfo;

//话单头
struct SCDRHead
{
	unsigned int flag:16;              //正常话单0  错单 1
	unsigned int sign:16;				 //业务标识
	unsigned int unused:32;
	SCDRHead()
	{
		flag= 0;
		sign = 0;
		unused = 0;
	}
};

struct SCDRData
{
	char m_body[SM_CDR_MSG_LEN_MAX];
};

#endif

