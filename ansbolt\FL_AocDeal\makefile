include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)
COMMON_INC=$(LBSPUBROOT)/SM/common/include

ANSSERVICE_INC=$(PWD)/include
ANSSERVICE_SRC=$(PWD)/src
ANSSERVICE_OBJ=$(PWD)/obj

ANSSERVICE_CPP=DCAocDealFlow.cpp DCAocBase.cpp desc_aocdeal.cpp
           
ANSSERVICE_SRCS=$(addprefix $(ANSSERVICE_SRC)/, $(ANSSERVICE_CPP))
ANSSERVICE_OBJS=$(patsubst $(ANSSERVICE_SRC)/%.cpp, $(ANSSERVICE_OBJ)/%.o, $(ANSSERVICE_SRCS))

CFLAGS += -std=c++11

TLIB= $(RELEASE_PATH)/plugin/libAocDeal.so

INCLUDE =-I$(ANSSERVICE_INC) \
				 -I$(COMMON_INC) \
				 -I$(ITF)/include \
         -I$(AVRO)/include \
         -I$(DCLOGCLI)/include \
         -I$(DFM_INC_PATH) \
		 -I$(TXML)/include \
         -I$(MQ)/include

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(MQ)/lib -L$(LBSPUBROOT)/release/realbilling/lib -L$(TXML)/lib
LIBSLIST= -lCommonIF -lrocketmq64 -ltinyxml

tmpvar:=$(call CreateDir, $(ANSSERVICE_OBJ))
.PHONY: all clean dup
all:$(TLIB)	
$(TLIB): $(ANSSERVICE_OBJS)
	@echo "build libAocDeal.so----"
	$(CC) $(DFLAGS)  -o $(TLIB) $(ANSSERVICE_OBJS) $(LIBPATH) $(LIBSLIST)
$(ANSSERVICE_OBJS):$(ANSSERVICE_OBJ)/%.o:$(ANSSERVICE_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	
$(ANSSERVICE_SRC)/desc_aocdeal.cpp:$(ANSSERVICE_SRC)/desc_aocdeal.clog
	$(TOOL)/clogtool -i $< -o $@
clean:
	@rm -rf $(ANSSERVICE_OBJS) $(TLIB)
       
dup:
	@cp -pf $(TLIB) $(PROJECT_RPATH)/plugin/SM && echo "dup $(TLIB) to $(PROJECT_RPATH)/plugin/SM"
	
