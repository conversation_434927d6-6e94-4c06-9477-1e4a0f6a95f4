
/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCAnsBaseFlow.h
*Indentifier：
*
*Description：
*      reqbolt主类
*Version：
*       V1.0
*Author:
*       
*Finished：
*       
*History:
*     
********************************************/
#ifndef DC_REQ_BASE_FLOW_H_
#define DC_REQ_BASE_FLOW_H_

#include <stdio.h>
#include "DCBaseFlow.h"
#include "DCSeriaOp.h"
#include "DCOBJSet.h"
#include "DCBizMsgDef.h"
#include "UStaMsg.h"
//#include "DCMqProduceServer.h"


class DCAnsBaseFlow: public DCBaseFlow 
{
public:
	DCAnsBaseFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBaseFlow(category,func,version),m_de(ESeriaBinString),m_en(ESeriaBinString)
	{
	
	}
	
	virtual ~DCAnsBaseFlow()
	{
	
	}

protected:	
	virtual int init();
	virtual int process(void* input, void* output);

	virtual int call_all(void* input, void* output);

private:

    int composeStaMsg(STBizMsg* bizMsg, int nType, std::string sImportType, std::string sOnlineMod5G);
	bool checkSumFile(ocs::SComHead& pSComhead,STBizMsg* bizMsg,ocs::RatingMessageInfo_t& bodySM);

	DCOBJSetPool* m_pool;
 	DCSeriaDecoder m_de;
	//DCMqProduceServer* m_producer;
	const char *m_Topic[5];

	DCSeriaPrinter m_print;
	DCSeriaEncoder m_en;
	int m_combinaSwitch;  //普通账户是否发组合消息开关
};

#endif 

