/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsDSL.h
*Indentifier：
*
*Description：
*		DSL业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_DSL_H__
#define __DC_ANS_DSL_H__

#include "DCAns.h"
#include "DCAnsPara.h"
#include "TConfig.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"

#include "DCSeriaOp.h"

#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "DCCommonIF.h"
#include "REMsgTypeDef.h"
#include "ErrorCode.h"

class DCAnsDSL : public DCAns
{
	public:

		DCAnsDSL();
		virtual ~DCAnsDSL();

	public:

		int Work(void *data);

	protected:
		
		virtual int XdrEvent(STBizMsg* bizMsg);
		virtual int ComposeCCA(STBizMsg* bizMsg);
};

#endif
