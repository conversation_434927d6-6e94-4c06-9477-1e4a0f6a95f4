﻿#include "DCReq5G.h"
#include "BizLenDef.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "TConfig.h"
#include "DCSeriaOp.h"
#include "DCLogMacro.h"
#include "BizCdrDef.h"
#include "func_sqlindex.h"
#include "DCRbMsgDef.h"
#include "REMsgTypeDef.h"
#include "BizCdrDefTEL.h"
#include "DCCommonIF.h"
#include "UHead.h"
#include <time.h>
#include "DCKpiSender.h"
#include "DCServEvtCheck.h"

static long time2sec(long date)
{
	//20101001231105
	time_t tsec = 0;
	if((date/10100101010101)>=1)
	{
		struct tm strtime = {0};
		strtime.tm_year = date/10000000000-1900;
		strtime.tm_mon = (date%10000000000)/100000000-1;
		strtime.tm_mday = (date%100000000)/1000000;
		strtime.tm_hour = (date%1000000)/10000;
		strtime.tm_min = (date%10000)/100;
		strtime.tm_sec = date%100;

		tsec = mktime(&strtime);
	}
	else
	{
		;
	}
	return tsec;
}

DCReq5G::DCReq5G()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

DCReq5G::~DCReq5G()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

int DCReq5G::SwitchReqType(STBizMsg* bizMsg)
{
	if(!bizMsg->m_base)
	{
		return -1;
	}

	if (bizMsg->m_iRollFlag == 1)
	{
		DCServEvtCheck::instance()->SetCacheKey("ROLL_FLAG", "1");
	}

	int iRet = RET_SUCCESS;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	ocs::SUSU* MSCC = &(base->MSCC[0]);
	SCCR5GInfo* data =(SCCR5GInfo*)bizMsg->m_extend;
	SUserInfo* userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	TSMPara* smpara = bizMsg->m_smpara;
	DCKpiMon* ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
	char monGroup[50] = {0};
	DCKpiSender::instance()->GetFullGroup("TraceKpi", userInfo->ilatnid, monGroup);
	DCServEvtCheck::instance()->SetCacheKey("ACC_NBR", base->subscription.phone);
	DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", bizMsg->m_offline == 1 ? "SMOfR" : "SMOnR", NULL, 1);

	if (base->MSCC.size() == 0)
	{
		DCKpiSender::instance()->cycle_array_inc(ptrBPMon,monGroup, "", bizMsg->m_offline == 1 ? "SMOfA" : "SMOnA", NULL, 1);
		bizMsg->m_resultcode = 2001;
		return send5GInitCCA(bizMsg, base);
	}

	if (smpara->Get5GPara()->index_timeout_sec > 0)
	{
		if (!(bizMsg->m_offline == 1 || bizMsg->m_requestType == SM_SESSION_XDR_CODE))
		{
			m_index.SetTimeoutSec(smpara->Get5GPara()->index_timeout_sec,smpara->Get5GPara()->index_SeqTimeOut_sec);
			string strDupBeginTime = DCServEvtCheck::instance()->GetCurrentTime();
			string strSessionId = bizMsg->m_sessionID;
			if (m_index.IsDup(strSessionId, bizMsg->m_requestNumber, false))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "invocationSequenceNumber is repetition");
				DCServEvtCheck::instance()->SetCacheKey("DUP_RESULT", "2");
				DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_BEGIN_TIME", strDupBeginTime);
				DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_END_TIME", DCServEvtCheck::instance()->GetCurrentTime());
				bizMsg->m_resultcode = SM_OCP_REQNUM_REPEATED;
				iRet = send5GInitCCA(bizMsg, base);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA ok");
				DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "SMOnA", NULL, 1);
				return iRet;
			}
		}
	}
	else
	{
		DCServEvtCheck::instance()->SetCacheKey("DUP_RESULT", "1");
		DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_BEGIN_TIME", DCServEvtCheck::instance()->GetCurrentTime());
		DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_END_TIME", DCServEvtCheck::instance()->GetCurrentTime());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "dup config[%d]", smpara->Get5GPara()->index_timeout_sec);
	}

	if (bizMsg->m_offline || bizMsg->m_requestType == SM_SESSION_XDR_CODE)
	{
		if (bizMsg->m_bImsFilter)
		{
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", bizMsg->m_offline == 1 ? "SMOfA" : "SMOnA", NULL, 1);
			bizMsg->m_iCdrRet = RET_NOT_NEED_ANS;
			return RET_CDR;
		}

		iRet = OfflineAndXDR(base, data, bizMsg);
		if (iRet != RET_SUCCESS)
		{
		    DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", bizMsg->m_offline == 1 ? "SMOfA" : "SMOnA", NULL, 1);
			bizMsg->m_iCdrRet = send5GInitCCA(bizMsg, base);
		}
		else
		{ // offline消息由chfproxy拆分RG, xdr消息由采集格式化拆分RG, 所以只有单RG
			if ((smpara->Get5GPara()->index_timeout_sec > 0) && (bizMsg->m_iRollFlag != 1)) // 只对非回退的消息剔重
			{
				string sDupBeginTime = DCServEvtCheck::instance()->GetCurrentTime();
				m_index.SetTimeoutSec(smpara->Get5GPara()->index_timeout_sec,smpara->Get5GPara()->index_SeqTimeOut_sec);
		        string strSessionId = bizMsg->m_sessionID;
				bool isXdrMsg = false;
				if(bizMsg->m_requestType == SM_SESSION_XDR_CODE)
				{
					isXdrMsg = true;
					if(strSessionId.size() > 3 && strSessionId.substr(0,3) == "XDR")
					{
						strSessionId.erase(0,3);
					}
				}
				if (m_index.IsDup(strSessionId, bizMsg->m_requestNumber, isXdrMsg, MSCC->ratingGroup,atoi(base->smExt.kv["RGNUM"].c_str())))
				{ // Offline消息和XDR剔重, 应答ACK消息
					DCServEvtCheck::instance()->SetCacheKey("DUP_RESULT", "2");
					DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_BEGIN_TIME", sDupBeginTime);
					DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_END_TIME", DCServEvtCheck::instance()->GetCurrentTime());
					bizMsg->m_vectorMsg.clear();
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "invocationSequenceNumber[%d] and rg[%ld] is repetition", bizMsg->m_requestNumber, MSCC->ratingGroup);
					bizMsg->m_iCdrRet = send5GInitCCA(bizMsg, base);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send ACK ok");
					DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", bizMsg->m_offline == 1 ? "SMOfA" : "SMOnA", NULL, 1);
					bizMsg->m_iOfflineXDREptFlag = EPT_5G_DUP_MSG_CDR;
					return RET_CDR;
				}

				DCServEvtCheck::instance()->SetCacheKey("DUP_RESULT", "1");
				DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_BEGIN_TIME", sDupBeginTime);
				DCServEvtCheck::instance()->SetCacheKey("DUP_REMOVE_END_TIME", DCServEvtCheck::instance()->GetCurrentTime());
			}
		}
	}
	else
	{
		switch(bizMsg->m_requestType)
		{
			case SM_SESSION_INITIAL_CODE:
			{
				iRet = Init(base, data, bizMsg);
				break;
			}
			case SM_SESSION_UPDATE_CODE:
			{
				iRet = Update(base, data, bizMsg);
				break;
			}
			case SM_SESSION_TERMINATION_CODE:
			{
				iRet = Term(base, data, bizMsg);
				break;
			}
			default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "invalid request type[%d]", bizMsg->m_requestType);
				iRet = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
				break;
			}
		}

		if (bizMsg->m_offline == 0 && bizMsg->m_requestType != SM_SESSION_XDR_CODE && (iRet == RET_SUCCESS || iRet == RET_OVER) && (smpara->Get5GPara()->index_timeout_sec > 0))
		{
		    m_index.SetTimeoutSec(smpara->Get5GPara()->index_timeout_sec,smpara->Get5GPara()->index_SeqTimeOut_sec);
			string strSessionId = bizMsg->m_sessionID;
			//m_index.IsDup(strSessionId, bizMsg->m_requestNumber, false);
		}

		if (bizMsg->m_bImsFilter)
		{
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", bizMsg->m_offline == 1 ? "SMOfA" : "SMOnA", NULL, 1);
			bizMsg->m_iCdrRet = RET_NOT_NEED_ANS;
			return RET_CDR;
		}

	}

	return iRet;
}

int DCReq5G::send5GInitCCA(STBizMsg* bizMsg, SCCRBase* base)
{
	char serviceFlowId[48] = {0};
	ocs::UHead uhd;
	ocs::SCCA5G cca;
	uhd.checkKey = bizMsg->m_strCheckKey;
	uhd.uid = bizMsg->m_uid;
	cca.trace  = bizMsg->m_trace_flag;
	cca.serial  = bizMsg->m_serial;
	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = bizMsg->m_resultcode;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.ServiceContextID = bizMsg->m_serviceContextID;

	DCCommonIF::GetServiceFlowID(serviceFlowId);
	cca.ServiceFlowID = serviceFlowId;

	for (std::vector<ocs::SUSU>::iterator itRG = base->MSCC.begin(); itRG != base->MSCC.end(); itRG++)
	{
		ocs::AUSU gsu;
		gsu.ratinggroup	= itRG->ratingGroup;
		STDynamicStepInfo5G stDynamicStepInfo5G;
		if (bizMsg->m_smpara->Get5GPara()->iDynUseTable > 0)
		{
			//新增5G下,获取动态步长函数
			int iRet = GetDynamicStepInfo5G(bizMsg->m_dbm, bizMsg->m_userinfo->servID, bizMsg->m_userinfo->ilatnid, itRG->ratingGroup, bizMsg->timestampCCR, stDynamicStepInfo5G);
			//返回 triggers
			if (stDynamicStepInfo5G.bGetSucceed)
			{
				//5G配有动态步长数据，则取5G配的,通过triggers传递
				//时长、流量动态步长额外从dynamic_limit_inst得到
				gsu.triggers.timeLimit = stDynamicStepInfo5G.lnTimeLimit;
				gsu.triggers.volumeLimit64 = stDynamicStepInfo5G.lnVolumeLimit;
				gsu.triggers.triggerType = "1";  //有效步长
			}
		}
		else if (itRG->dynLimitInfo.timeLimit || itRG->dynLimitInfo.volumeLimit64)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "use dynlimit,time[%d],volumeLimit[%ld]",
				itRG->triggerInfoMation.timeLimit, itRG->triggerInfoMation.volumeLimit64);
			gsu.triggers.timeLimit = itRG->dynLimitInfo.timeLimit;
			gsu.triggers.volumeLimit64 = itRG->dynLimitInfo.volumeLimit64;
			gsu.triggers.triggerType = "1";
			if (gsu.triggers.timeLimit == 0 && gsu.triggers.volumeLimit64 == 0)
			{
				gsu.triggers.triggerType = "";
			}
		}
		
		if (!bizMsg->m_offline)
		{
			TSERVICE_QUOTA_CONF* conf = bizMsg->m_smpara->GetServiceQuotaConf(RATING_5G_GROUP);
			if (!conf)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "no find default RATING_5G_GROUP[-8]");
			}
			else
			{
				if (conf->DURATION)
				{
					gsu.Unit = RB_UNIT_CODE_SECOND;
					gsu.CCTime = conf->DURATION;
				}
			
				if (conf->TOTAL_OCTETS)
				{
					gsu.Unit = RB_UNIT_CODE_TOTAL_BYTES;
					gsu.Unittotal = conf->TOTAL_OCTETS;
				}

				gsu.ValidityTime = conf->VALID_TIME;
				gsu.QuotaHoldingTime = conf->QUOTA_HOLDING_TIME;
				gsu.QuotaConsumptionTime = conf->QUOTA_CONSUMPTION_TIME;
				gsu.TimeQuotaThreshold = conf->TIME_QUOTA_THRESHOLD;
				if ((conf->VOLUME_QUOTA_THRESHOLD_1 != 0) 
				&&	(gsu.Unittotal <= conf->VOLUME_QUOTA_THRESHOLD) 
				&&	(RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit))  //授权量<=授权量，并且是按流量计费的，取门限值2
				{
					gsu.VolumeQuotaThreshold = conf->VOLUME_QUOTA_THRESHOLD_1;
				}
				else
				{
					gsu.VolumeQuotaThreshold = conf->VOLUME_QUOTA_THRESHOLD;
				}
			}
		}

		gsu.ResultCode = 2001;
		cca.MUU.push_back(gsu);
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch (exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "", "encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->m_vectorMsg.clear();
	bizMsg->data = (char*)m_en.data();
	return 	RET_OVER;
}

int DCReq5G::Init(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg)
{
	int iRet = 0;
	std::vector<long> v_rg;
	for (int i = 0; i < base->MSCC.size(); i++)
	{
		v_rg.push_back(base->MSCC[i].ratingGroup);
	}

	bizMsg->m_vectorMsg.clear();
	for (int i = 0; i < base->MSCC.size(); i++)
	{
		ocs::SUSU* MSCC = &(base->MSCC[i]);
		SSessionCacheData5G cacheData5G;
		sprintf(bizMsg->m_childsessionID, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
		bizMsg->m_ratingGroup = MSCC->ratingGroup;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", bizMsg->m_childsessionID);

		bool bUpdateNoChild = false;
		iRet = composeRER(bizMsg, MSCC, cacheData5G, v_rg, bUpdateNoChild);
		if (0 != iRet)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RER failed[%d]", iRet);
			return iRet;
		}

		iRet = InsertChildSession(bizMsg, MSCC, cacheData5G);
		if (0 != iRet)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "InsertChildSession failed[%d]", iRet);
			return iRet;
		}
	}

	DCAnsPara *spara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	if (NULL == spara)
	{
		BData* base = new DCAnsPara();
		iRet= DCRFData::instance()->regist("DCAnsPara",base);
		if(iRet)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "regist ans param failed!");
			return -1;
		}
	}

	return RET_SUCCESS;
}

int DCReq5G::Update(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg)
{
	int iRet = RET_SUCCESS;
	if (base->MSCC.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, SM_DATA_TYPE, "", "missing MSCC");
		bizMsg->m_resultcode = 2001;
		iRet = send5GInitCCA(bizMsg, base);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA ok");
		return iRet;
	}

	std::vector<long> v_rg;
	for (int i = 0; i < base->MSCC.size(); i++)
	{
		v_rg.push_back(base->MSCC[i].ratingGroup);
	}

	bizMsg->m_bExistRGFiltered = false;
	bizMsg->m_RgFiltSet.clear();
	bizMsg->m_vectorMsg.clear();
	std::vector<long> vFilterRG;
	for (int i = 0; i < base->MSCC.size(); i++)
	{
		bool bAllUSUFiltered = false;
		ocs::SUSU* MSCC = &(base->MSCC[i]);
		SSessionCacheData5G cacheData5G;
		bizMsg->m_requestType = SM_SESSION_UPDATE_CODE;
		sprintf(bizMsg->m_childsessionID, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
		bizMsg->m_ratingGroup = MSCC->ratingGroup;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", bizMsg->m_childsessionID);
		bool bUpdateNoChild = false;
		iRet = composeRER(bizMsg, MSCC, cacheData5G, v_rg, bUpdateNoChild);
		if (iRet == SM_ALL_MSCC_FILTER)
		{
			bAllUSUFiltered = true;
			vFilterRG.push_back(MSCC->ratingGroup);
		}
		else if (iRet != RET_SUCCESS)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "compose RER failed[%d]", iRet);
			return iRet;
		}

		if (bUpdateNoChild)
		{
			iRet = InsertChildSession(bizMsg, MSCC, cacheData5G);
			if (iRet != RET_SUCCESS)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "InsertChildSession failed[%d]", iRet);
				return iRet;
			}
		}
		else
		{
			iRet = UpdateChildSession(bizMsg, MSCC, cacheData5G, bAllUSUFiltered);
			if (iRet != RET_SUCCESS)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "UpdateChildSession failed[%d]", iRet);
				return iRet;
			}
		}
	}

	bizMsg->m_bExistMSCCFiltered = IsExistFilter(base);
	if (base->MSCC.size() == vFilterRG.size())
	{ // 全部RG都被过滤了，直接返回CCA
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "all RG filterd");
		DCServEvtCheck::instance()->SetCacheKey("FILTER_FLAG", "1");
		bizMsg->m_iCdrRet = send5GInitCCA(bizMsg, base);
		return RET_CDR;
	}

	if (bizMsg->m_bExistMSCCFiltered || bizMsg->m_bExistRGFiltered)
	{ // 过滤的这部分先出过滤单，再把未过滤的部分发给批价
		bizMsg->m_iCdrRet = RET_SUCCESS;
		return RET_CDR;
	}

	return RET_SUCCESS;
}

int DCReq5G::Term(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg)
{
	int iRet = RET_SUCCESS;
	if (base->MSCC.size() == 0)
	{
		char szRatingGroup[20]  = {0};
		UDBSQL* pQuery = bizMsg->m_dbm->GetSQL(_5G_SelectSessionStoreRbansRg); //q_5g_session_rbansrg
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, bizMsg->m_sessionID);
			pQuery->Execute();
			while (pQuery->Next())
			{
				pQuery->GetValue(2, szRatingGroup);//OCP_LNG_RATING_GROUP
				ocs::SUSU MSCC;
				MSCC.ratingGroup = atol(szRatingGroup);
				base->MSCC.push_back(MSCC);
			}
		}
		catch (UDBException& e)
		{
			std::string strSql;
			pQuery->GetSqlString(strSql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", strSql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select all child session execption[%s]", e.ToString());
		}
	}

	if (base->MSCC.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "missing MSCC");
		bizMsg->m_resultcode = 2001;
		iRet = send5GInitCCA(bizMsg, base);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA ok");
		return iRet;
	}

	std::vector<long> v_rg;
	for (int i = 0; i < base->MSCC.size(); i++)
	{
		v_rg.push_back(base->MSCC[i].ratingGroup);
	}

	bizMsg->m_bExistRGFiltered = false;
	bizMsg->m_RgFiltSet.clear();
	bizMsg->m_vectorMsg.clear();
	std::vector<long> vFilterRG;
	for (int i = 0; i < base->MSCC.size(); i++)
	{
		bool bAllUSUFiltered = false;
		ocs::SUSU* MSCC = &(base->MSCC[i]);
		SSessionCacheData5G cacheData5G;
		sprintf(bizMsg->m_childsessionID, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
		bizMsg->m_ratingGroup = MSCC->ratingGroup;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", bizMsg->m_childsessionID);
		bool bUpdateNoChild = false;
		iRet = composeRER(bizMsg, MSCC, cacheData5G, v_rg, bUpdateNoChild);
		if (SM_ALL_MSCC_FILTER == iRet)
		{
			bAllUSUFiltered = true;
			vFilterRG.push_back(MSCC->ratingGroup);
		}
		else if (iRet != RET_SUCCESS)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "compose RER failed[%d]", iRet);
			bizMsg->m_vectorMsg.clear();
			return iRet;
		}

		if (bUpdateNoChild)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "not find child sessionId[%s]", bizMsg->m_childsessionID);
			bizMsg->m_vectorMsg.clear();
			return SM_OCP_UNKNOWN_SESSION_ID;
		}
		else
		{
			iRet = UpdateChildSession(bizMsg, MSCC, cacheData5G, bAllUSUFiltered);
			if (iRet != RET_SUCCESS)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "UpdateChildSession failed[%d]", iRet);
				bizMsg->m_vectorMsg.clear();
				return iRet;
			}
		}
	}

	bizMsg->m_bExistMSCCFiltered = IsExistFilter(base);
	if (base->MSCC.size() == vFilterRG.size())
	{ // 全部RG都被过滤了, 发送使用量为0的使用量组给批价释放会话
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "all RG filterd");
		DCServEvtCheck::instance()->SetCacheKey("FILTER_FLAG", "1");
		bizMsg->m_iCdrRet = RET_SUCCESS;
		return RET_CDR;
	}
	
	if (bizMsg->m_bExistMSCCFiltered || bizMsg->m_bExistRGFiltered)
	{ // 过滤的这部分先出过滤单，再把未过滤的部分发给批价
		bizMsg->m_iCdrRet = RET_SUCCESS;
		return RET_CDR;
	}

	return RET_SUCCESS;
}

int DCReq5G::OfflineAndXDR(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg)
{
	int iRet = ComposeOfflineMsg(bizMsg);
	if (iRet == SM_ALL_MSCC_FILTER)
	{
		bizMsg->m_bExistMSCCFiltered = true;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "all RG filterd");
		DCServEvtCheck::instance()->SetCacheKey("FILTER_FLAG", "1");
		bizMsg->m_resultcode = 2001;
		return RET_CDR;
	}
	else if(iRet == SM_5G_RG_PAR_SM_FILTER_CONDITION)
	{	// 处理失败，直接落异常单，与回退拦截用同一个topic，返回ACK消息给OfflineToMsg
		bizMsg->m_iOfflineXDREptFlag = SM_5G_RG_PAR_SM_FILTER_CONDITION;
		bizMsg->m_resultcode = 2001;
		return RET_CDR;
	}
	else if (iRet != RET_SUCCESS)
	{ // 处理失败，直接落异常单，与回退拦截用同一个topic，返回ACK消息给OfflineToMsg
		bizMsg->m_iOfflineXDREptFlag = EPT_5G_OFFLINE_XDR_FAIL_CDR;
		bizMsg->m_resultcode = 2001;
		return RET_CDR;
	}
	else
	{ // 消息处理正常，发送给批价了，不用处理
		
	}

	return RET_SUCCESS;
}

bool DCReq5G::FilterUSU(TSMPara* pSMparam, ocs::SCCRDataUnit& USU)
{
	// 超短话单:按流量
	int nShortOctet5G = pSMparam->Get5GPara()->shortCDROctet5G;
	if (nShortOctet5G && (USU.unitTotal < nShortOctet5G))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short cdr, shortoctet5G[%d], usu octet[%d]", nShortOctet5G, USU.unitTotal);
		return true;
	}

	// 超短话单:按时长
	int nShortCDRTime5G = pSMparam->Get5GPara()->shortCDRTime5G;
	if (nShortCDRTime5G && (USU.duration < nShortCDRTime5G))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short cdr, nShortCDRTime5G[%d], usu duration[%d]", nShortCDRTime5G, USU.duration);
		return true;
	}

	// 超短时长流量过滤
	long lnMCondShortTime5G = pSMparam->Get5GPara()->lnMCondShortTime5G;
	long lnMCondShortOct5G = pSMparam->Get5GPara()->lnMCondShortOct5G;
	if (!lnMCondShortTime5G && !lnMCondShortOct5G)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short cdr, multi condition filter not open");
		return false;
	}

	if (USU.duration <= lnMCondShortTime5G && USU.unitTotal <= lnMCondShortOct5G)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short cdr, multi condition filter, usu dur[%d] oct[%ld], condition dur[%ld] oct[%ld]", 
									USU.duration, USU.unitTotal, lnMCondShortTime5G, lnMCondShortOct5G);
		return true;
	}

	return false;
}

int DCReq5G::FilterMultiUSU(TSMPara* pSMparam, ocs::SUSU* MSCC)
{
	int iFilterNum = 0;
	std::vector<ocs::SCCRDataUnit>::iterator itUSU = MSCC->VecUSU.begin();
	for ( ; itUSU != MSCC->VecUSU.end(); itUSU++)
	{
		if (FilterUSU(pSMparam, *itUSU))
		{
			itUSU->filterFlag = 1;
			iFilterNum++;
		}
		else
		{
			itUSU->filterFlag = 0;
		}
	}

	return iFilterNum;
}

bool DCReq5G::FilterMSCC(TSMPara* smpara, ocs::SUSU* MSCC, ocs::rbext& ext)
{
	if (MSCC->USU1.duration || MSCC->USU1.unitInput || MSCC->USU1.unitOutput || MSCC->USU1.unitTotal)
	{
		ext.kv["cutcdr_flag"] = "1";
	}

	if (MSCC->VecUSU.size() == 0)
	{
		bool bFilterUSU0 = false, bFilterUSU1 = false;
		bFilterUSU0 = FilterUSU(smpara, MSCC->USU0);
		if (bFilterUSU0)
		{
			MSCC->USU0.filterFlag = 1;
		}

		if (ext.kv["cutcdr_flag"] == "1")
		{
			bFilterUSU1 = FilterUSU(smpara, MSCC->USU1);
			if (bFilterUSU1)
			{
				MSCC->USU1.filterFlag = 1;
				ext.kv["cutcdr_flag"] = "0";
			}
		}
		else
		{
			bFilterUSU1 = true;
		}

		if (bFilterUSU0 && bFilterUSU1)
		{
			return true;
		}
		else if (bFilterUSU0 && !bFilterUSU1)
		{
			ocs::SCCRDataUnit usuTemp;
			usuTemp = MSCC->USU0;
			MSCC->USU0 = MSCC->USU1;
			MSCC->USU1 = usuTemp;
			ext.kv["cutcdr_flag"] = "0";
			MSCC->USU1.tariffChangeUsage = 0;
		}
	}
	else
	{
		int iFilterNum = FilterMultiUSU(smpara, MSCC);
		if (iFilterNum == MSCC->VecUSU.size())
		{
			return true;
		}

		if (MSCC->VecUSU.size() - iFilterNum <= 2)
		{
			int iUSUNum = 0;
			std::vector<ocs::SCCRDataUnit>::iterator itUSU = MSCC->VecUSU.begin();
			for ( ; itUSU != MSCC->VecUSU.end(); itUSU++)
			{
				if (!itUSU->filterFlag)
				{
					if (iUSUNum == 0)
					{
						MSCC->USU0 = *itUSU;
					}
					else
					{
						MSCC->USU1 = *itUSU;
						
					}
					iUSUNum++;
				}
			}

			if (iUSUNum == 2)
			{
				ext.kv["cutcdr_flag"] = "1";
			}

			ext.kv["5gusu"] = "0";
		}
		else
		{
			ext.kv["5gusu"] = "1";
		}
	}

	return false;
}

bool DCReq5G::IsExistFilter(SCCRBase* base)
{
	for(int i = 0; i < base->MSCC.size(); i++)
	{
		if (base->MSCC[i].VecUSU.size() > 0)
		{
			for (int iusu = 0; iusu < base->MSCC[i].VecUSU.size(); iusu++)
			{
				if (base->MSCC[i].VecUSU[iusu].filterFlag)
				{
					return true;
				}
			}
		}
		else
		{
			if (base->MSCC[i].USU0.filterFlag || base->MSCC[i].USU1.filterFlag)
			{
				return true;
			}
		}
	}

	return false;
}

int DCReq5G::composeRER(STBizMsg* bizMsg, ocs::SUSU* MSCC, SSessionCacheData5G& cacheData5G, std::vector<long>& v_rg, bool& bUpdateNoChild)
{
	char value[BIZ_TEMP_LEN_256] = {0};
	ocs::SCCRDataUnit TUSU;
	long lastCCRTime = 0;
	int  nLastGsuUnit = 0;
	long lnlastGsuObject = 0;
	long lnlastGsuTime = 0;
	TSERVICE_QUOTA_CONF* conf = NULL;
	ocs::SCCR5GInfo* data = (SCCR5GInfo*)bizMsg->m_extend;
	TSMPara* smpara	= (TSMPara *)bizMsg->m_smpara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	SUserInfo* userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	ocs::SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	AREA_INFO* subVisit	= bizMsg->m_visit;
	int iRoamType = bizMsg->m_roamtype;
	STDynamicStepInfo5G stDynamicStepInfo5G;
	char szEVT[20]={0};
	char szRTI[10]={0};
	char szvisitArea[BIZ_TEMP_LEN_64]={0};
	bUpdateNoChild = false;
	if (subVisit->area)
	{
		if (6 == iRoamType || 9 == iRoamType)
		{
			sprintf(szvisitArea, "00%d", subVisit->area);
		}
		else
		{
			sprintf(szvisitArea, "0%d", subVisit->area);
		}
	}
	else
	{
		szvisitArea[0] = '\0';
	}
	cacheData5G.n_RE_INT_ROAM_TYPE = iRoamType;
	strcpy(cacheData5G.sz_RE_STR_SUB_VISIT_AREA, szvisitArea);


	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cacheData5G info roamType[%d],visitArea[%s]", cacheData5G.n_RE_INT_ROAM_TYPE, cacheData5G.sz_RE_STR_SUB_VISIT_AREA);

	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdata rbr;
	ocs::rsu_t rsu;
	ocs::rbext ext;
	long balance = 0;

	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.trace = bizMsg->m_trace_flag;
	uhd.flag = bizMsg->m_iRollFlag;
	head.type = RE_SERVICE_TYPE_INT_5G_REQ;
	head.mode = bizMsg->m_offline; //Re 定义 0 online mode, 1 offline mode
	head.version = 2;
	head.stamp = bizMsg->timestampCCR;
	head.session = bizMsg->m_childsessionID;
	head.serial = bizMsg->m_serial;
	head.trace = bizMsg->m_trace_flag;
	head.result = 0;
	head.topology = base->topology;
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);
	rbr.calling_hcarrier = base->subscription.carriers;
	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose latn_id[%d]", userInfo->ilatnid);
	rbr.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_nbr[%s]", rbr.charged_nbr.c_str());
	sprintf(value, "0%d", base->subscription.area);
	rbr.calling_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_harea[%s]", value);
	sprintf(value, "%d", data->chargingId);
	rbr.charging_id = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCP_STR_CHARGING_ID[%s]", value);

	//R306 PDU类型
	rbr.pdp_type = data->pduType;
	rbr.pdp_addr = data->ServingNFIDInfo.pduIPv4Address;
	if (rbr.pdp_addr.empty())
	{
		rbr.pdp_addr = data->ServingNFIDInfo.pduIPv6Address;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_type[%s] pdp_addr[%s]", rbr.pdp_type.c_str(), rbr.pdp_addr.c_str());

	if (bizMsg->m_requestType != SM_SESSION_INITIAL_CODE)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ChildSessionId[%s]", bizMsg->m_childsessionID);
		UDBSQL* pQuery = dbm->GetSQL(_5G_CCR_SelectChildSession); //q_5g_ccr_childsession
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, bizMsg->m_sessionID);
			pQuery->BindParam(2, bizMsg->m_childsessionID);
			pQuery->Execute();
			if (pQuery->Next())
			{
				pQuery->GetValue(1, value);//RE_INT_LAST_GSU_UNIT
				cacheData5G.nLastGsuUnit = nLastGsuUnit= atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu unit[%d]", cacheData5G.nLastGsuUnit);

				//获取上次预占流量
				pQuery->GetValue(2, value);//RE_LNG_LAST_GSU_TOTAL_OCT
				lnlastGsuObject = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu total object[%ld],unitTotal[%ld]", lnlastGsuObject,(MSCC->USU0.unitTotal+MSCC->USU1.unitTotal));
				cacheData5G.lnlastGsuObject = lnlastGsuObject;

				//获取上次预占时长
				pQuery->GetValue(3, value);//RE_LNG_LAST_GSU_TIME
				lnlastGsuTime = atol(value);
				cacheData5G.lnlastGsuTime = lnlastGsuTime;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu total object[%ld],duration[%ld]",lnlastGsuTime, (MSCC->USU0.duration+MSCC->USU1.duration));

				//总使用时长
				pQuery->GetValue(4, value); //SM_LNG_ALL_USU_TIME
				TUSU.duration = atoi(value);
				cacheData5G.duration = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%u]", TUSU.duration);

				//总使用总流量
				pQuery->GetValue(5, value); //SM_LNG_ALL_USU_TOTAL_OCT
				TUSU.unitTotal = atol(value);
				cacheData5G.unitTotal = TUSU.unitTotal;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%lld]", TUSU.unitTotal);

				//总使用上行流量
				pQuery->GetValue(6, value); //SM_LNG_ALL_USU_INPUT_OCT
				TUSU.unitInput = atol(value);
				cacheData5G.unitInput = TUSU.unitInput;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%lld]", TUSU.unitInput);

				//总使用下行流量
				pQuery->GetValue(7, value); //SM_LNG_ALL_USU_OUTPUT_OCT
				TUSU.unitOutput = atol(value);
				cacheData5G.unitOutput = TUSU.unitOutput;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%lld]", TUSU.unitOutput);

				pQuery->GetValue(8, value); //RE_LNG_LAST_CCR_TIME
				rbr.pre_dtime = atol(value);
				rbr.sess_start_time = value;
				lastCCRTime = atol(value);
				cacheData5G.lastCCRTime = lastCCRTime;

				rbr.active_flag = 0;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "delete ActiveFlag");
			}
			else
			{
				bUpdateNoChild = true;
			}
		}
		catch(UDBException& e)
		{
			string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "query execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	if (bizMsg->m_bImsFilter)
	{
		return RET_SUCCESS;
	}


	bool bAllMSCCFilter = false;
	/*if (bUpdateNoChild)
	{ // first update 只有预占量没有使用量，和init一样
		head.sreq = SM_SESSION_INITIAL_CODE;
	}
	else*/
	{
		head.sreq = bizMsg->m_requestType;
		if (bizMsg->m_requestType != SM_SESSION_INITIAL_CODE)
		{
			if (FilterMSCC(smpara, MSCC, ext))
			{
				bAllMSCCFilter = true;
			}
		}
	}

	if (bizMsg->m_requestType == SM_SESSION_UPDATE_CODE && bAllMSCCFilter)
	{
		return SM_ALL_MSCC_FILTER;
	}

	//R505 主叫号码拜访费率区
	rbr.calling_varea = szvisitArea;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling visit area[%s]", szvisitArea);
	
	rbr.roam_type = iRoamType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%d]", iRoamType);

	rbr.tac = MSCC->USU0.userLocInfoMation.tac;
	rbr.lac = MSCC->USU0.userLocInfoMation.tac;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose tac[%s]", rbr.tac.c_str());

	rbr.nrcellid = MSCC->USU0.userLocInfoMation.nrCellId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose nrcellid[%s]", rbr.nrcellid.c_str());

	rbr.mcc = MSCC->USU0.userLocInfoMation.mcc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose mcc[%s]", rbr.mcc.c_str());

	rbr.mnc = MSCC->USU0.userLocInfoMation.mnc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose mnc[%s]", rbr.mnc.c_str());

	rbr.msc = MSCC->USU0.userLocInfoMation.msc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", rbr.msc.c_str());

	rbr.cell = MSCC->USU0.userLocInfoMation.cellid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", rbr.cell.c_str());

	sprintf(value, "0%d", base->subscription.area);
	rbr.charged_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", rbr.charged_harea.c_str());

	//R85	用户付费属性标识
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");

	//R604	本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cur_dtime[%ld]",bizMsg->timestampCCR);

	//R605	是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");

	//R3017 LTE业务标识
	rbr.lte_flag = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lte_flag[1]");

	sprintf(cacheData5G.sz_OCP_STR_CPRS_QOS, "%d", MSCC->QosInfoMation.qi5g);
	rbr.qos = cacheData5G.sz_OCP_STR_CPRS_QOS;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos [%s]", cacheData5G.sz_OCP_STR_CPRS_QOS);

	sprintf(value, "%d", MSCC->QosInfoMation.priorityLevel);
	cacheData5G.n_OCT_INT_QOS_LEVEL = MSCC->QosInfoMation.priorityLevel;
	rbr.qos_class_id = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos_class_id [%d]", cacheData5G.n_OCT_INT_QOS_LEVEL);

	strcpy(cacheData5G.sz_OCP_STR_RATTYPE, MSCC->rATType.c_str());
	rbr.rat_type = MSCC->rATType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", cacheData5G.sz_OCP_STR_RATTYPE);

	if (SM_SESSION_INITIAL_CODE == bizMsg->m_requestType /*|| bUpdateNoChild */)
	{
		balance = -1;
	}

	if ((conf = smpara->GetServiceQuotaConf(MSCC->ratingGroup, balance)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "invalid RG[%u]", MSCC->ratingGroup);
		if ((conf = smpara->GetServiceQuotaConf(RATING_5G_GROUP, balance)) == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "no find service quota config[%lld]", MSCC->ratingGroup);
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	if (smpara->Get5GPara()->iDynUseTable > 0)
	{ //新增5G下,获取动态步长函数
		int iRet = GetDynamicStepInfo5G(dbm, userInfo->servID, userInfo->ilatnid, MSCC->ratingGroup, bizMsg->timestampCCR, stDynamicStepInfo5G);
		if (0 != iRet)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "GetDynamicStepInfo5G fail");
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	cacheData5G.valid_time = conf->VALID_TIME;
	rbr.valid_time = conf->VALID_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get VALID_TIME[%d]", conf->VALID_TIME);

	if (stDynamicStepInfo5G.bGetSucceed)
	{
		conf->DURATION = stDynamicStepInfo5G.lnTimeLimit;
		conf->TOTAL_OCTETS = stDynamicStepInfo5G.lnVolumeLimit;
	}
	else if (MSCC->dynLimitInfo.timeLimit || MSCC->dynLimitInfo.volumeLimit64)
	{
		conf->DURATION = MSCC->dynLimitInfo.timeLimit;
		conf->TOTAL_OCTETS = (long long)MSCC->dynLimitInfo.volumeLimit64;
	}

	if (!conf->DURATION && !conf->TOTAL_OCTETS)
	{
		conf->DURATION = MSCC->R5GSU.duration;
		conf->TOTAL_OCTETS = (long long)MSCC->R5GSU.unitTotal;
	}

	cacheData5G.conf_QUOTA_CONSUMPTION_TIME = conf->QUOTA_CONSUMPTION_TIME;
	cacheData5G.conf_VOLUME_QUOTA_THRESHOLD = conf->VOLUME_QUOTA_THRESHOLD;
	cacheData5G.conf_VOLUME_QUOTA_THRESHOLD1 = conf->VOLUME_QUOTA_THRESHOLD_1;
	cacheData5G.conf_TIME_QUOTA_THRESHOLD = conf->TIME_QUOTA_THRESHOLD;
	cacheData5G.conf_QUOTA_HOLDING_TIME = conf->QUOTA_HOLDING_TIME;
	cacheData5G.rsu_duration = conf->DURATION;
	cacheData5G.rsu_totalOCtets = conf->TOTAL_OCTETS;

	rbr.snssai_sst = atoi(base->smExt.kv["sNSSAI_SST"].c_str());
	rbr.snssai_sd = base->smExt.kv["sNSSAI_SD"];

	if (SM_SESSION_UPDATE_CODE == bizMsg->m_requestType || SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
	{
		if (!conf->DURATION && !conf->TOTAL_OCTETS && !conf->INPUT_OCTETS && !conf->OUTPUT_OCTETS)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "invalid rating group :no rsu value [%d]", RATING_5G_GROUP);
			return SM_OCP_UNABLE_TO_COMPLY;
		}

		sprintf(value, "%lld", MSCC->ratingGroup);
		rsu.rating_group = value;
		//时长
		if (conf->DURATION)
		{
			rsu.unit = RB_UNIT_CODE_SECOND;
			rsu.amount = conf->DURATION;
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", rsu.amount);
		}

		//总流量
		if (conf->TOTAL_OCTETS)
		{
			rsu.unit = RB_UNIT_CODE_TOTAL_BYTES;
			rsu.amount = conf->TOTAL_OCTETS;
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_TOTAL_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", rsu.amount);
		}

		//上行流量
		if (conf->INPUT_OCTETS)
		{
			rsu.unit = RB_UNIT_CODE_UP_BYTES;
			rsu.amount = conf->INPUT_OCTETS;
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_UP_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", rsu.amount);
		}

		//下行流量
		if (conf->OUTPUT_OCTETS)
		{
			rsu.unit = RB_UNIT_CODE_DOWN_BYTES;
			rsu.amount = conf->OUTPUT_OCTETS;
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_DOWN_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", rsu.amount);
		}
	}

	if (SM_SESSION_INITIAL_CODE == bizMsg->m_requestType /*|| bUpdateNoChild */)
	{
		strcpy(szEVT, bizMsg->m_eventType);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose EVT[%s]", szEVT);
		strcpy(szRTI, subVisit->sector_id);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RTI[%s]", szRTI);
		rbr.pre_dtime = bizMsg->timestampCCR;
		sprintf(value, "%ld", bizMsg->timestampCCR);
		rbr.sess_start_time = value;
		rbr.active_flag = userInfo->isActive; //R606 激活用户
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose active_flag[%s]", value);
	}
	else if (SM_SESSION_UPDATE_CODE == bizMsg->m_requestType || SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		sprintf(value, "%lld", MSCC->ratingGroup);
		ocs::usu u;
		u.rating_group = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
		if (atoi(ext.kv["5gusu"].c_str()) == 0) //2组usu以内
		{
			u.sreq = 0;
			//时长
			u.unit = RB_UNIT_CODE_SECOND;
			if (!MSCC->USU0.filterFlag)
			{
				u.amount = MSCC->USU0.duration;
			}

			if (!MSCC->USU1.filterFlag)
			{
				u.amount2 = MSCC->USU1.duration;
			}
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);

			//总流量
			u.unit = RB_UNIT_CODE_TOTAL_BYTES;
			if (!MSCC->USU0.filterFlag)
			{
				u.amount = MSCC->USU0.unitTotal;
			}

			if (!MSCC->USU1.filterFlag)
			{
				u.amount2 =  MSCC->USU1.unitTotal;
			}
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_TOTAL_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);

			//上行流量
			u.unit = RB_UNIT_CODE_UP_BYTES;
			if (!MSCC->USU0.filterFlag)
			{
				u.amount = MSCC->USU0.unitInput;
			}

			if (!MSCC->USU1.filterFlag)
			{
				u.amount2 =  MSCC->USU1.unitInput;
			}
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_UP_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);

			//下行流量
			u.unit = RB_UNIT_CODE_DOWN_BYTES;
			if (!MSCC->USU0.filterFlag)
			{
				u.amount = MSCC->USU0.unitOutput;
			}

			if (!MSCC->USU1.filterFlag)
			{
				u.amount2 =  MSCC->USU1.unitOutput;
			}
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_DOWN_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);
			if ((MSCC->USU1.unitTotal > 0 || MSCC->USU1.duration > 0) && !MSCC->USU1.filterFlag)
			{
				rbr.cell2 = MSCC->USU1.userLocInfoMation.cellid;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell2[%s]", rbr.cell2.c_str());
				rbr.location2 = MSCC->USU1.userLocInfoMation.nrCellId;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location2[%s]", rbr.location2.c_str());
				rbr.msc2 = MSCC->USU1.userLocInfoMation.msc;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc2[%s]", rbr.msc2.c_str());
				rbr.lac2 = MSCC->USU1.userLocInfoMation.tac;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lac2[%s]", rbr.lac2.c_str());
				MSCC->userLocInfoMation = MSCC->USU1.userLocInfoMation;
			}
		}
		else //3组usu及以上
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usu size[%d]", MSCC->VecUSU.size());
			PushUSV(MSCC->VecUSU, domain, u);
		}
	}

	if (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		if (atoi(ext.kv["5gusu"].c_str()) == 0)
		{
			//累计总使用量
			if (!MSCC->USU0.filterFlag)
			{
				TUSU.duration += MSCC->USU0.duration;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);
				
				TUSU.unitTotal += MSCC->USU0.unitTotal;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);
				
				TUSU.unitInput += MSCC->USU0.unitInput;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);
				
				TUSU.unitOutput += MSCC->USU0.unitOutput;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);
			}

			if (!MSCC->USU1.filterFlag)
			{
				//累计总使用量
				TUSU.duration += MSCC->USU1.duration;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

				TUSU.unitTotal += MSCC->USU1.unitTotal;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

				TUSU.unitInput += MSCC->USU1.unitInput;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

				TUSU.unitOutput += MSCC->USU1.unitOutput;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);
			}
		}
		else
		{
			TotalUSUAdd(TUSU, MSCC->VecUSU);
		}

		ocs::debit totalusu;
		sprintf(value, "%lld", MSCC->ratingGroup);
		totalusu.rating_group = value;

		//B03
		//时长
		totalusu.unit = RB_UNIT_CODE_SECOND;
		totalusu.amount = TUSU.duration;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);

		//总流量
		totalusu.unit = RB_UNIT_CODE_TOTAL_BYTES;
		totalusu.amount = TUSU.unitTotal;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_TOTAL_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);

		//上行流量
		totalusu.unit = RB_UNIT_CODE_UP_BYTES;
		totalusu.amount = TUSU.unitInput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_UP_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);

		//下行流量
		totalusu.unit = RB_UNIT_CODE_DOWN_BYTES;
		totalusu.amount = TUSU.unitOutput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_DOWN_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);
	}

	rbr.tariff_info = 1;
	rbr.rating_info = 1;
	rbr.balance_info = 1;
	rbr.balance_query = 1;
	rbr.accumlator_info = 1;

	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}

	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;
	ext.kv["RTI"] = szRTI;
	ext.kv["EVT"] = szEVT;
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["switchId"] = bizMsg->m_switchId;
	sprintf(value, "%d", bizMsg->m_ilatnId);
	ext.kv["LatnId"] = value;
	ext.kv["operListId"] = bizMsg->m_operListId;
	ext.kv["opertype"] = bizMsg->m_operType;
	ext.kv["dNNID"] = data->dNNID;
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	ext.kv["RBR_SOURCE"] = "REQ";
	uhd.checkKey = bizMsg->m_strCheckKey;
	ext.kv["CDR_STR_BATCH_ID"] = bizMsg->m_sBatchId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
	ext.kv["CDR_STR_CUT_TYPE"] = MSCC->triggerInfoMation.triggerType;
	ext.kv["CDR_PUB_STR_HOSTID"] = base->topology;
	ext.kv["CDR_PUB_STR_CHARGED_PARTY"] = base->subUnified;
	//2100预付费, 非2100后付费，预付费写1, 后付费写0
	if ("2100" == bizMsg->m_payMentMode)
	{
		ext.kv["RE_INT_PAY_FLAG"] = "1";
	}
	else
	{
		ext.kv["RE_INT_PAY_FLAG"] = "0";
	}
	sprintf(value, "%ld", userInfo->servID);
	ext.kv["CDR_PUB_LNG_SERVID"] = value;
	sprintf(value, "%ld", userInfo->custID);
	ext.kv["CDR_PUB_LNG_CUSTID"] = value;
	sprintf(value, "%ld", userInfo->lnAcctID);
	ext.kv["CDR_LNG_ACCT_ID"] = value;
	ext.kv["OCP_STR_SESSION_ID"] = bizMsg->m_childsessionID;
	sprintf(value, "%d", base->subscription.carriers);
	ext.kv["RE_INT_SUB_OPERATOR"] = value;
	sprintf(value, "0%d", base->subscription.area);
	ext.kv["RE_STR_SUB_AREA"] = value;
	sprintf(value, "%d", bizMsg->m_ilatnId);
	ext.kv["CDR_INT_LATN_ID"] = value;
	string strImsi = base->smExt.kv["IMSI"];
	if (strImsi.find("imsi-") != string::npos)
	{
		strImsi = strImsi.substr(5);
	}
	ext.kv["SM_STR_IMSI"] = strImsi;
	ext.kv["OCP_STR_CHARGING_ID"] = rbr.charging_id;
	ext.kv["OCP_STR_NF_ADDR"] = data->ServingNFIDInfo.pduIPv4Address;
	ext.kv["OCP_STR_NF_ADDR_IPV6"] = data->ServingNFIDInfo.pduIPv6Address;
	ext.kv["OCP_STR_PDU_TYPE"] = data->pduType;
	ext.kv["OCP_STR_PDU_ADDR"] =  base->nFIPv4Address;
	ext.kv["OCP_STR_PDU_ADDR_IPV6"] = base->nFIPv6Address;
	sprintf(value, "%d", data->qi5g);
	ext.kv["OCP_INT_PDU_5QI"] = value;
	sprintf(value, "%ld", MSCC->ratingGroup);
	ext.kv["OCP_LNG_RATING_GROUP"] = value;
	ext.kv["OCP_STR_SERVED_GPSI"] = data->servedGPSI;
	ext.kv["OCP_STR_DNNID"] = data->dNNID;
	sprintf(value, "%d", data->qosId);
	ext.kv["OCP_INT_PDU_QOS_ID"] = value;
	sprintf(value, "%d", bizMsg->m_iRollFlag);
	ext.kv["SOURCE_FILE_TYPE"] = value;
	ext.kv["CDR_LNG_AMBRUL"] = data->AuthorizedSessionAMBR.uplink;
	ext.kv["CDR_LNG_AMBRDL"] = data->AuthorizedSessionAMBR.downlink;
	ext.kv["OCP_STR_PDU_SESSION_ID"] = data->pduSessionID;
	ext.kv["CDR_QUOTA_MANAGE_INDICATOR"] = "ONLINE_CHARGING";
	sprintf(value, "%d", bizMsg->m_requestNumber);
	ext.kv["OCP_INT_REQ_NBR"] = value;
	ext.kv["CDR_CNPLMN"]=base->smExt.kv["servingCNPlmnId"];

	// R3019 转为16进制后赋值
	char hexMsc[32] = {0};
	long mscVal = atol(rbr.msc.c_str());
	DCCommonIF::DECTOHEX(mscVal, hexMsc, sizeof(hexMsc));
	ext.kv["R3019"] = hexMsc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose R3019[%s], SOURCE[%s]", ext.kv["R3019"].c_str(), rbr.msc.c_str());

	for (unsigned int i = 0; i < v_rg.size(); i++)
	{
		char key[32] = {0};
		char rgValue[64] = {0};
		sprintf(key, "RG%d", i);
		sprintf(rgValue, "%ld", v_rg[i]);
		ext.kv[key] = rgValue;
	}

	if (MSCC->VecUSU.size() > 0)
	{
		string sLastUseOfTime = MSCC->VecUSU[0].lastUseOfTime;
		std::vector<SCCRDataUnit >::iterator itusu = MSCC->VecUSU.begin();
		for ( ; itusu != MSCC->VecUSU.end(); itusu++)
		{
			if (strcmp(sLastUseOfTime.c_str(), itusu->lastUseOfTime.c_str()) < 0)
			{
				sLastUseOfTime = itusu->lastUseOfTime;
			}
		}

		DCServEvtCheck::instance()->PushBackCacheVecKey("END_TIME", sLastUseOfTime);
	}
	else
	{
		if (strcmp(MSCC->USU0.lastUseOfTime.c_str(), MSCC->USU1.lastUseOfTime.c_str()) > 0)
		{
			DCServEvtCheck::instance()->PushBackCacheVecKey("END_TIME", MSCC->USU0.lastUseOfTime);
		}
		else
		{
			DCServEvtCheck::instance()->PushBackCacheVecKey("END_TIME", MSCC->USU1.lastUseOfTime);
		}
	}

	if (atoi(ext.kv["5gusu"].c_str()) > 0)
	{
		ExtUSUInsert(MSCC->VecUSU, ext.kv, base, iRoamType, szvisitArea, bizMsg);
	}
	else
	{
		ext.kv["5gusu"] = "0";
		ExtUSUInsert(MSCC->USU0, ext.kv, base, iRoamType, szvisitArea, bizMsg, 0);
		if ("1" == ext.kv["cutcdr_flag"])
		{
			ExtUSUInsert(MSCC->USU1, ext.kv, base, iRoamType, szvisitArea, bizMsg, 1);
		}
	}

	//增加5G RG过来规则判断
	DCAnsPara *spara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	if(1 == spara->Get5GPara()->iRgFilterSwitch)
	{
		int iRet = 0;
		std::map<int, std::vector<ST_ParSmRgCondtion> > parSmRgCondition;

		SCCRBase* tmpBase = (SCCRBase*)bizMsg->m_base;
		char szRatingGroup[128] = {0};
		sprintf(szRatingGroup, "%lld", bizMsg->m_ratingGroup);
		tmpBase->smExt.kv["ratingGroup"] =  szRatingGroup;
		//查询 PAR_SM_FILTER_CONDITION 表获取对应的规则
		GetParSmFilterRgCondition(bizMsg, atoi(bizMsg->m_operListId.c_str()), parSmRgCondition);
		
		if(-1 == Deal5GRgFilterRule(parSmRgCondition, bizMsg))
		{
			bizMsg->m_RgFiltSet.insert(MSCC->ratingGroup);
			bizMsg->m_bExistRGFiltered = true;
			return SM_ALL_MSCC_FILTER;
		}
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char *)m_en.data();
	bizMsg->m_vectorMsg.push_back(bizMsg->data);
	return RET_SUCCESS;
}

int DCReq5G::ComposeOfflineMsg(STBizMsg* bizMsg)
{
	int iRet = RET_SUCCESS;
	SUserInfo* userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	TSMPara* smpara = bizMsg->m_smpara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	int iRoamType = bizMsg->m_roamtype;
	ocs::SCCR5GInfo* data = (SCCR5GInfo *)bizMsg->m_extend;
	ocs::SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	AREA_INFO* subVisit	= bizMsg->m_visit;
	char value[BIZ_TEMP_LEN_256] = {0};
	char szEVT[20]={0};
	char szRTI[10]={0};
	char szvisitArea[BIZ_TEMP_LEN_64]={0};
	ocs::SUSU* MSCC = &(base->MSCC[0]);
	ocs::SCCRDataUnit TUSU;
	sprintf(bizMsg->m_childsessionID, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
	bizMsg->m_ratingGroup = MSCC->ratingGroup;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", bizMsg->m_childsessionID);

	if (subVisit->area)
	{
		if (6 == iRoamType || 9 == iRoamType)
		{
			sprintf(szvisitArea, "00%d", subVisit->area);
		}
		else
		{
			sprintf(szvisitArea, "0%d", subVisit->area);
		}
	}
	else
	{
		szvisitArea[0] = '\0';
	}

	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdata rbr;
	ocs::rsu_t rsu;
	ocs::usu u;
	ocs::debit totalusu;
	ocs::rbext ext;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "roamType[%d], visitArea[%s]", iRoamType, szvisitArea);
	if (FilterMSCC(smpara, MSCC, ext))
	{ // 判断是否全部使用量都被过滤, 全部被过滤则不发rbr, term流程则发使用量为0的消息给批价删除会话表
		if (bizMsg->m_requestType != SM_SESSION_TERMINATION_CODE)
		{
		    return SM_ALL_MSCC_FILTER;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SM_SESSION_TERMINATION set unitTotal = 0");
	}
	
	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.trace = bizMsg->m_trace_flag;
	uhd.flag = bizMsg->m_iRollFlag;
	head.type = RE_SERVICE_TYPE_INT_5G_REQ;
	head.mode = bizMsg->m_offline; //Re 定义 0 online mode, 1 offline mode
	head.version = bizMsg->m_requestType == SM_SESSION_XDR_CODE ? 1 : 2;
	head.stamp = bizMsg->timestampCCR;
	head.session = bizMsg->m_childsessionID;
	head.serial = bizMsg->m_serial;
	head.trace = bizMsg->m_trace_flag;
	head.result = 0;
	head.topology = base->topology;
	if (bizMsg->m_requestType == SM_SESSION_XDR_CODE)
	{
		head.sreq = 4;
		rbr.sreq = SM_SESSION_TERMINATION_CODE;
	}
	else if (1 == bizMsg->m_offline && SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
	{ 	//离线模式不预占，所以 init 消息也发2
		head.sreq = 2;
	}
	else
	{
		head.sreq = bizMsg->m_requestType;
	}
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.calling_hcarrier = base->subscription.carriers;
	rbr.latn_id = userInfo->ilatnid;
	rbr.charged_nbr = base->subscription.phone;
	sprintf(value, "0%d", base->subscription.area);
	rbr.calling_harea = value;
	rbr.active_flag = 0;
	sprintf(value, "%u", data->chargingId);
	rbr.charging_id = value;
	rbr.pdp_type = data->pduType;
	rbr.pdp_addr = data->ServingNFIDInfo.pduIPv4Address;
	if (rbr.pdp_addr.size() == 0)
	{
		rbr.pdp_addr = data->ServingNFIDInfo.pduIPv6Address;
	}

	rbr.pre_dtime = 0; //R603 会话上次扣费开始时间

	//R505 主叫号码拜访费率区
	rbr.calling_varea = szvisitArea;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling visit area[%s]", szvisitArea);

	rbr.roam_type = iRoamType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%d]", iRoamType);

	strcpy(szEVT, bizMsg->m_eventType);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose EVT[%s]", szEVT);

	strcpy(szRTI, subVisit->sector_id);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RTI[%s]", szRTI);

	rbr.tac = MSCC->userLocInfoMation.tac;
	rbr.lac = MSCC->userLocInfoMation.tac;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose tac[%s]", MSCC->userLocInfoMation.tac.c_str());

	rbr.nrcellid = MSCC->userLocInfoMation.nrCellId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose nrcellid[%s]", MSCC->userLocInfoMation.nrCellId.c_str());

	rbr.mcc = MSCC->userLocInfoMation.mcc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose mcc[%s]", rbr.mcc.c_str());

	rbr.mnc = MSCC->userLocInfoMation.mnc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose mnc[%s]", rbr.mnc.c_str());

	rbr.msc = MSCC->userLocInfoMation.msc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", rbr.msc.c_str());

	rbr.cell = MSCC->userLocInfoMation.cellid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", rbr.cell.c_str());

	sprintf(value, "0%d", base->subscription.area);
	rbr.charged_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", rbr.charged_harea.c_str());
	//R85	用户付费属性标识
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");

	//R604	本次计费请求开始时间
	rbr.cur_dtime = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cur_dtime[%ld]",bizMsg->timestampCCR);

	//R605	是否进行使用量累计标识
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");

	//R3017 LTE业务标识
	rbr.lte_flag = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lte_flag[1]");

	sprintf(value,"%d",MSCC->QosInfoMation.qi5g);
	rbr.qos = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos [%s]", value);

	sprintf(value,"%d",MSCC->QosInfoMation.priorityLevel);
	rbr.qos_class_id = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos_class_id [%s]", rbr.qos_class_id.c_str());

	rbr.rat_type = MSCC->rATType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", rbr.rat_type.c_str());

	rbr.valid_time = 3600;

	rbr.snssai_sst = atoi(base->smExt.kv["sNSSAI_SST"].c_str());
	rbr.snssai_sd = base->smExt.kv["sNSSAI_SD"];

	sprintf(value, "%lld", MSCC->ratingGroup);
	u.rating_group = value;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
	if (atoi(ext.kv["5gusu"].c_str()) == 0) //2组usu以内
	{
		u.sreq = 0;
		//时长
		u.unit = RB_UNIT_CODE_SECOND;
		if (!MSCC->USU0.filterFlag)
		{
			u.amount = MSCC->USU0.duration;
		}

		if (!MSCC->USU1.filterFlag)
		{
			u.amount2 = MSCC->USU1.duration;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);

		//总流量
		u.unit = RB_UNIT_CODE_TOTAL_BYTES;
		if (!MSCC->USU0.filterFlag)
		{
			u.amount = MSCC->USU0.unitTotal;
		}

		if (!MSCC->USU1.filterFlag)
		{
			u.amount2 =  MSCC->USU1.unitTotal;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_TOTAL_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);

		//上行流量
		u.unit = RB_UNIT_CODE_UP_BYTES;
		if (!MSCC->USU0.filterFlag)
		{
			u.amount = MSCC->USU0.unitInput;
		}

		if (!MSCC->USU1.filterFlag)
		{
			u.amount2 =  MSCC->USU1.unitInput;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_UP_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]", u.amount2);

		//下行流量
		u.unit = RB_UNIT_CODE_DOWN_BYTES;
		if (!MSCC->USU0.filterFlag)
		{
			u.amount = MSCC->USU0.unitOutput;
		}

		if (!MSCC->USU1.filterFlag)
		{
			u.amount2 =  MSCC->USU1.unitOutput;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_DOWN_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]",u.amount2);
		if ((MSCC->USU1.unitTotal > 0 || MSCC->USU1.duration > 0) && !MSCC->USU1.filterFlag)
		{
			rbr.cell2 = MSCC->USU1.userLocInfoMation.cellid;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell2[%s]", rbr.cell2.c_str());

			rbr.location2 = MSCC->USU1.userLocInfoMation.nrCellId;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location2[%s]", rbr.location2.c_str());
			//MSC
			rbr.msc2 = MSCC->USU1.userLocInfoMation.msc;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc2[%s]", rbr.msc2.c_str());
			//LAC
			rbr.lac2 = MSCC->USU1.userLocInfoMation.tac;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lac2[%s]", rbr.lac2.c_str());

			MSCC->userLocInfoMation = MSCC->USU1.userLocInfoMation;
		}

		//累计总使用量
		if (!MSCC->USU0.filterFlag)
		{
			TUSU.duration += MSCC->USU0.duration;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

        if (!MSCC->USU0.filterFlag)
		{
			TUSU.unitTotal += MSCC->USU0.unitTotal;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

        if (!MSCC->USU0.filterFlag)
		{
			TUSU.unitInput += MSCC->USU0.unitInput;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

        if (!MSCC->USU0.filterFlag)
		{
			TUSU.unitOutput += MSCC->USU0.unitOutput;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);

		if ((MSCC->USU1.unitTotal > 0 || MSCC->USU1.duration > 0) && !MSCC->USU1.filterFlag)
		{
			//累计总使用量
			if (!MSCC->USU1.filterFlag)
			{
				TUSU.duration += MSCC->USU1.duration;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

            if (!MSCC->USU1.filterFlag)
			{
				TUSU.unitTotal += MSCC->USU1.unitTotal;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

            if (!MSCC->USU1.filterFlag)
			{
				TUSU.unitInput += MSCC->USU1.unitInput;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

            if (!MSCC->USU1.filterFlag)
			{
				TUSU.unitOutput += MSCC->USU1.unitOutput;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);
		}
	}
	else //3组usu及以上
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usu size[%d]", MSCC->VecUSU.size());
		PushUSV(MSCC->VecUSU, domain, u);
		TotalUSUAdd(TUSU, MSCC->VecUSU);
	}

	sprintf(value, "%lld", MSCC->ratingGroup);
	totalusu.rating_group = value;
	//B03
	//时长
	totalusu.unit = RB_UNIT_CODE_SECOND;
	totalusu.amount = TUSU.duration;
	domain.dbv.push_back(totalusu);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);
	//总流量
	totalusu.unit = RB_UNIT_CODE_TOTAL_BYTES;
	totalusu.amount = TUSU.unitTotal;
	domain.dbv.push_back(totalusu);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_TOTAL_BYTES);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);

	//上行流量
	totalusu.unit = RB_UNIT_CODE_UP_BYTES;
	totalusu.amount = TUSU.unitInput;
	domain.dbv.push_back(totalusu);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_UP_BYTES);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);

	//上行流量
	totalusu.unit = RB_UNIT_CODE_DOWN_BYTES;
	totalusu.amount = TUSU.unitOutput;
	domain.dbv.push_back(totalusu);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]", totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]", RB_UNIT_STR_DOWN_BYTES);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]", totalusu.amount);

	rbr.tariff_info = 1;
	rbr.rating_info = 1;
	rbr.balance_info = 1;
	rbr.balance_query = 1;
	rbr.accumlator_info = 1;

	if (1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}

	// 回退需要给批价出单, 非回退批价自己生成
	ext.kv["ticketId"] = base->smExt.kv["ticketId"];
	ext.kv["ticketSeq"] = base->smExt.kv["ticketSeq"];

	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;
	ext.kv["RTI"] = szRTI;
	ext.kv["EVT"] = szEVT;
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["switchId"] = bizMsg->m_switchId;
	sprintf(value, "%d", bizMsg->m_ilatnId);
	ext.kv["LatnId"] = value;
	ext.kv["operListId"] = bizMsg->m_operListId;
	ext.kv["opertype"] = bizMsg->m_operType;
	ext.kv["dNNID"] = data->dNNID;
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	ext.kv["RBR_SOURCE"] = "REQ";
	uhd.checkKey = bizMsg->m_strCheckKey;
	ext.kv["CDR_STR_BATCH_ID"] = bizMsg->m_sBatchId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
	ext.kv["CDR_STR_CUT_TYPE"] = MSCC->triggerInfoMation.triggerType;

	sprintf(value, "%ld", bizMsg->timestampCCR);
	rbr.pre_dtime = 0;
	rbr.sess_start_time = value;

	ext.kv["CDR_PUB_STR_HOSTID"] = base->topology;
	ext.kv["CDR_PUB_STR_CHARGED_PARTY"] = base->subUnified;
	//2100预付费, 非2100后付费，预付费写1, 后付费写0
	if ("2100" == bizMsg->m_payMentMode)
	{
		ext.kv["RE_INT_PAY_FLAG"] = "1";
	}
	else
	{
		ext.kv["RE_INT_PAY_FLAG"] = "0";

	}
	sprintf(value, "%ld", userInfo->servID);
	ext.kv["CDR_PUB_LNG_SERVID"] = value;
	sprintf(value, "%ld", userInfo->custID);
	ext.kv["CDR_PUB_LNG_CUSTID"] = value;
	sprintf(value, "%ld", userInfo->lnAcctID);
	ext.kv["CDR_LNG_ACCT_ID"] = value;
	ext.kv["OCP_STR_SESSION_ID"] = bizMsg->m_childsessionID;
	sprintf(value, "%d", base->subscription.carriers);
	ext.kv["RE_INT_SUB_OPERATOR"] = value;
	ext.kv["CDR_LNG_DISCOUNT_FEE"] = "0";
	sprintf(value, "0%d", base->subscription.area);
	ext.kv["RE_STR_SUB_AREA"] = value;

	sprintf(value, "%d", bizMsg->m_ilatnId);
	ext.kv["CDR_INT_LATN_ID"] = value;
	string strImsi = base->smExt.kv["IMSI"];
	if (strImsi.find("imsi-") != string::npos)
	{
		strImsi = strImsi.substr(5);
	}
	ext.kv["SM_STR_IMSI"] = strImsi;
	ext.kv["OCP_STR_CHARGING_ID"] = rbr.charging_id;
	ext.kv["OCP_STR_NF_ADDR"] = data->ServingNFIDInfo.pduIPv4Address;
	ext.kv["OCP_STR_NF_ADDR_IPV6"] = data->ServingNFIDInfo.pduIPv6Address;
	ext.kv["OCP_STR_PDU_TYPE"] = data->pduType;
	ext.kv["OCP_STR_PDU_ADDR"] =  base->nFIPv4Address;
	ext.kv["OCP_STR_PDU_ADDR_IPV6"] = base->nFIPv6Address;
	sprintf(value, "%d", data->qi5g);
	ext.kv["OCP_INT_PDU_5QI"] = value;
	sprintf(value, "%ld", MSCC->ratingGroup);
	ext.kv["OCP_LNG_RATING_GROUP"] = value;
	ext.kv["OCP_STR_SERVED_GPSI"] = data->servedGPSI;
	ext.kv["OCP_STR_DNNID"] = data->dNNID;
	sprintf(value, "%d", data->qosId);
	ext.kv["OCP_INT_PDU_QOS_ID"] = value;
	sprintf(value, "%d", bizMsg->m_iRollFlag);
	ext.kv["SOURCE_FILE_TYPE"] = value;
	ext.kv["CDR_LNG_AMBRUL"] = data->AuthorizedSessionAMBR.uplink;
	ext.kv["CDR_LNG_AMBRDL"] = data->AuthorizedSessionAMBR.downlink;
	ext.kv["OCP_STR_PDU_SESSION_ID"] = data->pduSessionID;
	ext.kv["CDR_QUOTA_MANAGE_INDICATOR"] = bizMsg->m_offline == 1 ? "OFFLINE_CHARGING": "ONLINE_CHARGING";
	sprintf(value, "%d", bizMsg->m_requestNumber);
	ext.kv["OCP_INT_REQ_NBR"] = value;
	ext.kv["CDR_CNPLMN"]=base->smExt.kv["servingCNPlmnId"];

	// R3019 转为16进制后赋值
	char hexMsc[32] = {0};
	long mscVal = atol(rbr.msc.c_str());
	DCCommonIF::DECTOHEX(mscVal, hexMsc, sizeof(hexMsc));
	ext.kv["R3019"] = hexMsc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose R3019[%s], SOURCE[%s]", ext.kv["R3019"].c_str(), rbr.msc.c_str());

	if (atoi(ext.kv["5gusu"].c_str()) > 0)
	{
		ExtUSUInsert(MSCC->VecUSU, ext.kv, base, iRoamType, szvisitArea, bizMsg);
	}
	else
	{
		ext.kv["5gusu"] = "0";
		ExtUSUInsert(MSCC->USU0, ext.kv, base, iRoamType, szvisitArea, bizMsg, 0);
		if ("1" == ext.kv["cutcdr_flag"])
		{
			ExtUSUInsert(MSCC->USU1, ext.kv, base, iRoamType, szvisitArea, bizMsg, 1);
		}
	}

	if (MSCC->VecUSU.size() > 0)
	{
		string sLastUseOfTime = MSCC->VecUSU[0].lastUseOfTime;
		std::vector<SCCRDataUnit >::iterator itusu = MSCC->VecUSU.begin();
		for ( ; itusu != MSCC->VecUSU.end(); itusu++)
		{
			if (strcmp(sLastUseOfTime.c_str(), itusu->lastUseOfTime.c_str()) < 0)
			{
				sLastUseOfTime = itusu->lastUseOfTime;
			}
		}

		DCServEvtCheck::instance()->PushBackCacheVecKey("END_TIME", sLastUseOfTime);
	}
	else
	{
		if (strcmp(MSCC->USU0.lastUseOfTime.c_str(), MSCC->USU1.lastUseOfTime.c_str()) > 0)
		{
			DCServEvtCheck::instance()->PushBackCacheVecKey("END_TIME", MSCC->USU0.lastUseOfTime);
		}
		else
		{
			DCServEvtCheck::instance()->PushBackCacheVecKey("END_TIME", MSCC->USU1.lastUseOfTime);
		}
	}

	//增加5G RG过来规则判断
	DCAnsPara *spara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	if(1 == spara->Get5GPara()->iRgFilterSwitch)
	{
		int iRet = 0;
		std::map<int, std::vector<ST_ParSmRgCondtion> > parSmRgCondition;

		SCCRBase* tmpBase = (SCCRBase*)bizMsg->m_base;
		char szRatingGroup[128] = {0};
		sprintf(szRatingGroup, "%lld", bizMsg->m_ratingGroup);
		tmpBase->smExt.kv["ratingGroup"] =  szRatingGroup;
		//查询 PAR_SM_FILTER_CONDITION 表获取对应的规则
		GetParSmFilterRgCondition(bizMsg, atoi(bizMsg->m_operListId.c_str()), parSmRgCondition);

		if(-1 == Deal5GRgFilterRule(parSmRgCondition, bizMsg))
		{
			return SM_5G_RG_PAR_SM_FILTER_CONDITION;
		}
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch (exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char *)m_en.data();
	bizMsg->m_vectorMsg.push_back(bizMsg->data);

	return RET_SUCCESS;
}

int DCReq5G::InsertChildSession(STBizMsg* bizMsg, SUSU *MSCC, SSessionCacheData5G& cacheData5G)
{
	time_t et;
	time(&et);
	long lnNextCCRTime = et + cacheData5G.valid_time; //当前时间用于会话超时

	char userlocal[64] = {0};
	sprintf(userlocal,"%s~%s~%s~%s",MSCC->userLocInfoMation.mcc.c_str(), MSCC->userLocInfoMation.mnc.c_str(),
				MSCC->userLocInfoMation.tac.c_str(), MSCC->userLocInfoMation.nrCellId.c_str());

	std::string strFirstUseOfTime = "";
	std::string strLastUseOfTime = "";
	if (MSCC->VecUSU.size() > 0)
	{
		strFirstUseOfTime = MSCC->VecUSU[0].firstUseOfTime;
		for (std::vector<SCCRDataUnit>::iterator itusu = MSCC->VecUSU.begin(); itusu != MSCC->VecUSU.end(); itusu++)
		{
			if (strcmp(strFirstUseOfTime.c_str(), itusu->firstUseOfTime.c_str()) > 0)
			{
				strFirstUseOfTime = itusu->firstUseOfTime;
			}
		}
	}
	else
	{
		if (strcmp(MSCC->USU0.firstUseOfTime.c_str(), MSCC->USU1.firstUseOfTime.c_str()) < 0)
		{
			strFirstUseOfTime = MSCC->USU0.firstUseOfTime;
		}
		else
		{
			strFirstUseOfTime = MSCC->USU1.firstUseOfTime;
		}
	}

	if (MSCC->VecUSU.size() > 0)
	{
		strLastUseOfTime = MSCC->VecUSU[0].lastUseOfTime;
		std::vector<SCCRDataUnit >::iterator itusu = MSCC->VecUSU.begin();
		for ( ; itusu != MSCC->VecUSU.end(); itusu++)
		{
			if (strcmp(strLastUseOfTime.c_str(), itusu->lastUseOfTime.c_str()) < 0)
			{
				strLastUseOfTime = itusu->lastUseOfTime;
			}
		}
	}
	else
	{
		if (strcmp(MSCC->USU0.lastUseOfTime.c_str(), MSCC->USU1.lastUseOfTime.c_str()) > 0)
		{
			strLastUseOfTime = MSCC->USU0.lastUseOfTime;
		}
		else
		{
			strLastUseOfTime = MSCC->USU1.lastUseOfTime;
		}
	}

	char sTimeVal[256] = {0};
	long long llTime = 0;
	if (!strFirstUseOfTime.empty())
	{
		llTime = atoll(strFirstUseOfTime.c_str());
		TimestampToDate(llTime, sTimeVal);
		strFirstUseOfTime = sTimeVal;
	}

	if (!strLastUseOfTime.empty())
	{
		llTime = atoll(strLastUseOfTime.c_str());
		TimestampToDate(llTime, sTimeVal);
		strLastUseOfTime = sTimeVal;
	}

	cacheData5G.lastCCRTime = atol(strLastUseOfTime.c_str());

	ocs::SCCRBase* base = (ocs::SCCRBase*)bizMsg->m_base;
	SUserInfo* userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	ocs::SCCR5GInfo* data = (ocs::SCCR5GInfo*)bizMsg->m_extend;
	char szValue[BIZ_TEMP_LEN_512] = {0};
	UDBSQL* pExec = bizMsg->m_dbm->GetSQL(_5G_CCR_InsertChildSession); //i_5g_childsession
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID); // OCP_STR_MAIN_SESSION_ID
		pExec->BindParam(2, bizMsg->m_childsessionID); // OCP_STR_SESSION_ID
		pExec->BindParam(3, lnNextCCRTime); //SM_LNG_TIME_TO_NEXT_CCR
		pExec->BindParam(4, (long)cacheData5G.valid_time); //SM_LNG_MSCC_VALIDITY_TIME
		pExec->BindParam(5, (long)cacheData5G.conf_QUOTA_CONSUMPTION_TIME);//SM_LNG_QUOTA_CONSUME_TIME
		pExec->BindParam(6, (long)cacheData5G.conf_QUOTA_HOLDING_TIME);//SM_LNG_QUOTA_HOLDING_TIME
		pExec->BindParam(7, (long)cacheData5G.conf_TIME_QUOTA_THRESHOLD);//SM_LNG_TIME_QUOTA_THRESHOLD
		pExec->BindParam(8, (long)cacheData5G.conf_VOLUME_QUOTA_THRESHOLD);//SM_LNG_VOLUME_QUOTA_THRESHOLD
		pExec->BindParam(9, (long)cacheData5G.conf_VOLUME_QUOTA_THRESHOLD1);//SM_LNG_VOLUME_QUOTA_THRESHO_1
		pExec->BindParam(10, (long)bizMsg->m_ratingGroup); //OCP_LNG_RATING_GROUP
		pExec->BindParam(11, TORB_ACTION); //SM_INT_SESSION_STATUS
		pExec->BindParam(12, 0); //SM_INT_RAR_SEND_FLAG
		pExec->BindParam(13, cacheData5G.lastCCRTime);//RE_LNG_LAST_CCR_TIME
		pExec->BindParam(14, cacheData5G.sz_RE_STR_SUB_VISIT_AREA); //RE_STR_SUB_VISIT_AREA,
		pExec->BindParam(15, cacheData5G.n_RE_INT_ROAM_TYPE); //RE_INT_ROAM_TYPE,
		pExec->BindParam(16, RB_UNIT_CODE_TOTAL_BYTES); // RE_INT_LAST_GSU_UNIT
		pExec->BindParam(17, (long)cacheData5G.rsu_totalOCtets); // RE_LNG_LAST_GSU_TOTAL_OCT
		pExec->BindParam(18, (long)cacheData5G.rsu_duration); // RE_LNG_LAST_GSU_TIME
		pExec->BindParam(19, (long)0); // SM_LNG_ALL_USU_TIME
		pExec->BindParam(20, (long)0); // SM_LNG_ALL_USU_TOTAL_OCT
		pExec->BindParam(21, (long)0); // SM_LNG_ALL_USU_INPUT_OCT
		pExec->BindParam(22, (long)0); // SM_LNG_ALL_USU_OUTPUT_OCT
		pExec->BindParam(23, base->subscription.phone.c_str()); // RE_STR_SUB_NBR
		sprintf(szValue, "0%d", base->subscription.area);
		pExec->BindParam(24, szValue); // RE_STR_SUB_AREA
		pExec->BindParam(25, userInfo->ilatnid); // CDR_INT_LATN_ID
		pExec->BindParam(26, userInfo->servID); // CDR_PUB_LNG_SERVID
		pExec->BindParam(27, atoi(base->smExt.kv["switchId"].c_str())); // CDR_INT_SWITCH_ID
		sprintf(szValue, "%d", data->chargingId);
		pExec->BindParam(28, szValue); // OCP_STR_CHARGING_ID
		pExec->BindParam(29, userInfo->lnAcctID); // CDR_LNG_ACCT_ID
		pExec->BindParam(30, data->pduType.c_str()); // OCP_STR_PDU_TYPE
		std::string strPduIpAddr = data->pduType == "IPv4" ? data->ServingNFIDInfo.pduIPv4Address : data->ServingNFIDInfo.pduIPv6Address;
		pExec->BindParam(31, strPduIpAddr.c_str()); // OCP_STR_PDU_ADDR
		pExec->BindParam(32, atol(strFirstUseOfTime.c_str())); // RE_LNG_CALL_START_TIME
		pExec->BindParam(33, atol(strLastUseOfTime.c_str())); // RE_LNG_CURRENT_CCR_TIME
		pExec->BindParam(34, userlocal); // CDR_USER_LOCATION_INFO
		pExec->BindParam(35, MSCC->rATType.c_str()); //OCP_STR_RATTYPE
		pExec->BindParam(36, MSCC->userLocInfoMation.msc.c_str()); // CDR_STR_MSC
		pExec->BindParam(37, MSCC->userLocInfoMation.cellid.c_str()); // CDR_STR_CELLID
		pExec->BindParam(38, base->topology.c_str()); // CDR_PUB_STR_HOSTID
		pExec->Execute();
		pExec->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		string strSql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(strSql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", strSql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "insert ok,child session[%s]", bizMsg->m_childsessionID);

	return RET_SUCCESS;
}

int DCReq5G::UpdateChildSession(STBizMsg* bizMsg, SUSU *MSCC, SSessionCacheData5G& cacheData5G, bool bAllUSUFiltered)
{
	ocs::SCCR5GInfo* data = (ocs::SCCR5GInfo*)bizMsg->m_extend;
	time_t et;
	time(&et);
	long lnNextCCRTime = et + cacheData5G.valid_time; //当前时间用于会话超时
	char userlocal[64] = {0};
	sprintf(userlocal,"%s~%s~%s~%s",MSCC->userLocInfoMation.mcc.c_str(), MSCC->userLocInfoMation.mnc.c_str(),
				MSCC->userLocInfoMation.tac.c_str(), MSCC->userLocInfoMation.nrCellId.c_str());

	std::string strLastUseOfTime = "";
	if (MSCC->VecUSU.size() > 0)
	{
		strLastUseOfTime = MSCC->VecUSU[0].lastUseOfTime;
		std::vector<SCCRDataUnit >::iterator itusu = MSCC->VecUSU.begin();
		for ( ; itusu != MSCC->VecUSU.end(); itusu++)
		{
			if (strcmp(strLastUseOfTime.c_str(), itusu->lastUseOfTime.c_str()) < 0)
			{
				strLastUseOfTime = itusu->lastUseOfTime;
			}
		}
	}
	else
	{
		if (strcmp(MSCC->USU0.lastUseOfTime.c_str(), MSCC->USU1.lastUseOfTime.c_str()) > 0)
		{
			strLastUseOfTime = MSCC->USU0.lastUseOfTime;
		}
		else
		{
			strLastUseOfTime = MSCC->USU1.lastUseOfTime;
		}
	}

	char sTimeVal[256] = {0};
	long long llTime = 0;
	if (!strLastUseOfTime.empty())
	{
		llTime = atoll(strLastUseOfTime.c_str());
		TimestampToDate(llTime, sTimeVal);
		strLastUseOfTime = sTimeVal;
	
	}

	cacheData5G.lastCCRTime = atol(strLastUseOfTime.c_str());
	
	UDBSQL* pUpdate = bizMsg->m_dbm->GetSQL(_5G_CCR_UpdateChildSession_RG); //u_5g_ccr_chlidsession_rg
	try
	{
		pUpdate->DivTable(bizMsg->m_sessionID);
		pUpdate->UnBindParam();
		pUpdate->BindParam(1, bAllUSUFiltered ? RECVRB_ACTION : TORB_ACTION); //SM_INT_SESSION_STATUS
		pUpdate->BindParam(2, lnNextCCRTime); //SM_LNG_TIME_TO_NEXT_CCR
		pUpdate->BindParam(3, atol(strLastUseOfTime.c_str())); // RE_LNG_CURRENT_CCR_TIME
		pUpdate->BindParam(4, cacheData5G.lastCCRTime); //RE_LNG_LAST_CCR_TIME
		pUpdate->BindParam(5, cacheData5G.valid_time); //SM_LNG_MSCC_VALIDITY_TIME
		pUpdate->BindParam(6, cacheData5G.rsu_duration); // RE_LNG_LAST_GSU_TIME
		pUpdate->BindParam(7, cacheData5G.rsu_totalOCtets); // RE_LNG_LAST_GSU_TOTAL_OCT
		pUpdate->BindParam(8, userlocal); // CDR_USER_LOCATION_INFO
		pUpdate->BindParam(9, MSCC->rATType.c_str()); //OCP_STR_RATTYPE
		pUpdate->BindParam(10, MSCC->userLocInfoMation.msc.c_str()); // CDR_STR_MSC
		pUpdate->BindParam(11, MSCC->userLocInfoMation.cellid.c_str()); // CDR_STR_CELLID
		pUpdate->BindParam(12, bizMsg->m_sessionID);
		pUpdate->BindParam(13, bizMsg->m_childsessionID);
		pUpdate->Execute();
		pUpdate->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update chilid ok,session[%s]", bizMsg->m_childsessionID);
	}
	catch (UDBException& e)
	{
		string sql;
		pUpdate->Connection()->Rollback();
		pUpdate->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "update execption[%s],SQL[%s],SQLCODE[%d]", 
							e.ToString(), e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}

//获取流量、时长动态步长信息
int DCReq5G::GetDynamicStepInfo5G(DCDBManer* pdbm, long serv_id, int nLatn, long ratingGroup, long lnStartTime, STDynamicStepInfo5G& stDynamicStepInfo5G)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","DCReq5G::GetDynamicStepInfo5G begin, serv_id[%ld], latn[%d], ratingGroup[%lld]", serv_id, nLatn, ratingGroup);

	stDynamicStepInfo5G.clear();

	char strEffTime[16] = {0};
	char strExpTime[16] = {0};

	//select Volume_Limit, Time_Limit, to_char(Eff_Date, 'yyyymmddhh24miss') EffDate, to_char(Exp_Date, 'yyyymmddhh24miss') ExpDate from dynamic_limit_inst where Acc_Nbr=? and Lan_Id = ? and Rating_group = ? and Status_Cd = 0;
	UDBSQL* pQuery =  pdbm->GetSQL(_5G_CCR_SelectDynamicStep);//q_5g_dynamic_step_select 动态步长查询
	if (!pQuery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "","getSql [q_5g_dynamic_step_select] fail");
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	try
	{
		pQuery->UnBindParam();
		pQuery->BindParam(1, serv_id);
		pQuery->BindParam(2, nLatn);
		pQuery->BindParam(3, ratingGroup);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, stDynamicStepInfo5G.lnVolumeLimit);
			pQuery->GetValue(2, stDynamicStepInfo5G.lnTimeLimit);
			pQuery->GetValue(3, strEffTime);
			pQuery->GetValue(4, strExpTime);

			//时间过滤
			if ((atol(strEffTime) <= lnStartTime) && (lnStartTime <= atol(strExpTime)))
			{
				stDynamicStepInfo5G.bGetSucceed = true;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","DCReq5G::GetDynamicStepInfo5G, StartTime Filter fail, StartTime[%ld], Eff_Date[%s], Exp_Date[%s]", lnStartTime, strEffTime, strExpTime);
			}
		}
	}
	catch(UDBException& e)
	{
		string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", sql.c_str());

		return SM_OCP_UNABLE_TO_COMPLY;
	}


	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","DCReq5G::GetDynamicStepInfo5G end, get result bGetSucceed[%d], lnVolumeLimit[%ld], lnTimeLimit[%ld]", stDynamicStepInfo5G.bGetSucceed, stDynamicStepInfo5G.lnVolumeLimit, stDynamicStepInfo5G.lnTimeLimit);
	return 0;
}

int DCReq5G::PushUSV(std::vector<ocs::SCCRDataUnit>& vecusu, ocs::rbdomain& domain, ocs::usu& u)
{
	for(int iseq = 0; iseq < vecusu.size(); iseq++)
	{
		if (!vecusu[iseq].filterFlag)
		{
			u.sreq = iseq;
			//时长
			u.unit = RB_UNIT_CODE_SECOND;
			u.amount = vecusu[iseq].duration;
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%d], amount[%ld] seq[%d]", u.unit, u.amount, u.sreq);
			
			//总流量
			u.unit = RB_UNIT_CODE_TOTAL_BYTES;
			u.amount = vecusu[iseq].unitTotal;
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%d], amount[%ld] seq[%d]", u.unit, u.amount, u.sreq);
			
			//上行流量
			u.unit = RB_UNIT_CODE_UP_BYTES;
			u.amount = vecusu[iseq].unitInput;
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%d], amount[%ld] seq[%d]", u.unit, u.amount, u.sreq);
			
			//下行流量
			u.unit = RB_UNIT_CODE_DOWN_BYTES;
			u.amount = vecusu[iseq].unitOutput;
			domain.usv.push_back(u);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%d], amount[%ld] seq[%d]", u.unit, u.amount, u.sreq);
		}
	}

	return 0;
}

int DCReq5G::TotalUSUAdd(ocs::SCCRDataUnit& TUSU, const std::vector<ocs::SCCRDataUnit>& vecusu)
{
	for(int iseq = 0; iseq < vecusu.size(); iseq++)
	{
		if (!vecusu[iseq].filterFlag)
		{
			//累计总使用量
			TUSU.duration += vecusu[iseq].duration;
			TUSU.unitTotal += vecusu[iseq].unitTotal;
			TUSU.unitInput += vecusu[iseq].unitInput;
			TUSU.unitOutput += vecusu[iseq].unitOutput;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);

	return 0;
}

int DCReq5G::ExtUSUInsert(const std::vector<ocs::SCCRDataUnit>& vecusu, std::map<std::string, std::string>& ext, ocs::SCCRBase* base, int roamType, const char* visitArea, STBizMsg* bizMsg)
{
	char stmp[32] = {0};
	char samount[128] = {0};
	char szNewLocInfo[256] = {0};
	ocs::SCCR5GInfo *data = (SCCR5GInfo *)bizMsg->m_extend;
	if (!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find SCCR5GInfo");
		return -1;
	}

	int iusedSeq = 0;
	for(int iseq = 0; iseq < vecusu.size(); iseq++)
	{
		if (!vecusu[iseq].filterFlag)
		{
			sprintf(stmp, "USUAMOUNT%d", iusedSeq);
			sprintf(samount, "%d|%ld|%ld|%ld", vecusu[iseq].duration, vecusu[iseq].unitTotal, vecusu[iseq].unitInput, vecusu[iseq].unitOutput);
			ext[stmp] = samount;

			char sTimeVal[256] = {0};
			long long llTime = atoll(vecusu[iseq].firstUseOfTime.c_str());
			TimestampToDate(llTime, sTimeVal);
			long lnCallStartTime = atol(sTimeVal);

			llTime = atoll(vecusu[iseq].lastUseOfTime.c_str());
			TimestampToDate(llTime, sTimeVal);
			long lnCallEndTime = atol(sTimeVal);		

			sprintf(stmp, "NewLocInfo%d", iusedSeq);
			sprintf(szNewLocInfo, "%s|%s|%s|%s|%s~%s~%s~%s|%d||%s|%s|%s|%s|%ld|%ld",
					data->ServingNFIDInfo.pduIPv4Address.c_str(), vecusu[iseq].userLocInfoMation.cellid.c_str(), vecusu[iseq].userLocInfoMation.msc.c_str(), vecusu[iseq].userLocInfoMation.tac.c_str(),
					vecusu[iseq].userLocInfoMation.mcc.c_str(), vecusu[iseq].userLocInfoMation.mnc.c_str(), vecusu[iseq].userLocInfoMation.tac.c_str(),
					vecusu[iseq].userLocInfoMation.nrCellId.c_str(), roamType, visitArea,
					bizMsg->m_eventType, bizMsg->m_visit->sector_id, vecusu[iseq].rATType.c_str(), 
					lnCallStartTime, lnCallEndTime);
			ext[stmp] = szNewLocInfo;
			iusedSeq++;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "unfilter usu num [%d]", iusedSeq);
	sprintf(stmp, "%ld", iusedSeq);
	ext["5gusu"] = stmp;// 区分2组以上
	
	return 0;
}

int DCReq5G::ExtUSUInsert(ocs::SCCRDataUnit& usu, std::map<std::string, std::string>& ext, ocs::SCCRBase* base,  int roamType, const char* visitArea, STBizMsg* bizMsg, int iSeq)
{
	char stmp[32] = {0};
	char samount[128] = {0};
	char szNewLocInfo[256] = {0};
	ocs::SCCR5GInfo *data = (SCCR5GInfo *)bizMsg->m_extend;
	if (!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find SCCR5GInfo");
		return -1;
	}

	if (!usu.filterFlag)
	{
		sprintf(stmp, "USUAMOUNT%d", iSeq);
		sprintf(samount, "%d|%ld|%ld|%ld", usu.duration, usu.unitTotal, usu.unitInput, usu.unitOutput);
		ext[stmp] = samount;
		
		char sTimeVal[256] = {0};
		long long llTime = atoll(usu.firstUseOfTime.c_str());
		TimestampToDate(llTime, sTimeVal);
		long lnCallStartTime = atol(sTimeVal);
		
		llTime = atoll(usu.lastUseOfTime.c_str());
		TimestampToDate(llTime, sTimeVal);
		long lnCallEndTime = atol(sTimeVal);
		
		sprintf(stmp, "NewLocInfo%d", iSeq);
		sprintf(szNewLocInfo, "%s|%s|%s|%s|%s~%s~%s~%s|%d||%s|%s|%s|%s|%ld|%ld",
				data->ServingNFIDInfo.pduIPv4Address.c_str(), usu.userLocInfoMation.cellid.c_str(), usu.userLocInfoMation.msc.c_str(), usu.userLocInfoMation.tac.c_str(),
				usu.userLocInfoMation.mcc.c_str(), usu.userLocInfoMation.mnc.c_str(), usu.userLocInfoMation.tac.c_str(),
				usu.userLocInfoMation.nrCellId.c_str(), roamType, visitArea,
				bizMsg->m_eventType, bizMsg->m_visit->sector_id, usu.rATType.c_str(),
				lnCallStartTime, lnCallEndTime);
		ext[stmp] = szNewLocInfo;
	}
	
	return 0;
}

void DCReq5G::TimestampToDate(time_t timestamp, char *pszDate)
{
	//使用线程安全函数localtime_r
	timestamp -= 2208988800L;
	struct tm tmTime ;
	localtime_r(&timestamp, &tmTime);
	sprintf(pszDate, "%04d%02d%02d%02d%02d%02d", (1900 + tmTime.tm_year), (1 + tmTime.tm_mon), tmTime.tm_mday, tmTime.tm_hour, tmTime.tm_min, tmTime.tm_sec);

	return ;
}

int DCReq5G::GetParSmFilterRgCondition(STBizMsg* bizMsg, int iOperListId, std::map<int, std::vector<ST_ParSmRgCondtion> >& parSmRgCondition)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","begin to GetParSmFilterRgCondition, operlistid[%d]", iOperListId);
	std::map<int, std::vector<ST_ParSmRgCondtion> >::iterator iter;

	parSmRgCondition.clear();
	UDBSQL* pQuery =  bizMsg->m_dbm->GetSQL(_5G_GetParSmFilterRgCondition);//5G RG 过滤条件规则查询
	if (!pQuery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "","getSql [q_par_sm_filter_rg_condition] fail");
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	try
	{
		pQuery->UnBindParam();
		pQuery->BindParam(1, iOperListId);
		pQuery->Execute();
		while(pQuery->Next())
		{
			ST_ParSmRgCondtion filterRule;
			pQuery->GetValue(1, filterRule.iGroupId);
			pQuery->GetValue(2, filterRule.szCondtionType);
			pQuery->GetValue(3, filterRule.szItemCode);
			pQuery->GetValue(4, filterRule.szItemValue);
			pQuery->GetValue(5, filterRule.szOperators);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","get rule GroupId[%d], CondtionType[%s],  \
			ItemCode[%s], ItemValue[%s], Operators[%s]", filterRule.iGroupId, filterRule.szCondtionType,  \
			filterRule.szItemCode, filterRule.szItemValue, filterRule.szOperators);
	
			iter = parSmRgCondition.find(filterRule.iGroupId);
			if(iter != parSmRgCondition.end())
			{
				iter->second.push_back(filterRule);
			}
			else
			{
				std::vector<ST_ParSmRgCondtion> vParSmRgCondtion;
				vParSmRgCondtion.push_back(filterRule);
				parSmRgCondition.insert(pair<int, std::vector<ST_ParSmRgCondtion> >(filterRule.iGroupId, vParSmRgCondtion));
			}
		}

		pQuery->Close();
	}
	catch(UDBException& e)
	{
		string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", sql.c_str());

		return SM_OCP_UNABLE_TO_COMPLY;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","end to GetParSmFilterRgCondition");
	return 0;
}

int DCReq5G::Deal5GRgFilterRule(std::map<int, std::vector<ST_ParSmRgCondtion> >& parSmRgCondition,STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","begin to GetParSmFilterRgCondition");

	bool bFilterFlag = false;
	std::map<int, std::vector<ST_ParSmRgCondtion> >::iterator iter;
	for(iter = parSmRgCondition.begin(); iter != parSmRgCondition.end(); iter++)  //遍历多组过滤规则
	{
		std::vector<ST_ParSmRgCondtion>::iterator ruleIter;
		for(ruleIter = iter->second.begin(); ruleIter != iter->second.end(); ruleIter++)  //遍历同组下的过滤规则
		{
			if(!JudeRg5GFilterRule(*ruleIter, bizMsg))
			{
				break;
			}
		}

		if(ruleIter == iter->second.end()) //同组下面的规则是与的关系，需要满足所有的规则都满足才过滤
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","5GFilterRule match, group_id[%d], will filter", iter->first);
			bFilterFlag = true;
			break;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","end to GetParSmFilterRgCondition");
	return bFilterFlag == true ? -1: 0;
}

bool DCReq5G::JudeRg5GFilterRule(ST_ParSmRgCondtion& rgCondition, STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","begin to JudeRg5GFilterRule");

	std::map<std::string, std::string >::iterator iter;
	ocs::SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	std::string strLeftV;
	std::string strRigV;

	//找出左值 ,  找不到默认为不过滤，返回0
	iter = base->smExt.kv.find(rgCondition.szItemCode);
	if(iter == base->smExt.kv.end())
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","can no find ItemCode[%s] from smExt", rgCondition.szItemCode);
		return false;
	}
	else
	{
		strLeftV = iter->second;
	}

	//根据条件类型是否为20，判断右值是否需要从ext里面取出来
	if(strcmp(rgCondition.szCondtionType, "20") == 0)
	{
		iter = base->smExt.kv.find(rgCondition.szItemValue);
		if(iter == base->smExt.kv.end())
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","can no find szItemValue[%s] from smExt", rgCondition.szItemValue);
			return true;
		}
		else
		{
			strRigV = iter->second;
		}
	}
	else
	{
		strRigV = rgCondition.szItemValue;
	}

	bool bRes = false;
	int iOper = atoi(rgCondition.szOperators);
	bRes = CompareString(strLeftV.c_str(), strRigV.c_str(), iOper);
	if(bRes)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","5GFilterRule match, LeftV[%s], RigV[%s], iOper[%d]", strLeftV.c_str(), strRigV.c_str(), iOper);
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","end to JudeRg5GFilterRule");
	return bRes;
}


int DCReq5G::SplitStrInt(const char *pszStr, const char cSplit, std::list<int>& ltStr)
{
	int nValue;
	char *pch;
	char szBuf[100];
	char szTmp[20];

	strcpy(szBuf, pszStr);

	while (1)
	{
		pch = strchr(szBuf, cSplit);
		if (NULL == pch)
		{
			break;
		}

		memset(szTmp, 0, sizeof(szTmp));
		strncpy(szTmp, szBuf, pch - szBuf);
		nValue = atoi(szTmp);
		ltStr.push_back(nValue);
		strcpy(szBuf, pch + 1);
	}

	nValue = atoi(szBuf);
	ltStr.push_back(nValue);

	return 0;
}


int DCReq5G::JudgeExpress(char cValue, const char* pszExpress)
{
	char szValue[10], szExpress[31], *p;
	std::list<int> ltValue;
	int i, nValue, nStartValue, nEndValue;

	memset(szExpress, 0, 31);
	strncpy(szExpress, pszExpress, 30);

	memset(szValue, 0, 10);
	szValue[0] = cValue;
	nValue = atoi(szValue);
	if ((p = strchr(szExpress, '[')))
		*p = ' ';
	if ((p = strchr(szExpress, ']')))
		*p = 0;

	szValue[1] = 0;
	if ((p = strchr(szExpress, '-')))
	{
		szValue[0] = *(p - 1);
		nStartValue = atoi(szValue);
		szValue[0] = *(p + 1);
		nEndValue = atoi(szValue);
		for (i = nStartValue; i <= nEndValue; i++)
			ltValue.push_back(i);
	}
	else
	{
		SplitStrInt(szExpress, ',', ltValue);
	}

	//if(ltValue.end() != find(ltValue.begin(),ltValue.end(),nValue))
	//	return 0;	
	std::list<int>::iterator iter;
	for (iter = ltValue.begin(); iter != ltValue.end(); iter++)

	{
		if (nValue == *iter)
			return 0;
	}

	return -1;
}

int DCReq5G::SplitStr(const char *pszStr, const char cSplit, std::vector<std::string>& vecStr)
{
	//std::string str;
	//char *pch;
	char *pBegin = (char*)pszStr;

	while (pBegin)
	{
		char * pch = strchr(pBegin, cSplit);
		if (NULL == pch)
		{
			vecStr.push_back(pBegin);
			pBegin = NULL;
		}
		else
		{
			vecStr.push_back(std::string(pBegin, (pch - pBegin)));
			pBegin = pch + 1;
		}

	}
	return 0;
}


bool DCReq5G::CompareString(const char *v_a, const char *v_b, int &v_Operator)
{
	char	tmp_b[312];
	char	tmp_a[128];
	char	szExpress[64];
	char *psr;
	std::vector<std::string> ltStr;
	std::vector<std::string>::iterator ltStrIt;

	switch (v_Operator)
	{
	case 0: 	//nop 空操作符
		return true;
	case 10: //=
		return (strcmp(v_a, v_b) == 0);
	case 20: //!=
	case 94:
		return (strcmp(v_a, v_b) != 0);
	case 30: //like
		szExpress[0] = 0;
		if ((psr = strchr((char *)v_b, '[')))
		{
			strcpy(szExpress, psr);
			//		*psr = 0;
		}
		if (strncmp(v_a, v_b, strlen(v_b)) == 0)
		{
			tmp_a[0] = *(v_a + strlen(v_b));
			if ((0 != tmp_a[0]) && (psr = strchr(szExpress, '[')))
			{
				//printf("va=[%s],value=[%c],express=[%s]\n",v_a,tmp_a[0],szExpress);
				if (JudgeExpress(tmp_a[0], szExpress) >= 0)
				{
					return true;
				}
			}
			else
				return true;

			return false;
		}
		else
			return false;
	case 31:	//不相似,not like
		if (!strncmp(v_a, v_b, strlen(v_b)))	//相似
		{
			return false;
		}
		else	//不相似
		{
			return true;
		}
	case 40: //>
		return strcmp(v_a, v_b) > 0;
		//			return ( atol(v_a)>atol(v_b) ); 		
	case 41: //>=		
		return (atol(v_a) >= atol(v_b));
	case 50: //<		
		return strcmp(v_a, v_b) < 0;
		//			return ( atol(v_a)<atol(v_b) );
	case 51: //<=		
		return (atol(v_a) <= atol(v_b));
	case 60: //当前变量值包含在值中
		if (v_b[strlen(v_b) - 1] != ',')
			sprintf(tmp_b, "%s,\0", v_b);
		else
			strcpy(tmp_b, v_b);

		if (0 == strlen(v_a))
			return false;

		sprintf(tmp_a, "%s,", v_a);

		//printf("b = [%s],a = [%s]\n",tmp_b,tmp_a);
		psr = strstr(tmp_b, tmp_a);
		if (psr == NULL)
		{
			return false;
		}
		else
		{
			if (0 != (psr - tmp_b))
			{
				sprintf(tmp_a, ",%s,", v_a);
				psr = strstr(tmp_b, tmp_a);
				if (NULL == psr)
				{
					return false;
				}
				else
				{
					return true;
				}
			}
			return true;
		}
		break;
	case 61: //当前变量值不包含在值中
		if (v_b[strlen(v_b) - 1] != ',')
			sprintf(tmp_b, "%s,\0", v_b);
		else
			strcpy(tmp_b, v_b);

		if (0 == strlen(v_a))
			return true;

		sprintf(tmp_a, "%s,", v_a);

		psr = strstr(tmp_b, tmp_a);
		if (psr == NULL)
			return true;
		else
			return false;
		break;

	case 62:	//包含相似
		SplitStr(v_b, ',', ltStr);	//拆分字段
		strcpy(tmp_a, v_a);
		for (ltStrIt = ltStr.begin(); ltStrIt != ltStr.end(); ++ltStrIt)	//比较每一字段
		{
			if ((*ltStrIt) != "")
			{
				if (!strncmp(tmp_a, (*ltStrIt).c_str(), (*ltStrIt).size())) //相似
				{
					return true;
				}
			}
		}
		return false;

	case 63:	//不包含相似
		SplitStr(v_b, ',', ltStr);	//拆分字段
		strcpy(tmp_a, v_a);
		for (ltStrIt = ltStr.begin(); ltStrIt != ltStr.end(); ++ltStrIt)	//比较每一字段
		{
			if ((*ltStrIt) != "")
			{
				if (!strncmp(tmp_a, (*ltStrIt).c_str(), (*ltStrIt).size())) //相似
				{
					return false;
				}
			}
		}
		return true;

	default:
		return false;
	}
}


