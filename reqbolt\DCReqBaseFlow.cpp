﻿#include "DCReqBaseFlow.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCRFData.h"
#include "TSMPara.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "DCMqProduceServer.h"
#include "ErrorCode.h"
#include "UHead.h"
#include "DCCommonIF.h"
#include "DCEvtCheck.h"

using namespace std;
using namespace ocs;


int DCReqBaseFlow::init()
{
	int ret  =0 ;
	BData* base = new TSMPara();
	ret= DCRFData::instance()->regist("TSMPara",base);
	if(ret)
	{
		return ret;
	}

	//mq初始化
    /*const char *brokers = DCParseXml::Instance()->GetParam("mqservAddr","Common/mq");

	m_producer = new DCMqProduceServer(MQ_ROCKET);
	ret = m_producer->Init(brokers,"reqBoltGroup");
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init producer failed!");
		return -1;
	}
	m_producer->Start();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init producer successful!");*/

	char sz_offline_cdr[8] = {0};
	strncpy(sz_offline_cdr, DCParseXml::Instance()->GetParam("offline_cdr","SM"), sizeof(sz_offline_cdr));
	m_offline_cdr = atoi(sz_offline_cdr);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get offline_cdr [%d]", m_offline_cdr);

	char sz_combinaSwitch[8] = {0};
	strncpy(sz_combinaSwitch, DCParseXml::Instance()->GetParam("combinaSwitch","SM"), sizeof(sz_combinaSwitch));
	m_combinaSwitch = atoi(sz_combinaSwitch);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get combinaSwitch [%d]", m_combinaSwitch);

  	m_pool = DCOBJSetPool::instance();
	m_pool->reg<UHead>();
	m_pool->reg<SCCRBase>();
	m_pool->reg<STBizMsg>();
	m_pool->reg<SCCRVOICE>();
	m_pool->reg<SCCRSMS>();
	m_pool->reg<SCCRDATA>();
	m_pool->reg<SCCRISMP>();
	m_pool->reg<SCCRDSL>();
    m_pool->reg<SCCR5GInfo>();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "register object to pool successful!");

	return 0;
}

int DCReqBaseFlow::process(void* input, void* output)
{
	std::string &recvCCRMsg =  *(std::string *)input;

	DCOBJSet* pset = m_pool->get();
	UHead* uhd = pset->get<UHead>();

	SCCRBase* base = pset->get<SCCRBase>();
	STBizMsg* bizMsg = pset->get<STBizMsg>();

	bizMsg->m_pSendMsg = (std::multimap<string,string>*)output ;
	bizMsg->m_dbm=dbm();

	m_cdrFlag = 0;
	//bizMsg->m_producer = m_producer;
	/*
	for(int i=0;i<5;i++)
	{
		bizMsg->m_topic[i] = m_Topic[i];
	}
	*/
	bizMsg->m_eptType = 1;//如果 请求流程异常，异常类型为1
	bizMsg->m_offlineCdrSwitch = m_offline_cdr;
	bizMsg->m_iCdrRet = RET_SUCCESS;
	bizMsg->m_bExistMSCCFiltered = false;
	bizMsg->m_iOfflineXDREptFlag = 0;

	vector<uint8_t> vectorMsg;
	try
	{
		vectorMsg = HexDecode(recvCCRMsg.c_str(),recvCCRMsg.size());

	    //公共消息反序列化
		m_de.set(vectorMsg);
		m_de.decode(*uhd);
		m_de.decode(*base);

		//std::string skey = base->sessionId + "|" + uhd->uid;
		std::string skey = uhd->uid;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, base->trace);	// 设置号码跟踪

		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "sessionID[%s], uid[%s]", base->sessionId.c_str(), uhd->uid.c_str());

		//打印base内容
		m_print.clear();
		m_print.print(*uhd);
		m_print.print(*base);
		if(uhd->checkKey.size() > 0)
		{
			DCEvtCheck::instance()->SetByCheckKey(uhd->checkKey);
			DCEvtCheck::instance()->Set_ID(DCEvtCheck::instance()->CreateUUID());
			DCEvtCheck::instance()->Set_P("SMR",uhd->uid);
			DCEvtCheck::instance()->Set_ID_T(DCEvtCheck::instance()->CreateTimeStamps());
			bizMsg->m_strCheckKey = DCEvtCheck::instance()->GetCheckKey();
		}

		bizMsg->m_topology = base->topology.c_str();
		bizMsg->m_uid = uhd->uid.c_str();
		bizMsg->m_pSendMsg->insert(pair<string,string>("UID",uhd->uid));

	    bizMsg->m_trace_flag = base->trace;
		bizMsg->m_anstopic = base->anstopic.c_str();
		//bizMsg->m_plugin = (m_flows.back()).start;

		bizMsg->m_operListId = base->smExt.kv["operListId"];
		if (bizMsg->m_operListId.empty())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "operListId empty");
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "sessionId[%s], uid[%s]", base->sessionId.c_str(), uhd->uid.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SERVTYPE(bizMsg->m_serviceContextID), "", "BASE[%s]", m_print.data());
		}
		
		std::multimap<string,string>::iterator iter;
		iter = bizMsg->m_pSendMsg->find("TaskId");
		if(iter != bizMsg->m_pSendMsg->end())
		{
			strcpy(bizMsg->m_taskId,iter->second.c_str());
		}
		
	    string strOffLine = base->smExt.kv["OFFLINE"];
		bizMsg->m_pSendMsg->insert(pair<string,string>("CHECKOFFLINE",strOffLine));
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "CHECKOFFLINE[%s]", strOffLine.c_str());

		if(base->smExt.kv.size() > 0)
		{
			bizMsg->addressIpv6 = base->smExt.kv["PDPAddressIpv6"];
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get PDPAddressIpv6[%s]", bizMsg->addressIpv6.c_str());
		}
	    //业务消息反序列化
		switch(base->ServiceInformation)
		{
			case __IN_Information:
				{
					SCCRVOICE* voice = pset->get<SCCRVOICE>();
					m_de.decode(*voice);
					//打印VOICE内容
					m_print.print(*voice);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "VOICE[%s]",m_print.data());
				}
				break;
			case __P2PSMS_Information:
				{
					SCCRSMS* sms = pset->get<SCCRSMS>();
					m_de.decode(*sms);
					//打印P2PSMS内容
					m_print.print(*sms);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SMS[%s]", m_print.data());
				}
				break;
			case __PS_Information:
				{
					SCCRDATA* data = pset->get<SCCRDATA>();
					m_de.decode(*data);
					//打印DATA内容
					m_print.print(*data);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DATA[%s]", m_print.data());
				}
				break;
            case __5G_Information:
                {
                    SCCR5GInfo* data = pset->get<SCCR5GInfo>();
                    m_de.decode(*data);
                    //打印DATA内内容
                    m_print.print(*data);

                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DATA_5g[%s]", m_print.data());
                }
                break;
			case __ISMP_Information:
				{
					SCCRISMP* ismp = pset->get<SCCRISMP>();
					m_de.decode(*ismp);
					//打印ISMP内容
					m_print.print(*ismp);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ISMP[%s]", m_print.data());
				}
				break;
			case __DSL_Information:
				{
					SCCRDSL* dsl = pset->get<SCCRDSL>();
					m_de.decode(*dsl);
					//打印DSL内容
					m_print.print(*dsl);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DSL[%s]", m_print.data());
				}
				break;
			default:
				break;
		}
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_DECODE_CORE, "","decode failed");
              m_pool->put(pset);
		return ERR_DECODE_CORE;
	}
    int ret = 0;
    ret = call_all(pset, pset);
    if(1 == bizMsg->m_offlineCdrFlag)
    {
		SCCR5GInfo* data = (SCCR5GInfo*)bizMsg->m_extend;

		bizMsg->m_requestType = base->requestType;
        bizMsg->m_ilatnId = atoi(base->smExt.kv["latnId"].c_str());
        bizMsg->m_resultcode = 2001;

        if(SM_SESSION_INITIAL_CODE != bizMsg->m_requestType)
        {
            int nRet = composeOfflineCdrMsg(bizMsg, base, data);
            if(0 != nRet)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_DECODE_CORE, "","composeOfflineCdrMsg failed.");
            }
        }
		else
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "Msg is initial, requestType[%d].", bizMsg->m_requestType);
		}

		string str = bizMsg->data;
		string ansmsg = HexEncode((const uint8_t*)str.c_str(),str.size());
		bizMsg->m_pSendMsg->insert(pair<string,string>("SENDMSG",ansmsg));
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ans msg:%s", ansmsg.c_str());
    }
	else
	{
	    int size = bizMsg->m_vectorMsg.size();
	    std::string strRbr ;
	    std::string rbrmsg;

	    if(size >1)//针对term 多rg有多条RBR消息的情况
	    {
	        for(int i=0;i<size;i++)
	        {
	            strRbr = bizMsg->m_vectorMsg[i];
	            rbrmsg = HexEncode((const uint8_t*)strRbr.c_str(),strRbr.size());
	            bizMsg->m_pSendMsg->insert(pair<string,string>("SENDMSG",rbrmsg));
	            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "rbr msg:%s", rbrmsg.c_str());
	        }
	    }
	    else
	    {
			string str = bizMsg->data;
			rbrmsg = HexEncode((const uint8_t*)str.c_str(),str.size());
			bizMsg->m_pSendMsg->insert(pair<string,string>("SENDMSG",rbrmsg));
			if (ret == RET_OVER)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "cca msg:%s", rbrmsg.c_str());
				if (bizMsg->m_requestType == SM_SESSION_XDR_CODE)
				{
					DCEvtCheck::instance()->Push_O("5GXdrA", bizMsg->m_uid);
				}
				else if (bizMsg->m_offline == 0)
				{
					DCEvtCheck::instance()->Push_O("ChfpA", bizMsg->m_uid);
				}
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "rbr msg:%s", rbrmsg.c_str());
			}
		}

	    char stmp[12] = {0};
	    sprintf(stmp, "%d", bizMsg->m_resultcode);
	    bizMsg->m_pSendMsg->insert(pair<string,string>("ResultCode",stmp));

	    // 组装StaBolt消息
	    composeStaMsg(bizMsg, ret);
	}

    m_pool->put(pset);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "end reqbase flow :%d", ret);

    return ret;
}

int DCReqBaseFlow::call_all(void* input, void* output)
{
	STFlowItem* iflow = &m_flows.front();
    int ret = 0;
	void* mid = input;
	std::map<std::string, STFlowItem*>::iterator ntflow;
	while(iflow)
	{
		ret = iflow->start->call(mid, output);
    	if(ret == RET_SUCCESS)
		{
			ntflow = iflow->next.find("OK");
		}
		else if(ret == RET_CDR)
		{
			m_cdrFlag = 1;
			ntflow = iflow->next.find("CDR");
		}
		else if(ret == RET_OVER || ret == OFF_LINE)
		{
			ntflow = iflow->next.find("OVER");
		}
		else if(ret == RET_CONTINUE)
		{
			ntflow = iflow->next.find("CONTINUE");
		}
		else if(ret == RET_AOC)
		{
			ntflow = iflow->next.find("AOC");
		}
		else if (ret == RET_NOT_NEED_ANS)
		{
			ntflow = iflow->next.end();
		}
		else
		{
			ntflow = iflow->next.find("FAIL");
		}

		if(ntflow == iflow->next.end())
		{
			iflow = NULL;
		}
		else
		{
			iflow = ntflow->second;
			mid = output;
		}
	}
	return ret;
}

int DCReqBaseFlow::composeStaMsg(STBizMsg* bizMsg, int nRet)
{

	char value[256] = {0};
	ocs::SComHead pSComhead;
	ocs::StatRecord pStatRecord;
	ocs::RatingMessageInfo_t bodySM;
	bool checkSum = false;
	pSComhead.checkKey = bizMsg->m_strCheckKey;
	pSComhead.AckEndBoltFlag = 0;
	pSComhead.collectid = 0;
	if(bizMsg->m_ilatnId == 0 && bizMsg->m_userinfo)
	{
		bizMsg->m_ilatnId = bizMsg->m_userinfo->ilatnid;
	}
	pSComhead.latnid = bizMsg->m_ilatnId;
	pSComhead.operlistid = atoi(bizMsg->m_operListId.c_str());
	pSComhead.switchid = atoi(bizMsg->m_switchId.c_str());
	pSComhead.opertype = bizMsg->m_operType;
	pSComhead.sourceid = atol(bizMsg->m_sourceId.c_str());
	pSComhead.recordid = 0;
	pSComhead.sourcefile = "";
	pSComhead.m_sProcerName = "B";
	pSComhead.msgtype = "idx";
	pSComhead.epttype = 0;
	pSComhead.resultcode = bizMsg->m_resultcode;
	pSComhead.BatchNo = bizMsg->m_sBatchId;
	pSComhead.uid = bizMsg->m_uid;
	pSComhead.trace = bizMsg->m_trace_flag;
	pSComhead.modid = 0;
	pSComhead.ip = "";
	if(bizMsg->m_requestType == SM_SESSION_EVENT_CODE || bizMsg->m_requestType == SM_SESSION_XDR_CODE)
	{
		pSComhead.ext["IMPORT_P"] = "File";
	}
	else
	{
		pSComhead.ext["IMPORT_P"] = "Msg";
	}

	pSComhead.ext["ROLL_INTERCEPT_USER"] = "0";
	if (bizMsg->m_serviceContextID == DATA_5G)
	{
		pSComhead.ext["OnlineMod5g"] = (bizMsg->m_offline == 1) ? "1" : "0";
		pSComhead.ext["IMS_CHG"] = (bizMsg->m_bImsFilter == true) ? "1" : "0";
		pSComhead.ext["FILTER_SMALL_USU"] = "0";
		if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_DUP_MSG_CDR || bizMsg->m_iOfflineXDREptFlag == SM_5G_RG_PAR_SM_FILTER_CONDITION)
		{
			pSComhead.ext["FILTER_SMALL_USU"] = "1";
		}
		else if(bizMsg->m_bExistRGFiltered)
		{
			pSComhead.ext["FILTER_SMALL_USU"] = "1";
		}
		else if (bizMsg->m_bExistMSCCFiltered)
		{
			pSComhead.ext["FILTER_SMALL_USU"] = "1";
		}
		else if (bizMsg->m_iOfflineXDREptFlag)
		{
			pSComhead.ext["ROLL_INTERCEPT_USER"] = "1"; 		// 和回退拦截共用一个topic
		}
	}

	pStatRecord.m_iNormalRecords = 0;
    pStatRecord.m_iInvalidRecords = 0;
    pStatRecord.m_iAbNormalRecords = 0;
    pStatRecord.m_iNoUserRecords = 0;
    pStatRecord.m_iDualRecords = 0;
    pStatRecord.m_iTotalRecords = 0;

	m_print.clear();
	m_print.print(pSComhead);
	m_print.print(pStatRecord);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LOGRATING:%s", m_print.data());

	m_en.clear();
	m_en.encode(pSComhead);
	m_en.encode(pStatRecord);

	string strMsg = HexEncode(m_en.data(),m_en.size());
	//头部加上固定16位时间戳
	char buf[30]={0};
	struct timeval tmv;
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	strMsg.insert(0,buf,16);
	bizMsg->m_pSendMsg->insert(pair<string,string>("LOGRATING",strMsg));

	std::multimap<string,string>::iterator itBeg;
	std::multimap<string,string>::iterator itEnd;
	std::multimap<string,string>::iterator iter;
	std::vector<std::string> vfileInfo;

	if(1 == m_cdrFlag)
	{
		if(bizMsg->m_testFlag == 0)
		{
			pSComhead.msgtype = "SM";	  // SM 正常清单
			if(DATA_5G == bizMsg->m_serviceContextID)
			{
				pSComhead.msgtype = "SM_5G";
			}

			if(m_combinaSwitch == 1)
				checkSum = checkSumFile(pSComhead,bizMsg, bodySM);

			if(!checkSum)
			{
				itBeg = bizMsg->m_pSendMsg->lower_bound("PAYFLAGTOPIC");
				itEnd = bizMsg->m_pSendMsg->upper_bound("PAYFLAGTOPIC");
				if (bizMsg->m_iRollFlag == 1)  // 回退清单
				{
					for(iter = itBeg; iter != itEnd; ++iter)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC roll MSG:%s", iter->second.c_str());
						bodySM.vRollCHGMessage.push_back(iter->second);
					}
				}
				else
				{
					for(iter = itBeg; iter != itEnd; ++iter)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC MSG:%s", iter->second.c_str());
						bodySM.vCHGmessage.push_back(iter->second);
					}
				}

				itBeg = bizMsg->m_pSendMsg->lower_bound("ACCUMULATION");
				itEnd = bizMsg->m_pSendMsg->upper_bound("ACCUMULATION");
				for(iter = itBeg; iter != itEnd; ++iter)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ACCUMULATION MSG:%s", iter->second.c_str());
					bodySM.vAccunumlationInfo.push_back(iter->second);
				}

				itBeg = bizMsg->m_pSendMsg->lower_bound("RATABLEINFO");
				itEnd = bizMsg->m_pSendMsg->upper_bound("RATABLEINFO");
				for(iter = itBeg; iter != itEnd; ++iter)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RATABLEINFO MSG:%s", iter->second.c_str());
					bodySM.vRatableInfo.push_back(iter->second);
				}
			}
		}
		else
		{
			pSComhead.msgtype = "SMTest";	  // SMTest 模拟拨测清单
			itBeg = bizMsg->m_pSendMsg->lower_bound("TESTCDRTOPIC");
			itEnd = bizMsg->m_pSendMsg->upper_bound("TESTCDRTOPIC");
			for(iter = itBeg; iter != itEnd; ++iter)
			{
				bodySM.vCHGmessage.push_back(iter->second);
			}
		}
		m_en.clear();
		m_en.encode(pSComhead);
		m_en.encode(bodySM);

		string cdrMsg = HexEncode(m_en.data(),m_en.size());
		cdrMsg.insert(0,buf,16);

		bizMsg->m_pSendMsg->insert(pair<string,string>("CDRMSG",cdrMsg));

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CDRMSG:%s", cdrMsg.c_str());
	}
	return 0;
}

int DCReqBaseFlow::composeCdrMsg(ocs::SComHead &pSComhead, STBizMsg* bizMsg, SCCRBase* base, SCCR5GInfo* data, SUSU *MSCC)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DCReqBaseFlow::composeCdrMsg.");
    ocs::RatingMessageInfo_t  bodySM;
    char szbuf[2*2048] = {0};

    string imsi = base->smExt.kv["IMSI"];
	if(imsi.find("imsi-") != string::npos)
	{
		imsi = imsi.substr(5);
	}
    string sNSSAI = base->smExt.kv["sNSSAI_SST"] + "#" + base->smExt.kv["sNSSAI_SD"];
    string userLocalInfo = MSCC->userLocInfoMation.mcc + "#" + MSCC->userLocInfoMation.mnc + "#" + bizMsg->m_hexLocInfoTac + "#" + MSCC->userLocInfoMation.nrCellId;

    char pduTmpInfo[64] = {0};
    sprintf(pduTmpInfo,"%d#%d#%d",
    data->pduAddressInfo.pduAddressprefixlength,
    data->pduAddressInfo.iPv4dynamicAddressFlag,
    data->pduAddressInfo.iPv6dynamicPrefixFlag
    );
    string pduAddressInfo = data->pduAddressInfo.pduIPv4Address + "#" + data->pduAddressInfo.pduIPv6AddresswithPrefix + "#" + pduTmpInfo;

    string strRATType;
    if(MSCC->rATType.empty())
    {
        strRATType = data->rATType;
	    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "MSCC->rATType is empty, use data->rATType[%s]", data->rATType.c_str());
    }
    else
    {
        strRATType = MSCC->rATType;
	    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "MSCC->rATType[%s}", MSCC->rATType.c_str());
    }

	//networkFunctionPLMNIdentifier
	string strNFPLMNID = MSCC->servingNodeId.mcc + MSCC->servingNodeId.mnc;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "NFPLMNID[%s] = mcc[%s] + mnc[%s]", strNFPLMNID.c_str(), MSCC->servingNodeId.mcc.c_str(), MSCC->servingNodeId.mnc.c_str());
    string tmpIpv6Address = base->nFIPv6Address;
	for(int i = 0; i < tmpIpv6Address.size();i++)
	{
		if(tmpIpv6Address[i] == ':')
			tmpIpv6Address[i] = '#';
	}
    memset(szbuf, 0, sizeof(szbuf));
    //sprintf(szbu ^^1^^2^^3^^4^^5^^6^^07^^08^09^10^^11^^12^^13^14^^15^16^17^18^19^20^21^22^23^24^25^26^27^28^29^30^31^32^33^34^35^36^37^38^39^40^41^42^43^44^45^46^47^48^49^50^51^52^53^54
    sprintf(szbuf, "%s|%s|%s|%d|%s|%ld|%d|%ld|%s|%d|%ld|%ld|%ld|%ld|%s|%d|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%d|%d|%d|%d|%d|%d|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s",
            imsi.c_str(), //bizMsg->m_userinfo->IMSI,
            data->servedGPSI.c_str(),
            sNSSAI.c_str(),
            data->chargingId,
            base->sessionId.c_str(),    //##5
            base->timestamp,  //USU0:triggerTimestamp   USU1:invocationTimestamp
            base->requestNumber,
            MSCC->ratingGroup,
            MSCC->ProductOfferId.c_str(), //serviceId  //Service Identifier
            MSCC->USU0.duration,         //##10 time
            MSCC->USU0.unitTotal,
            MSCC->USU0.unitInput,
            MSCC->USU0.unitOutput,
            MSCC->serviceSpecificUnits, //serviceSpecificUnits
            MSCC->eventTimeStamps.c_str(), //EventTimeStamps     //##15
            MSCC->localSequenceNumber,//LocalSequenceNumber
			"OFFLINE_CHARGING", //quotaManagementIndicator
            strRATType.c_str(),
            MSCC->servingNodeId.aMFId.c_str(),
            data->startTime.c_str(),          //##20
            data->stopTime.c_str(),
            MSCC->sponsorIdentity.c_str(), //sponsorIdentity,
            MSCC->appserviceProviderId.c_str(),//applicationserviceProviderIdentity
            base->smExt.kv["uetimeZone"].c_str(),
            data->servedPEI.c_str(),               //##25
            data->roamerInOut.c_str(),//roamerInOut
            userLocalInfo.c_str(),
			bizMsg->m_hexLocInfoTac.c_str(),
			MSCC->userLocInfoMation.mcc.c_str(),
			MSCC->userLocInfoMation.mnc.c_str(),   //#30
			MSCC->userLocInfoMation.nrCellId.c_str(),
            data->SubscribedQoSInformation.qosId,
            data->SubscribedQoSInformation.qi5g,
            data->SubscribedQoSInformation.priorityLevel,
            data->authorizedQoSInformation.qosId,    //##35
            data->authorizedQoSInformation.qi5g,
            data->authorizedQoSInformation.priorityLevel,
            data->AuthorizedSessionAMBR.downlink.c_str(),  //AuthorizedSessionuplink
            data->AuthorizedSessionAMBR.uplink.c_str(),  //AuthorizedSessiondownlink
            data->subscribedSessionAMBR.downlink.c_str(),  //subscribedSessionuplink  //##40
            data->subscribedSessionAMBR.uplink.c_str(),  //subscribedSessiondownlink
            data->pduSessionID.c_str(),
            data->pduType.c_str(),
            pduAddressInfo.c_str(),  //pDUAddress,
            data->hPlmnId.c_str(),   //##45
            data->dNNID.c_str(),
            data->chargingCharacteristics.c_str(),  //chargingCharacteristics
            MSCC->upfID.c_str(),
            MSCC->servingNodeId.nodeFunctionality.c_str(),
            strNFPLMNID.c_str(),  //networkFunctionPLMNIdentifier  //##50
            MSCC->servingNodeId.nFName.c_str(),
            base->nFIPv4Address.c_str(),
            tmpIpv6Address.c_str(),
            MSCC->servingNodeId.nFFqdn.c_str(), //NFFQDN
            base->smExt.kv["servingCNPlmnId"].c_str() //CNPlmn
    );

    bodySM.vOfflineModeCDR.push_back(szbuf);

    m_print.clear();
    m_print.print(pSComhead);
    m_print.print(bodySM);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OFFLIN_MODE_CDR:%s", m_print.data());

    m_en.clear();
    m_en.encode(pSComhead);
    m_en.encode(bodySM);

    string strMsg = HexEncode(m_en.data(),m_en.size());
    //头部加上固定16位时间戳
    char buf[30]={0};
    struct timeval tmv;
    gettimeofday(&tmv, NULL);
    sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
    strMsg.insert(0,buf,16);

    bizMsg->m_pSendMsg->insert(pair<string,string>("OFFLIN_MODE_CDR",strMsg));
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OFFLIN_MODE_CDR:%s", strMsg.c_str());

    return 0;
}

int DCReqBaseFlow::composeOfflineCdrMsg(STBizMsg* bizMsg, SCCRBase* base, SCCR5GInfo* data)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DCReqBaseFlow::composeOfflineCdrMsg.");
	ocs::SComHead pSComhead;
    ocs::SUSU *MSCC = NULL;

	pSComhead.AckEndBoltFlag = 0;
	pSComhead.collectid = 0;
	pSComhead.latnid = bizMsg->m_ilatnId;
	pSComhead.operlistid = 0;
	pSComhead.switchid = 0;
	pSComhead.opertype = bizMsg->m_szServiceContextIDStr;
	pSComhead.sourceid = atol(bizMsg->m_sourceId.c_str());
	pSComhead.recordid = 0;
	pSComhead.sourcefile = "";
	pSComhead.m_sProcerName = "B";
	pSComhead.msgtype = "SM";
	pSComhead.epttype = 0;
	pSComhead.resultcode = bizMsg->m_resultcode;
	pSComhead.BatchNo = bizMsg->m_sBatchId;
	pSComhead.uid = bizMsg->m_uid;
	pSComhead.trace = base->trace;
	pSComhead.modid = 0;
	pSComhead.ip = "";
	pSComhead.checkKey = bizMsg->m_strCheckKey;

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "MSCC size[%d]", base->MSCC.size());
    for(int i=0; i < base->MSCC.size(); i++)
    {
        MSCC = &(base->MSCC[i]);
        if(!MSCC)
        {
            continue;
        }

        //USU1
        if(0 != MSCC->USU1.duration || 0 != MSCC->USU1.unitTotal)
        {
            //USU0
            base->timestamp = MSCC->triggerTimestamp;
            composeCdrMsg(pSComhead, bizMsg, base, data, MSCC);

            //USU1 invocationTimestamp
            base->timestamp = bizMsg->timestampCCR;
            MSCC->USU0.duration = MSCC->USU1.duration;
            MSCC->USU0.unitTotal = MSCC->USU1.unitTotal;
            MSCC->USU0.unitInput = MSCC->USU1.unitInput;
            MSCC->USU0.unitOutput = MSCC->USU1.unitOutput;
            composeCdrMsg(pSComhead, bizMsg, base, data, MSCC);
        }
        else
        {
            //USU0
            base->timestamp = bizMsg->timestampCCR;
            composeCdrMsg(pSComhead, bizMsg, base, data, MSCC);
        }
    }

	return 0;
}

bool DCReqBaseFlow::checkSumFile(ocs::SComHead& pSComhead,STBizMsg* bizMsg,ocs::RatingMessageInfo_t& bodySM)
{
    int realtime = 0;
    ocs::CombineRecord bodySMRecord;
    std::vector<std::string> vfileInfo;
    std::multimap<string,string>::iterator itBeg;
    std::multimap<string,string>::iterator itEnd;
    std::multimap<string,string>::iterator iter;
    iter = bizMsg->m_pSendMsg->find("SUMINFO");

    if(iter != bizMsg->m_pSendMsg->end())
    {
        //判断n_if_realtime_disct是否为1
        vfileInfo.clear();
        DCCommonIF::SplitString(iter->second, '|', vfileInfo);
        realtime = atoi(vfileInfo[21].c_str());
        if(realtime == 1)
        {
            itBeg = bizMsg->m_pSendMsg->lower_bound("SUMINFO");
            itEnd = bizMsg->m_pSendMsg->upper_bound("SUMINFO");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                //sum文件出两次,组合一次，单独一次
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SUMINFO MSG:%s", iter->second.c_str());
                bodySM.vSumInfo.push_back(iter->second);
                bodySMRecord.vSumInfo.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("PAYFLAGTOPIC");
            itEnd = bizMsg->m_pSendMsg->upper_bound("PAYFLAGTOPIC");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC MSG:%s", iter->second.c_str());
                bodySMRecord.vCHGmessage.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("ACCUMULATION");
            itEnd = bizMsg->m_pSendMsg->upper_bound("ACCUMULATION");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ACCUMULATION MSG:%s", iter->second.c_str());
                bodySMRecord.vAccunumlationInfo.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("RATABLEINFO");
            itEnd = bizMsg->m_pSendMsg->upper_bound("RATABLEINFO");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RATABLEINFO MSG:%s", iter->second.c_str());
                bodySMRecord.vRatableInfo.push_back(iter->second);
            }

            //编码
            m_en.clear();
			m_en.encode(pSComhead);
            m_en.encode(bodySMRecord);
            bodySM.m_sCombineMsg = HexEncode(m_en.data(),m_en.size());
        }
        else
            return false;
    }
    else
        return false;

    return true;
}


DYN_PLUGIN_CREATE(DCReqBaseFlow, "FL_REQBASEFLOW", "FL_ReqBaseFlow", "1.0.0")
