@组件名称：libEptBaseFlow.so.x.x.x
@创建日期：2016/6/24
@修改日期：2016/12/10
@当前版本：1.0.0
@功能描述：异常流程组件，反序列化并解析异常消息，调用异常功能组件
@版本历史：
@1.0.0：
---2016/7/11： 参数读取配置文件修改
---2016/7/16:  eptbolt问题修改，sp消息超时实现
---2016/7/26:  RBA超时缺陷修改
---2016/8/13:  会话超时修改
---2016/8/22:  请求实名制发送CCA topic支持可配置，用户管理流程返回结果码判断
---2016/8/24:  离线二次批价
---2016/9/14:  增加eptbolt对rar的判断
---2016/9/22:  号码跟踪问题修改
---2016/10/21: 统一添加UHead消息头
---2016/11/24: 试运行版本改动
---2016/11/28: 反序列化异常捕获
---2016/11/30: 多个SP对应一个top图，将CCA的topic放入消息体，传到后端
---2016/12/10: 解决异常时内存泄漏问题