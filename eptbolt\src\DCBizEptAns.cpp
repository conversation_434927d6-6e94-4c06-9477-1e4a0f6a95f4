﻿#include "DCBizEptAns.h"
#include "DCLogMacro.h"
#include "OCPMsgDef.h"
#include "ErrorCode.h"
#include <sys/time.h>
#include "TSMPara.h"
#include "func_sqlindex.h"
#include "DCOBJSet.h"
#include "DCEptMsgDef.h"

//
int DCBizEptAns::Work(void *data)
{
	STBizMsg* bizMsg = (STBizMsg*)data;
	// cvar->m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));

	//PERF LOG
	//bizMsg->m_bizType = BIZ_EPT;
	//bizMsg->m_perf.GetTimeT3_B();

	//批价返回的离线文件消息类型是4,需要转成SM的离线消息类型5
	if(4 == bizMsg->m_requestType)
	{
		bizMsg->m_requestType = 5;
	}

	bizMsg->m_iEptFlag = 1; // ept两组使用量场景批价异常，出单不用减去第二组使用量
    int ret = UpdateRecvRGNum(bizMsg);

    if(ret != RET_SUCCESS)
    {
             return ret;
    }

	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		case SMS:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		case DATA:
		case CCG:
		case PGW:
			{
				ret = SwitchDATA(bizMsg);
			}
			break;
		case DATA_5G:
			{
				ret = Switch5G(bizMsg);
			}
			break;
		case ISMP:
		case HRS:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		case DSL:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
		default:
			{
				ret = SwitchCommon(bizMsg);
			}
			break;
	}

	
	//PERF LOG
	//bizMsg->m_perf.GetTimeT3_E();
	
	return ret;
	
}

int DCBizEptAns::SwitchCommon(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				return InitAns(bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				return Update(bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return Term(bizMsg);
			}
			break;
		case SM_SESSION_EVENT_CODE:		
		case SM_SESSION_EVENT_REFUND_CODE:		
		case SM_SESSION_EVENT_BALANCE_CODE:
			{
				return Event(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "unknow request type[%d]", bizMsg->m_requestType);
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::SwitchDATA(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request type[%u]", bizMsg->m_requestType);
	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				return InitData(bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				return UpdateData(bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				return TermData(bizMsg);
			}
			break;
		case SM_SESSION_EVENT_CODE:		
		case SM_SESSION_EVENT_REFUND_CODE:		
		case SM_SESSION_EVENT_BALANCE_CODE:
			{
				return Event(bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "unknow request type[%d]", bizMsg->m_requestType);
				SendErrorResult(bizMsg);
				return RET_SUCCESS;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::SwitchRATA(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request type[%u]", bizMsg->m_requestType);
	SendErrorResult(bizMsg);
	DelSession(bizMsg);
	
	return RET_SUCCESS;
}

int DCBizEptAns::Init5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	return RET_SUCCESS;
}

int DCBizEptAns::Update5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	//如果配置了系统参数表&& bizMsg->m_resultcode == -30232
	//流程退出不包装TermRER
	bool bFlagRER = bizMsg->m_anspara->IsCfgServContexID(bizMsg->m_szServiceContextIDStr);
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
		{
			if (!bFlagRER)
			{
				SendTermRER(bizMsg);
			}
			return RET_SUCCESS;
		}
		break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				if (!bFlagRER)
				{
					SendTermRER(bizMsg);
				}
				return RET_SUCCESS;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				if (!bFlagRER)
				{
					SendTermRER(bizMsg);
				}
				return RET_SUCCESS;
			}
			break;
		default:
			{
				//时间受限的情况(RB init消息时返回),不需要发送term RER
				if ((bizMsg->m_resultcode == -40100) || (bizMsg->m_resultcode == -40101) || (bizMsg->m_resultcode == -40200))
				{
					SendErrorResult(bizMsg);
					DelSession(bizMsg);
					return RET_SUCCESS;
				}
				else if (bizMsg->m_resultcode == -30232)
				{
					//RB返回-30232的时候不带最后一个流量片的费用信息
					//所以SM需要等待term RBA返回最后一个流量片后再出单，与其他异常结果码的流程不同
					if (bizMsg->m_requestType == SM_SESSION_UPDATE_FIRST_CODE)
					{
						SendErrorResult(bizMsg);
						DelSession(bizMsg);
						return RET_SUCCESS;
					}
					
					if (!bFlagRER)
					{
						SendTermRER(bizMsg);
					}

					SendErrorResult(bizMsg);
					DelSession(bizMsg);
					return RET_SUCCESS;
				}
				else
				{
					SendErrorResult(bizMsg);
					if (!bFlagRER)
					{
						SendTermRER(bizMsg);
					}
					DelSession(bizMsg);
					return RET_SUCCESS;
				}
			}

			break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::Term5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	switch (bizMsg->m_resultcode)
	{
		case NE_USELESS:
		{
			return RET_SUCCESS;
		}
		break;
		case RB_SM_UNABLE_TO_COMPLY:
		{
			SendErrorResult(bizMsg);
			return RET_SUCCESS;
		}
		break;
		case RB_RBA_MSG_ERROR:
		{
			SendErrorResult(bizMsg);
			return RET_SUCCESS;
		}
		break;
		default:
		{
			SendErrorResult(bizMsg);
			return RET_SUCCESS;
		}
		break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::Switch5G(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request type[%u]", bizMsg->m_requestType);
	switch (bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
		{
			return Init5G(bizMsg);
		}
		break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
		{
			return Update5G(bizMsg);
		}
		break;
		case SM_SESSION_TERMINATION_CODE:
		{
			return Term5G(bizMsg);
		}
		break;
		default:
		{
			return RET_SUCCESS;
		}
		break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::InitAns(STBizMsg* bizMsg)
{
	int ret = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
			}
			break;
		default:
			{
				SendErrorResult(bizMsg);
				DelSession(bizMsg);
			}
			break;
	}

	return ret;
}

int DCBizEptAns::Update(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	
	//如果配置了系统参数表&& bizMsg->m_resultcode == -30232
	//流程退出不包装TermRER 
	bool bFlagRER =  bizMsg->m_anspara->IsCfgServContexID(bizMsg->m_szServiceContextIDStr);
   
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}

				return RET_SUCCESS;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		default:
			{
				//时间受限的情况(RB init消息时返回),不需要发送term RER 
				if((bizMsg->m_resultcode == -40100) ||(bizMsg->m_resultcode == -40101) || (bizMsg->m_resultcode == -40200))
				{
					SendErrorResult(bizMsg);
					DelSession(bizMsg);
					return RET_SUCCESS;
				}
				else if(bizMsg->m_resultcode == -30232)
				{
					SendErrorResult(bizMsg);
					if ( !bFlagRER )
					{
					    SendTermRER(bizMsg);
					}
					else
			        {
			            UpdateSessionWithoutTermRER( bizMsg);
						// return RET_CDR;
			        }
					return RET_SUCCESS;
				}				
				else
				{
					SendErrorResult(bizMsg);
					if(!bFlagRER)
					{
						SendTermRER(bizMsg);
						return RET_CDR;
					}
					return RET_SUCCESS;
				}
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::Term(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				return RET_CDR;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		default:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
	}
}

int DCBizEptAns::InitData(STBizMsg* bizMsg)
{
	int ret = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);

       /* 数据业务不走此流程
        switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				ret = SendTermRER(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				SendErrorResult(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
	}
        */
	return RET_SUCCESS;
}

int DCBizEptAns::UpdateData(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	
	//如果配置了系统参数表&& bizMsg->m_resultcode == -30232
	//流程退出不包装TermRER 
	bool bFlagRER = bizMsg->m_anspara->IsCfgServContexID(bizMsg->m_szServiceContextIDStr);

	// 应答异常出单只针对当前RG
	bizMsg->m_longCDR = 5;
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
		{
		    if (!bFlagRER)
		    {
		        SendTermRER(bizMsg);        
		        return RET_CDR;
		    }
			return RET_SUCCESS;
		}
		break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		default:
			{
				//时间受限的情况(RB init消息时返回),不需要发送term RER 
				if((bizMsg->m_resultcode == -40100) ||(bizMsg->m_resultcode == -40101) || (bizMsg->m_resultcode == -40200))
				{
					SendErrorResult(bizMsg);
					DelSession(bizMsg);
					return RET_SUCCESS;
				}
				else if(bizMsg->m_resultcode == -30232)
				{	
					//如果是第一个RG初始余额不足，则直接释放会话
					{
						int nSessionNum = 0;			//会话状态标识
						nSessionNum = GetSessionNum(bizMsg);
						if(1 == nSessionNum)
						{
							SendErrorResult(bizMsg);
							DelSession(bizMsg);
							return RET_SUCCESS;
						}
					}
					//RB返回-30232的时候不带最后一个流量片的费用信息
					//所以SM需要等待term RBA返回最后一个流量片后再出单，与其他异常结果码的流程不同
					if ( !bFlagRER )
					{
						SendTermRER(bizMsg);
					}
					else
					{
						// 不上报TERMRER 则将记录本次使用量
						UpdateSessionWithoutTermRER( bizMsg);
					}

					SendErrorResult(bizMsg);	
					return RET_SUCCESS;
				}				
				else
				{
					SendErrorResult(bizMsg);
					if ( !bFlagRER )
					{
						SendTermRER(bizMsg);
						return RET_CDR;
					}
					return RET_SUCCESS;
				}
			}
			break;
	}

	return RET_SUCCESS;
}



int DCBizEptAns::UpdatePGW(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);

    //如果配置了系统参数表&& bizMsg->m_errorCode == -30232
    //流程退出不包装TermRER 
    bool bFlagRER =  bizMsg->m_anspara->IsCfgServContexID(bizMsg->m_szServiceContextIDStr);
	int ret = 0;
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				if(!bFlagRER)
				{
					SendTermRER(bizMsg);
					return RET_CDR;
				}
				return RET_SUCCESS;
			}
			break;
		default:
			{
				//时间受限的情况(RB init消息时返回),不需要发送term RER 
				if((bizMsg->m_resultcode == -40100) ||(bizMsg->m_resultcode == -40101) || (bizMsg->m_resultcode == -40200))
				{
					SendErrorResult(bizMsg);
					DelSession(bizMsg);
					return RET_SUCCESS;
				}
				else if(bizMsg->m_resultcode == -30232)
				{	
					//如果是第一个RG初始余额不足，则直接释放会话
					{
						int sessionFlag = 0;			//会话状态标识
						sessionFlag = GetSessionNum(bizMsg);
						if(1 == sessionFlag)
						{
							SendErrorResult(bizMsg);
							DelSession(bizMsg);
							return RET_SUCCESS;
						}
					}
					//RB返回-30232的时候不带最后一个流量片的费用信息
					//所以SM需要等待term RBA返回最后一个流量片后再出单，与其他异常结果码的流程不同
					if ( !bFlagRER )
					{
					    ret = SendTermRER(bizMsg);
					}
					else
			        {
			            UpdateSessionWithoutTermRER( bizMsg);
			        }
					SendErrorResult(bizMsg);	
					return ret;
				}				
				else
				{
					SendErrorResult(bizMsg);
					if(!bFlagRER)
					{
						SendTermRER(bizMsg);
						return RET_CDR;
					}
					return RET_SUCCESS;
				}
			}
			break;
	}

	return RET_SUCCESS;
}


int DCBizEptAns::TermData(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
       // 数据业务Ans 流程走异常截单流程
       bizMsg->m_longCDR = 5;
       switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				return RET_CDR;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		default:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
	}
}


int DCBizEptAns::TermPGW(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				return RET_CDR;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
		default:
			{
				SendErrorResult(bizMsg);
				return RET_CDR;
			}
			break;
	}
}


int DCBizEptAns::Event(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "error code[%d]", bizMsg->m_resultcode);
	switch(bizMsg->m_resultcode)
	{
		case NE_USELESS:
			{
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case RB_SM_UNABLE_TO_COMPLY:
			{
				SendErrorResult(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		case RB_RBA_MSG_ERROR:
			{
				SendErrorResult(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
		default:
			{
				SendErrorResult(bizMsg);
				DelSession(bizMsg);
				return RET_SUCCESS;
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEptAns::UpdateRecvRGNum(STBizMsg* bizMsg)
{
    UDBSQL* query = NULL;
	UDBSQL* queryMain = NULL;
    UDBSQL* exec = NULL;
    UDBSQL* exec_rc = NULL;
    int og_state = 0;
    int resultcode = 0;
    int rgnum = 0;
    int affect_num = -1;
        
    DCAnsPara* anspara = (DCAnsPara *)bizMsg->m_anspara;
    if(SM_SESSION_XDR_CODE != bizMsg->m_requestType)
	{
		resultcode = anspara->GetOCPResultCode(bizMsg->m_resultcode);
	}
	else
	{
		resultcode = bizMsg->m_resultcode;
	}
    if(resultcode == -1)
    {
        resultcode = 3004;
	}

	DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "ServiceContextID[%d], request_type[%d], resultcode[%d], ocpcode[%d], spiltflag[%d]", bizMsg->m_serviceContextID,bizMsg->m_requestType,bizMsg->m_resultcode,resultcode,bizMsg->m_spiltflag);

    // DATA 业务类型可能不准, 需要修正
	if(bizMsg->m_serviceContextID == DATA)
	{
		query = bizMsg->m_dbm->GetSQL(DATA_S_RECV_RGNUM);
		try
		{
		      // 查询主会话
		    query->DivTable(bizMsg->m_sessionID);
		    query->UnBindParam();
		    query->BindParam(1, bizMsg->m_sessionID);
		    query->Execute();
		    if(query->Next())
		    {
			    int sci = 0;
			    query->GetValue(3,  sci);
			    if(sci == CCG)
			    {
				    bizMsg->m_serviceContextID = CCG;
				    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","real m_serviceContextID is CCG");
			    }
		    }
		    else
		    {
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","not find main session, do nothing");
			    return RET_ERROR;
		    }
		}
		catch(UDBException&e)
		{
		    DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "","query main ept[%s]",  e.ToString());
		    return RET_ERROR;
		}
	}
	
	int flag_pgw = 0;
	if(bizMsg->m_serviceContextID == PGW && (bizMsg->m_requestType == 3 || bizMsg->m_requestType == 2))
	{
		query = bizMsg->m_dbm->GetSQL(PGW_S_RECV_RGNUM);
		exec = bizMsg->m_dbm->GetSQL(PGW_U_RECV_RGNUM);
		exec_rc = bizMsg->m_dbm->GetSQL(PGW_EPT_UPDATE_CODE);
		queryMain = query;
		flag_pgw = 1;
	}
	else if((bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == DATA) && bizMsg->m_requestType == 3)
	{
		query = bizMsg->m_dbm->GetSQL(DATA_S_RECV_RGNUM);
		exec = bizMsg->m_dbm->GetSQL(DATA_U_RECV_RGNUM);
		exec_rc = bizMsg->m_dbm->GetSQL(DATA_EPT_UPDATE_CODE);
		queryMain = query;
	}
	else
	{
		return RET_SUCCESS;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","select rg");
	try
	{
		if(!query)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","not find childsession, do nothing");
			return RET_ERROR;
		}
		// 查询子会话SM_INT_SESSION_STATUS
		query->DivTable(bizMsg->m_sessionID);
		query->UnBindParam();
		query->BindParam(1, bizMsg->m_childsessionID);
		query->Execute();
		if(query->Next())
		{
			query->GetValue(2,  og_state);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","not find childsession, do nothing");
			return RET_ERROR;
		}

		//og_state 不为TORB_ACTION表示已经更新过
        if (flag_pgw && og_state != TORB_ACTION)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","childsession have old_session_state[%d], skip", og_state);
			return RET_ERROR;
		}
		//ResultCode 有值表示已经更新过
		else if(!flag_pgw && og_state)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","childsession have old_result[%d], skip", og_state);
            return RET_ERROR;
		}
	}
	catch(UDBException&e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "","query ept[%s]",  e.ToString());
		return RET_ERROR;
	}

	try
	{
		if(!exec_rc)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","not find childsession, do nothing");
			return RET_ERROR;
		}
			// 更新子会话结果码
		exec_rc->DivTable(bizMsg->m_sessionID);
		exec_rc->UnBindParam();
        if (flag_pgw)
		{
			exec_rc->BindParam(1, resultcode);
			exec_rc->BindParam(2, RECVRB_ACTION);
			exec_rc->BindParam(3, bizMsg->m_childsessionID);
		}
		else
		{
			 exec_rc->BindParam(1, resultcode);
             exec_rc->BindParam(2, bizMsg->m_childsessionID);
		}

		exec_rc->Execute();
		exec_rc->Connection()->Commit();

		//更新主会话RGNUM
		exec->DivTable(bizMsg->m_sessionID);
		exec->UnBindParam();
		exec->BindParam(1, bizMsg->m_sessionID);
		exec->Execute();
		affect_num = exec->GetRowCount();
		exec->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","update child resultcode[%d],  main recv rgnum", resultcode);
	}
	catch(UDBException&e)
	{
		exec->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "","update ept[%s]",  e.ToString());
		return RET_ERROR;
	}

	try
	{
		if(!queryMain)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","not find childsession, do nothing");
			return RET_ERROR;
		}
		// 查询主会话RGNUM
		queryMain->DivTable(bizMsg->m_sessionID);
		queryMain->UnBindParam();
		queryMain->BindParam(1, bizMsg->m_sessionID);
		queryMain->Execute();
		if(queryMain->Next())
		{
			queryMain->GetValue(1,  rgnum);
		}
		else
		{
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","not find main session, do nothing");
			return RET_ERROR;
		}
	}
	catch(UDBException&e)
	{
	     DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "","query main ept[%s]",  e.ToString());
	     return RET_ERROR;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","main session rgnum[%d]", rgnum);
	if(rgnum <= 1 && affect_num == 0)
	{
	     return RET_SUCCESS;      // 所有RG 均返回
	 }

	// 出单时走异常截单流程
	bizMsg->m_longCDR = 5;
	return RET_CDR;

}


int DCBizEptAns::init()
{
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	return 0;
}

int DCBizEptAns::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	bizMsg->m_anspara = m_anspara;
	m_pMsendMsg =(std::multimap<string,string>*)bizMsg->m_pSendMsg;
	int ret = Work(bizMsg);
	return ret;
	
}

DYN_PLUGIN_CREATE(DCBizEptAns, "FC_EPTANS", "FC_EptAns", "1.0.0")

