#include "DCAnsDATA.h"
#include "DCLogMacro.h"
#include "DCAnsPara.h"
#include "DCRbMsgDef.h"
#include <sys/time.h>
#include "ErrorCode.h"

using namespace ocs;

DCAnsDATA::DCAnsDATA()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "", "");
}

DCAnsDATA::~DCAnsDATA()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "", "");
}

int DCAnsDATA::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG,  "", "null msg");
		return SM_OCP_NULL_MSG;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;
		
	//PERF LOG
	//bizMsg->m_perf.GetTimeT2_B();
	
	int ret = ComposeCCA(bizMsg);

	//bizMsg->m_perf.GetTimeT2_E();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
	
	if(RET_CDR == ret || RET_SUCCESS == ret)
	{	

	}
	else
	{	
		bizMsg->m_resultcode= ret;
		
		return ret;
	}
	return ret;
}

int DCAnsDATA::ComposeCCA(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

