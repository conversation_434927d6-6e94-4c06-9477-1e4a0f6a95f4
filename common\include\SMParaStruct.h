/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       SMParaStruct.h
*Indentifier：
*
*Description：
*       SM系统参数及业务参数结构定义
*Version：
*       V1.0
*Author:
*
*Finished：
*
*History:
*
********************************************/
#ifndef _SMPARASTRUCT_H_
#define _SMPARASTRUCT_H_

#include <vector>
#include <string>
using namespace std;

//业务配额信息
typedef struct TSERVICE_QUOTA_CONF
{
	int 		SERVICE_TYPE;					//设备类型 0-voice service 1-data service
	int 		SUB_SERVICE_TYPE;				//授权类型
	char 	    CONFIG_DESC[64];				//描述
	long long   RATING_GROUP;					//批价组合

	long long   INPUT_OCTETS;					//请求上行流量
	long long   OUTPUT_OCTETS;				//请求下行流量
	long long   TOTAL_OCTETS;					//请求总流量
	int 		DURATION;						//数据业务授权步长，秒

	int 		TOKEN;							//语音业务授权步长，秒
	int 		VALID_TIME;					//授权有效时长，秒
	int 		QUOTA_HOLDING_TIME;			//QHT
	int 		TIME_QUOTA_THRESHOLD;		//时间门限值，秒
	int 		VOLUME_QUOTA_THRESHOLD;		//流量门限值，byte
	int 		VOLUME_QUOTA_THRESHOLD_1;		//流量门限值2，byte
	int 		QUOTA_CONSUMPTION_TIME;		//QCT
	char        szProductOfferId[128];
	long        MinBalance;
	long        MaxBalance;
	long 	MinRatio;						  //最小速率
	long 	MaxRatio;						 //最大速率
	int  		THRESHOLD_RATIO;                 	// 配额比例
}TSERVICE_QUOTA_CONF;


//系统参数项
typedef struct
{
	char 	PARA_GROUP[64];
	char 	PARA_KEY[64];
	char 	PARA_VALUE[256];
}SSystemPara;


//系统通用参数
typedef struct
{
	unsigned int	currentProvCode;			//默认省份号
	unsigned int	currentAreaCode;			//默认区号
	unsigned int	onlySubFlag;				//支持不带区号计费号码开关
	unsigned int	onlineSession;				//最高在线会话数
	unsigned int	platformOverLoad;			//系统过载开关
	unsigned int	isTranslateDataCard;			//数据卡转换开关
	int	 	    	isTraceNumOnff;				//信令跟踪开关
	int			    INForbindenSwitch;			//IN禁拨开关
	int			    P2PSMSForbindenSwitch;		//IN禁拨开关
	int			    PSForbindenSwitch;			//IN禁拨开关
	int			    ISMPForbindenSwitch;		//IN禁拨开关
	int			    DSLForbindenSwitch;			//IN禁拨开关
	int			    EventISMPSwitch;			//ISMP事件扣负开关
	int			    EventSMSSwitch;				//SMS事件扣负开关
	int			    TIMEOUTVALUE;				//设置超时时间
	char 		    CdrFence;					//话单间隔字符(单个字符)
	char	 		CdrEndchar;					//话单info字段结尾字符
	char            SmsSendNo[32];    			//短信发送号
	char            SmsExperienceSendNo[32];    			//短信发送号
	int			    INFreeSwitch;				// 语音免费号码开关
	int			    SMSFreeSwitch;			//短信免费号码开关
	int			    DATAFreeSwitch;			//数据业务免费号码开关
    int			    _5GFreeSwitch;			//数据业务免费号码开关
	int			    ISMPFreeSwitch;			//ismp免费号码开关
	int			    DSLFreeSwitch;			// dsl免费号码开关
	unsigned int	invalidMsisdnArea;		//无效号段默认区号
	unsigned int 	CdrTariffid;			//话单资费类信息显示为0开关
	int             BalanceInfoType;      //配置为1:话单中balanceInfo中账本类型项取B085;配置为0:话单中balanceInfo中账本类型项取B082
	int             invalidMsisdnDeal;    //无效号段的处理方式 1:采用默认的区号;0:拒绝使用,默认1
	int             nAocTemplateMinit;//短信模板参数中是含有分钟      1是，0否，默认1
	int             nAocTemplateTotalByte;//短息模板参数中是否含有总的累积流量   1是，0否，默认1
	int             nRsuUsuCCmoneyUnits;  //RSU和USU组中CC_Money组内Value-Digits计算出来单位0:元;1:分;2:Exponent为-2时认为是元,Exponent为0时认为是分 默认1
	int             nGsuCCmoneyExponent;  //GSU组中CC_Money组内Exponent值0，-2，默认0
	int             nGsuCostInformationExponent;  //GSU组中Cost-Information组内Exponent值0，-2，默认0
	int			    SessionTimeOutMaxMessageForEachSecond;		//会话超时每秒最大释放消息数
	int 	        nReleaseFlag ;//超时释放配置，0:正常业务使用,1:升级时使用
	int             iLatnRelQueryControl;			//按照TB_NBR_LATN_REL_QUERY_CONTROL表查询用户归属区控制开关，0，关，1，开
	int 	        iSendEPTRBROnOff;			//会话超时场景是否组装释放RBR消息开关，0，关，1，开
	int             iCdrVersionType;		//话单版本字段值类型控制:0,保持现有的不变;非0正整数，清单中version字段展示的值为time+para+serial
	int             INAuthUserResultCode;		//IN非实名制用户返回结果码
	int             P2PSMSAuthUserResultCode;	//SMS非实名制用户返回结果码
	int             PSAuthUserResultCode;		//PS非实名制用户返回结果码
    int             _5GAuthUserResultCode;		//PS非实名制用户返回结果码
	int             ISMPAuthUserResultCode;		//ISMP非实名制用户返回结果码
	int             DSLAuthUserResultCode;		//DSL非实名制用户返回结果码
	int             nFreeAddCdr;
	int             iUpdateUserStateSwitch;		//update消息判断用户状态开关:0，关，1开
	int             isubAreaControl;            //清单中出area_code还是latn_id
	int             iPricePlanID;				//清单中定价ID展示，配置为1时，展示最后一组，配置其他全部展示
	int             iDslMonthCDRSwitch;		//dsl 跨月清单开关，0，关，1，开
	int             iTariffidSwitch;                //业务使用量开关，0，关，1开
	int             iUserDefinedAocSwitch;   //四川自定义小余额提醒开关
    int             iOffset;                //互转跨月偏移时间参数，配置精确到分钟（不需要的现场配置为0）
	long 			lPlcavalidtime;
	int 			nOssSwitch;
	int 			iAAAMinTime;               //AAA业务时长计费超短阀值配置参数
	std::vector <std::string> vecServContextId; //系统参数表配置需要屏蔽TERMRER的业务
	int 			iSendEPTRBROnOffPGW;		//会话超时场景是否组装释放RBR消息开关，0，关，1，开
	int 			iSendEPTRBROnOff5G;		//会话超时场景是否组装释放RBR消息开关，0，关，1，开
	int				iAocType;
	int 			iOfflineCdrFlag;            //离线话单扣负标志
	int				iBatchIdTime;				//批次号更新时间间隔，单位分钟
}SCommonPara;

//IN业务参数
typedef struct
{
	int longCDRTime;
	int shortCDRTime;			//长途通话超短
	int shortCDRTimeCity;			//市话超短话单时间
	int shortCDRTimeRoam;		//漫游超短
	int favCellIDSwitch;			//小区优惠开关
	int remindDay;				//过期前N天提醒
	int roamMSCorVLR;			//MSC、VLR
	int roamCELLIDorLAC;		//省内漫游判断方式
	int edgeRoamSwitch;			//边漫判断方式
	char roamColligateMSC[32];	//综合判断方式
	int defaultProv;				//未知MSC时默认省份
	int defaultArea;				//未知MSC时默认区号
	int INMaxStepLth; 			//语音业务最大单步步长，单位: 分钟
	int balanceGsu;        //是否为使用前余额:B017+B213
	int shortCDRCountry;	//超短话单国际长途、漫游开关
	int resultCode;			//优惠小区返回结果码
	int isOrgCellIdOrSai; 	//配置是否输出原始cellid信息
	int iSMIfalg;
	int initRoma;            //0 init时判断漫游1 init/update/term都需要判断漫游(西藏)
	int nRomaLongRelation; //长途类型的判断是否受边漫影响 0，无影响，1，有影响(例如省内边界漫游,并且边界归属区和被叫相同,并且主叫和被叫归属区不同，则长途类型是省内长途,默认1
	int iInitActiveSwitch;		//是否支持init激活参数配置，0，不支持，1支持
	int nCellidConvert;
	int iMonthCDRTime;		// 语音业务按月截单0,不截单>0,这个时间段都走截单流程
	char freeCdrField[512];
	int iDenyHBUser;			// 语音业务是否拒绝后付费用户，0 不拒绝，1 拒绝
	int volteCdrFalg;			//volte业务单独出单开关
	int inVolteCdrSwitch;		//VOLTE音视频切换截单开关
}SINPara;

//GGSN业务超长话单
typedef struct
{
	long longtime;			//超长时间/超长流量
	int type;				//长话单类型
}PSLONGCDR;

//PS业务参数
typedef struct
{
	PSLONGCDR longcdr;				//超长话单
	int longCDRTime;			//超长话单时长
	int longCDRVolumn;          //超长话单流量

	char shortCDRTime[1024];			//超短话单
	int shortCDROctet;			//超短话单
	int finalUnitAction;			//余额不足动作
	int redirectType;				//重定向地址类型
	int PSMaxStepAmount; 			//数据业务流量最大单步步长，单位: M
	int PSMaxStepDuration; 			//数据业务时长最大单步步长，单位: min
	char redirectServer[128];		//重定向地址
	char accumulateinfo[128];		//下线提示，获取累积量代码
	char szRedirectServerRecharge[128]; //充值期重定向地址
	char szRedirectServerLock[128];       //锁定期重定向地址
	int lRechargePageRG;    //充值页面Rating-Group
	char szBasicStateRecharge[4];    //充值期用户的基本状态basic_state
	char szBasicStateLock[4];    //锁定期用户的基本状态basic_state
	int cdrRatingGroupSwitch;			//话单中ratinggroup字段是否携带总流量组信息开关
	int nOfflineAllUsu;//0:只包含离线时间片的使用量(异常走离线时发送TERM RBR)；1:包含在线和离线的全部使用量(异常走离线时不发送TERM RBR)
	int iRGUNITSwitch;//清单中RG流量信息展示单位，0为Byte，1为KB
	int iAccuUNITSwitch;//清单中累积量信息展示单位，0为Byte，1为KB
	int nRatTypeSwitch;
	int nFinalUintResultCode;
	int nUsuOverloadRefuse;
	int iPsVisitArea;           //陕西数据业务话单AREA_CODE出3gpp解析出来的值
	char szServiceContextId[128];//LTE业务上传R3017
	int iCdrRatingSwitch;     //CCG按照RG出单控制开关

	int iGsuReplaceUsuSwitch;//湖北当USU>GSU的时候，使用GSU计费开关
	int iMonthPGWCDRTime;
	int iMonthAAACDRTime;	// 跨月截单开关0,不截单>0,这个时间段都走截单流程

	int dynamic_camp_on;	// 是否启用动态速率控制
	int LLBalance;			// 速率控制最小百分比
	int iDenyHBUser;		// 数据业务是否拒绝后付费用户，0 不拒绝，1 拒绝
	int	iLocChCellid;		//PGW位置变更时是否参考CELLID, 0 不参考, 1 参考
}SPSPara;
//5g业务参数
typedef struct
{
    long longCDRTime5G;            //超长话单时长
	long longCDRVolumn5G;          //超长话单流量
    char szServiceContextId5G[128];//5G业务上传R3017
    int iMonth5GCDRTime;		//5g 跨月截单
    int iDenyHBUser;			// 数据业务是否拒绝后付费用户，0 不拒绝，1 拒绝
    long lnMCondShortTime5G;         // 多条件过滤超短时长
    long lnMCondShortOct5G;          // 多条件过滤超小流量
    int shortCDROctet5G;			//超短话单流量
    long shortCDRTime5G;     ////超短话单时长
    int iDay5GCDRTime;			//5g按天截单
    std::vector<int> v_onlineLatn;  //SM落offline文件可按照本地网配置, 配置的本地网不落 offline 文件
    int index_timeout_sec;		//去重索引超时时间
    int index_SeqTimeOut_sec;  //离线文件话单清临时缓存写索引区间时间
    int iDynUseTable;		//动态步长使用借口表开关
    int iRgFilterSwitch;             // 5G RG过滤开关
    string str5GCNPLMN;   			 //异网漫游CNPlmn
}S5GPara;

//5G动态步长信息
struct STDynamicStepInfo5G
{
    long lnVolumeLimit;     //流量动态步长
    long lnTimeLimit;       //时长动态步长

    bool bGetSucceed;       //获取动态步长标志  true 成功，false 失败

    STDynamicStepInfo5G() : lnVolumeLimit(0), lnTimeLimit(0),bGetSucceed(false)
    {

    }

    void clear()
    {
        lnVolumeLimit = 0;
        lnTimeLimit = 0;
        bGetSucceed = false;
    }

};


typedef struct
{
	int debitControl;			//扣重控制参数
	char sp_id[256];            //加载的sp_id
	char debitSPCPorductId[256];//需扣重的SPC定义
	int iMonthCDRSwitch;		//跨月清单开关，0，关，1，开
	int iDenyHBUser;			// 增值业务是否拒绝后付费用户，0 不拒绝，1 拒绝
}SISMPPara;

typedef struct
{
	int debitflag;				//短信扣费记录表控制参数
	int iDenyHBUser;			// 短信业务是否拒绝后付费用户，0 不拒绝，1 拒绝
	char freeCdrField[512];
}SP2PSMSPara;

typedef struct
{
	char dslnumsuffix[256];	 //dsl计费号码后缀
	int iDenyHBUser;			// 宽带业务是否拒绝后付费用户，0 不拒绝，1 拒绝
	int iMonthDSLCDRTime;	// 跨月截单开关0,不截单>0,这个时间段都走截单流程
	int iDslLongCdrTime; 	// 超长截单阀值
}SDSLPara;

//AOC配置参数
typedef struct
{
	unsigned int serviceType;
	unsigned int networkType;
	unsigned int reqType;
	unsigned int callType;
	unsigned int isAocLowBalance;
	unsigned int aocLowBalance;
}SAocPara;


//结果码映射
typedef struct
{
	int resultCode;
	int	OCPResultCode;
	int	codeType;
}SResultCode;


/*typedef struct
{
	int  access;	//接入号
	int  country;	//国家号
	int	 province;	//归属省
	int  area;		//区号
	int	 carriers;	//运营商
	char phone[20];	//电话号码
	int  networkType;  	//网络类型
}SPhone;*/

typedef struct
{
	int access;		//接入号
	int	length;		//接入号长度
}ACCESS_INFO;

typedef struct
{
	int country;	//国家号
	char countryCode[16];
}COUNTRY_INFO;

typedef struct
{
	int  area;		//区号
	int  province;	//对应省份ID
	int	 carriers;	//固话运营商
	int	 length;	//区号长度
	char szArea[8];
	char szcarriers[10];//运营商码
	char sector_id[10];  // 资费
}AREA_INFO;

typedef struct
{
	char msccode[16];
	char area[8];
}CELL_INFO;

typedef struct
{
	int		area;		//对应区号
	int     province;	//省
	int		carriers;	//运营商
	long long max;		//号码区间
	long long min;		//号码区间
}MOBILE_INFO;

enum brand_type
{
	BRAND_2G = 1,
	BRAND_3G
};

typedef struct
{
	long long endMsisdn; 	//号段尾
	int homeProv;       //归属省
	int areaCode; 		//区号
	int userType; 		//用户类型
	int bizType;		//业务类型
	int cspid;          //运营商类型
	int networkType;  	//网络类型
	int flag;
}MSISDN_MAP_INFO;

typedef struct
{
	char serviceType[8];//业务类型
	char provCode[8];	  //省代码
	char areaCode[8];	  //区代码
}URGENT_NUMBER_INFO;

typedef struct
{
	int resultCode;		//结果码
	char basicState[8];	//主状态
	char extState[8];		//拓展状态
	char stausCd[8];	//后付费状态
	char stopType[8];	//停机状态
}USER_STATE_INFO;

typedef struct
{
	char areaCode[12]; 	 //区号信息
	char ecellAreaCode[12];
	char szmsc[16];
	int isProvEdge;		 //是否是省际漫游
}EDGE_ROAM_INFO;

typedef struct
{
	int prov_code;		     //PDSN对应省号或者国家码
	int area_code;
}PDSN_INFO;

typedef struct
{
	char prov_code[12];	//SGSN对应省号或者国家码
	char area_code[12];
}SGSN_INFO;

typedef struct
{
	int prov_code;
	int area_code;
}DSL_INFO;

typedef struct
{
	char para_key[64];
	int err_code;
	char biz_type[8];
}SMS_SEND_PRO_INFO;

typedef struct
{
	char area_code[16];
	char carriers[10];
}MSC_INFO;
typedef struct
{
    char area_code[16];
    char prov_code[16];
}PROV_INFO;
typedef struct
{
    std::string tac;
    std::string nFIPv4Address;
    std::string nFIPv6Address;
}IP_INFO;
typedef struct
{
	string msc_code;
	string iccarrier_code;
	string country_code;
	int area_code;
}MSC_COUNTRY;
struct sRoamBorderLine
{
	char m_sVisitMscId[11];		//来访交换机号
	char m_sVisitLacId[6];		//来访位置ID
	char m_sVisitCellId[6];		//来访小区ID
	char m_sHomeAreaCode[9];	//拜访对端区号
	char m_sEffDate[18];		//生效时间
	char m_sExpDate[18];		//失效时间
	char m_sMsisdn[16];			//计费号码
	char m_sBorderType[2];		// 1,省内 2,省际
	char m_sVisitAreaCode[9];	//归属区号
};

struct sRoamBorderLineData
{
	char home_msc_id[10];
	char visit_lac_id[10];
	char home_latn_id[10];
	char event_type_id[10];
	char bsid[100];
	char eff_date[10];
	char exp_date[10];
	char billing_object[10];
};


#endif

