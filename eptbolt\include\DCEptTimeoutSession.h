/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCEptTimeoutSession.h
*Indentifier：
*		
*Description：
*		异常处理基类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_EPT_TIMEOUT_SESSION_H__
#define __DC_EPT_TIMEOUT_SESSION_H__
#include "DCBizEpt.h"
#include "DCBasePlugin.h"
#include "DCAnsPara.h"
//
class DCEptTimeoutSession:public DCBizEpt,public DCBasePlugin
{
	public:

		DCEptTimeoutSession(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */) 	   :DCBasePlugin(category,func,version)
		{
		
		}


		virtual ~DCEptTimeoutSession()
		{
		
		}

		virtual int Work(void *data);

	protected:
		virtual int init();
		virtual int process(void* input, void* output);

	private:
		
		int SwitchVOICE(STBizMsg* bizMsg);
		int SwitchSMS(STBizMsg* bizMsg);
		int SwitchDATA(STBizMsg* bizMsg);
		int SwitchPGW(STBizMsg* bizMsg);
		int Switch5G(STBizMsg* bizMsg);
		int SwitchISMP(STBizMsg* bizMsg);
		int SwitchDSL(STBizMsg* bizMsg);

		int SendASR(STBizMsg* bizMsg);
		int SendRAR(STBizMsg* bizMsg);				//发送RAR消息
		private:
		DCAnsPara* m_anspara;
};

#endif

