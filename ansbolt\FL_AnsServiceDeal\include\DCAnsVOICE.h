/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsVOICE.h
*Indentifier：
*
*Description：
*		语音业务处理基类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_VOICE_H__
#define __DC_ANS_VOICE_H__

#include "DCAns.h"
#include "BizLenDef.h"
#include "ErrorCode.h"

#include "DCSeriaOp.h"

#include "DCAnsPara.h"
#include "TConfig.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "DCCommonIF.h"
#include "REMsgTypeDef.h"

using namespace ocs;

class DCAnsVOICE:public DCAns
{
	public:

		DCAnsVOICE();
		virtual ~DCAnsVOICE();

	public:

		int Work(void *data);

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);
		virtual int XdrEvent(STBizMsg* bizMsg);

};

#endif

