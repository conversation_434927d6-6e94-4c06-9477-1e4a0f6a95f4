#include "DCReqDSLTEL.h"
#include "BizCdrDefTEL.h"
#include "BizLenDef.h"
#include "ErrorCode.h"
#include "CDR.h"
#include "TSMPara.h"
#include "TConfig.h"
#include "UHead.h"
#include "DCCommonIF.h"

DCReqDSLTEL::DCReqDSLTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "", "");
}

DCReqDSLTEL::~DCReqDSLTEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "", "");
}

int DCReqDSLTEL::SwitchReqType(STBizMsg* bizMsg)
{
	if(!bizMsg->m_base)
	{
		return -1;
	}

	int ret = RET_SUCCESS;
	SCCRDSL* data =(SCCRDSL*)bizMsg->m_extend;
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;

	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				ret = Init(base, data, bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{

				ret = Update(base, data, bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{

				ret = Term(base, data, bizMsg);
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				ret = XdrEvent(base, data, bizMsg);
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "invalid request type[%s]", bizMsg->m_requestType);
				ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
			}
			break;
	}

	return ret;
}

int DCReqDSLTEL::Init(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int resultCode 						= 0;
	int roamType							= 0;
	long nextCCTime						= 0;
	string	iCreatFlag;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	AREA_INFO subVisit					= {0};
	SUserInfo *userInfo						= NULL;
	userInfo =(SUserInfo *) bizMsg->m_userinfo;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
	int inoaccountswitch = 1;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;



       if(base->usename.size()>32)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "username:[%s] length great than 32", base->usename.c_str());
	}

	data->userName = base->usename;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "insert username[%s],base username[%s]", data->userName.c_str(),base->usename.c_str());
	char usename[128] = {0};
	strcpy(usename,base->usename.c_str());
	ret = smpara->getsuffix(usename);
	if(0==ret)
	{
		data->productSpecID = "2200";
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "set R904/OCP_STR_PRODUCT_SPECID=2200", data->userName.c_str(),base->usename.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "username:[%s],no suffix in systemparam", base->usename.c_str());
	}

	//漫游类型
	if(1 == data->roamFlag)		// 当roamFlag = 1时表示省内漫游
	{
		roamType = 1;
	}
	else if(2 == data->roamFlag)	// 当roamFlag = 2 时表示国内漫游
	{
		roamType = 4;
	}
	else if(3 == data->roamFlag)	// 当roamFlag = 3 时表示国际漫游
	{
		roamType = 6;
	}
	else 							// 当raomflag值为非0,1,2,3或者未上报此字段时，全部按照未漫游处理，0也表示未漫游
	{
		roamType = 0;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "roam type [%d]", roamType);

	//获取配额信息
	if((conf = smpara->GetServiceQuotaConf(DSL_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "no find service quota config[%d]", DSL_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + conf->VALID_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	subVisit.area = base->subscription.area;  // 通过roamFlag 判断漫游类型无法获取拜访地，这里直接赋值

	//组装RER	消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdsl rbdsl;
	ocs::rsu_t rsu;

	ocs::usu   u;
	ocs::debit totalusu;
	ocs::rbext ext;
	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	head.type = RE_SERVICE_TYPE_INT_DSL_REQ;
	head.version = 2;
	head.sreq  = bizMsg->m_requestType;
	head.stamp = bizMsg->timestampCCR;
	head.session = bizMsg->m_sessionID;
	head.serial = bizMsg->m_serial;
	head.trace =  bizMsg->m_trace_flag;
	head.result = 0;
	head.topology = base->topology;
	head.prodinstid = userInfo->servID;
	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	head.creditCtlFlag = atoi(iCreatFlag.c_str());
	//100   MsgType
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_MESSAGE_TYPE[%s]" , RE_SERVICE_TYPE_STR_DSL_REQ);

	//000   会话ID
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_SESSION_ID[%s]" , bizMsg->m_sessionID);

	//101   EventTimeStamp
	sprintf(value, "%ld", bizMsg->timestampCCR);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RB_CODE_EVENT_TIMESTAMP[%s]" , value);

	//R01   付费号码
	rbdsl.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CHARGE[%s]" , base->subscription.phone.c_str());

	//R04  计费对象
	rbdsl.charged_nbr = base->subscription.phone;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER[%s]" , base->subscription.phone.c_str());

	//R71   会话开始时间
	rbdsl.sess_start_time = "0";
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_SESSION_START_TIME[%s]",  "0");

	//R85   用户付费属性标识
	rbdsl.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PAY_TYPE[1]","" );

	//R504 主叫号码归属费率区
	sprintf(value, "0%d", base->subscription.area);
	rbdsl.calling_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CALLING_AREA[%s]" , value);

	//R505 主叫号码拜访费率区
	sprintf(value, "0%d", subVisit.area);
	rbdsl.calling_varea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CALLING_VISIT_AREA[%s]" , value);

	//R5011 漫游类型
	rbdsl.roamtype = roamType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ROAM_TYPE[%d]" , roamType);

	//R5012 付费号码归属费率区
	sprintf(value, "0%d", base->subscription.area);
	rbdsl.charged_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CHARGING_AREA[%s]" , value);

	//R601 重发标记
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_REPEAT_FLAG[%s]" , "0");

	//R602 计费类型
	rbdsl.sreq = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_SESSION_STATE[%s]" , SM_SESSION_INITIAL_STRING);

	//R603  会话上次扣费开始时间
	rbdsl.pre_dtime = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PRE_DEBIT_TIME[%s]" , "0");

	//R604  本次计费请求开始时间
	sprintf(value, "%ld", bizMsg->timestampCCR);
	rbdsl.cur_dtime =bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RB_CODE_R_CUR_DEBIT_TIME[%s]" , value);

	//R605  是否进行使用量累计标识
	rbdsl.ratable_flag=0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_RATABLE_TIME[%s]" , "0");

	//R606  激活用户
	if(userInfo->isActive)
	{
		rbdsl.active_flag = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACTIVE_FLAG[%s]" , "1");
	}
	else
	{
		rbdsl.active_flag = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACTIVE_FLAG[%s]" , "0");
	}


	//R901  接入服务器IP
	rbdsl.access_addr = data->NASIP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACCESS_IP[%s]", data->NASIP.c_str());

	//R902  用户IP
	rbdsl.user_addr = data->frameIP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER_IP[%s]" , data->frameIP.c_str());

	//R903  授权有效时间
	sprintf(value, "%d", conf->VALID_TIME);
	rbdsl.valid_time = conf->VALID_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_VALIDITY_TIME[%s]" , value);

	//R904  ProductSpecID
	rbdsl.product_spec_id = data->productSpecID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PRODUCT_SPEC_ID[%s]" , data->productSpecID.c_str());

	//R905
	rbdsl.user_node_id = data->userNodeID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER_NODE_ID[%s]" , data->userNodeID.c_str());

	//组装预占组
	int numUnit = 0;
	if(conf->DURATION)
	{
		numUnit++;
	}
	if(conf->TOTAL_OCTETS)
	{
		numUnit++;
	}
	if(conf->INPUT_OCTETS)
	{
		numUnit++;
	}
	if(conf->OUTPUT_OCTETS)
	{
		numUnit++;
	}

	if(numUnit <1)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "invalid rating group :no rsu value [%d]", DSL_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	numUnit = 0;

	if(conf->DURATION)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_SECOND);
		rsu.unit = RB_UNIT_CODE_SECOND;
		sprintf(value, "%d", conf->DURATION);
		rsu.amount = conf->DURATION;
		domain.rsv.push_back(rsu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);

	}

	if(conf->TOTAL_OCTETS)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" , RB_UNIT_STR_TOTAL_BYTES);
		rsu.unit = 3;
		sprintf(value, "%lld", conf->TOTAL_OCTETS);
		rsu.amount = conf->TOTAL_OCTETS;
		domain.rsv.push_back(rsu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);

	}

	if(conf->INPUT_OCTETS)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_UP_BYTES);
		rsu.unit = 4;
		rsu.amount = conf->INPUT_OCTETS;
		sprintf(value, "%lld", conf->INPUT_OCTETS);
		domain.rsv.push_back(rsu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);

		numUnit++;
	}

	if(conf->OUTPUT_OCTETS)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_DOWN_BYTES);
		rsu.unit = 5;
		rsu.amount = conf->OUTPUT_OCTETS;
		sprintf(value, "%lld", conf->OUTPUT_OCTETS);
		domain.rsv.push_back(rsu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);
	}

	rbdsl.latn_id = userInfo->ilatnid;
	rbdsl.accumlator_info = 1;
	rbdsl.tariff_info     = 1;
	rbdsl.balance_info    = 1;
	rbdsl.rating_info     = 1;
	rbdsl.balance_query   = 1;

	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbdsl.acct_info = 1;
		rbdsl.test_info = 1;
	}
	//设置信令跟踪标志，通知RB需要信令跟踪
	sprintf(value, "%d", bizMsg->m_trace_flag);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_TRACE_FLAG[%s]" , value);


	UDBSQL *pExec =  pdbm->GetSQL(DSL_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, bizMsg->m_sessionID);
		pExec->BindParam(2, base->originHost);
		pExec->BindParam(3, (int)bizMsg->m_requestNumber);
		pExec->BindParam(4, data->callingNumber);
		pExec->BindParam(5, data->calledNumber);
		pExec->BindParam(6, data->userName);
		pExec->BindParam(7, data->productSpecID);
		pExec->BindParam(8, data->NASIP);
		pExec->BindParam(9, data->frameIP);
		pExec->BindParam(10, data->userNodeID);
		pExec->BindParam(11, SM_SESSION_INITIAL_CODE);
		pExec->BindParam(12, TORB_ACTION);
		pExec->BindParam(13, (long)nextCCTime);
		pExec->BindParam(14, userInfo->aocType);
		pExec->BindParam(15, (int)base->subscriptionType);
		pExec->BindParam(16, base->subscription.phone);

		sprintf(value, "0%d", base->subscription.area);
		pExec->BindParam(17, value);

		pExec->BindParam(18, base->subscription.carriers);
		pExec->BindParam(19, base->subscription.country);
		pExec->BindParam(20, data->callingNumber);
		pExec->BindParam(21, data->calledNumber);
		pExec->BindParam(22, roamType);
		pExec->BindParam(23, (long)bizMsg->timestampCCR);
		pExec->BindParam(24, (long)bizMsg->timestampCCR);
		pExec->BindParam(25, SM_CDR_SEQ_NUM);
		pExec->BindParam(26, 1);
		pExec->BindParam(27, SM_CDR_VERSION);
		pExec->BindParam(28, SM_CDR_TICKETTYPE);
		pExec->BindParam(29, timeStamp);
		pExec->BindParam(30, base->topology);
		pExec->BindParam(31, 0);
		pExec->BindParam(32, 0);
		pExec->BindParam(33, SERVICES_CENARIOUS_DSL);
		pExec->BindParam(34, base->subUnified);
		pExec->BindParam(35, data->callingNumber);
		pExec->BindParam(36, data->calledNumber);
		pExec->BindParam(37, userInfo->servID);
		pExec->BindParam(38, userInfo->custID);
		pExec->BindParam(39, MASTER_PRODUCTID_CDMA);

		sprintf(value, "0%d", subVisit.area);
		pExec->BindParam(40, value);
		pExec->BindParam(41, base->routeRecord);

		pExec->BindParam(42, bizMsg->m_trace_flag);
		pExec->BindParam(43, (long)bizMsg->m_serial);
		pExec->BindParam(44, (long)base->timestamp);
		pExec->BindParam(45, userInfo->userType);
		pExec->BindParam(46, userInfo->mvnoID);
		pExec->BindParam(47, base->topology);
		pExec->BindParam(48, userInfo->ilatnid);
		pExec->BindParam(49, userInfo->lnAcctID);
		pExec->BindParam(50, bizMsg->m_szServiceContextIDStr);
		pExec->BindParam(51, bizMsg->addressIpv6);
		pExec->BindParam(52, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		std::string sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "insert ok", "");
	bizMsg->m_resultcode = 2001;
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbdsl);
		m_en.encode(ext);
		bizMsg->data = (char *)m_en.data();


		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbdsl);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	return RET_SUCCESS;
}


int DCReqDSLTEL::Update(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	long nextCCTime						= 0;
	ocs::SCCRDataUnit* USU					= NULL;

	ret = compocRER(base, data, bizMsg,nextCCTime);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "compose RBR failed[%d]", ret);
		return ret;
	}

	USU = &(base->USU);
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	// 保存到bizCache 中
	UDBSQL *pExec = pdbm->GetSQL(DSL_UpdateSession_req);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新返回信息
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, SM_SESSION_UPDATE_CODE);
			pExec->BindParam(2, TORB_ACTION);
			pExec->BindParam(3, (int)bizMsg->m_requestNumber);
			pExec->BindParam(4, bizMsg->m_serial);
			pExec->BindParam(5, nextCCTime);
			pExec->BindParam(6, (long)bizMsg->timestampCCR);
			pExec->BindParam(7, base->timestamp);
			pExec->BindParam(8, (long)USU->duration);
			pExec->BindParam(9, (long)USU->unitTotal);
			pExec->BindParam(10, (long)USU->unitInput);
			pExec->BindParam(11, (long)USU->unitOutput);
			pExec->BindParam(12, bizMsg->m_sBatchId.c_str());
			pExec->BindParam(13, bizMsg->addressIpv6);
			pExec->BindParam(14, bizMsg->m_sessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	return RET_SUCCESS;
}

int DCReqDSLTEL::Term(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	long nextCCTime						= 0;
	ocs::SCCRDataUnit* USU					= NULL;

	ret = compocRER(base, data, bizMsg,nextCCTime);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "compose RBR failed[%d]", ret);
		return ret;
	}


	USU = &(base->USU);
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	// 保存到bizCache 中
	UDBSQL *pExec = pdbm->GetSQL(DSL_UpdateSession_req);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新返回信息
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, SM_SESSION_TERMINATION_CODE);
			pExec->BindParam(2, TORB_ACTION);
			pExec->BindParam(3, (int)bizMsg->m_requestNumber);
			pExec->BindParam(4, bizMsg->m_serial);
			pExec->BindParam(5, nextCCTime);
			pExec->BindParam(6, (long)bizMsg->timestampCCR);
			pExec->BindParam(7, (long)base->timestamp);
			pExec->BindParam(8, (long)USU->duration);
			pExec->BindParam(9, (long)USU->unitTotal);
			pExec->BindParam(10, (long)USU->unitInput);
			pExec->BindParam(11, (long)USU->unitOutput);
			pExec->BindParam(12, bizMsg->m_sBatchId.c_str());
			pExec->BindParam(13, bizMsg->addressIpv6);
			pExec->BindParam(14, bizMsg->m_sessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "term execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	return RET_SUCCESS;
}

int DCReqDSLTEL::XdrEvent(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;
	int traceNumOnff					= 0;
	long nextCCTime						= 0;
	string iCreatFlag;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	ocs::SCCRDataUnit USU;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	SUserInfo *userInfo						= NULL;
	userInfo =(SUserInfo *) bizMsg->m_userinfo;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	AREA_INFO *subVisit					= bizMsg->m_visit;
	int roamType						= bizMsg->m_roamtype;
       long ln_sess_start                                     = 0;

	//获取配额信息
	if((conf = smpara->GetServiceQuotaConf(DSL_RATING_GROUP)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "no find service quota config[%d]", DSL_RATING_GROUP);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	USU = base->USU;

	//漫游类型
	if(1 == data->roamFlag)		// 当roamFlag = 1时表示省内漫游
	{
		roamType = 1;
	}
	else if(2 == data->roamFlag)	// 当roamFlag = 2 时表示国内漫游
	{
		roamType = 4;
	}
	else if(3 == data->roamFlag)	// 当roamFlag = 3 时表示国际漫游
	{
		roamType = 6;
	}
	else 							// 当raomflag值为非0,1,2,3或者未上报此字段时，全部按照未漫游处理，0也表示未漫游
	{
		roamType = 0;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "roam type [%d]", roamType);

 	data->userName = base->usename;
	char usename[128] = {0};
	strcpy(usename,base->usename.c_str());
	ret = smpara->getsuffix(usename);
	if(0==ret)
	{
		data->productSpecID = "2200";
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "set R904/OCP_STR_PRODUCT_SPECID=2200,%s,%s", data->userName.c_str(),base->usename.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "username:[%s],no suffix in systemparam", base->usename.c_str());
	}
	long minsec = 0;
	long lastsec = 0;
	long lastAmount = 0;
       ln_sess_start = base->starttime;
	sprintf(value,"%lld",base->starttime);
	int day = DCCommonIF::GetDateDiff( value,USU.duration,minsec,lastsec)+1;
	int spiltflag = day > 1? 1: 0;
       long LockTime = 0;
       long BeforeMonth =  0;
       if(spiltflag)
       {
                // 有跨天，重新计算封账账期
                std::string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("LockTime");
                LockTime = smpara->GetBillAttr(servAttr);
                if(LockTime < 0) LockTime = 0;
                sprintf(value, "%ld01%06ld", base->timestamp/100000000, LockTime);
                LockTime = atol(value);
                DCCommonIF::ChangeMonth(value, -1);
                BeforeMonth = atoi(value);
       }

	for(int i =0;i<day;i++)
	{

		//组装RER	消息
		ocs::UHead uhd;
		ocs::rbhead head;
		ocs::rbdomain domain;
		ocs::rbdsl rbdsl;
		ocs::rbext ext;
		ocs::rsu_t rsu;

		ocs::usu   u;
		ocs::debit totalusu;
		uhd.car = "1";
		uhd.uid = bizMsg->m_uid;
		uhd.trace = bizMsg->m_trace_flag;
		uhd.checkKey = bizMsg->m_strCheckKey;
		head.type = RE_SERVICE_TYPE_INT_DSL_REQ;
		head.version = 1;
		head.sreq  = 4;
		head.stamp = bizMsg->timestampCCR;
		head.session = bizMsg->m_sessionID;
		head.session.erase(0,3);
		head.serial = bizMsg->m_serial;
		head.trace =  bizMsg->m_trace_flag;
		head.result = 0;
		head.topology = base->topology;
		head.prodinstid = userInfo->servID;
		if("2100" == bizMsg->m_payMentMode)
		{
			iCreatFlag = "3";
		}
		else
		{
			iCreatFlag = "2";
		}
		head.creditCtlFlag = atoi(iCreatFlag.c_str());
		//100   MsgType
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_MESSAGE_TYPE[%s]" , RE_SERVICE_TYPE_STR_DSL_REQ);

		//000   会话ID
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_SESSION_ID[%s]" , bizMsg->m_sessionID);

		//101   EventTimeStamp
		sprintf(value, "%ld", bizMsg->timestampCCR);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose RB_CODE_EVENT_TIMESTAMP[%s]" , value);

		//R01   付费号码
		rbdsl.charged_nbr = base->subscription.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CHARGE[%s]" , base->subscription.phone.c_str());

		//R04  计费对象
		rbdsl.charged_nbr = base->subscription.phone;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER[%s]" , base->subscription.phone.c_str());

		//R71   会话开始时间
		sprintf(value, "%ld", ln_sess_start);
		rbdsl.sess_start_time = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_SESSION_START_TIME[%s]",  value);

		//R85   用户付费属性标识
		rbdsl.pay = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PAY_TYPE[1]","" );

		//R504 主叫号码归属费率区
		sprintf(value, "0%d", base->subscription.area);
		rbdsl.calling_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CALLING_AREA[%s]" , value);

		//R505 主叫号码拜访费率区
		sprintf(value, "0%d", subVisit->area);
		rbdsl.calling_varea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CALLING_VISIT_AREA[%s]" , value);

		//R5011 漫游类型
		rbdsl.roamtype = roamType;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ROAM_TYPE[%d]" , roamType);

		//R5012 付费号码归属费率区
		sprintf(value, "0%d", base->subscription.area);
		rbdsl.charged_harea = value;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CHARGING_AREA[%s]" , value);

		//R602 计费类型
		rbdsl.sreq = 1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_SESSION_STATE[%s]" , SM_SESSION_INITIAL_STRING);


		//R605  是否进行使用量累计标识
		rbdsl.ratable_flag=0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_RATABLE_TIME[%s]" , "0");

		//R606  激活用户
		if(userInfo->isActive)
		{
			rbdsl.active_flag = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACTIVE_FLAG[%s]" , "1");
		}
		else
		{
			rbdsl.active_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACTIVE_FLAG[%s]" , "0");
		}

		//R901  接入服务器IP
		rbdsl.access_addr = data->NASIP;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACCESS_IP[%s]", data->NASIP.c_str());

		//R902  用户IP
		rbdsl.user_addr = data->frameIP;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER_IP[%s]" , data->frameIP.c_str());

		//R903  授权有效时间
		sprintf(value, "%d", conf->VALID_TIME);
		rbdsl.valid_time = conf->VALID_TIME;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_VALIDITY_TIME[%s]" , value);

		//R904  ProductSpecID
		rbdsl.product_spec_id = data->productSpecID;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PRODUCT_SPEC_ID[%s]" , data->productSpecID.c_str());

		//R905
		rbdsl.user_node_id = data->userNodeID;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER_NODE_ID[%s]" , data->userNodeID.c_str());

		//B038
		string servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectArrear");
		rbdsl.neg_debit= smpara->GetBillAttr(servAttr);
		if(rbdsl.neg_debit == -1)
		rbdsl.neg_debit = 1;
		//B03总使用量
		int billduration = 0;
		if(1==day)
		{
                     //不跨天
			//R603   本次计费请求开始时间
			rbdsl.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbdsl.pre_dtime);

			//R604  本次计费请求结束时间
			rbdsl.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose cur_dtime[%ld]" , rbdsl.cur_dtime);

                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			sprintf(value, "%d", USU.duration);
			billduration = USU.duration;
			totalusu.amount = USU.duration;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);
		}
		else if(0==i)
		{
		       // 跨天第一条单
			//R603   本次计费请求开始时间
			rbdsl.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbdsl.pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			rbdsl.cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose cur_dtime[%ld]" , rbdsl.cur_dtime);

                     //重新计算账期
                    if(base->timestamp >= LockTime)
                    {
                            // 封账之后取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else if(rbdsl.cur_dtime/100000000 <  base->timestamp/100000000)
                    {
                            //封账前取上月账期
                            bizMsg->billcycle = BeforeMonth;
                    }
                    else if(rbdsl.cur_dtime/100000000 ==  base->timestamp/100000000)
                    {
                            //封账前取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else
                    {
                            bizMsg->billcycle = rbdsl.cur_dtime/100000000;
                    }
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			sprintf(value, "%d", minsec);
			billduration = minsec;
			totalusu.amount = minsec;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);
		}
		else if(i==day-1)
		{
		       // 跨天最后一条单
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			rbdsl.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbdsl.pre_dtime);

			//R604  本次计费请求结束时间
			rbdsl.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose cur_dtime[%ld]" , rbdsl.cur_dtime);

                      //重新计算账期
                    if(base->timestamp >= LockTime)
                    {
                            // 封账之后取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else if(rbdsl.cur_dtime/100000000 <  base->timestamp/100000000)
                    {
                            //封账前取上月账期
                            bizMsg->billcycle = BeforeMonth;
                    }
                    else if(rbdsl.cur_dtime/100000000 ==  base->timestamp/100000000)
                    {
                            //封账前取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else
                    {
                            bizMsg->billcycle = rbdsl.cur_dtime/100000000;
                    }
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

                     //B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			sprintf(value, "%d", lastsec);
			billduration = lastsec;
			totalusu.amount = lastsec;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);

		}
		else
		{
		       // 跨天中间单
			//R603   本次计费请求开始时间
			base->starttime = DCCommonIF::SetStartTime(base->starttime);
			rbdsl.pre_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose pre_dtime[%ld]" , rbdsl.pre_dtime);

			//R604  本次计费请求结束时间
			base->starttime = DCCommonIF::SetEndTime(base->starttime);
			rbdsl.cur_dtime = base->starttime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose cur_dtime[%ld]" , rbdsl.cur_dtime);

                      //重新计算账期
                    if(base->timestamp >= LockTime)
                    {
                            // 封账之后取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else if(rbdsl.cur_dtime/100000000 <  base->timestamp/100000000)
                    {
                            //封账前取上月账期
                            bizMsg->billcycle = BeforeMonth;
                    }
                    else if(rbdsl.cur_dtime/100000000 ==  base->timestamp/100000000)
                    {
                            //封账前取本月账期
                            bizMsg->billcycle = base->timestamp/100000000;
                    }
                    else
                    {
                            bizMsg->billcycle = rbdsl.cur_dtime/100000000;
                    }
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "billcycle[%ld]",	bizMsg->billcycle);

			//B03总使用量
			totalusu.unit = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",	RB_UNIT_STR_SECOND);
			billduration =  24*3600;

			totalusu.amount = 24*3600;
			domain.dbv.push_back(totalusu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%d]", totalusu.amount);

		}

		sprintf(value,"%ld",lastAmount);
		ext.kv["lastAmount"] = value;
		lastAmount = totalusu.amount;

		sprintf(value,"%d",spiltflag);
		ext.kv["spiltflag"] = value;
		spiltflag++;


		//总使用量
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]", RB_UNIT_STR_TOTAL_BYTES);
		sprintf(value, "%lld", USU.unitTotal);
		totalusu.unit = 3;
		totalusu.amount = (long)USU.unitTotal;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);

		//上行流量
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]", RB_UNIT_STR_UP_BYTES);
		sprintf(value, "%lld", USU.unitInput);
		totalusu.unit = 4;
		totalusu.amount = (long)USU.unitInput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]",value);

		//下行流量
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]", RB_UNIT_STR_DOWN_BYTES);
		sprintf(value, "%lld", USU.unitOutput);
		totalusu.unit = 5;
		totalusu.amount = (long)USU.unitOutput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);


		//话单是否剔重
		servAttr=string(bizMsg->m_szServiceContextIDStr)+string("OffTime");
		int  eliminateflag = smpara->GetBillAttr(servAttr);
		sprintf(value,"%d",eliminateflag);
		if(eliminateflag>=0)
		{
			ext.kv["eliminateflag"] = value;
		}
		//R07离线批价扣费标识
		servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
		int ratingflag= smpara->GetBillAttr(servAttr);
		sprintf(value,"%d",ratingflag);
		if(ratingflag>=0)
		{
			ext.kv["ratingflag"] = value;
		}

		rbdsl.latn_id = userInfo->ilatnid;
		rbdsl.accumlator_info = 1;
		rbdsl.tariff_info     = 1;
		rbdsl.balance_info    = 1;
		rbdsl.rating_info     = 1;
		rbdsl.balance_query   = 1;
		//CER主叫
		ext.kv["CER"] = bizMsg->m_subNumber;
		//设置信令跟踪标志，通知RB需要信令跟踪
		sprintf(value, "%d", bizMsg->m_trace_flag);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_TRACE_FLAG[%s]" , value);

		ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
		ext.kv["anstopic"] = bizMsg->m_anstopic;
		ext.kv["taskId"] = bizMsg->m_taskId;
		ext.kv["sourceId"] = bizMsg->m_sourceId;
		ext.kv["operListId"] = bizMsg->m_operListId;
		// 获取批次号
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, smpara->GetCommonPara()->iBatchIdTime, 2);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());
		ext.kv["batchId"] = bizMsg->m_sBatchId;
		try
		{
			m_en.clear();
			m_en.encode(uhd);
			m_en.encode(head);
			m_en.encode(domain);
			m_en.encode(rbdsl);
			m_en.encode(ext);
			bizMsg->data = (char*)m_en.data();
			bizMsg->m_vectorMsg.push_back(bizMsg->data);

			//打印head消息
			m_print.clear();
			m_print.print(uhd);
			m_print.print(head);
			m_print.print(domain);
			m_print.print(rbdsl);
			m_print.print(ext);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
		}
		catch(exception& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","encode failed");
			return ERR_ENCODE_CODE;
		}


		UDBSQL *pExec = pdbm->GetSQL(DSL_InsertSession_Offline);
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			if(0==i)
			{
				pExec->BindParam(1, bizMsg->m_sessionID);
			}
			else
			{
				value[0]='\0';
				sprintf(value,"%s;00%d",bizMsg->m_sessionID,i+1);
				pExec->BindParam(1, value);
			}
			pExec->BindParam(2, base->originHost);
			pExec->BindParam(3, (int)bizMsg->m_requestNumber);
			pExec->BindParam(4, data->callingNumber);
			pExec->BindParam(5, data->calledNumber);
			pExec->BindParam(6, data->userName);
			pExec->BindParam(7, data->productSpecID);
			pExec->BindParam(8, data->NASIP);
			pExec->BindParam(9, data->frameIP);
			pExec->BindParam(10, data->userNodeID);
			pExec->BindParam(11, SM_SESSION_XDR_CODE);
			pExec->BindParam(12, TORB_ACTION);
			pExec->BindParam(13, nextCCTime);
			pExec->BindParam(14, 0);
			pExec->BindParam(15, (int)base->subscriptionType);
			pExec->BindParam(16, base->subscription.phone);

			sprintf(value, "0%d", base->subscription.area);
			pExec->BindParam(17, value);

			pExec->BindParam(18, base->subscription.carriers);
			pExec->BindParam(19, base->subscription.country);
			pExec->BindParam(20, data->callingNumber);
			pExec->BindParam(21, data->calledNumber);
			pExec->BindParam(22, roamType);
			pExec->BindParam(23, rbdsl.pre_dtime);
			pExec->BindParam(24, rbdsl.cur_dtime);
			pExec->BindParam(25, SM_CDR_SEQ_NUM);
			pExec->BindParam(26, 1);
			pExec->BindParam(27, SM_CDR_VERSION);
			pExec->BindParam(28, SM_CDR_TICKETTYPE);
			pExec->BindParam(29, timeStamp);
			pExec->BindParam(30, base->topology);
			pExec->BindParam(31, 0);
			pExec->BindParam(32, 0);
			pExec->BindParam(33, SERVICES_CENARIOUS_DSL);
			pExec->BindParam(34, base->subUnified);
			pExec->BindParam(35, data->callingNumber);
			pExec->BindParam(36, data->calledNumber);
			pExec->BindParam(37, userInfo->servID);
			pExec->BindParam(38, userInfo->custID);
			pExec->BindParam(39, MASTER_PRODUCTID_CDMA);

			sprintf(value, "0%d", subVisit->area);
			pExec->BindParam(40, value);
			pExec->BindParam(41, base->routeRecord);

			pExec->BindParam(42, traceNumOnff);
			pExec->BindParam(43, (long)bizMsg->m_serial);
			pExec->BindParam(44, bizMsg->billcycle);
		//	pExec->BindParam(45,  userInfo->szBalanceAocType);该表中没有SM_STR_BALANCE_AOC_TYPE
			pExec->BindParam(45, (long)billduration);
			pExec->BindParam(46, (long)USU.unitTotal);
			pExec->BindParam(47, (long)USU.unitInput);
			pExec->BindParam(48, (long)USU.unitOutput);
//
			pExec->BindParam(49, userInfo->lnAcctID);
			pExec->BindParam(50, userInfo->userType);
			pExec->BindParam(51, userInfo->ilatnid);
			pExec->BindParam(52, userInfo->mvnoID);
			pExec->BindParam(53, bizMsg->m_szServiceContextIDStr);
			pExec->BindParam(54, 1);
			long switchId = atol(base->smExt.kv["SwitchId"].c_str());
			pExec->BindParam(55, switchId);
			pExec->BindParam(56, bizMsg->m_sBatchId.c_str());
			pExec->BindParam(57, bizMsg->addressIpv6);
			pExec->BindParam(58, iCreatFlag);
			pExec->Execute();
			pExec->Connection()->Commit();

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "insert ok", "");
		}
		catch(UDBException& e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "insert execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	return RET_SUCCESS;
}


int DCReqDSLTEL::compocRER(SCCRBase* base, SCCRDSL* data,STBizMsg* bizMsg,long &nextCCTime)
{
	int ret 							= RET_SUCCESS;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	ocs::SCCRDataUnit* USU					= NULL;
	ocs::SCCRDataUnit TUSU  ;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	long lnRelastGsuTime = 0;
	char szBatchId[32] = {0};
	int roamType = 0;
	char szSubNbr[20]					= {0};
	long  balance                       =0;

	SUserInfo *userInfo						= NULL;
	userInfo =(SUserInfo *) bizMsg->m_userinfo;
	TSMPara * smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;

	USU = &(base->USU);

	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdsl rbdsl;
	ocs::rsu_t rsu;
	ocs::usu   u;
	ocs::debit totalusu;
	ocs::rbext ext;

	UDBSQL *pQuery  = NULL;
	pQuery =  pdbm->GetSQL(DSL_GetSessionInfo);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(22, value);//SM_INT_FREE_FLAG
			int nFreeFlag=atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  bizMsg->m_sessionID, "free flag[%d]",nFreeFlag);


			pQuery->GetValue(2, value);//SM_INT_SESSION_STATUS
			uhd.car = "1";
			uhd.uid = bizMsg->m_uid;
			uhd.trace = bizMsg->m_trace_flag;
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_DSL_REQ;
			head.version = 2;
			head.sreq  = bizMsg->m_requestType;
			head.stamp = bizMsg->timestampCCR;
			head.session = bizMsg->m_sessionID;
			head.serial = bizMsg->m_serial;
			head.trace =  bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = base->topology;

			pQuery->GetValue(45, value);
			head.creditCtlFlag = atoi(value);

			//100   MsgType
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_MESSAGE_TYPE[%s]" , RE_SERVICE_TYPE_STR_DSL_REQ);

			//000   会话ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_SESSION_ID[%s]" , bizMsg->m_sessionID);

			//101   EventTimeStamp
			sprintf(value, "%ld", bizMsg->timestampCCR);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_EVENT_TIMESTAMP[%s]" , value);

			//R85   用户付费属性标识
			rbdsl.pay = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PAY_TYPE[1]", "");

			// serv_id
			pQuery->GetValue(39, value);
			head.prodinstid = userInfo->servID = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose servID[%ld]", userInfo->servID);

			// latn_id
			pQuery->GetValue(38, value);
			rbdsl.latn_id = userInfo->ilatnid = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose latn_id[%d]", userInfo->ilatnid);
			bizMsg->m_ilatnId = atoi(value);

			pQuery->GetValue(23, value);//RE_LNG_LAST_GSU_TIME
			lnRelastGsuTime= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SERVTYPE(bizMsg->m_serviceContextID),  "", "last gsu time[%s]", value);

			if(bizMsg->m_requestType == SM_SESSION_TERMINATION_CODE)
			{
				if(!lnRelastGsuTime)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE,  bizMsg->m_sessionID, "last gsu time is zero, set usu[%u] to zero", USU->duration);
                	 USU->duration = 0;
				}
			}

			//判断网元上报的USU是否超大
			if((USU->duration >2*lnRelastGsuTime) &&(1 == smpara->GetPSPara()->nUsuOverloadRefuse))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "object:usu[%u]>2 times gsu[%ld],refuse", USU->duration,lnRelastGsuTime);
				return SM_OCP_USU_OVERLOAD;
			}

			pQuery->GetValue(37, value);//SM_LNG_BALANCE_INFO
			balance = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "balance[%lld]",balance);

			//获取配额信息
			if((conf = smpara->GetServiceQuotaConf(DSL_RATING_GROUP,balance)) == 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "no find service quota config[%d]", DSL_RATING_GROUP);
				return SM_OCP_UNABLE_TO_COMPLY;
			}

			//当前时间用于会话超时
			time_t et;
			time(&et);
			nextCCTime = et + conf->VALID_TIME;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "next cc time[%ld]", nextCCTime);
			sprintf(timeStamp,"%ld",base->timestamp);

			//总使用时长
			pQuery->GetValue(3, value);
			TUSU.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "duration[%u]", TUSU.duration);

			//总使用总流量
			pQuery->GetValue(4, value);
			TUSU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitTotal[%lld]", TUSU.unitTotal);

			//总使用上行流量
			pQuery->GetValue(5, value);
			TUSU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitInput[%lld]", TUSU.unitInput);

			//总使用下行流量
			pQuery->GetValue(6, value);
			TUSU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitOutput[%lld]", TUSU.unitOutput);

			//R71   会话开始时间
			pQuery->GetValue(8, value);
			rbdsl.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_SESSION_START_TIME[%s]" , value);

			//R01   付费号码
			pQuery->GetValue(10, value);
			rbdsl.charged_nbr = value;
			strcpy(szSubNbr,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CHARGE[%s]" , value);

			//R04  计费对象
			pQuery->GetValue(10, value);
			rbdsl.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER[%s]",  value);

			//R504 主叫号码归属费率区
			pQuery->GetValue(11, value);
			rbdsl.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CALLING_AREA[%s]",  value);

			//R505 主叫号码拜访费率区
			pQuery->GetValue(13, value);
			rbdsl.calling_varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CALLING_VISIT_AREA[%s]" , value);

			//R5011 漫游类型
			pQuery->GetValue(12, value);
			rbdsl.roamtype = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ROAM_TYPE[%s]" , value);

			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);
			rbdsl.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CHARGING_AREA[%s]" , value);

			//R601 重发标记
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_REPEAT_FLAG[%s]" , "0");

			//R602 计费类型
			rbdsl.sreq = bizMsg->m_requestType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_SESSION_STATE[%d]" , bizMsg->m_requestType);

			//R603  会话上次扣费开始时间
			pQuery->GetValue(9, value);//RE_LNG_CURRENT_CCR_TIME
			rbdsl.pre_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PRE_DEBIT_TIME[%s]" , value);

			//R604  本次计费请求开始时间
			sprintf(value, "%ld", bizMsg->timestampCCR);
			rbdsl.cur_dtime = atol(value);

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_CUR_DEBIT_TIME[%s]" , value);

			//R605  是否进行使用量累计标识
			rbdsl.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_RATABLE_TIME[%s]" , "0");

			//R901  接入服务器IP
			pQuery->GetValue(14, value);
			rbdsl.access_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_ACCESS_IP[%s]" ,value);

			//R902  用户IP
			pQuery->GetValue(15, value);
			rbdsl.user_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER_IP[%s]", value);

			//R903  授权有效时间
			sprintf(value, "%d", conf->VALID_TIME);
			rbdsl.valid_time = conf->VALID_TIME;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_VALIDITY_TIME[%s]" ,value);

			//R904  ProductSpecID
			pQuery->GetValue(16, value);
			rbdsl.product_spec_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_PRODUCT_SPEC_ID[%s]" ,value);

			//R905  Use_Node_ID
			pQuery->GetValue(17, value);
			rbdsl.user_node_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_R_USER_NODE_ID[%s]" ,value);

			//CDR_STR_BATCH_ID
			pQuery->GetValue(43, szBatchId);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "last BatchId[%s]" ,szBatchId);

			memset(value, 0x0, sizeof(value));
			pQuery->GetValue(44, value); //OCP_STR_IP_PDP_ADDR_IPV6
			if(bizMsg->addressIpv6.length() == 0 && strlen(value) > 0)
			{
				bizMsg->addressIpv6 = value;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "get  addressIpv6[%s]" ,bizMsg->addressIpv6.c_str());
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "not find session[%s]", bizMsg->m_sessionID);
			return  SM_OCP_UNKNOWN_SESSION_ID;
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "query execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//累计总使用量
	TUSU.duration += USU->duration;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "TUSU druation[%u]", TUSU.duration);

	TUSU.unitTotal += USU->unitTotal;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

	TUSU.unitInput += USU->unitInput;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

	TUSU.unitOutput += USU->unitOutput;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);

	//update
	if(bizMsg->m_requestType == SM_SESSION_UPDATE_CODE)
	{
		//组装预占组
		int numUnit = 0;
		if(conf->DURATION)
		{
			numUnit++;
		}
		if(conf->TOTAL_OCTETS)
		{
			numUnit++;
		}
		if(conf->INPUT_OCTETS)
		{
			numUnit++;
		}
		if(conf->OUTPUT_OCTETS)
		{
			numUnit++;
		}

		if(numUnit <1)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "invalid rating group :no rsu value [%d]", DSL_RATING_GROUP);
			return SM_OCP_UNABLE_TO_COMPLY;
		}

		//时长
		if(conf->DURATION)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_SECOND);
			sprintf(value, "%d", conf->DURATION);
			rsu.unit = 1;
			rsu.amount = conf->DURATION;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);

			domain.rsv.push_back(rsu);
		}
		//总流量
		if(conf->TOTAL_OCTETS)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_TOTAL_BYTES);
			sprintf(value, "%lld", conf->TOTAL_OCTETS);
			rsu.unit = 3;
			rsu.amount = conf->TOTAL_OCTETS;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);
			domain.rsv.push_back(rsu);

		}
		//上行流量
		if(conf->INPUT_OCTETS)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_UP_BYTES);
			sprintf(value, "%lld", conf->INPUT_OCTETS);
			rsu.unit = 4;
			rsu.amount = conf->INPUT_OCTETS;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]", value);
			domain.rsv.push_back(rsu);

		}
		//下行流量
		if(conf->OUTPUT_OCTETS)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_UNIT[%s]" ,RB_UNIT_STR_DOWN_BYTES);
			sprintf(value, "%lld", conf->OUTPUT_OCTETS);
			rsu.unit = 5;
			rsu.amount = conf->OUTPUT_OCTETS;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_REQUESTED_AMOUNT[%s]" ,value);
			domain.rsv.push_back(rsu);

		}
	}

	//B30更新使用量
	//时长
	u.unit = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_USED_UNIT[%s]" ,RB_UNIT_STR_SECOND);
	sprintf(value, "%u", USU->duration);
	u.amount = USU->duration;
	domain.usv.push_back(u);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_USED_AMOUNT[%s]" ,value);

	//总流量
	u.unit = 3;
	u.amount = USU->unitTotal;
	domain.usv.push_back(u);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_USED_UNIT[%s]" ,RB_UNIT_STR_TOTAL_BYTES);

	u.unit = 4;
	u.amount = USU->unitInput;
	domain.usv.push_back(u);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_USED_AMOUNT[%ld]" ,u.amount);

	u.unit = 5;
	u.amount = USU->unitOutput;
	domain.usv.push_back(u);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_USED_UNIT[%s]" ,RB_UNIT_STR_DOWN_BYTES);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_USED_AMOUNT[%ld]" ,u.amount);


	if(bizMsg->m_requestType == SM_SESSION_TERMINATION_CODE)
	{
		//B03总使用量
		//时长
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]",  RB_UNIT_STR_SECOND);
		sprintf(value, "%d", TUSU.duration);
		totalusu.unit = 1;
		totalusu.amount = (long)TUSU.duration;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]",value);

		//总使用量
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]", RB_UNIT_STR_TOTAL_BYTES);
		sprintf(value, "%lld", TUSU.unitTotal);
		totalusu.unit = 3;
		totalusu.amount = (long)TUSU.unitTotal;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);

		//上行流量
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]", RB_UNIT_STR_UP_BYTES);
		sprintf(value, "%lld", TUSU.unitInput);
		totalusu.unit = 4;
		totalusu.amount = (long)TUSU.unitInput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]",value);

		//下行流量
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_UNIT[%s]", RB_UNIT_STR_DOWN_BYTES);
		sprintf(value, "%lld", TUSU.unitOutput);
		totalusu.unit = 5;
		totalusu.amount = (long)TUSU.unitOutput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose RB_CODE_B_DEBIT_AMOUNT[%s]", value);
	}

	//info 日志
	DCDATLOG("SM00010:%d%ld%ld%ld", USU->duration,USU->unitTotal,USU->unitInput,USU->unitOutput);

	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;

	rbdsl.accumlator_info = 1;
	rbdsl.tariff_info     = 1;
	rbdsl.balance_info    = 1;
	rbdsl.rating_info     = 1;
	rbdsl.balance_query   = 1;

	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbdsl.acct_info = 1;
		rbdsl.test_info = 1;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	if(0 == strlen(szBatchId))
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID, smpara->GetCommonPara()->iBatchIdTime, 1);
	}
	else
	{
		bizMsg->m_sBatchId = szBatchId;
	}
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbdsl);
		m_en.encode(ext);
		bizMsg->data = (char*)m_en.data();

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbdsl);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	return RET_SUCCESS;
}
