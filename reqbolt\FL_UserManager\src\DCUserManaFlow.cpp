#include "DCUserManaFlow.h"
#include "DCLogMacro.h"
#include "DCOBJSet.h"
#include "ErrorCode.h"
#include "DCBizMsgDef.h"
int DCUserManaFlow::init()
{
	
	return 0;
}

const char* DCUserManaFlow::desc()
{
	return "FC_UserManager";
}

//消息头|公共消息|业务消息
int DCUserManaFlow::process(void* input, void* output)
{	
	DCOBJSet* pset = (DCOBJSet*)input;	
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	
	int ret  = 0;
	ret = call_all(pset,NULL);
	if(ret!=0 && RET_OVER != ret)
	{
		bizMsg->m_resultcode = ret;
	}
	return ret;
}


DYN_PLUGIN_CREATE(DCUserManaFlow, "FC_USERMANAGER", "FC_UserManager", "1.0.0")

