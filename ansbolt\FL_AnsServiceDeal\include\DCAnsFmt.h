/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsFmt.h
*Indentifier：
*
*Description：
*		应答消息解析类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_FMT_H__
#define __DC_ANS_FMT_H__
#include "DCOBJSet.h"
#include "DCAns.h"

class DCAnsFmt : public DCAns
{
	public:

		DCAnsFmt();
		virtual ~DCAnsFmt();

	public:

		int Work(void *data);

	private:

		int FormatCommon(DCOBJSet* pset);
	private:
		unsigned int m_nRevErrNum;
};

#endif

