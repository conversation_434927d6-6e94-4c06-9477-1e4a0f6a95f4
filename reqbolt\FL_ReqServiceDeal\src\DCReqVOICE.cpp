#include "DCReqVOICE.h"
#include "TSMPara.h"
#include "ErrorCode.h"
#include "func_sqlindex.h"
#include "OCPMsgDef.h"
#include <sys/time.h>
#include "DCMqProduceServer.h"
#include "UHead.h"

DCReqVOICE::DCReqVOICE()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

DCReqVOICE::~DCReqVOICE()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

int DCReqVOICE::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG,  "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}

	STBizMsg *bizMsg = (STBizMsg*)data;

	//PERF LOG
	//cvar->m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));

	//bizMsg->m_perf.GetTimeT2_B();

	int ret = SwitchReqType(bizMsg);

	bizMsg->m_resultcode = ret;
	//bizMsg->m_perf.GetTimeT2_E();

	return ret;
}

int DCReqVOICE::SwitchReqType(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}


int DCReqVOICE::GetCallStartTime(long termtime, int overtime, char* value)
{
	int year= termtime/10000000000;
	int mon = (termtime/100000000)%100;
	int day = (termtime/1000000)%100;
	int hour= (termtime/10000)%100;
	int min = (termtime/100)%100;
	int sec = termtime%100;

	sec-=overtime%60;
	if(sec<0)
	{
		sec+=60;
		min-=1;
	};
	min-=(overtime-(overtime/3600)*3600)/60;
	if(min<0)
	{
		min+=60;
		hour-=1;
	}
	hour-=overtime/3600;
	if(hour<0)
	{
		hour+=24;
		day-=1;
		if(day==0)
		{
			mon-=1;
			if(mon==0)
			{
				mon+=12;
				year-=1;
			}
			switch(mon)
			{
			case 2:{
				day=28;
				break;
				   }
			case 4:;
			case 6:;
			case 9:;
			case 11:{
				day=30;
				break;
					}
			default:day=31;
			}
			if((year%4 && !year%100)||!year%400)
			{
				day=29;
			}
		}
	}
	if(mon>12||mon<=0)	return -1;
	if(day>31||day<=0)	return -2;
	if(hour>=24||hour<0)return -3;
	if(min>=60||min<0)	return -4;
	if(sec>=60||sec<0)	return -5;
	sprintf(value, "%.4d%.2d%.2d%.2d%.2d%.2d", year, mon, day, hour, min, sec);

	return 0;
}

int DCReqVOICE::Check(SCCRBase* base, STBizMsg* bizMsg)
{
	int ret								= 0;
	char value[BIZ_DATA_LEN_256]			= {0};
	SUserInfo *userInfo					= NULL;
	userInfo = (SUserInfo * )bizMsg->m_userinfo;
	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbquery rbr;

	ocs::rbdomain rbdomain;
	ocs::rbext ext;

	uhd.uid = bizMsg->m_uid;
	uhd.car = "1";
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	ext.kv["sourceId"] = base->source;
	ext.kv["operListId"] = bizMsg->m_operListId;
	head.type = RE_SERVICE_TYPE_INT_BALANCE_REQ;
	head.sreq  = bizMsg->m_requestType;
	head.stamp = bizMsg->timestampCCR;
	head.session = bizMsg->m_sessionID;
	head.serial = bizMsg->m_serial;
	head.trace =  bizMsg->m_trace_flag;
	head.result = 0;
	head.topology = base->topology;
	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.charged_nbr = base->subscription.phone;
	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose latnid[%d]" , rbr.latn_id);
	sprintf(value, "0%d", base->subscription.area);
	rbr.charged_harea = value;
	rbr.balance_query = 1;
	rbr.bill_cycle = "1";



	//当前时间用于会话超时
	time_t et;
	time(&et);
	long nextCCTime = et + 300; 	//默认是300s
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	UDBSQL *pInsert =  pdbm->GetSQL(Voice_UpdateSession_event);
	try
	{
		pInsert->DivTable(bizMsg->m_sessionID);
		pInsert->UnBindParam();
		pInsert->BindParam(1, SM_SESSION_EVENT_CODE);
		pInsert->BindParam(2, (int)bizMsg->m_requestNumber);
		pInsert->BindParam(3, TORB_ACTION);
		pInsert->BindParam(4, bizMsg->m_serial);
		pInsert->BindParam(5, nextCCTime);
		pInsert->BindParam(6, (long)bizMsg->timestampCCR);
		pInsert->BindParam(7, (long)base->timestamp);
		pInsert->BindParam(8, bizMsg->m_trace_flag);
		pInsert->BindParam(9, bizMsg->m_sessionID);
		pInsert->BindParam(10, base->topology.c_str());
		pInsert->Execute();
		pInsert->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		std::string sql;
		pInsert->GetSqlString(sql);
		pInsert->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_VOICE_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	//CEE被叫 CER主叫
	ext.kv["CER"] = bizMsg->m_callingNumber;
	ext.kv["CEE"] = bizMsg->m_calledNumber;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(rbdomain);
		m_en.encode(rbr);
		m_en.encode(ext);


		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(rbdomain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "cache voice event session ok", "");

	return RET_SUCCESS;
}

int DCReqVOICE::sendCCA(STBizMsg* bizMsg)
{
	char serviceFlowId[48] = {0};   //Service-Flow-Id
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = 2001;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.serial = bizMsg->m_serial;
	cca.requestType = 5;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial        = bizMsg->m_serial;
	DCCommonIF::GetServiceFlowID(serviceFlowId);
	cca.ServiceFlowID = serviceFlowId;
       cca.trace = bizMsg->m_trace_flag;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	bizMsg->data = (char *)m_en.data();
	bizMsg->m_RARFlag = RET_OVER;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "send CCA data[%s]",bizMsg->data.c_str());

	//DCMqProduceServer* producer = bizMsg->m_producer;
/*	string sendmsg = HexEncode(m_en.data(),m_en.size());

       struct timeval tmv;
       char buf[20];

       // 头部加固定16位微妙时间戳
       gettimeofday(&tmv, NULL);
       sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
       sendmsg.insert(0, buf);

	//int ret = producer->Produce(sendmsg.c_str(), sendmsg.length(), bizMsg->m_anstopic);
	bizMsg->m_topictype = 1;
	int ret = bizMsg->m_plugin->call((void *)(&sendmsg), (void *)bizMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Produce CCAVOICE failed, ret=%d\n", ret);
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCA to MQ,topic[%s] msglen:%d", bizMsg->m_anstopic, m_en.size());
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "send CCA data[%s]",sendmsg.c_str());*/

	return 	RET_OVER;
}

