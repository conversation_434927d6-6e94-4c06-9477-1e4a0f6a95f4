include ../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)
COMMON_INC=$(LBSPUBROOT)/SM/common/include
TLIBFLOW= $(RELEASE_PATH)/plugin/libAnsBaseFlow.so
TLIBBOLT= $(RELEASE_PATH)/plugin/libAnsBolt.so
#MAIN_BIN= $(RELEASE_PATH)/bin/ansserv
#voice= $(RELEASE_PATH)/bin/ansserv_voice
#dsl= $(RELEASE_PATH)/bin/ansserv_dsl

INCLUDE =-I$(AVRO)/include -I$(DCLOGCLI)/include -I$(COMMON_INC) -I$(ITF)/include -I$(DFM_INC_PATH) -I$(PWD) -I$(MQ)/include -I$(TXML)/include  -I$(KPI_SENDER_INC) -I$(EVT_CHECK_INC)
INCLUDEALL =$(INCLUDE) -I$(DCLOGCLI)/include -I$(JSTORM_INC) -I$(JSON_INC)  -I$(ZK_INC) -I$(DCA_INC)/json_dca -I$(ACE_INC_PATH)

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib  -L$(DCLOGCLI)/lib -L$(DFM_LIB_PATH) -L$(JSTORM_LIB) -L$(JSON_LIB) -L$(ZK_LIB) -L$(DCA_LIB) -L$(MQ)/lib -L$(TXML)/lib -L$(ACE_LIB_PATH) -L$(LBSPUBROOT)/release/realbilling/lib -L$(KPI_SENDER_LIB) -L$(EVT_CHECK_LIB)
LIBSLIST= -ldclogcli -lCommonIF -ltinyxml -lkpisender -lCheckSDK

LIBSALL=-ldclogcli -ldfm -lcstorm -lzookeeper_mt  -ldcmq -lrocketmq64 -ltinyxml -lACE -lCommonIF -lkpisender -lCheckSDK

libtarget=$(TLIBFLOW) $(TLIBBOLT)

CXXFLAGS += -std=c++11

.PHONY: all clean dup
all:$(TLIBFLOW)	$(TLIBBOLT) $(MAIN_BIN) 
#$(MAIN_BIN):DCAnsServ.o
#	@echo "build DCAnsServ----"
#	$(CC)  $(CXXFLAGS) $(LFLAGS) -o $(MAIN_BIN) DCAnsServ.o $(LIBPATH) $(LIBSALL)
#$(voice):DCAnsServ_voice.o
#	@echo "build DCAnsServ_voice----"
#	$(CC)  $(CXXFLAGS) $(LFLAGS) -o $(voice) DCAnsServ_voice.o $(LIBPATH) $(LIBSALL)
#$(dsl):DCAnsServ_dsl.o
#	@echo "build ansserv_dsl----"
#	$(CC)  $(CXXFLAGS) $(LFLAGS) -o $(dsl) DCAnsServ_dsl.o $(LIBPATH) $(LIBSALL)
$(TLIBBOLT):DCAnsBolt.o desc_ansbolt.o
	@echo "build libAnsBolt.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBBOLT) DCAnsBolt.o desc_ansbolt.o $(LIBPATH) $(LIBSALL)
$(TLIBFLOW): DCAnsBaseFlow.o desc_ansbaseflow.o
	@echo "build libAnsBaseFlow.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBFLOW) DCAnsBaseFlow.o desc_ansbaseflow.o $(LIBPATH) $(LIBSLIST)
	
#DCAnsServ_voice.o:DCAnsServ_voice.cpp
#	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)	
#DCAnsServ_dsl.o:DCAnsServ_dsl.cpp
#	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)	
#DCAnsServ.o:DCAnsServ.cpp
#	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)	
DCAnsBolt.o:DCAnsBolt.cpp
	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)	
DCAnsBaseFlow.o:DCAnsBaseFlow.cpp
	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)
desc_ansbolt.o:desc_ansbolt.cpp
	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@
desc_ansbaseflow.o:desc_ansbaseflow.cpp
	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@
desc_ansbolt.cpp:desc_ansbolt.clog
	$(TOOL)/clogtool -i $< -o $@
desc_ansbaseflow.cpp:desc_ansbaseflow.clog
	$(TOOL)/clogtool -i $< -o $@
clean:
	@rm -rf DCAnsBolt.o DCAnsBaseFlow.o desc_ansbaseflow.o desc_ansbolt.o desc_ansbaseflow.o desc_ansbolt.o

dup:
	@cp -pf $(MAIN_BIN) $(PROJECT_RPATH)/bin && echo "dup $(MAIN_BIN) to $(PROJECT_RPATH)/bin"
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
	
