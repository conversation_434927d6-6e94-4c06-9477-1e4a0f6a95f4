include ../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include
TLIBFLOW= $(RELEASE_PATH)/plugin/libReqBaseFlow.so
TLIBBOLT= $(RELEASE_PATH)/plugin/libReqBolt.so
TLIBREBOLT= $(RELEASE_PATH)/plugin/libReBolt.so
TLIBRTESTBOLT= $(RELEASE_PATH)/plugin/libTestBolt.so
TLIBROUTBOLT= $(RELEASE_PATH)/plugin/libRoutBolt.so
##MAIN_BIN= $(RELEASE_PATH)/bin/reqserv
##voice = $(RELEASE_PATH)/bin/reqserv_voice
##dsl = $(RELEASE_PATH)/bin/reqserv_dsl

INCLUDE =-I$(AVRO)/include -I$(DCLOGCLI)/include -I$(ITF)/include -I$(COMMON_INC) -I$(DFM_INC_PATH) -I$(PWD)/include  -I$(TXML)/include -I$(MQ)/include -I$(KPI_SENDER_INC) -I$(EVT_CHECK_INC) -I$(CTG_CHECK)/inc
INCLUDEALL =$(INCLUDE) -I$(DCLOGCLI)/include -I$(JSTORM_INC) -I$(JSON_INC)  -I$(ZK_INC) -I$(DCA_INC)/json_dca -I$(ACE_INC_PATH)

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(DCLOGCLI)/lib -L$(DFM_LIB_PATH) -L$(JSTORM_LIB) -L$(JSON_LIB) -L$(ZK_LIB) -L$(DCA_LIB) -L$(MQ)/lib $(TXML)/lib/libtinyxml.a -L$(ACE_LIB_PATH) -L$(LBSPUBROOT)/release/realbilling/lib -L$(KPI_SENDER_LIB) -L$(EVT_CHECK_LIB) -L$(CTG_CHECK_LIB) -L$(SEQ_CACHE)/lib
LIBSLIST=  -ldclogcli  -lCommonIF -lkpisender -lCheckSDK -lCtgCheckSDK -lCtgCheckSDK

LIBSALL=-ldclogcli -ldfm -lcstorm -lzookeeper_mt  -ldcmq -lACE -lrocketmq64  -lCommonIF -lkpisender -lCheckSDK -lCtgCheckSDK

libtarget=$(TLIBFLOW) $(TLIBBOLT) $(TLIBREBOLT) $(TLIBROUTBOLT)

CFLAGS += -std=c++11

.PHONY:all clean dup

all: $(TLIBREBOLT) $(TLIBRTESTBOLT) $(TLIBFLOW) $(TLIBBOLT) $(TLIBROUTBOLT)
$(TLIBROUTBOLT):DCRoutBolt.o
	@echo "build libRoutBolt.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBROUTBOLT) DCRoutBolt.o $(LIBPATH) $(LIBSALL)
$(TLIBREBOLT):DCReBolt.o
	@echo "build libReBolt.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBREBOLT) DCReBolt.o $(LIBPATH) $(LIBSALL)
$(TLIBBOLT):DCReqBolt.o desc_ReqBolt.o
	@echo "build libReqBolt.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBBOLT) DCReqBolt.o desc_ReqBolt.o $(LIBPATH) $(LIBSALL)
$(TLIBRTESTBOLT):CTestBoltAns.o
	@echo "build libTestBolt.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBRTESTBOLT) CTestBoltAns.o $(LIBPATH) $(LIBSALL)
$(TLIBFLOW): DCReqBaseFlow.o desc_ReqBaseFlow.o
	@echo "build libReqBaseFlow.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBFLOW) DCReqBaseFlow.o desc_ReqBaseFlow.o $(LIBPATH) $(LIBSLIST)
DCRoutBolt.o:DCRoutBolt.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)
DCReBolt.o:DCReBolt.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)
CTestBoltAns.o:CTestBoltAns.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)	$(INCLUDEALL)
DCReqBolt.o:DCReqBolt.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)
DCReqBaseFlow.o:DCReqBaseFlow.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)
desc_ReqBaseFlow.o:desc_ReqBaseFlow.cpp
	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@
desc_ReqBolt.o:desc_ReqBolt.cpp
	$(CC) -c $(CXXFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@
desc_ReqBaseFlow.cpp:desc_ReqBaseFlow.clog
	$(TOOL)/clogtool -i $< -o $@
desc_ReqBolt.cpp:desc_ReqBolt.clog
	$(TOOL)/clogtool -i $< -o $@
clean:
	@rm -rf DCReqBolt.o DCReqBaseFlow.o DCReBolt.o DCRoutBolt.o CTestBoltAns.o desc_ReqBolt.o desc_ReqBaseFlow.o

dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"

