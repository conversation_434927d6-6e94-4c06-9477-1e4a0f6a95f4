/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       BizDataDef.h
*Indentifier：
*
*Description：
*       转换DCC AVP为SM内部数据结构
*Version：
*       V1.0
*Author:
*

*Finished：
*
*History:
*
********************************************/
#ifndef _BIZ_DATA_DEF_H_
#define _BIZ_DATA_DEF_H_

#include <vector>
#include <string.h>
#include "BizLenDef.h"


using namespace std;

#define BIZ_DATA_LEN_1024	1024
#define BIZ_DATA_LEN_512	512
#define BIZ_DATA_LEN_256	256
#define BIZ_DATA_LEN_128 	128
#define BIZ_DATA_LEN_32 	32
#define BIZ_DATA_LEN_8 		8
#define BIZ_DATA_LEN_4 		4

//信用单位
const int CREDITUNIT_TIMELEN    = 1;
const int CREDITUNIT_MONEY      = 2;
const int CREDITUNIT_TOTALVAL   = 3;
const int CREDITUNIT_UPVAL      = 4;
const int CREDITUNIT_DOWNVAL    = 5;
const int CREDITUNIT_COUNT      = 6;


//Requested-Action
#define SM_REQUESTED_ACTION_DEBIT 	0
#define SM_REQUESTED_ACTION_REFUND 	1
#define SM_REQUESTED_ACTION_CHECK 	2
#define SM_REQUESTED_ACTION_QUERY_RATABLE_1 	113
#define SM_REQUESTED_ACTION_QUERY_RATABLE_2 	115

#define SM_TABLE_TYPE_QUERY_RATABLE				5


//session state
#define SM_SESSION_INITIAL_CODE  			1
#define SM_SESSION_INITIAL_STRING 			"1"
#define SM_SESSION_UPDATE_CODE 			    2
#define SM_SESSION_UPDATE_STRING 			"2"
#define SM_SESSION_TERMINATION_CODE 		3
#define SM_SESSION_TERMINATION_STRING 	    "3"
#define SM_SESSION_EVENT_CODE 			    4
#define SM_SESSION_EVENT_STRING 			"4"
/*new xdr session*/
#define SM_SESSION_XDR_CODE 	            5
/*new xdr session*/
#define SM_SESSION_EVENT_REFUND_CODE 	5
#define SM_SESSION_EVENT_REFUND_STRING 	"5"
#define SM_SESSION_EVENT_BALANCE_CODE 	6
#define SM_SESSION_EVENT_BALANCE_STRING "6"
#define SM_SESSION_UPDATE_FIRST_CODE 	7
#define SM_SESSION_UPDATE_FIRST_STRING "7"

//当前CCR 消息处理状态
#define STATUS_IDLE					0
#define RECORD_ACTION				1
#define TORB_ACTION					2
#define RECVRB_ACTION				3
#define SESSION_TIMEOUT_ACTION	10

//计费单元定义
#define RB_UNIT_CODE_SECOND           					1
#define RB_UNIT_CODE_CENT        						2
#define RB_UNIT_CODE_TOTAL_BYTES        				3
#define RB_UNIT_CODE_UP_BYTES        					4
#define RB_UNIT_CODE_DOWN_BYTES        				    5
#define RB_UNIT_CODE_TIME        						6
#define RB_UNIT_STR_SECOND           					"1"
#define RB_UNIT_STR_CENT        						"2"
#define RB_UNIT_STR_TOTAL_BYTES        				    "3"
#define RB_UNIT_STR_UP_BYTES        					"4"
#define RB_UNIT_STR_DOWN_BYTES					        "5"
#define RB_UNIT_STR_TIME        						"6"

#define VOICE		1
#define SMS		    2
#define DATA		3
#define ISMP		4
#define DSL		    5
#define PGW		    6
#define CCG		    7
#define RATA		8
#define HRS		    9  //HIGH-RISK-SERVICE
#define OFFLINE		10  //OFFLINE
#define DATA_5G     11


//日志业务类型
typedef enum
{
	SM_DEFAULT_TYPE=-1,
	SM_OTHER_TYPE=0,	//other
	SM_VOICE_TYPE = 1,	//语音
	SM_P2PSMS_TYPE,		//短信
	SM_DATA_TYPE,		//数据业务
	SM_ISMP_TYPE,		//ismp业务
	SM_DSL_TYPE, 		//DSL
	SM_5G_TYPE
}SM_LOG_TYPE;
#define SERVTYPE(a) a<=DSL&&a>=VOICE?(SM_LOG_TYPE)a:SM_OTHER_TYPE


//AVP存在标记组
struct SAVPElement
{
	unsigned int 		AVP1:1;
	unsigned int 		AVP2:1;
	unsigned int 		AVP3:1;
	unsigned int 		AVP4:1;

	unsigned int 		AVP5:1;
	unsigned int 		AVP6:1;
	unsigned int 		AVP7:1;
	unsigned int 		AVP8:1;

	unsigned int 		AVP9:1;
	unsigned int 		AVP10:1;
	unsigned int 		AVP11:1;
	unsigned int 		AVP12:1;

	unsigned int 		AVP13:1;
	unsigned int 		AVP14:1;
	unsigned int 		AVP15:1;
	unsigned int 		AVP16:1;
	unsigned int		AVP17:1;
};

union UAVPCheck
{
	SAVPElement AVP;
	unsigned short check;
};


/***************超时消息数据**************/
struct STimeoutInfo
{
	long			serial;								//DCC序列号
	char			sessionID[BIZ_DATA_LEN_256];			//会话ID
	char			originHost[BIZ_DATA_LEN_128];		//源主机
	unsigned int	serviceContextID;					//业务ID
	unsigned int	requestType;						//请求类型
	unsigned int	requestNumber;						//会话请求序列号
	int			routeRecord;						//路由信息
};
/************************************************/


/***************REA消息数据*****************/
struct STFeeItem
{
	int iFee;
	int iItem;
	int iPriority;
	STFeeItem()
	{
		iFee = 0;
		iItem = 0;
		iPriority = 0;
	}
};


//授权、实扣、补款信息B10、B03、B04
struct SREAamount
{
	int			type;
	int			unit;
	long		value;
	SREAamount()
	{
		type = 0;
		unit = 0;
		value = 0;
	}
};

//累积量信息B06
struct SREAAccumulator
{
	int			accountID;
	int			unit;
	int			value;
	int			total;
	SREAAccumulator()
	{
		accountID = 0;
		unit = 0;
		value = 0;
		total = 0;
	}
};

//资费信息B07
struct SREATariff
{
	int			TariffID;
	int			value;
	SREATariff()
	{
		TariffID = 0;
		value = 0;
	}
};

//余额改变、余额信息B08、B21
struct SREABalance
{
	int			accountID;
	int			unit;
	int			value;
	int			total;
	SREABalance()
	{
		accountID = 0;
		unit = 0;
		value = 0;
		total = 0;
	}
};

//费率B20
struct SREARate
{
	int			time;
	int			unit;
	int			step;
	int			price;
	SREARate()
	{
		time = 0;
		unit = 0;
		step = 0;
		price = 0;
	}
};

struct SUseInfo
{
	long id;
	long unit;
	long change;
	long total;
	SUseInfo()
	{
		id = 0;
		unit = 0;
		change = 0;
		total = 0;
	}
};

struct STariffInfo
 {
	 long offerId;
	 long acctItemId;
	 long measure;
	 long amount;
	 long billingDuration;
	 long distFee;
	 long groupid;
	 long ofr_inst_id;
	 long counts;
	 long refacctItemId;
	 long ori_tariff;
	 long disct_tariff;
	 STariffInfo()
	 {
		 offerId = 0;
		 acctItemId = 0;
		 measure = 0;
		 amount = 0;
		 billingDuration = 0;
		 distFee = 0;
		 groupid = 0;
		 ofr_inst_id = 0;
		 counts = 0;
		 refacctItemId = 0;
		 ori_tariff = 0;
		 disct_tariff = 0;
	 }
 };



struct SOriChargeInfo
{
	long acctItemId;
	long measure;
	long oriAmount;
	SOriChargeInfo()
	{
		acctItemId = 0;
		measure = 0;
		oriAmount = 0;
	}
};

 struct SAccumInfo
  {
	  long accu_type_id;
	  long offerId;
	  long unit;
	  long amount;
	  long total;
	  long ofrinstid;
	  long accu_id;
  	  long ratableBalanceId;
  	  long billingLatn;  	//计费号码本地网
  	  long iLatnid;			//量本归属本地网
	  // 江西特殊改造，按后付费的格式输出
      int updateflag;
      long usage_voice;
      long usage_sms;
      long accu_use_id;
      long billingCycle; // 账期
      
	  SAccumInfo()
	  {
	  	  accu_id = 0;
		  offerId = 0;
		  ratableBalanceId = 0;
		  unit = 0;
		  amount = 0;
		  total = 0;
		  ofrinstid = 0 ;
		  accu_type_id = 0;
		  billingLatn = 0;
		  iLatnid = 0;
		  updateflag = 0;
		  usage_voice = 0;
		  usage_sms = 0;
		  accu_use_id = 0;
		  billingCycle = 0;
	  }
  };


 struct STariffAccumCDRInfo
 {
	 long offerId;
	 long acctItemId;
	 long measure;
	 long amount;
	 long ori_acctItemId;
	 long ori_amount;
	 long billingDuration;
	 long distFee;
	 long groupid;
	 long ofrinstid;
	 long counts;
	 long ratableValue;
	 long basefee;
	 long lnOriTariff;		// 标批费用
	 long lnDisctTariff;	// 优惠费用
	 char sz_accumInfo[BIZ_DATA_LEN_1024];
	 long totalValue;
	 
	 int flag;
	 STariffAccumCDRInfo()
	 {
		 offerId = 0;
		 acctItemId = 0;
		 measure = 0;
		 amount = 0;
		 ori_acctItemId = 0;
		 ori_amount = 0;
		 billingDuration = 0;
		 distFee = 0;
		 flag = 0;
		 groupid = 0;
		 ofrinstid = 0;
		 counts = 0;
		 ratableValue = 0;
		 basefee = 0;
		 flag = 0;
		 lnOriTariff = 0;
		 lnDisctTariff = 0;
		 memset(sz_accumInfo,0,sizeof(sz_accumInfo));
	 }
 };

 struct SREAInfo
 {
	char sz_balanceInfo[BIZ_TEMP_LEN_256];
	char sz_balanceInfo2[BIZ_TEMP_LEN_256];
	char sz_tariffIdInfo[BIZ_TEMP_LEN_1024];
	char sz_accumlatorInfo[BIZ_TEMP_LEN_256];
	char sz_chargeInfo[BIZ_TEMP_LEN_256];
	char szPricingPlanID[BIZ_TEMP_LEN_128];
	char sz_oriChargeInfo[BIZ_TEMP_LEN_256];
	int  sz_gAmount;

	SREAInfo()
	{
		clear();
	}

	void clear()
	{
		memset(sz_balanceInfo, 0, sizeof(sz_balanceInfo));
		memset(sz_balanceInfo2, 0, sizeof(sz_balanceInfo2));
		memset(sz_tariffIdInfo, 0, sizeof(sz_tariffIdInfo));
		memset(sz_accumlatorInfo, 0, sizeof(sz_accumlatorInfo));
		memset(sz_chargeInfo, 0, sizeof(sz_chargeInfo));
		memset(szPricingPlanID, 0, sizeof(szPricingPlanID));
		memset(sz_oriChargeInfo, 0, sizeof(sz_oriChargeInfo));
		sz_gAmount = 0;
	}

	SREAInfo& operator=(const SREAInfo& rhs)
	{
		memcpy(this->sz_balanceInfo, rhs.sz_balanceInfo, sizeof(rhs.sz_balanceInfo));
		memcpy(this->sz_balanceInfo2, rhs.sz_balanceInfo2, sizeof(rhs.sz_balanceInfo2));
		memcpy(this->sz_tariffIdInfo, rhs.sz_tariffIdInfo, sizeof(rhs.sz_tariffIdInfo));
		memcpy(this->sz_accumlatorInfo, rhs.sz_accumlatorInfo, sizeof(rhs.sz_accumlatorInfo));
		memcpy(this->sz_chargeInfo, rhs.sz_chargeInfo, sizeof(rhs.sz_chargeInfo));
		memcpy(this->szPricingPlanID, rhs.szPricingPlanID, sizeof(rhs.szPricingPlanID));
		memcpy(this->sz_oriChargeInfo, rhs.sz_oriChargeInfo, sizeof(rhs.sz_oriChargeInfo));
		this->sz_gAmount = rhs.sz_gAmount;
		return *this;
	}

	bool IsTruncate()
	{
		if (strlen(this->sz_balanceInfo) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		if (strlen(this->sz_balanceInfo2) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		if (strlen(this->sz_tariffIdInfo) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		if (strlen(this->sz_accumlatorInfo) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		if (strlen(this->sz_chargeInfo) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		if (strlen(this->szPricingPlanID) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		if (strlen(this->sz_oriChargeInfo) >= BIZ_TEMP_LEN_256)
		{
			return true;
		}

		return false;
	}
 };


 struct DataCDRInfo
 {
 	char rating[BIZ_TEMP_LEN_128];
	char szProductOfferId[BIZ_TEMP_LEN_128];
	char balanceInfo[BIZ_DATA_LEN_256];
	char balanceInfo2[BIZ_DATA_LEN_256];
	char tarifInfo[BIZ_DATA_LEN_1024];
	char accumuInfo[BIZ_DATA_LEN_1024];
	char sessionId[BIZ_DATA_LEN_256];
	char chargeInfo[BIZ_DATA_LEN_256];
	char orichargeInfo[BIZ_DATA_LEN_256];
	char planInfo[BIZ_DATA_LEN_256];
	char sessionStart[BIZ_DATA_LEN_32];
	char sessionCurrent[BIZ_DATA_LEN_32];
	char szVlr[BIZ_DATA_LEN_32];
	char szNewLocInfo[BIZ_TEMP_LEN_128];
	STFeeItem stFeeItem[BIZ_DATA_LEN_4];
	char Day[BIZ_DATA_LEN_4];
	char CallingArea[BIZ_DATA_LEN_8];
	char CalledArea[BIZ_DATA_LEN_8];
	char CallingVlr[BIZ_DATA_LEN_32];
	char CalledVlr[BIZ_DATA_LEN_32];
	char MscId[BIZ_DATA_LEN_32];
	char LacId[BIZ_DATA_LEN_32];
	char CallingCellid[BIZ_DATA_LEN_32];
	char CalledCellid[BIZ_DATA_LEN_32];
	char user_domain[BIZ_DATA_LEN_32];
	char CallingNbr[BIZ_DATA_LEN_32];
	char CalledNbr[BIZ_DATA_LEN_32];
	char CallingParty[BIZ_DATA_LEN_32];
	char CalledParty[BIZ_DATA_LEN_32];

	char ticketId[BIZ_TEMP_LEN_128];
	int ticketSeq;

//add hubing
	char ratType[BIZ_DATA_LEN_8];
	char cellid[BIZ_DATA_LEN_32];
	char nrCellId[BIZ_DATA_LEN_32];
	char msc[BIZ_DATA_LEN_8];
	char mnc[BIZ_DATA_LEN_8];
	char mcc[BIZ_DATA_LEN_8];
	char tac[BIZ_DATA_LEN_8];
	char userMsc[BIZ_DATA_LEN_32];
	char userLocationInfo[BIZ_TEMP_LEN_128];
	char servID[BIZ_DATA_LEN_32];
	char imsi[BIZ_DATA_LEN_32];
	char ambrUplink[BIZ_DATA_LEN_32];
	char ambrDownlink[BIZ_DATA_LEN_32];
	unsigned long long money;
	unsigned long long duration;
	unsigned long long total;
	unsigned long long output;
	unsigned long long input;
	unsigned long long usedduration;
	unsigned long long usedtotal;
	unsigned long long usedinput;
	unsigned long long usedoutput;
	unsigned long long outputtemp;
	unsigned long long inputtemp;
	unsigned long long totaltemp;
	unsigned long long durationtemp;
	unsigned int longCdrFlag;
	unsigned long long discount_fee;
	long long octCdrTotalVolume;

	unsigned long long tariffChangeDuration;
	unsigned long long tariffChangeTotal;
	unsigned long long tariffChangeInput;
	unsigned long long tariffChangeOutput;
	//add for linux optimize
	int nServiceScenarious;
	int nCallType;
	int nLongType;
	int nRomatype;
	int nChargeType;
	long long lCdrVersionSerial;
	int nResultCode;
	int nFeeType;
	long Payment;
	int nCutnum;//用于记录拆单条数
	int seqNum; //用于传递截单拆单序列号
	int oper_list_id;
	int PayFlag;

	DataCDRInfo()
	{
		rating[0] = '\0';
		szProductOfferId[0] = '\0';
		balanceInfo[0] = '\0';
		balanceInfo2[0] = '\0';
		tarifInfo[0] = '\0';
		accumuInfo[0] = '\0';
		sessionId[0] = '\0';
		chargeInfo[0] = '\0';
		orichargeInfo[0] = '\0';
		planInfo[0] = '\0';
		sessionStart[0] = '\0';
		sessionCurrent[0] = '\0';
		szVlr[0] = '\0';
		szNewLocInfo[0] = '\0';
		Day[0] = '\0';
		CallingArea[0] = '\0';
		CallingVlr[0] = '\0';
		CalledVlr[0] = '\0';
		CallingCellid[0] = '\0';
		CalledCellid[0] = '\0';
		MscId[0] = '\0';
		LacId[0] = '\0';
		CalledArea[0] = '\0';
		user_domain[0] = '\0';
		CallingNbr[0] = '\0';
		CalledNbr[0] = '\0';
		CallingParty[0] = '\0';
		CalledParty[0] = '\0';
		memset(stFeeItem, 0, sizeof(stFeeItem));

		ratType[0] = '\0';
		nrCellId[0] = '\0';
		msc[0] = '\0';
		mnc[0] = '\0';
		mcc[0] = '\0';
		tac[0] = '\0';

		userMsc[0] = '\0';
		userLocationInfo[0] = '\0';
		servID[0] = '\0';
		ambrUplink[0] = '\0';
		ambrDownlink[0] = '\0';

		money = 0;
		duration = 0;
		total = 0;
		output = 0;
		input = 0;
		usedduration = 0;
		usedtotal = 0;
		usedinput = 0;
		usedoutput = 0;
		outputtemp = 0;
		inputtemp = 0;
		totaltemp = 0;
		durationtemp = 0;
		longCdrFlag = 0;
		discount_fee = 0;
		octCdrTotalVolume = 0;
		nFeeType = 0;

		tariffChangeDuration = 0;
		tariffChangeTotal = 0;
		tariffChangeInput = 0;
		tariffChangeOutput = 0;
		//add for linux optimize
		nServiceScenarious = 0;
		nCallType = 0;
		nLongType = 0;
		nRomatype = 0;
		nChargeType = 0;
		lCdrVersionSerial = 0;
		nResultCode = 0;
		Payment = 0;
		nCutnum = 0;
		seqNum = 0;
		oper_list_id = 0;
		PayFlag = 0;
	}


	DataCDRInfo& operator=(DataCDRInfo& cdrinfo)
    {
		strncpy(rating,cdrinfo.rating,sizeof(rating));
		strncpy(szProductOfferId,cdrinfo.szProductOfferId,sizeof(szProductOfferId));
		strncpy(balanceInfo,cdrinfo.balanceInfo,sizeof(balanceInfo));
		strncpy(balanceInfo2,cdrinfo.balanceInfo2,sizeof(balanceInfo2));
		strncpy(tarifInfo,cdrinfo.tarifInfo,sizeof(tarifInfo));
		strncpy(accumuInfo,cdrinfo.accumuInfo,sizeof(accumuInfo));
		strncpy(sessionId,cdrinfo.sessionId,sizeof(sessionId));
		strncpy(chargeInfo,cdrinfo.chargeInfo,sizeof(chargeInfo));
		strncpy(planInfo,cdrinfo.planInfo,sizeof(planInfo));
		strncpy(sessionStart,cdrinfo.sessionStart,sizeof(sessionStart));
		strncpy(sessionCurrent,cdrinfo.sessionCurrent,sizeof(sessionCurrent));
		strncpy(szVlr,cdrinfo.szVlr,sizeof(szVlr));
		strncpy(szNewLocInfo,cdrinfo.szNewLocInfo,sizeof(szNewLocInfo));
		memcpy(&stFeeItem,&cdrinfo.stFeeItem,sizeof(stFeeItem));

		strncpy(ratType,cdrinfo.ratType,sizeof(ratType));
		strncpy(userMsc,cdrinfo.userMsc,sizeof(userMsc));
		strncpy(userLocationInfo,cdrinfo.userLocationInfo,sizeof(userLocationInfo));
		strncpy(LacId,cdrinfo.LacId,sizeof(LacId));
		strncpy(servID,cdrinfo.servID,sizeof(servID));

		strncpy(CallingArea,cdrinfo.CallingArea,sizeof(CallingArea));
		strncpy(CallingVlr,cdrinfo.CallingVlr,sizeof(CallingVlr));
		strncpy(CalledVlr,cdrinfo.CalledVlr,sizeof(CalledVlr));
		strncpy(CalledCellid,cdrinfo.CalledCellid,sizeof(CalledCellid));
		strncpy(MscId,cdrinfo.MscId,sizeof(MscId));
		strncpy(CallingCellid,cdrinfo.CallingCellid,sizeof(CallingCellid));
		strncpy(CalledArea,cdrinfo.CalledArea,sizeof(CalledArea));
		strncpy(ambrUplink,cdrinfo.ambrUplink,sizeof(ambrUplink));
		strncpy(ambrDownlink,cdrinfo.ambrDownlink,sizeof(ambrDownlink));

	    money = cdrinfo.money;
	    duration = cdrinfo.duration;
	    total = cdrinfo.total;
		output = cdrinfo.output;
		input  = cdrinfo.input;
		usedduration = cdrinfo.usedduration;
		usedtotal = cdrinfo.usedtotal;
		usedinput = cdrinfo.usedinput;
		usedoutput = cdrinfo.usedoutput;
		outputtemp = cdrinfo.outputtemp;
		inputtemp  = cdrinfo.inputtemp;
		totaltemp  = cdrinfo.totaltemp;
		durationtemp  = cdrinfo.durationtemp;
		longCdrFlag   = cdrinfo.longCdrFlag;
		discount_fee  = cdrinfo.discount_fee;
		octCdrTotalVolume = cdrinfo.octCdrTotalVolume;

		tariffChangeDuration = cdrinfo.tariffChangeDuration;
		tariffChangeTotal = cdrinfo.tariffChangeTotal;
		tariffChangeInput = cdrinfo.tariffChangeInput;
		tariffChangeOutput = cdrinfo.tariffChangeOutput;
		//add for linux optimize
		nServiceScenarious = cdrinfo.nServiceScenarious;
		nCallType = cdrinfo.nCallType;
		nLongType = cdrinfo.nLongType;
		nRomatype = cdrinfo.nRomatype;
		nChargeType = cdrinfo.nChargeType;
		lCdrVersionSerial = cdrinfo.lCdrVersionSerial;
		nResultCode = cdrinfo.nResultCode;
		Payment = cdrinfo.Payment;
		nCutnum = cdrinfo.nCutnum;
		seqNum = cdrinfo.seqNum;
		oper_list_id = cdrinfo.oper_list_id;
		PayFlag = cdrinfo.PayFlag;
	    return *this;

    }
 };


struct SSessionCache
{
	int nEventId;
    int duration;
    long unitInput;
    long unitOutput;
    long unitTotal;
	long lnDiscountFee;
	long money;
	long lastCCRTime;
	long valid_time;
	long rsu_totalOCtets;
	long rsu_duration;
	long nextCCTime;
	long callStartTime;
	int usu_duration;
	int lastRSU;
	int nCCAFlag;
	int nActiveFlag ;
	int nAocType;
	unsigned int roamType;
	unsigned int RERcallType;
	int longType;
	char szCalledVisitArea[16];
	char szCalledVlr[20];
	char szCalledCellid[32];
	char szBearerCapability[8];
	int  conf_QUOTA_CONSUMPTION_TIME;
	int  conf_VOLUME_QUOTA_THRESHOLD;
	int  conf_TIME_QUOTA_THRESHOLD;
	int  conf_TIME_QUOTA_THRESHOLD1;
	int  nsendCCAFlag;
	int volteInViedoSwitch;
	SSessionCache()
	{
		nEventId = 0;
		duration = 0;
		unitInput = 0;
		unitOutput = 0;
		unitTotal = 0;
		lnDiscountFee = 0;
		money = 0;
		lastCCRTime = 0;
		valid_time = 0;
		rsu_totalOCtets = 0;
		rsu_duration = 0;
		nextCCTime = 0;
		callStartTime = 0;
		usu_duration = 0;
		lastRSU = 0;
		nCCAFlag = 0;
		nActiveFlag = 0;
		nAocType = 0;
		roamType = 0;
		RERcallType = 0;
		longType = 0;
		szCalledVisitArea[0] = '\0';
		szCalledVlr[0] = '\0';
		szBearerCapability[0]='\0';
		conf_QUOTA_CONSUMPTION_TIME = 0;
		conf_VOLUME_QUOTA_THRESHOLD = 0;
		conf_TIME_QUOTA_THRESHOLD = 0;
		conf_TIME_QUOTA_THRESHOLD1 = 0;
		nsendCCAFlag = 0;
		volteInViedoSwitch = 0;
	}
};

struct SSessionCacheData
{
    int duration;
    long unitInput;
    long unitOutput;
    long unitTotal;
	long lastCCRTime;
	long valid_time;
	long rsu_totalOCtets;
	long rsu_duration;
	int  conf_QUOTA_CONSUMPTION_TIME;
	int  conf_QUOTA_HOLDING_TIME;
	int  conf_VOLUME_QUOTA_THRESHOLD;
	int  conf_VOLUME_QUOTA_THRESHOLD1;
	int  conf_TIME_QUOTA_THRESHOLD;
	int  nsendCCAFlag;

	//需要插入子会话的字段信息
	int n_CDR_PUB_INT_CORRELATIONID;
	int n_CDR_PUB_INT_TICKETSEQUENCEID;
	char sz_CDR_PUB_STR_CHARGED_PARTY[32];
	long l_CDR_PUB_LNG_SERVID;
	long l_CDR_PUB_LNG_CUSTID;
	char sz_CDR_PUB_STR_MASTERPRODUCTID[32];
	char sz_OCP_STR_ORIGIN_HOST[64];
	int n_RE_INT_SUB_OPERATOR;
	char sz_SM_STR_IMSI[32];
	char sz_OCP_STR_CDMA_CHARGING_ID[24];
	char sz_OCP_STR_IP_PDSN_ADDR[20];
	char sz_CDR_USER_LOCATION_INFO[32];
	char sz_CDR_STR_USER_TYPE[16];
	char sz_CDR_STR_MVNO_ID[16];
	int n_SM_INT_SESSION_TYPE;

	//主会话原来就查出来的部分
	char sz_RE_STR_SUB_AREA[8];
	char sz_RE_STR_SUB_VISIT_AREA[8];
	int  n_RE_INT_PAY_FLAG;
	int  n_RE_INT_ROAM_TYPE ;
	int n_SM_INT_SESSION_STATUS;
	char sz_OCP_STR_IP_GGSN_ADDR[20];
	char sz_OCP_STR_IP_SGSN_ADDR[20];
	char sz_OCP_STR_APN_INFO[128];
	char sz_OCP_STR_PDP_TYPE[20];
	char sz_OCP_STR_IP_PDP_ADDR[20] ;
	char sz_OCP_STR_IP_PDP_ADDRIpv6[64];
	char sz_OCP_STR_CPRS_QOS[128];
	char sz_OCP_STR_LAC_CODE[32];
	char sz_OCP_STR_3GPP_RAT_TYPE[32];
	int n_RE_INT_ISACTIVE_FLAG ;
	char sz_CDR_STR_CELLID[20];
	char sz_CDR_STR_LAC[20];
	int n_SM_INT_FREE_FLAG ;
	int n_OCP_INT_CLASS_IDENTIFIER ;
	char sz_OCP_STR_3GPP_CHARGING_ID[32] ;
	char sz_RB_CODE_R_CALLING_MSC[32];
	int n_CDR_INT_LATN_ID;
	long ln_CDR_ACCT_ID ;
	int ServiceScenarious;
	int nLastGsuUnit;
	int nAocType;
	char szCreatFlag[3];
	SSessionCacheData()
	{
		duration = 0;
		unitInput = 0;
		unitOutput = 0;
		unitTotal = 0;
		lastCCRTime = 0;
		valid_time = 0;
		rsu_totalOCtets = 0;
		rsu_duration = 0;
		conf_QUOTA_CONSUMPTION_TIME = 0;
		conf_QUOTA_HOLDING_TIME = 0;
		conf_VOLUME_QUOTA_THRESHOLD = 0;
		conf_VOLUME_QUOTA_THRESHOLD1 = 0;
		conf_TIME_QUOTA_THRESHOLD = 0;
		nsendCCAFlag = 0;

		//需要插入子会话的字段信息
		n_CDR_PUB_INT_CORRELATIONID = 0;
		n_CDR_PUB_INT_TICKETSEQUENCEID = 0;
		sz_CDR_PUB_STR_CHARGED_PARTY[0] = '\0';
		l_CDR_PUB_LNG_SERVID = 0;
		l_CDR_PUB_LNG_CUSTID = 0;
		sz_CDR_PUB_STR_MASTERPRODUCTID[0] = '\0';
		sz_OCP_STR_ORIGIN_HOST[0] = '\0';
		n_RE_INT_SUB_OPERATOR = 0;
		sz_SM_STR_IMSI[0] = '\0';
		sz_OCP_STR_CDMA_CHARGING_ID[0] = '\0';
		sz_OCP_STR_IP_PDSN_ADDR[0] = '\0';
		sz_CDR_USER_LOCATION_INFO[0] = '\0';
		sz_CDR_STR_USER_TYPE[0] = '\0';
		sz_CDR_STR_MVNO_ID[0] = '\0';
		n_SM_INT_SESSION_TYPE = 0;

		//主会话原来就查出来的部分
		sz_RE_STR_SUB_AREA[0] = '\0';
		sz_RE_STR_SUB_VISIT_AREA[0] = '\0';
		n_RE_INT_PAY_FLAG = 0;
		n_RE_INT_ROAM_TYPE  = 0;
		n_SM_INT_SESSION_STATUS = 0;
		sz_OCP_STR_IP_GGSN_ADDR[0] = '\0';
		sz_OCP_STR_IP_SGSN_ADDR[0] = '\0';
		sz_OCP_STR_APN_INFO[0] = '\0';
		sz_OCP_STR_PDP_TYPE[0] = '\0';
		sz_OCP_STR_IP_PDP_ADDR[0] = '\0';
		sz_OCP_STR_IP_PDP_ADDRIpv6[0] = '\0';
		sz_OCP_STR_CPRS_QOS[0] = '\0';
		sz_OCP_STR_LAC_CODE[0] = '\0';
		sz_OCP_STR_3GPP_RAT_TYPE[0] = '\0';
		n_RE_INT_ISACTIVE_FLAG = 0;
		sz_CDR_STR_CELLID[0] = '\0';
		sz_CDR_STR_LAC[0] = '\0';
		n_SM_INT_FREE_FLAG = 0;
		n_OCP_INT_CLASS_IDENTIFIER = 0;
		sz_OCP_STR_3GPP_CHARGING_ID[0] = '\0';
		sz_RB_CODE_R_CALLING_MSC[0] = '\0';
		n_CDR_INT_LATN_ID = 0;
		ln_CDR_ACCT_ID  = 0;
		ServiceScenarious = 0;
		nLastGsuUnit = 0;
		nAocType = 0;
		szCreatFlag[0] = '\0';
	}

};

struct SSessionCacheData5G
{
    int duration;
    long unitInput;
    long unitOutput;
    long unitTotal;
	long lastCCRTime;
	long valid_time;
	long rsu_totalOCtets;
	long rsu_duration;
	int  conf_QUOTA_CONSUMPTION_TIME;
	int  conf_QUOTA_HOLDING_TIME;
	int  conf_VOLUME_QUOTA_THRESHOLD;
	int  conf_VOLUME_QUOTA_THRESHOLD1;
	int  conf_TIME_QUOTA_THRESHOLD;
	int  nsendCCAFlag;

	//需要插入子会话的字段信息
	int n_CDR_PUB_INT_CORRELATIONID;
	int n_CDR_PUB_INT_TICKETSEQUENCEID;
	char sz_CDR_PUB_STR_CHARGED_PARTY[32];
	long l_CDR_PUB_LNG_SERVID;
	long l_CDR_PUB_LNG_CUSTID;
	char sz_CDR_PUB_STR_MASTERPRODUCTID[32];
	char sz_OCP_STR_ORIGIN_HOST[64];
	int n_RE_INT_SUB_OPERATOR;
	char sz_SM_STR_IMSI[32];
	//char sz_OCP_STR_CDMA_CHARGING_ID[24];
	//char sz_OCP_STR_IP_PDSN_ADDR[20];
	char sz_CDR_USER_LOCATION_INFO[32];
	char sz_CDR_STR_USER_TYPE[16];
	char sz_CDR_STR_MVNO_ID[16];
	int n_SM_INT_SESSION_TYPE;

	//主会话原来就查出来的部分
	char sz_RE_STR_SUB_AREA[8];
	char sz_RE_STR_SUB_VISIT_AREA[8];
	int  n_RE_INT_PAY_FLAG;
	int  n_RE_INT_ROAM_TYPE ;
	int n_SM_INT_SESSION_STATUS;
	//char sz_OCP_STR_IP_GGSN_ADDR[20];
	//char sz_OCP_STR_IP_SGSN_ADDR[20];
	//char sz_OCP_STR_APN_INFO[128];
	//char sz_OCP_STR_PDP_TYPE[20];
	//char sz_OCP_STR_IP_PDP_ADDR[20] ;
	//char sz_OCP_STR_IP_PDP_ADDRIpv6[64];
	char sz_OCP_STR_CPRS_QOS[16];
	int n_OCT_INT_QOS_LEVEL;
	//char sz_OCP_STR_LAC_CODE[32];
	char sz_OCP_STR_RATTYPE[32];
	int n_RE_INT_ISACTIVE_FLAG ;
	char sz_CDR_STR_CELLID[20];
	char sz_CDR_STR_LAC[20];
	int n_SM_INT_FREE_FLAG ;
	//int n_OCP_INT_CLASS_IDENTIFIER ;
	char sz_OCP_STR_CHARGING_ID[32] ;
	char sz_RB_CODE_R_CALLING_MSC[32];
	int n_CDR_INT_LATN_ID;
	long ln_CDR_ACCT_ID ;
	int ServiceScenarious;
	int nLastGsuUnit;
	long lnlastGsuObject;
	long lnlastGsuTime;
	int nAocType;
	char szCreatFlag[3];

	SSessionCacheData5G()
	{
		duration = 0;
		unitInput = 0;
		unitOutput = 0;
		unitTotal = 0;
		lastCCRTime = 0;
		valid_time = 0;
		rsu_totalOCtets = 0;
		rsu_duration = 0;
		conf_QUOTA_CONSUMPTION_TIME = 0;
		conf_QUOTA_HOLDING_TIME = 0;
		conf_VOLUME_QUOTA_THRESHOLD = 0;
		conf_VOLUME_QUOTA_THRESHOLD1 = 0;
		conf_TIME_QUOTA_THRESHOLD = 0;
		nsendCCAFlag = 0;

		//需要插入子会话的字段信息
		n_CDR_PUB_INT_CORRELATIONID = 0;
		n_CDR_PUB_INT_TICKETSEQUENCEID = 0;
		sz_CDR_PUB_STR_CHARGED_PARTY[0] = '\0';
		l_CDR_PUB_LNG_SERVID = 0;
		l_CDR_PUB_LNG_CUSTID = 0;
		sz_CDR_PUB_STR_MASTERPRODUCTID[0] = '\0';
		sz_OCP_STR_ORIGIN_HOST[0] = '\0';
		n_RE_INT_SUB_OPERATOR = 0;
		sz_SM_STR_IMSI[0] = '\0';
		//sz_OCP_STR_CDMA_CHARGING_ID[0] = '\0';
		//sz_OCP_STR_IP_PDSN_ADDR[0] = '\0';
		sz_CDR_USER_LOCATION_INFO[0] = '\0';
		sz_CDR_STR_USER_TYPE[0] = '\0';
		sz_CDR_STR_MVNO_ID[0] = '\0';
		n_SM_INT_SESSION_TYPE = 0;

		//主会话原来就查出来的部分
		sz_RE_STR_SUB_AREA[0] = '\0';
		sz_RE_STR_SUB_VISIT_AREA[0] = '\0';
		n_RE_INT_PAY_FLAG = 0;
		n_RE_INT_ROAM_TYPE  = 0;
		n_SM_INT_SESSION_STATUS = 0;
		//sz_OCP_STR_IP_GGSN_ADDR[0] = '\0';
		//sz_OCP_STR_IP_SGSN_ADDR[0] = '\0';
		//sz_OCP_STR_APN_INFO[0] = '\0';
		//sz_OCP_STR_PDP_TYPE[0] = '\0';
		//sz_OCP_STR_IP_PDP_ADDR[0] = '\0';
		//sz_OCP_STR_IP_PDP_ADDRIpv6[0] = '\0';
		sz_OCP_STR_CPRS_QOS[0] = '\0';
		//sz_OCP_STR_LAC_CODE[0] = '\0';
		sz_OCP_STR_RATTYPE[0] = '\0';
		n_RE_INT_ISACTIVE_FLAG = 0;
		sz_CDR_STR_CELLID[0] = '\0';
		sz_CDR_STR_LAC[0] = '\0';
		n_SM_INT_FREE_FLAG = 0;
		//n_OCP_INT_CLASS_IDENTIFIER = 0;
		sz_OCP_STR_CHARGING_ID[0] = '\0';
		sz_RB_CODE_R_CALLING_MSC[0] = '\0';
		n_CDR_INT_LATN_ID = 0;
		ln_CDR_ACCT_ID  = 0;
		ServiceScenarious = 0;
		nLastGsuUnit = 0;
		lnlastGsuObject = 0;
		lnlastGsuTime = 0;
		nAocType = 0;
		szCreatFlag[0] = '\0';
		n_OCT_INT_QOS_LEVEL = 0;
	}

};
struct STCodeOfr
{
	int oldResultCode;
	int resultcode;
	char ofrid[128];
	int nHaveRgFlag;
	int nSessionStatus;
	int gUnit;
	long long gTime;
	long long gTotal;
	int validityTime;
	int quotaHoldingTime;
	int quotaConsumeTime;
	int timeQuotaThreshold;
	int timeQuotaThreshold2;
	int volumeQuotaThreshold;
	int volumeQuotaThreshold2;
	long long ratingGroup;
	int finalAction;
    long lnServId;
    int usuDuration;
    long usuUnitTotal;

    long triggerTime;
    long triggerVolume;


	STCodeOfr()
	{
		oldResultCode = 0;
		resultcode = 0;
		ofrid[0] = '\0';
		nHaveRgFlag = 0;
		nSessionStatus = 0;
		gUnit = 0;
		gTime = 0;
		gTotal = 0;
		validityTime = 0;
		quotaHoldingTime = 0;
		quotaConsumeTime = 0;
		timeQuotaThreshold = 0;
		timeQuotaThreshold2 = 0;
		volumeQuotaThreshold = 0;
		volumeQuotaThreshold2 = 0;
		ratingGroup = 0;
		finalAction = 0;
        lnServId = 0;
        usuDuration = 0;
        usuUnitTotal = 0;
        triggerTime = 0;
        triggerVolume = 0;
	}
};

struct SUserInfo
{
	int		bizType;	//input	业务类型
	int		isActive;	//ouput	是否首次使用
	int		resultCode;	//ouput	用户状态处理结果
	int		aocType;	//ouput	AOC类型
	long		servID;		//ouput
	long		custID;		//ouput
	char		IMSI[64];	//ouput
	int		province;	//ouput
	int		area;		//ouput
	int		isRemind;	//ouput
	int      iBrandType; //品牌标识:2G或3G
	char   szBasicState[4];    //主状态
	char   szExtStatus[4];	// 拓展状态
	char	szStausCd[16];	// 后付费状态
	char	szStopType[16];	// 停机状态
	int     ilatnid;
	char	  szBalanceAocType[16];
	char userType[8];//用户类型
	char mvnoID[16];//转售商ID
	long lnAcctID;
	long lnBillingTypeId;	// 计费用户类型: 100000001 公免
	char LongDistance[16];	//  固网用户允许呼打的长途类型
	char AttachServiceNbr[32]; //固网附加主号
	char szRangeCode[8]; //手机号码归属营业区
	char segment_id[8];
	SUserInfo()
	{
		bizType = 0;
		isActive = 0;
		resultCode = 0;
		aocType = 0;
		servID = 0;
		custID = 0;
		IMSI[0]='\0';
		province = 0;
		area = 0;
		isRemind = 0;
		iBrandType = 0;
		szBasicState[0]='\0';
		szExtStatus[0] = '\0';
		szStausCd[0] = '\0';
		szStopType[0] = '\0';
		ilatnid = 0;
		szBalanceAocType[0] ='\0';
		userType[0]= '\0';
		mvnoID[0]= '\0';
		lnAcctID = 0;
		lnBillingTypeId =0;
		LongDistance[0]= '\0';
		AttachServiceNbr[0]= '\0';
		szRangeCode[0]= '\0';
		segment_id[0]= '\0';

	}
};


struct SCDRField
{
	int flag;
	char value[64];
};


#endif

