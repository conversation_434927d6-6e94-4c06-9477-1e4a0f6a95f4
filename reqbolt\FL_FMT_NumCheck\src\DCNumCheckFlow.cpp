#include "DCNumCheckFlow.h"
#include "numcheck.h"
#include "DCRFData.h"
#include "TSMPara.h"
#include "ErrorCode.h"



int DCNumCheckFlow::init()
{
	int ret = 0;

	m_smpara = dynamic_cast<TSMPara *>(DCRFData::instance()->get("TSMPara"));
	NumCheck::Init(m_smpara);


	return ret;
}

//消息头|公共消息|业务消息
int DCNumCheckFlow::process(void* input, void* output)
{
	int ret = 0;
	DCOBJSet* pset = (DCOBJSet*)input;


	STBizMsg* bizMsg = pset->get<STBizMsg>();
	bizMsg->m_smpara = m_smpara;

	ret = m_fmt.FormatCCR(pset);


	if(ret!=0 && RET_OVER != ret)
	{
		bizMsg->m_resultcode = ret;
	}
	output = pset;
	return ret;
}


DYN_PLUGIN_CREATE(DCNumCheckFlow, "FC_NUMCHECK", "FC_Numcheck", "1.0.0")

