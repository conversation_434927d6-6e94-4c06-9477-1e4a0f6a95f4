#include "DCReqIndex.h"
#include "DCCommonIF.h"
DCReqIndex::DCReqIndex()
{
	head = NULL;
	tail = NULL;
	Q_head = NULL;
	Q_tail = NULL;
	timeoutSec = 1;
	m_seqtimeoutSec = 1;
	chainSize = 0;
}

DCReqIndex::~DCReqIndex()
{
	for(TimeChain *p = head; p != NULL; )
	{
		head = p->next;
		delete p;
		p = head;
	}
	for(SeqTimeChain *p = Q_head; p != NULL; )
	{
		Q_head = p->next;
		delete p;
		p = Q_head;
	}
	chainSize = 0;
}

int DCReqIndex::GetSize()
{
	return m_index.size();
}
int DCReqIndex::GetChainSize()
{
	return chainSize;
}
int DCReqIndex::IsEmpty()
{
	ClearTImeoutChain();
	if(head == NULL)
		return 1;
	return 0;
}
void DCReqIndex::SetTimeoutSec(int sec,int SeqSec)
{
	timeoutSec = sec < 1?1:sec;
	m_seqtimeoutSec = SeqSec<1?1:SeqSec;
}

bool DCReqIndex::IsDup(string &sessionID,unsigned int seq,bool IsXDrMsg,long curRG,int allNum)
{
	bool ret = false;
	string md5sum = sessionID;
	map<std::string,ReqIndex>::iterator iter = m_index.find(md5sum);
	if(iter == m_index.end())
	{
		ReqIndex newIndex;
		if(allNum == 1 && !IsXDrMsg)
		{
			newIndex.index.push_back(seq << 16);
		}
		else
		{
			TempRGCount rgCount;
			rgCount.SeqTimePos=NULL;
			rgCount.v_rg.insert(curRG);
			rgCount.RGSize = IsXDrMsg?0:allNum;
			newIndex.v_TRequest.insert(pair<unsigned int,TempRGCount>(seq,rgCount));
			if(IsXDrMsg)
			{		
					newIndex.v_TRequest[seq].SeqTimePos=new SeqTimeChain(seq,md5sum);
					InsertSeqChain(newIndex.v_TRequest[seq].SeqTimePos);
			}
		}
		newIndex.timePos = new TimeChain(md5sum);
		InsertChain(newIndex.timePos);
		m_index[md5sum] = newIndex;
	}
	else
	{
		if(iter->second.v_TRequest.count(seq))//缓存有序列
		{
			if(iter->second.v_TRequest[seq].v_rg.count(curRG))
			{
				ret = true;
				ClearSeqTimeOutChain();
				return ret;//重复
			}
			else
			{
				iter->second.v_TRequest[seq].v_rg.insert(curRG);  //不重复,写入本次 rg
				if(IsXDrMsg && iter->second.v_TRequest[seq].SeqTimePos==NULL)//数据最开始是在线消息存入的，转为离线文件消息缓存模式
				{
					iter->second.v_TRequest[seq].RGSize=0;
					iter->second.v_TRequest[seq].SeqTimePos=new SeqTimeChain(seq,md5sum);
					InsertSeqChain(iter->second.v_TRequest[seq].SeqTimePos);

				}
				else if(IsXDrMsg && iter->second.v_TRequest[seq].SeqTimePos)
				{
					UpdateSeqChain(iter->second.v_TRequest[seq].SeqTimePos);
				}
				if(iter->second.v_TRequest[seq].v_rg.size() == iter->second.v_TRequest[seq].RGSize) //在线序号判断临时缓存是否需要删除
				{
					//****在线会话这里应该走不进begin***
					if(iter->second.v_TRequest[seq].SeqTimePos)
					{
						cout<<"IsDup5 sess:"<<sessionID<<" seq:"<<seq<<endl;
						EraseSeqTimeChain(iter->second.v_TRequest[seq].SeqTimePos);
					}
					//****在线会话这里应该走不进end***
					
					iter->second.v_TRequest.erase(seq);		//删除临时缓存
					if(iter->second.index.size() == 0)		//判断是否首个区间
					{
						iter->second.index.push_back(seq << 16);
						ret = false;
					}
					else
					{
						ret = UpdateIndex(&(iter->second),seq, true);//更新及判断索引区间
					}
					ClearTImeoutChain();
					ClearSeqTimeOutChain();
					return ret;
				}

			}
		}
		else if(allNum>1 || IsXDrMsg)
		{
			ret = UpdateIndex(&(iter->second),seq,false); //只判断是否重复
			if(ret == false)//需要写入时,索引区间不存在,需写入临时缓存
			{
				TempRGCount rgCount;
				rgCount.SeqTimePos=NULL;
				rgCount.v_rg.insert(curRG);
				rgCount.RGSize = IsXDrMsg?0:allNum;
				iter->second.v_TRequest.insert(pair<unsigned int,TempRGCount>(seq,rgCount));
				if(IsXDrMsg)
				{
					iter->second.v_TRequest[seq].SeqTimePos=new SeqTimeChain(seq,md5sum);
					InsertSeqChain(iter->second.v_TRequest[seq].SeqTimePos);
				}
			}
			ClearSeqTimeOutChain();
			return ret;
		}
		else
		{
			//总 rg 数为 1 或不需要写入时,直接判断索引区间
			ret = UpdateIndex(&(iter->second),seq,true);
		}
	}
	ClearTImeoutChain();
	ClearSeqTimeOutChain();
	return ret;
}

void DCReqIndex::InsertChain(TimeChain *p)
{
	chainSize++;
	if(head == NULL)
	{
		head = tail = p;
		return;
	}
	tail->next = p;
	p->pre = tail;
	tail = p;
	return;
}

void DCReqIndex::UpdateChain(TimeChain *p)
{
	p->lastTime = time(NULL);
	if(p == tail)
	{
		return;
	}
	else if(p == head)
	{
		head = head->next;
	}
	else
	{
		p->pre->next = p->next;
		p->next->pre = p->pre;
	}
	tail->next = p;
	p->next = NULL;
	p->pre = tail;
	tail = p;
	return;
}

void DCReqIndex::ClearTImeoutChain()
{
	time_t now = time(NULL);
	for(TimeChain *p = head; p != NULL;)
	{
		if(now - p->lastTime < timeoutSec)
			break;
		head = p->next;
		if(head == NULL)
		{
			tail = head;
		}
		else
		{
			head->pre = NULL;
		}
		chainSize--;
		std::map<unsigned int,TempRGCount>::iterator iter=m_index[p->key].v_TRequest.begin();
		for(iter;iter!=m_index[p->key].v_TRequest.end();iter++)
		{
			EraseSeqTimeChain(iter->second.SeqTimePos);
		}
		m_index.erase(p->key);
		delete p;
		p = head;
	}
}

void DCReqIndex::InsertSeqChain(SeqTimeChain *p)
{
	if(Q_head == NULL)
	{
		Q_head = Q_tail = p;
		return;
	}
	Q_tail->next = p;
	p->pre = Q_tail;
	Q_tail = p;
	return;
}
void DCReqIndex::UpdateSeqChain(SeqTimeChain *p)
{
	p->lastTime = time(NULL);
	if(p == Q_tail)
	{
		return;
	}
	else if(p == Q_head)
	{
		Q_head = Q_head->next;
	}
	else
	{
		p->pre->next = p->next;
		p->next->pre = p->pre;
	}
	Q_tail->next = p;
	p->next = NULL;
	p->pre = Q_tail;
	Q_tail = p;
	return;
}
void DCReqIndex::ClearSeqTimeOutChain()
{
	time_t now = time(NULL);
	for(SeqTimeChain *p = Q_head; p != NULL;)
	{
		if(now - p->lastTime < m_seqtimeoutSec)
			break;
		Q_head = p->next;
		if(head == NULL)
		{
			tail = head;
		}
		else
		{
			head->pre = NULL;
		}
		//达到指定时间写到索引区,清除临时缓存
		if(m_index.count(p->key2) && m_index[p->key2].v_TRequest.count(p->key1))
		{
			m_index[p->key2].v_TRequest.erase(p->key1); //删除临时缓存
			if(m_index[p->key2].index.size() == 0)		//判断是否首个区间
			{
				m_index[p->key2].index.push_back(p->key1 << 16);
			}
			else
			{
				UpdateIndex(&(m_index[p->key2]),p->key1, true);//更新及判断索引区间
			}
		}	
		delete p;
		p = Q_head;
	}
}
void DCReqIndex::EraseSeqTimeChain(SeqTimeChain *p)
{	
	if(p==NULL)
	{
		return;
	}
	if(p == Q_head)
	{
		Q_head = Q_head->next;
		if(Q_head==NULL)
		{
			Q_tail=Q_head;
		}
	}
	else if(p == Q_tail)
	{
		Q_tail->pre->next=NULL;
		Q_tail=Q_tail->pre;
		
	}
	else
	{
		p->pre->next = p->next;
		p->next->pre = p->pre;
	}
	delete p;
}

bool DCReqIndex::UpdateIndex(ReqIndex *req,unsigned int seq,bool isNeedToWrite)
{
	UpdateChain(req->timePos);
	for(std::vector<unsigned int>::iterator it = req->index.begin(); it != req->index.end(); it++)
	{
		unsigned int pos = *it >> 16;
		unsigned int length = *it & 0xffff;
		if(seq > pos + length + 1)
			continue;
		if(seq >= pos && seq <= pos + length)
		{
			return true;
		}
		else if(isNeedToWrite == false)
		{
			return false;
		}
		else if(seq == pos + length + 1)
		{
			(*it)++;
			std::vector<unsigned int>::iterator itNext = it;
			itNext++;
			if(itNext != req->index.end() && ((*itNext >> 16) -1) == seq)
			{
				(*it)++;
				*it += *itNext & 0xffff;
				req->index.erase(itNext);
			}
			return false;
		}
		else if(pos > 0 && seq == pos - 1)
		{
			pos --;
			*it &= 0xffff;
			*it |= pos << 16;
			(*it) ++;
			return false;
		}
		//remain seq < pos
		else
		{
			req->index.insert(it,seq<<16);
			return false;
		}
	}
	if(isNeedToWrite)
		req->index.push_back(seq << 16);
	return false;
}
