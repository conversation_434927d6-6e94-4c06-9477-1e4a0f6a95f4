#include "DCBizCdrNormalVoice.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "DCOcpMsgDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"

using namespace ocs;
static void selftimeAdd(char* begin_time,int seconds)
{
  int year,mon,day,hh,mm,ss;
  char temp[5] = {0};
  char szOldTime[15] = {0};
  struct tm tm1;
  struct tm tm2;
  time_t ltime;

  strcpy(szOldTime,begin_time);
  strncpy(temp,begin_time,4);

  year=atol(temp);
  year=year-1900;
  memset(temp,'\0',5);
  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;
  strncpy(temp,begin_time+6,2);

  day=atol(temp);
  strncpy(temp,begin_time+8,2);

  hh=atol(temp);
  strncpy(temp,begin_time+10,2);

  mm=atol(temp);
  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  memset(&tm1,0,sizeof(tm));

  tm1.tm_sec=ss;
  tm1.tm_min=mm;
  tm1.tm_hour=hh;
  tm1.tm_mday=day;
  tm1.tm_mon=mon;
  tm1.tm_year=year;

  ltime=mktime(&tm1);
  ltime+=seconds;

  localtime_r(&ltime,&tm2);

  sprintf(begin_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2.tm_year+1900,tm2.tm_mon+1,
            tm2.tm_mday,tm2.tm_hour,tm2.tm_min,tm2.tm_sec);

}

DCBizCdrNormalVoice::DCBizCdrNormalVoice()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormalVoice::~DCBizCdrNormalVoice()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

int DCBizCdrNormalVoice::PretreatVOICE(STBizMsg* bizMsg)
{
	unsigned int longType 	= -1;
	unsigned int roamType 	= -1;
	unsigned int callType 	= -1;
	int aocflag				=  0;
	int TUSU = 0;
       int ifree = 0;
	char value[BIZ_TEMP_LEN_256] = {0};
	char starttime[BIZ_TEMP_LEN_256] = {0};
	DataCDRInfo stCdrInfo;
	int ret = 0;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	UDBSQL *pQuery = dbm->GetSQL(Voice_GetSessionInfo_Cdr);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();

              if(bizMsg->m_spiltflag > 1)
              {
                    pQuery->BindParam(1, bizMsg->m_childsessionID);
              }
              else
              {
                    pQuery->BindParam(1, bizMsg->m_sessionID);
              }

		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			TUSU = atol(value);
			stCdrInfo.duration=TUSU;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "TUSU[%s]", value);

			pQuery->GetValue(2, value);
			longType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "longType[%s]", value);

			pQuery->GetValue(3, value);
			roamType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "roamType[%s]", value);

			pQuery->GetValue(4, value);
			callType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "callType[%s]", value);

			pQuery->GetValue(6, value);
			stCdrInfo.longCdrFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "longCdrFlag[%s]", value);

			pQuery->GetValue(8, stCdrInfo.planInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "planInfo[%s]", stCdrInfo.planInfo);

			pQuery->GetValue(9, stCdrInfo.tarifInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "tarifInfo[%s]", stCdrInfo.tarifInfo);

			pQuery->GetValue(10, stCdrInfo.chargeInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "chargeInfo[%s]", stCdrInfo.chargeInfo);

			pQuery->GetValue(11, value);

			strncpy(starttime,value,BIZ_TEMP_LEN_256);
			strncpy(stCdrInfo.sessionStart,value,sizeof(stCdrInfo.sessionStart));

			if(bizMsg->m_eptType == 3)
			{
                            // 会话超时，在线
				selftimeAdd(starttime,stCdrInfo.duration);
				strncpy(stCdrInfo.sessionCurrent,starttime,sizeof(stCdrInfo.sessionCurrent));
                strcpy(value, stCdrInfo.sessionCurrent);

                stCdrInfo.Day[0] = value[6];
				stCdrInfo.Day[1] = value[7];
				stCdrInfo.Day[2] = '\0';
				int day = atoi(stCdrInfo.Day);
				sprintf(stCdrInfo.Day,"%d",day);
			}
			else
			{
				pQuery->GetValue(5, value);
				strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "sessionCurrent[%s]", value);
				stCdrInfo.Day[0] = value[6];
				stCdrInfo.Day[1] = value[7];
				stCdrInfo.Day[2] = '\0';
				int day = atoi(stCdrInfo.Day);
				sprintf(stCdrInfo.Day,"%d",day);
			}
			pQuery->GetValue(26, value);
			stCdrInfo.Payment= atol(value);
			if(stCdrInfo.Payment == 0)
			{
				strcpy(value,stCdrInfo.sessionCurrent);
				value[6] = '\0';
				stCdrInfo.Payment= atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "Payment[%ld]", stCdrInfo.Payment);

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "Day[%s]", stCdrInfo.Day);

			pQuery->GetValue(12, value);
			stCdrInfo.lCdrVersionSerial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "OCS_SM_CDR_VERSION_SEQ_ID.nextval[%ld]", stCdrInfo.lCdrVersionSerial);

			pQuery->GetValue(13, value);
			aocflag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "aocflag[%s]", value);

			pQuery->GetValue(15, stCdrInfo.accumuInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "accumuInfo[%s]", stCdrInfo.accumuInfo);

			pQuery->GetValue(16, stCdrInfo.seqNum);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "seqNum[%d]", stCdrInfo.seqNum);

			pQuery->GetValue(17, stCdrInfo.CallingArea);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CallingArea[%s]", stCdrInfo.CallingArea);

			pQuery->GetValue(18, stCdrInfo.CalledArea);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CalledArea[%s]", stCdrInfo.CalledArea);

			pQuery->GetValue(19, stCdrInfo.CallingVlr);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CallingVlr[%s]", stCdrInfo.CallingVlr);

			pQuery->GetValue(20, stCdrInfo.CalledVlr);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CalledVlr[%s]", stCdrInfo.CalledVlr);

			pQuery->GetValue(21, stCdrInfo.CallingCellid);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CallingCellid[%s]", stCdrInfo.CallingCellid);

			pQuery->GetValue(22, stCdrInfo.CalledCellid);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CalledCellid[%s]", stCdrInfo.CalledCellid);

			pQuery->GetValue(23, value);
			stCdrInfo.PayFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "payflag[%d]", stCdrInfo.PayFlag);

                     pQuery->GetValue(24, value);
			strcpy(stCdrInfo.orichargeInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "orichargeInfo[%s]", value);

                     pQuery->GetValue(25, value);   // IMS_CHARGING_IDENTIFIER
			bizMsg->m_if4Gpp =  value[0] ? 1:0;  // 标识volte 话单
			if(bizMsg->m_volteFlag == 0)
			{
				bizMsg->m_volteFlag = bizMsg->m_if4Gpp;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "IMS_CHARGING_IDENTIFIER[%s]", value);


			if(2==callType)
			{
				char szmsc_id[8] = {0};
				char szmsc_t_id[8] = {0};
				int imsc_id = 0;
				int imsc_t_id = 0;
				strncpy(szmsc_id,stCdrInfo.CalledVlr,3);
				strncpy(szmsc_t_id,stCdrInfo.CalledVlr+3,3);
				imsc_id = atoi(szmsc_id);
				imsc_t_id = atoi(szmsc_t_id);
				sprintf(stCdrInfo.MscId,"%d",imsc_id*256+imsc_t_id);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "MscId[%s]", stCdrInfo.MscId);

				if(bizMsg->m_if4Gpp)
				{
					sprintf(stCdrInfo.LacId,"%s",stCdrInfo.CalledVlr);
				}
				else
				{
					sprintf(stCdrInfo.LacId,"%s",stCdrInfo.CalledCellid);
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "LacId[%s]", stCdrInfo.LacId);
			}
			else if(1==callType ||3==callType ||4==callType)
			{
				char szmsc_id[8] = {0};
				char szmsc_t_id[8] = {0};
				int imsc_id = 0;
				int imsc_t_id = 0;
				strncpy(szmsc_id,stCdrInfo.CallingVlr,3);
				strncpy(szmsc_t_id,stCdrInfo.CallingVlr+3,3);
				imsc_id = atoi(szmsc_id);
				imsc_t_id = atoi(szmsc_t_id);
				sprintf(stCdrInfo.MscId,"%d",imsc_id*256+imsc_t_id);

				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "MscId[%s]", stCdrInfo.MscId);

				if(bizMsg->m_if4Gpp)
				{
					sprintf(stCdrInfo.LacId,"%s",stCdrInfo.CallingVlr);
				}
				else
				{
					sprintf(stCdrInfo.LacId,"%s",stCdrInfo.CallingCellid);
				}
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "LacId[%s]", stCdrInfo.LacId);
			}

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "invalid session id", "");
			return -2;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

        // ifree ?¨ó??§à???ê±3¤?a0 ò23?μ￥
        if(bizMsg->m_version == 1 &&  bizMsg->m_ifree == 1)
        {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "ifree offline tusu[%d]", TUSU);
        }
	 else if(0 == TUSU)  //?TD§??μ￥?D??
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "tusu=0,short cdr", "");
		return 0;
	}
	else
	{
		//长途超短
		if(longType >= 2)
		{
			if(TUSU <= smpara->GetINPara()->shortCDRTime)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "exception short long", "");
				return 0;
			}
		}
		//判断市话超短话单
	 	else if((0==roamType) && (0==longType))
		{
			if(TUSU <= smpara->GetINPara()->shortCDRTimeCity)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "exception short city", "");
				return 0;
			}
		}
		//判断漫游超短话单
	 	else if(roamType >= 1)
		{
			if(TUSU <= smpara->GetINPara()->shortCDRTimeRoam)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "exception short roam", "");
				return 0;
			}
		}
	}

	switch(longType) //长途
	{
		case 0://基本通话
		case 1://农话
			longType = 1;
			break;
		case 2://省内长途
			longType = 2;
			break;
		case 3://省际长途
			longType = 3;
			break;
		case 4://国际长途
			longType = 5;
			break;
		case 5://港澳台长途
			longType = 4;
			break;
		default:
			break;
	}
    stCdrInfo.nLongType = longType;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "longType[%d]", stCdrInfo.nLongType);

	switch(roamType) //漫游
	{
		case 0://非漫游
			roamType = 0;
			break;
		case 1://省内漫游
			roamType = 1;
			break;
		case 3://省际漫游来访
			roamType = 2;
			break;
		case 4://省际漫游出访
			roamType = 5;
			break;
		case 5://国际漫游来访
			roamType = 7;
			break;
		case 6://国际漫游出访
			roamType = 4;
			break;
		case 7://省内边界漫游
			roamType = 8;
			break;
		case 8://省际边界漫游
			roamType = 9;
			break;
		case 9://港澳台漫游
			roamType = 3;
			break;
		default:
			break;
	}
	stCdrInfo.nRomatype = roamType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "roamType[%d]", stCdrInfo.nRomatype);
	//呼转类型
	stCdrInfo.nCallType = callType;
	if( 6 == callType )
	{
		stCdrInfo.nCallType = 4;
	}


	ret = PutCdr_VOICE(bizMsg,stCdrInfo);
    if(ret < 0)
    {
        return ret;
    }
		//超长话单
	if(1 == bizMsg->m_longCDR || 3 == bizMsg->m_longCDR)
	{
        ret = ComposeVOICELongCdr(bizMsg,stCdrInfo);
	}


	//info 日志
	DCDATLOG("SM00013:%d%s%s%d%s%s%s%s%s", bizMsg->m_longCDR,stCdrInfo.sessionStart, stCdrInfo.sessionCurrent,\
							TUSU,stCdrInfo.planInfo, stCdrInfo.tarifInfo, stCdrInfo.chargeInfo, stCdrInfo.accumuInfo, stCdrInfo.orichargeInfo);
	return ret;
}

int DCBizCdrNormalVoice::ComposeVOICE(STBizMsg* bizMsg)
{
	int ret = 0;

/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		return RET_SUCCESS;
	}
*/
	ret = PretreatVOICE(bizMsg);


	//非超长话单
	if(1 != bizMsg->m_longCDR  &&  3 != bizMsg->m_longCDR)
	{
	    if(ret != -2)
	    {
			DeleteSession(bizMsg);
    	}
	}

	return ret;
}

int DCBizCdrNormalVoice::ComposeVOICELongCdr(STBizMsg * bizMsg,DataCDRInfo &stCdrInfo)
{
	int cdrLongFlag = 0;//超长话单标识
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	//出话单成功，则更新数据库指定字段
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "Update SessionInfo","");

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "duration[%ld]", stCdrInfo.duration);

	if(SM_SESSION_UPDATE_CODE ==  bizMsg->m_requestType || 3 == bizMsg->m_longCDR || (SM_SESSION_TERMINATION_CODE ==  bizMsg->m_requestType && 1 == bizMsg->m_longCDR))
	{
		// update时需还原若干字段，同是要更新时间戳和使用量
		UDBSQL *pExecute = dbm->GetSQL(Voice_UpdateSession_LongCdr);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				pExecute->DivTable(bizMsg->m_sessionID);
				pExecute->UnBindParam();
				pExecute->BindParam(1, atol(stCdrInfo.sessionCurrent)); // RE_LNG_CALL_START_TIME
				pExecute->BindParam(2, (long)stCdrInfo.duration);		// SM_LNG_ALL_USU_TIME = SM_LNG_ALL_USU_TIME-?,
				pExecute->BindParam(3, (long)stCdrInfo.duration);		// CDR_LNG_ALL_USU_TIME = CDR_LNG_ALL_USU_TIME+?
				pExecute->BindParam(4, stCdrInfo.seqNum);				// 更新拆单次数
				pExecute->BindParam(5, "");
				pExecute->BindParam(6, bizMsg->m_sessionID);
				pExecute->Execute();
				pExecute->Connection()->Commit();
				success = true;
			}
			catch (UDBException &e)
			{
				pExecute->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "sql Code[%s]  execption[%s], error code[%d]", Voice_UpdateSession_LongCdr, e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;	
			}
		}
	}
	return 0;
}

int DCBizCdrNormalVoice::PutCdr_VOICE(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo)
{


	vector<STFeeItem> vecFeeItem;
	vector<STariffAccumCDRInfo> vecTaiAuCdr;
	vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

	//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
	ParaseTariffAccumCdrInfo(bizMsg,stCdrInfo,vecTaiAuCdr);

	long nLeftDuration = stCdrInfo.duration;
	long nDuration = stCdrInfo.duration;

	char szSessionStart[32]={0};
	strcpy(szSessionStart,stCdrInfo.sessionStart);

	vector<SCDRField*> *field;
	vector<SCDRField*>::iterator iter;
	char USUDefault[32] = {0};
	int ret = 0;
	//SCDRData cdr = {0};
	field = TCDRDict::instance()->GetINField();
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	UDBSQL *pQuery1 = dbm->GetSQL(COM_CDR_VOICE);
	int i = 1;
	try
	{
		string  sql;
		pQuery1->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "SQL[%s]", sql.c_str());

		pQuery1->DivTable(bizMsg->m_sessionID);
		pQuery1->UnBindParam();

              if(bizMsg->m_spiltflag > 1)
              {
                    pQuery1->BindParam(1, bizMsg->m_childsessionID);
              }
              else
              {
                    pQuery1->BindParam(1, bizMsg->m_sessionID);
              }

		pQuery1->Execute();
		if(pQuery1->Next())
		{
			int cdrSeqNum=0;
			if(stCdrInfo.seqNum > 0)
			{
				cdrSeqNum = stCdrInfo.seqNum;
			}

			if(vecTaiAuCdr.size()>0)
			{
				long nSize = vecTaiAuCdr.size();
				long nIndex = 0;

				std::map<long, TaiAcctItemIDCdr> acctItemIdmap;

				for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
				{

					nIndex ++ ;
					cdrSeqNum++;
					SCDRData cdr = {0};
					stCdrInfo.nCutnum = nSize;

					long nTempLeftDuration = 0 ;
					char szTempSessionStart[32] = {0};
					std::map<long, TaiAcctItemIDCdr>::iterator iter =  acctItemIdmap.find(TaiAuCdrIter->acctItemId);

					if (iter != acctItemIdmap.end())
					{
						nTempLeftDuration=iter->second.nLeftDuration;
						strcpy(szTempSessionStart,iter->second.szSessionStart);
					}
					else
					{
						nTempLeftDuration=nLeftDuration;
						strcpy(szTempSessionStart,szSessionStart);

					}

					if(nIndex < nSize)
					{
						if (nTempLeftDuration < TaiAuCdrIter->billingDuration)
						{
							stCdrInfo.duration = nTempLeftDuration;
							nTempLeftDuration = 0;
						}
						else
						{
							stCdrInfo.duration = TaiAuCdrIter->billingDuration;
							nTempLeftDuration -= TaiAuCdrIter->billingDuration;
						}
					}
					else
					{
						//最后一个
						stCdrInfo.duration = nTempLeftDuration;
					}

					strcpy(stCdrInfo.sessionStart,szTempSessionStart);

					//计算拆单的下一个清单的开始时间
					char szNextTaiSessionStart[32] = {0};
					strcpy(szNextTaiSessionStart,szTempSessionStart);

					selftimeAdd(szNextTaiSessionStart,stCdrInfo.duration);

					//当前清单的结束时间就是下次清单的开始时间
					strcpy(stCdrInfo.sessionCurrent,szNextTaiSessionStart);

					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "end Time:[%s]", stCdrInfo.sessionCurrent);

					ComposeVoiceCDR(bizMsg,stCdrInfo,pQuery1,*TaiAuCdrIter,field,cdrSeqNum,cdr);


					//更新拆单的下一个清单的开始时间，和剩余时长
					if (iter != acctItemIdmap.end())
					{
						iter->second.nLeftDuration = nTempLeftDuration;
						strcpy(iter->second.szSessionStart,szNextTaiSessionStart);
					}
					else
					{
						TaiAcctItemIDCdr stAcctItemId ;
						stAcctItemId.nLeftDuration = nTempLeftDuration;
						strcpy(stAcctItemId.szSessionStart,szNextTaiSessionStart);

						acctItemIdmap.insert(pair<long, TaiAcctItemIDCdr>(TaiAuCdrIter->acctItemId, stAcctItemId));
					}

					int len = strlen(cdr.m_body);
					/*
					cdr.m_body[len - 1] = '\r';
					cdr.m_body[len] = '\n';
					cdr.m_body[len + 1] = '\0';
					*/
					cdr.m_body[len - 1] = '\0';
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "cdr field:\n[%s]", cdr.m_body);

					UCDRData scdr;
					scdr.body = cdr.m_body;
					ret = ProduceCdr(bizMsg,scdr,stCdrInfo.PayFlag);
				}
			}
			else
			{
				cdrSeqNum++;
				SCDRData cdr = {0};
				STariffAccumCDRInfo TaiAuCdrInfo;

                DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
                SCommonPara* commonPara = smpara->GetCommonPara();
                char fence = commonPara->CdrFence;
                SOriChargeInfo tmpOriChargeInfo[100];//扩大一点。避免因数据异常导致core
                //增强实扣为0的acct_item_Id
                DCCommonIF::ParseOriChargeInfo(stCdrInfo.orichargeInfo,tmpOriChargeInfo,fence);
                TaiAuCdrInfo.acctItemId = tmpOriChargeInfo[0].acctItemId;
				TaiAuCdrInfo.basefee = tmpOriChargeInfo[0].oriAmount;

				stCdrInfo.nCutnum = 1;
				ComposeVoiceCDR(bizMsg,stCdrInfo,pQuery1,TaiAuCdrInfo,field,cdrSeqNum,cdr);

				int len = strlen(cdr.m_body);
				//cdr.m_body[len - 1] = '\r';
				//cdr.m_body[len] = '\n';
				//cdr.m_body[len + 1] = '\0';
				cdr.m_body[len - 1] = '\0';

				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "cdr field:\n[%s]", cdr.m_body);

				UCDRData scdr;
				scdr.body = cdr.m_body;
				ret = ProduceCdr(bizMsg,scdr,stCdrInfo.PayFlag);
			}

			stCdrInfo.seqNum = cdrSeqNum;
			stCdrInfo.duration = nDuration;

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "session no exist","");
			return -2;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE,  "", "sql execption[%s],SQL[%s],SQLCODE[%d]", e.ToString(),e.GetSqlInfo(), e.GetSqlCode());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

    return 0;
}

int DCBizCdrNormalVoice::ComposeVoiceCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery1,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr)
{
	int nFieldNum;
    char value[BIZ_TEMP_LEN_256] = {0};

	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	UDBSQL *pQuery = dbm->GetSQL(COM_SELECT_AREA_ID);
	vector<STFeeItem> vecFeeItem;
	vector<SCDRField*>::iterator iter;
	ParaseFeeItem(bizMsg,stCdrInfo,vecFeeItem);
	int i = 1;
	int j = 1;

	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");

	for(iter=field->begin(); iter!=field->end(); iter++)
	{
	 	if((0 == strcmp("SM_LNG_ALL_USU_TIME", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%lld", stCdrInfo.duration);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}
	 	if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)) && iCdrVersionType>0)
	 	{
	 		char szVersion[32] = {0};
	 		int iDay = timestampf();
	 		sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//??version??
	 		strcpy(value, szVersion);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}
		if(0 == strcmp("RE_LNG_CALL_START_TIME", (*iter)->value))
		{
			sprintf(value, "%s",stCdrInfo.sessionStart);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("RE_LNG_CURRENT_CCR_TIME", (*iter)->value))
		{
			sprintf(value, "%s",stCdrInfo.sessionCurrent);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if((0 == strcmp("CDR_BILL_CYCLE", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%ld", stCdrInfo.Payment);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
	 		continue;
	 	}

	 	if((0 == strcmp("RE_INT_LONG_TYPE", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%d", stCdrInfo.nLongType);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%s",bizMsg->m_xdrsource);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
	 		continue;
	 	}
	 	if((0 == strcmp("RE_INT_ROAM_TYPE", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%d", stCdrInfo.nRomatype);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}

	 	if((0 == strcmp("RE_INT_CALL_TYPE", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%d", stCdrInfo.nCallType);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery1->GetValue(i, temp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "RE_STR_SUB_NBR value %s", temp);
			ChangeCallNumberValue(temp,value);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
	 	if(0 == strcmp("RE_STR_CALLING_AREA", (*iter)->value))
	 	{
	 		pQuery1->GetValue(i, value);
	 		if((0 == strcmp(value, "0282")) || (0 == strcmp(value, "0283")))
	 		{
	 			memset(value, 0x00, sizeof(value));
	 			strcpy(value, "028");
	 		}
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}

		if(0 == strcmp("RE_INT_PAY_FLAG", (*iter)->value))
	 	{
	 		pQuery1->GetValue(i, value);
	 		if((0 == strcmp(value, "3")) || (0 == strcmp(value, "4")))
	 		{
	 			memset(value, 0x00, sizeof(value));
	 			strcpy(value, "4");
	 		}
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}

	 	if(0 == strcmp("RE_STR_CALLED_AREA", (*iter)->value))
	 	{
	 		pQuery1->GetValue(i, value);
	 		if((0 == strcmp(value, "0282")) || (0 == strcmp(value, "0283")))
	 		{
	 			memset(value, 0x00, sizeof(value));
	 			strcpy(value, "028");
	 		}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			i++;
	 		continue;
	 	}

	 	if(0 == strcmp("RE_STR_CALLING_VISIT_AREA", (*iter)->value))
	 	{
	 		pQuery1->GetValue(i, value);
	 		if((0 == strcmp(value, "0282")) || (0 == strcmp(value, "0283")))
	 		{
	 			memset(value, 0x00, sizeof(value));
	 			strcpy(value, "028");

	 		}
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}

	 	if(0 == strcmp("RE_STR_CALLED_VISIT_AREA", (*iter)->value))
	 	{
	 		pQuery1->GetValue(i, value);
	 		if((0 == strcmp(value, "0282")) || (0 == strcmp(value, "0283")))
	 		{
	 			memset(value, 0x00, sizeof(value));
	 			strcpy(value, "028");

	 		}
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}
	 	if((0 == strcmp("DAY", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%s", stCdrInfo.Day);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			//i++;
	 		continue;
	 	}
		if(0 == strcmp("OCP_STR_SESSION_ID", (*iter)->value))
	 	{
	 		pQuery1->GetValue(i, value);
			if(bizMsg->m_spiltflag > 1)
			{
				 char* p = strrchr(value, ';');
					 if(p) *p = 0x0;
			}
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
	 		continue;
	 	}
		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d",cdrSeqNum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s],pos[%d]", value,i);
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			//sprintf(value,"%s",TaiAuCdrInfo.sz_accumInfo);
			sprintf(value, "%s", stCdrInfo.accumuInfo);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_PUB_STR_ACCUMLATORINFO[%s],pos[%d]", value,i);
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_MSC_ID", (*iter)->value))
		{
			sprintf(value,"%s",stCdrInfo.MscId);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_MSC_ID[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_STR_LAC_ID", (*iter)->value))
		{
			sprintf(value,"%s",stCdrInfo.LacId);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_STR_LAC_ID[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery1->GetValue(i, temp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CDR_PUB_STR_CHARGED_PARTY value %s", temp);
			ChangeCallNumberValue(temp,value);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery1->GetValue(i, temp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "OCP_STR_ORIGIN_CALLING_NBR value %s", temp);
			ChangeCallNumberValue(temp,value);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery1->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery1->GetValue(i, temp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CDR_PUB_STR_CALLING_PARTY value %s", temp);
			ChangeCallNumberValue(temp,value);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery1->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_CALLING_AREA_ID", (*iter)->value))
		{
			try
			{
				pQuery->UnBindParam();
				pQuery->BindParam(1, stCdrInfo.CallingArea);
				pQuery->Execute();
				if(pQuery->Next())
				{
					pQuery->GetValue(1, value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CallingArea[%s],CallingAreaID[%s]", stCdrInfo.CallingArea,value);

				}
				else
				{
					value[0] = '\0';
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "invalid CallingArea[%s]", stCdrInfo.CallingArea);
				}
			}
			catch(UDBException& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
				return RB_SM_UNABLE_TO_COMPLY;
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_CALLING_AREA_ID[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_CALLED_AREA_ID", (*iter)->value))
		{
			try
			{
				pQuery->UnBindParam();
				pQuery->BindParam(1, stCdrInfo.CalledArea);
				pQuery->Execute();
				if(pQuery->Next())
				{
					pQuery->GetValue(1, value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "CalledArea[%s],CalledAreaID[%s]", stCdrInfo.CalledArea,value);
				}
				else
				{
					value[0] = '\0';
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE,  bizMsg->m_sessionID, "invalid CalledArea[%s]", stCdrInfo.CalledArea);
				}
			}
			catch(UDBException& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
				return RB_SM_UNABLE_TO_COMPLY;
			}

			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  "", "add item CDR_CALLED_AREA_ID[%s]", value);
			continue;
		}
              if(0 == strcmp("CDR_STR_BEARER_CAPABILITY", (*iter)->value))
		{
                     value[0] = 0x0;
                     if(bizMsg->m_if4Gpp == 1)
                     {
			    pQuery1->GetValue(i, value);
                     }
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());
			continue;
		}

	 	if((*iter)->flag)
	 	{
	 		//i++;
	 		nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
	 		if(nFieldNum)  //话单特殊字段处理
	 		{
	 			if(nFieldNum < 9)
	 				AddFeeItem(vecFeeItem,&cdr,nFieldNum);
				else
	 				AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
	 			//i--;
	 			continue;
	 		}
	 		else
	 		{
	 			pQuery1->GetValue(i, value);
	 			DealwithField(value);
	 			strcat(cdr.m_body, value);
	 			strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
	 		}
	 	}
	 	else
	 	{
	 		strcpy(value, (*iter)->value);
	 		DealwithField(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
	 	}
		}

}



