/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCFreeNumber.h
*Indentifier：
*
*Description：
*		消息解析类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_FREENUMBER_H__
#define __DC_FREENUMBER_H__
#include "DCBasePlugin.h"

class DCFreeNumber : public DCBasePlugin
{
	public:	
		DCFreeNumber(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCFreeNumber()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		virtual const char* desc();

};
#endif
