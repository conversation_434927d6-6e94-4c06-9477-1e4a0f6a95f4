include ../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include

EPT_INC=$(PWD)/include
EPT_SRC=$(PWD)/src
EPT_OBJ=$(PWD)/obj

EPT_CPP=DCBizEpt.cpp DCBizEptAns.cpp  DCBizEptReq.cpp DCEptBaseFlow.cpp DCEptBolt.cpp DCEptTimeoutREA.cpp DCEptTimeoutSession.cpp desc_EptAns.cpp \
		desc_EptBaseFlow.cpp desc_EptBolt.cpp desc_EptRBA.cpp desc_EptReq.cpp desc_EptSession.cpp
           
EPT_SRCS=$(addprefix $(EPT_SRC)/, $(EPT_CPP))
EPT_OBJS=$(patsubst $(EPT_SRC)/%.cpp, $(EPT_OBJ)/%.o, $(EPT_SRCS))

TLIBFLOW= $(RELEASE_PATH)/plugin/libEptBaseFlow.so
TLIBBOLT= $(RELEASE_PATH)/plugin/libEptBolt.so
TLIBREQ= $(RELEASE_PATH)/plugin/libEptReq.so
TLIBANS= $(RELEASE_PATH)/plugin/libEptAns.so
TLIBRBA= $(RELEASE_PATH)/plugin/libEptRBA.so
TLIBSESSION= $(RELEASE_PATH)/plugin/libEptSession.so

CFLAGS += -std=c++11

INCLUDE =-I$(AVRO)/include -I$(DCLOGCLI)/include -I$(COMMON_INC) -I$(ITF)/include -I$(DFM_INC_PATH) -I$(MQ)/include -I$(TXML)/include -I$(EPT_INC) -I$(KPI_SENDER_INC) -I$(EVT_CHECK_INC)
INCLUDEALL =$(INCLUDE) -I$(DCLOGCLI)/include -I$(JSTORM_INC) -I$(JSON_INC)  -I$(ZK_INC) -I$(DCA_INC)/json_dca -I$(ACE_INC_PATH)

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(DCLOGCLI)/lib -L$(DFM_LIB_PATH) -L$(JSTORM_LIB) -L$(JSON_LIB) -L$(ZK_LIB) -L$(DCA_LIB) -L$(TXML)/lib -L$(ACE_LIB_PATH) -L$(MQ)/lib -L$(LBSPUBROOT)/release/realbilling/lib -L$(KPI_SENDER_LIB) -L$(EVT_CHECK_LIB)
LIBSLIST=  -ldclogcli  -lCommonIF -lkpisender -lCheckSDK

LIBSALL=-ldclogcli -ldfm -lcstorm -lzookeeper_mt -ldcmq -lACE -ltinyxml -lrocketmq64 -lCommonIF -lkpisender -lCheckSDK

libtarget=$(TLIBREQ) $(TLIBANS) $(TLIBRBA) $(TLIBSESSION) $(TLIBFLOW) $(TLIBBOLT)

tmpvar:=$(call CreateDir, $(EPT_OBJ))
.PHONY: all clean dup
all:$(TLIBREQ) $(TLIBANS) $(TLIBRBA) $(TLIBSESSION) $(TLIBFLOW) $(TLIBBOLT)
$(TLIBBOLT): $(EPT_OBJS)
	@echo "build libEptBolt.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBBOLT)  $(EPT_OBJ)/DCEptBolt.o $(EPT_OBJ)/desc_EptBolt.o $(LIBPATH) $(LIBSLIST) $(LIBSALL)
$(TLIBFLOW): $(EPT_OBJS)
	@echo "build libEptBaseFlow.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBFLOW)  $(EPT_OBJ)/DCEptBaseFlow.o $(EPT_OBJ)/desc_EptBaseFlow.o $(LIBPATH) $(LIBSLIST)
$(TLIBSESSION): $(EPT_OBJS)
	@echo "build libEptSession.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBSESSION)  $(EPT_OBJ)/DCBizEpt.o $(EPT_OBJ)/DCEptTimeoutSession.o $(EPT_OBJ)/desc_EptSession.o $(LIBPATH) $(LIBSLIST)
$(TLIBRBA): $(EPT_OBJS)
	@echo "build libEptRBA.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBRBA)  $(EPT_OBJ)/DCBizEpt.o $(EPT_OBJ)/DCEptTimeoutREA.o $(EPT_OBJ)/desc_EptRBA.o $(LIBPATH) $(LIBSLIST)
$(TLIBANS): $(EPT_OBJS)
	@echo "build libEptAns.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBANS)  $(EPT_OBJ)/DCBizEpt.o $(EPT_OBJ)/DCBizEptAns.o $(EPT_OBJ)/desc_EptAns.o $(LIBPATH) $(LIBSLIST)
$(TLIBREQ): $(EPT_OBJS)
	@echo "build libEptReq.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBREQ)  $(EPT_OBJ)/DCBizEpt.o $(EPT_OBJ)/DCBizEptReq.o $(EPT_OBJ)/desc_EptReq.o $(LIBPATH) $(LIBSLIST)
$(EPT_OBJS):$(EPT_OBJ)/%.o:$(EPT_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDEALL)
$(EPT_SRC)/desc_EptBaseFlow.cpp:$(EPT_SRC)/desc_EptBaseFlow.clog
	$(TOOL)/clogtool -i $< -o $@
$(EPT_SRC)/desc_EptBolt.cpp:$(EPT_SRC)/desc_EptBolt.clog
	$(TOOL)/clogtool -i $< -o $@
$(EPT_SRC)/desc_EptRBA.cpp:$(EPT_SRC)/desc_EptRBA.clog
	$(TOOL)/clogtool -i $< -o $@
$(EPT_SRC)/desc_EptReq.cpp:$(EPT_SRC)/desc_EptReq.clog
	$(TOOL)/clogtool -i $< -o $@
$(EPT_SRC)/desc_EptSession.cpp:$(EPT_SRC)/desc_EptSession.clog
	$(TOOL)/clogtool -i $< -o $@
$(EPT_SRC)/desc_EptAns.cpp:$(EPT_SRC)/desc_EptAns.clog
	$(TOOL)/clogtool -i $< -o $@
clean:
	@rm -rf $(EPT_OBJS)
       
dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"
	
