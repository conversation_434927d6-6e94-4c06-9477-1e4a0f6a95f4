/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		OCPDEF_TEL.h
*Indentifier：
*		DIC-PRO-SRC-020620-TST(这里只作为示例)
*Description：
*		AVP相关的定义
		剥离出中国电信OCS系统AVP码定义
*Version：
*		V1.0
*Author:
*		fu.c
*Finished：
*		2009年8月27日
*History:
*		XXX	2007/11/27	V1.0 文件创建
********************************************/
#ifndef __OCPDEF_TEL_H__
#define __OCPDEF_TEL_H__

// #define __Volume_Quota_Threshold 869
// #define __Time_Quota_Threshold 868
// #define __Quota_Holding_Time 871
// #define __Quota_Consumption_Time 881

// #define __Account_Information 20356
// #define __AccountId 20357
// #define __Account_Type 20372
// #define __AccountDate 20359

// #define __Reporting_Reason 872 
// #define __Volume_Quota_Threshold 869
// #define __Time_Quota_Threshold 868

#define __PS_Information 874
#define __IN_Information  20300
#define __P2PSMS_Information 20400
#define __ISMP_Information 20500
#define __DSL_Information  20600

// #define __AoC_Information 20329
// #define __AoC_Balance 20330
// #define __AoC_Tariff 20332
// #define __AoC_Start_Time 20333
// #define __AoC_Unit 20334
// #define __AoC_Price 20335

//语音业务avp码定义
#define __Calling_Party_Number 20336
#define __Called_Party_Number 20337
#define __Service_Key    20338
#define __Calling_Vlr_Number 20302
#define __Calling_CellID_Or_SAI 20303
#define __Calling_LAI 20304
#define __Called_Vlr_Number 20305
#define __Called_CellID_Or_SAI 20306
#define __Called_LAI 20307
#define __Bearer_Capability 20313
#define __EventType_BCSM  20315
#define __Redirecting_Party_Id 20316
#define __Redirection_Information 20317
#define __IMSI 20201
#define __MSC_Address 20322
#define __Called_Short_Number 20381
#define __Group_Number 20382
#define __Vpn_Call_Type 20383

//PS业务avp码定义
#define __CG_Address 846
#define __GGSN_Address 847
#define __SGSN_Address 1228
#define __PDP_Address 1227

#define __CDMA_Charging_Id 21201
#define __CDMA_IP_Technology 21202
#define __CDMA_MS_IP_Address 21203
#define __CDMA_Qos_Profile_ID 21204
#define __PDSN_Address 21205
#define __HA_Address 21206
#define __AAA_Address 21207
#define __CDMA_Charging_Type 21208
#define __CDMA_Calling_Station_Id 21209

//LTE功能信息数据业务AVP
#define __Qos_Information 1016
#define __QoS_Class_Identifier 1028

//P2PSMS业务avp码定义
#define __OA_Subscription_Id 20511
#define __DA_Subscription_Id 20512
#define __SMSC_Address 20401
#define __SM_Id 20402
#define __SM_Length 20403
#define __ROMEING_Type 20404

//ISMP业务avp码定义
#define __Message_Id 20501
#define __Charge_Party_Type 20502
#define __SP_Id 20504
#define __Service_Enabler_Type 20505
#define __ChargingType 20515 
#define __Product_Id 20506
#define __Product_Offer_Id 20513
#define __Service_Type 20507
#define __Content_Id 20508
#define __Media_Type 20509
#define __Client_IP 20510

//DSL业务avp码定义
#define __ProductSpecID 30301
#define __NasIP 30302
#define __FRAMED_IP 30306
#define __Use_Node_ID 30313
#define __Roam_Flag 41234

//RATA 业务AVP码定义
#define  __Bill_Information 80700
#define  __Acc_Nbr 80125
#define  __Billing_Cycle 5043
#define  __Service_Result_Code 20631
#define  __Product_OFF_info 80175
#define  __Product_OFF_Name 80176
#define  __Begin_Date 80103
#define  __End_Date  80104
#define  __Respond_Ratable_Query 5041
#define  __Owner_Type 5011
#define  __Owner_ID 5012
#define  __Ratable_Resource_ID 5013
#define  __Ratable_Resource_name 80178
#define  __BeginTime 80525
#define  __EndTime 80527
#define  __Ratable_Amount 80177
#define  __Balance_Amount 5015
#define  __UnitType_Id 5019

//系统调用返回结果码
#define RET_SUCCESS               	0
#define RET_ERROR			     -1//错误

#endif

