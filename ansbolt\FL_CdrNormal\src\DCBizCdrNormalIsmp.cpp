#include "DCBizCdrNormalIsmp.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "DCOcpMsgDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"

using namespace ocs;

DCBizCdrNormalIsmp::DCBizCdrNormalIsmp()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormalIsmp::~DCBizCdrNormalIsmp()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

int DCBizCdrNormalIsmp::PretreatISMP(STBizMsg* bizMsg, int &iUserType,DataCDRInfo &stCdrInfo)
{
	int state = 0;
	SCCRDataUnit TUSU;
	int iServiceType = 0;
	char value[BIZ_TEMP_LEN_256] = {0};
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	UDBSQL *pQuery = dbm->GetSQL(ISMP__GetSessionInfo_Cdr);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			state = atoi(value);//SM_INT_REQ_TYPE
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "request type[%s]", value);
			
			pQuery->GetValue(2, value);
			TUSU.money= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "money[%s]", value);

			pQuery->GetValue(3, value);
			TUSU.duration = atoi(value);	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "duration[%s]", value);		

			pQuery->GetValue(4, value);
			TUSU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "unitTotal[%s]", value);

			pQuery->GetValue(5, value);
			TUSU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "unitInput[%s]", value);

			pQuery->GetValue(6, value);
			TUSU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "unitOutput[%s]", value);

			pQuery->GetValue(8, value);
			iServiceType = atol(value);
			if(SERVICES_CENARIOUS_ISMP_HRS_A == iServiceType)
			{
				iUserType = 1;
			}
			else if (701 == iServiceType)
			{
				iUserType = 2;
			}
			else
			{
				iUserType = 0;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "userType[%d]", iUserType);
			pQuery->GetValue(10, stCdrInfo.planInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "planInfo[%s]", stCdrInfo.planInfo);

			pQuery->GetValue(11, stCdrInfo.tarifInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "tarifInfo[%s]", stCdrInfo.tarifInfo);

			pQuery->GetValue(12, stCdrInfo.chargeInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "chargeInfo[%s]", stCdrInfo.chargeInfo);

			if(bizMsg->m_version != 1)
			{
                            // 在线
				//RE_LNG_CURRENT_CCR_TIME
				pQuery->GetValue(14, value);
				stCdrInfo.Day[0] = value[6];			
				stCdrInfo.Day[1] = value[7];			
				stCdrInfo.Day[2] = '\0';
				int day = atoi(stCdrInfo.Day);
				sprintf(stCdrInfo.Day,"%d",day);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "Day[%d]", day);
				strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "sessionCurrent[%s]", value);
			}
			else
			{
				// 离线
				//RE_LNG_CURRENT_CCR_TIME
				pQuery->GetValue(14, value);
				strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "sessionCurrent[%s]", value);

				//RE_LNG_SYS_CCR_TIME
				pQuery->GetValue(15, stCdrInfo.Payment);
				
				stCdrInfo.Day[0] = value[6];			
				stCdrInfo.Day[1] = value[7];			
				stCdrInfo.Day[2] = '\0';

				value[6] = 0;
				if(atoi(value) < stCdrInfo.Payment)
				{
					// 封账后上月话单使用本月账期DAY = 0
					stCdrInfo.Day[0] = '0';			
					stCdrInfo.Day[1] = '0';	
				}

				int day = atoi(stCdrInfo.Day);
				sprintf(stCdrInfo.Day,"%d",day);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "Day[%d]", day);
	
			}
			pQuery->GetValue(24, stCdrInfo.Payment);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "Payment[%ld]", stCdrInfo.Payment);
			if(stCdrInfo.Payment == 0)
			{
				strcpy(value,stCdrInfo.sessionCurrent);
				value[6] = '\0';
				stCdrInfo.Payment= atol(value);
			}
			pQuery->GetValue(16, value);
			stCdrInfo.lCdrVersionSerial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "OCS_SM_CDR_VERSION_SEQ_ID.nextval[%ld]", stCdrInfo.lCdrVersionSerial);

			pQuery->GetValue(17, stCdrInfo.accumuInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "accumuInfo[%s]", stCdrInfo.accumuInfo);

			pQuery->GetValue(18, value);   //OCP_INT_CHARGING_TYPE
			if(0 == atoi(value))
				stCdrInfo.nChargeType = 1;
			else if(1 == atoi(value))
				stCdrInfo.nChargeType = 2;
			else 
			{
				stCdrInfo.nChargeType = 0;
			}
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "nChargeType[%d]", stCdrInfo.nChargeType);

			pQuery->GetValue(19, value);
			stCdrInfo.PayFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "payflag[%d]", stCdrInfo.PayFlag);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_ISMP_TYPE,  "", "invalid session id", "");
			return -2;	
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	if(SERVICES_CENARIOUS_ISMP_HRS_A == iServiceType)
	{
	    iServiceType = SERVICES_CENARIOUS_ISMP;
	}
	stCdrInfo.nServiceScenarious = iServiceType;
	
	if((state == SM_SESSION_XDR_CODE+2)||(state == SM_SESSION_EVENT_CODE) || (state == SM_SESSION_EVENT_REFUND_CODE) || TUSU.money || TUSU.duration || TUSU.unitTotal || TUSU.unitInput || TUSU.unitOutput)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "stat[%d]", state);
		return 0;
	}
	else
	{
		return -1;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "usu is null", "");
	}
	
	//info 日志
	DCDATLOG("SM00014:%s%s%s%s%s%s%s", stCdrInfo.sessionStart, stCdrInfo.sessionCurrent,\
							stCdrInfo.planInfo, stCdrInfo.tarifInfo, stCdrInfo.chargeInfo, stCdrInfo.accumuInfo, stCdrInfo.orichargeInfo);
	return 0;
}

int DCBizCdrNormalIsmp::ComposeISMP(STBizMsg* bizMsg)
{
	int ret = 0;
	//SCDRData cdr = {0};
	SCDRData cdrHB = {0};
	vector<SCDRField*> *field;
	
	vector<SCDRField*> *fieldHB;
	vector<SCDRField*>::iterator iterHB;	
	
	DataCDRInfo stCdrInfo;
	
	int iUserType = 0;//用户类型标识:1、预付费;2、后付费
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;


/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		return RET_SUCCESS;
	}
*/
	ret = PretreatISMP(bizMsg, iUserType,stCdrInfo);
	if(ret == 0)
	{
		vector<STariffAccumCDRInfo> vecTaiAuCdr;
		vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

		//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
		ParaseTariffAccumCdrInfo(bizMsg,stCdrInfo,vecTaiAuCdr);
			
		field = TCDRDict::instance()->GetISMPField();
	
		UDBSQL *pQuery = dbm->GetSQL(COM_CDR_ISMP);
		int i = 1;

		try
		{
			string  sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "SQL[%s]", sql.c_str());
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, bizMsg->m_sessionID);

			pQuery->Execute();

			if(pQuery->Next())
			{
				int cdrSeqNum=0;				
				if(vecTaiAuCdr.size() > 0)
				{
					for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
					{
						cdrSeqNum++;
						SCDRData cdr = {0};	
						stCdrInfo.nCutnum = vecTaiAuCdr.size();						
						ComposeISMPCDR(bizMsg,stCdrInfo,pQuery,*TaiAuCdrIter,field,cdrSeqNum,cdr);
						
						int len = strlen(cdr.m_body);
						/*
						cdr.m_body[len - 1] = '\r';
						cdr.m_body[len] = '\n';
						cdr.m_body[len + 1] = '\0';
						*/
						cdr.m_body[len - 1] = '\0';

						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cdr field:\n[%s]", cdr.m_body);

						//发送话单数据
						UCDRData scdr;
						scdr.body = cdr.m_body;
						ret = ProduceCdr(bizMsg,scdr,stCdrInfo.PayFlag, iUserType);
					}

					if(ret != -2)
					{
						DeleteSession(bizMsg);
					}
				}
				else
				{
					cdrSeqNum++;
					SCDRData cdr = {0};	
					STariffAccumCDRInfo TaiAuCdrInfo;
					stCdrInfo.nCutnum = 1;
					ComposeISMPCDR(bizMsg,stCdrInfo,pQuery,TaiAuCdrInfo,field,cdrSeqNum,cdr);
					
					int len = strlen(cdr.m_body);
					//cdr.m_body[len - 1] = '\r';
					//cdr.m_body[len] = '\n';
					//cdr.m_body[len + 1] = '\0';
					cdr.m_body[len - 1] = '\0';

					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "cdr field:\n[%s]", cdr.m_body);

					//发送话单数据
					UCDRData scdr;
					scdr.body = cdr.m_body;
					ret = ProduceCdr(bizMsg,scdr,stCdrInfo.PayFlag, iUserType);

					if(ret != -2)
					{
						DeleteSession(bizMsg);
					}
				}
										
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_ISMP_TYPE, "", "invalid session id", "");
				return -2;
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE,  "", "select  execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		} 
		


		/*
		//按照HB 的规范出一份清单,电信需要
		if((0==pSMConfig->serv.bizType)&&strlen(pSMConfig->cdr.path.toHB)>0)
		{
		
			int HBFlag=1;
			pQuery = bizMsg->m_dbm->GetSQL(COM_CDR_ISMPHB);
			try
			{
				pQuery->UnBindParam();
				pQuery->BindParam(1, bizMsg->m_sessionID);
				pQuery->BindParam(2, bizMsg->m_subNumber);

				i = 1;
				if(pQuery->Execute())
				{
					fieldHB = TCDRDict::instance()->GetISMPFieldHB();
					for(iterHB=fieldHB->begin(); iterHB!=fieldHB->end(); iterHB++)
					{	
						if((0 == strcmp("CDR_PUB_INT_SERVICESCENARIOUS", (*iterHB)->value)))
						{
							sprintf(value,"%d", stCdrInfo.nServiceScenarious);
							DealwithField(value);
							strcat(cdr.m_body, value);
							strcat(cdr.m_body, "|");
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
							i++;
							continue;
						}
						

						if((*iterHB)->flag)
						{
							//i++;
							nFieldNum = CheckSpecialFiled((*iterHB)->value);
							if(nFieldNum)//在会话表中不存在的字段
							{
								//清单中加入Fee字段或者ACCT_Item_Type_ID字段
								AddFeeItem(vecFeeItem,&cdr,nFieldNum);
								//i--;
								continue;
							}
							else
							{
								pQuery->GetValue(i, value);
								DealwithField(value);
								strcat(cdrHB.m_body, value);
								strcat(cdrHB.m_body, "|");
								DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
								i++;
							}
						}
						else
						{
							strcpy(value, (*iterHB)->value);
							DealwithField(value);
							strcat(cdrHB.m_body, value);
							strcat(cdrHB.m_body, "|");
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
						}
					}
					int len = strlen(cdrHB.m_body);
					cdrHB.m_body[len - 1] = '\r';
					cdrHB.m_body[len] = '\n';
					cdrHB.m_body[len + 1] = '\0';
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_ISMP_TYPE,  bizMsg->m_sessionID, "no HB cdr needed", "");
					HBFlag = 0 ;//i=0时不必发送hb规范的清单
				}
			}
			catch(UDBException& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_P2PSMS_TYPE,  bizMsg->m_sessionID, "select  execption[%s]", e.ToString());
				return SM_OCP_UNABLE_TO_COMPLY;
			} 

			//发送话单数据
			if(HBFlag)
			{
				cdrHB.m_head.flag = SM_CDR_FLAG_NORMAL;
				cdrHB.m_head.sign = SM_CDR_TYPE_ISMP_HB;
				id = pSMConfig->cdr.queue[cdr.m_head.sign/SM_CDR_TYPE_IN-1].id;
				ret = sendCDR(&cdrHB, id);
				if(ret)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_ISMP_TYPE,  bizMsg->m_sessionID, "send CDR SM_CDR_TYPE_ISMP_HB failed of[%d],content:\n[%s]\n", id, cdrHB.m_body);
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "send CDR SM_CDR_TYPE_ISMP_HB successful[%d], content:\n[%s]\n", id, cdrHB.m_body);
				}
			}
		}*/
	}



	return RET_SUCCESS;
}

int DCBizCdrNormalIsmp::ComposeISMPCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr)
{
	char value[BIZ_TEMP_LEN_256] = {0};
	int nFieldNum;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;
	long long iCdrVersionSerial = 1;
	vector<SCDRField*>::iterator iter;
	int i = 1;
	vector<STFeeItem> vecFeeItem;

	string sqlbuf;
	pQuery->GetSqlString(sqlbuf);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "sql[%s]", sqlbuf.c_str());
	
	//账目类型和费用字段
	ParaseFeeItem(bizMsg,stCdrInfo,vecFeeItem);
	
	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");
	
	for(iter=field->begin(); iter!=field->end(); iter++)
	{
		if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)) && iCdrVersionType>0)
		{
			char szVersion[32] = {0};
			int iDay = timestampf();
			sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//组装version信息
			strcpy(value, szVersion);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			//i++;
			continue;
		}

		if((0 == strcmp("CDR_PUB_INT_SERVICESCENARIOUS", (*iter)->value)))
		{
			sprintf(value,"%d", stCdrInfo.nServiceScenarious);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",bizMsg->m_xdrsource);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],set", (*iter)->value,value);
			continue;
		}	
		if((0 == strcmp("CDR_BILL_CYCLE", (*iter)->value)))
		 {
			memset(value,0x00,sizeof(value));
			sprintf(value,"%ld", stCdrInfo.Payment);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],set", (*iter)->value,value);
			continue;
		 }
		if((0 == strcmp("DAY", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",stCdrInfo.Day);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d",cdrSeqNum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s]", value);
			//i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			//sprintf(value,"%s",TaiAuCdrInfo.sz_accumInfo);
			sprintf(value, "%s", stCdrInfo.accumuInfo);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "add item CDR_PUB_STR_ACCUMLATORINFO[%s]", value);
			//i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);			
			continue;						
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);			
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_FEETYPE", (*iter)->value))
		{
			if(0 == stCdrInfo.nChargeType)
			{
				value[0] = '\0';
			}
			else
				sprintf(value,"%d",stCdrInfo.nChargeType);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE,  "", "add item OCP_INT_CHARGING_TYPE[%s]", value);	
			//i++;
			continue;						
		}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}

		if(0 == strcmp("CDR_STR_CREATFLAG", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			DealwithField(temp);
	 		strcat(cdr.m_body, temp);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,temp,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());			
			continue;			
		}
		
		if((*iter)->flag)
		{
			//i++;
			nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
			if(nFieldNum)////话单特殊字段处理
			{
				if(nFieldNum < 9)
				//清单中加入Fee字段或者ACCT_Item_Type_ID字段
					AddFeeItem(vecFeeItem,&cdr,nFieldNum);
				else
					AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
				//i--;
				continue;
			}
			else
			{
				pQuery->GetValue(i, value);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
			}
		}
		else
		{
			strcpy(value, (*iter)->value);
			DealwithField(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
		}
	}

}

