﻿/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizMsgDef.h
*Indentifier：
*
*Description：
*		业务消息定义
*Version：
*		V1.0
*Author:
*		ZY.F

*Finished：
*
*History:
********************************************/
#ifndef _DC_BIZ_MSGDEF_H_
#define _DC_BIZ_MSGDEF_H_
#include <time.h>
#include <string>
#include <map>
#include "BizDataDef.h"
#include "SMParaStruct.h"
#include "DCBasePlugin.h"
#include <set>

enum EMsgType
{
	MSG_TYPE_CCR=0,
	MSG_TYPE_REA,
	MSG_TYPE_TONE,
	MSG_TYPE_TIMEOUT_SESSION,
	MSG_TYPE_TIMEOUT_REA
};

enum EptCdrType
{
	EPT_5G_DUP_MSG_CDR = 1,
	EPT_5G_UNKNOW_USER_CDR = 2,
	EPT_5G_OFFLINE_XDR_FAIL_CDR = 3,
	EPT_5G_FILTER_STATUS_CD = 4,
	EPT_5G_FILTER_AFTER_PAY = 5,
};

#define BIZ_TEMP_LEN_2048	2048
#define BIZ_TEMP_LEN_1024	1024
#define BIZ_TEMP_LEN_512	512
#define BIZ_TEMP_LEN_256	256
#define BIZ_TEMP_LEN_128 	128
#define BIZ_TEMP_LEN_64 	64
#define BIZ_TEMP_LEN_32 	32
#define BIZ_TEMP_LEN_16 	16
#define BIZ_TEMP_LEN_4 		4

#define __PS_Information 874
#define __IN_Information  20300
#define __P2PSMS_Information 20400
#define __ISMP_Information 20500
#define __DSL_Information  20600
#define __5G_Information 20800

#define OCP_TIME_BASE 2208988800
#define DCC_MSG_TIMEOUT_SEC 5

//系统默认配额组
#define VOICE_RATING_GROUP		-1   // 语音业务默认配额
#define DATA_RATING_GROUP		-2   // 数据业务3G 默认配额
#define ISMP_RATING_GROUP		-3   // 增值业务默认配额
#define DSL_RATING_GROUP		-4   // 宽带业务默认配额
#define DATA_SESSION_RELEASE_RATING		-5 //AAA和CCG主会话释放用到的rating_group
#define PGW_SESSION_RELEASE_RATING		-6 //PGW 业务默认配额
#define PGW_SESSION_RELEASE_RATIO		-7 //PGW 上网速率预占配置
#define RATING_5G_GROUP					-8 // 5G业务默认配额
#define RATING_5G_RATIO					-9 // 5G上网速率预占配置


//计费单元定义
#define RB_UNIT_CODE_SECOND           	1
#define RB_UNIT_CODE_CENT        		2
#define RB_UNIT_CODE_TOTAL_BYTES        3
#define RB_UNIT_CODE_UP_BYTES        	4
#define RB_UNIT_CODE_DOWN_BYTES        	5
#define RB_UNIT_CODE_TIME        		6
#define RB_UNIT_STR_SECOND           	"1"
#define RB_UNIT_STR_CENT        		"2"
#define RB_UNIT_STR_TOTAL_BYTES        	"3"
#define RB_UNIT_STR_UP_BYTES        	"4"
#define RB_UNIT_STR_DOWN_BYTES			"5"
#define RB_UNIT_STR_TIME        		"6"

class TSMPara;
class DCAnsPara;
class DCDBManer;
//class DCMqProduceServer;
struct STBizMsg
{
	long			m_serial;
	char			m_sessionID[256];
	char  			m_childsessionID[256];
	char			m_subNumber[32];					   //原始计费号码，可能携带接入号，国际码、区号
	char 			m_callingNumber[32];					//原始主叫号码
	char 			m_calledNumber[32];						//原始被叫号码
	char			m_szServiceContextIDStr[64];
	char			m_ServiceFlowID[128];
	char			m_ProductOfferId[128];
	char            m_mvnoId[16];
	char			m_eventType[16];
	char            m_xdrsource[128];
	char            m_taskId[16];
	char 			m_spid[32];
	unsigned int	m_serviceContextID;
	unsigned int    m_offline;			//离线标志
	unsigned int	m_requestType;
	unsigned int	m_requestNumber;

	// 4 bits
	int             m_serviceflag;
	int 			m_freeFlag:4;
	int 			m_sendASRFlag:4;						//标志批量释放会话时，是否发送ASR
	int 			m_longCDR:4;							//标识超长话单: 1 long time,  2 long octets, 3 month, 4 locaton, 5 ept cdr
	int 			m_RARFlag:4;							//标识RAR消息
	int 			m_testFlag:4;							//模拟拨测消息标识
    int             m_cdrCallType:4;
	int             m_roamtype;
	int             m_longtype;

	int 			m_trace_flag:4; 						//跟踪号码标志，1开启跟踪，0未开启跟踪
	int 			m_if4Gpp;

	int				m_naoc_type;
	int				m_nTUSU;
	int 			m_version;								//  1表示离线，2表示在线，默认为0；
	int  			m_spiltflag;								//  离线宽带、语音拆单用。
	// 4*8 bits
	int	            m_resultcode;

	// 2*4*8 bits
	long long		m_ratingGroup;

	int             m_userType;//请求流程中用来保存高风险用户的getuserinfo的错误码。应答流程中为本来作用。

	int				m_eptType;  // 1 req ept , 2  ans ept, 3 session timeout ept , 4 cca timeout ept , 5 cdr cut
	time_t			timestamps;
	unsigned long	timestampCCR;
	int				m_ifree;		// ifree 卡用户标志，0 不是，1 是

	string          data;//存放数据
	vector<string>  m_vectorMsg;
	std::multimap<string,string>* m_pSendMsg;

	void*			m_base;
	void*			m_extend;
	void*			m_ratingMsg;
	AREA_INFO*      m_visit;
	SUserInfo*      m_userinfo;
	TSMPara*        m_smpara;
	DCAnsPara*      m_anspara;
	DCDBManer*      m_dbm;
	long            billcycle;//离线账期

	//DCMqProduceServer* m_producer;
	const char* 		m_anstopic;
	const char*		m_cdrTopic;
	const char*		m_payflagTopic;
	const char*		m_topology;
	const char*		m_uid;
	const char*     m_testcdrTopic;
	//DCBasePlugin*   m_plugin;
	int             m_topictype;
	string			m_sourceId;
	string          m_switchId;
	string			m_sBatchId;
	string 			addressIpv6;
	string			m_CreditCtlFlag;
	string 			m_operListId;
	string          m_operType;
	int 			m_ilatnId;
	string			m_payMentMode;
	int 			m_volteFlag;
	int 			m_inVolteSwitchCdr;
	string 			visitAreaCode;
	int 			m_offlineCdrSwitch;   //离线落文件开关
	int 			m_offlineCdrFlag;     //离线落文件标识
	string			m_hexLocInfoTac;

	bool 			m_calledNumberIsDefaultArea; //被叫号码是否是默认区号
	bool 			termAllRgRecv;  //term消息 rg 是否全部收到	
	char 			m_sessionUuid[64];  //主、子会话表关联 uuid
	bool 			m_updateFlag;		//直接上 update 消息标识
	int             m_iRollFlag;        // 回退标识, 1-表示回退, 2-表示回收Json格式, 3-表示回收分隔符格式
	bool            m_bRollUser;        // 是否是回退用户
	bool 			m_ReDealAns;
    bool            m_bImsFilter;      // ims过滤
	bool            m_bExistMSCCFiltered;
	int             m_iOfflineXDREptFlag;    // offline处理异常
	string 			m_strCheckKey;		//稽核信息头
	int             m_iCdrRet;
	int             m_iEptFlag;
	string 			m_strservingCNPlmnId;
	std::set<long long>  m_RgFiltSet; 
	bool            m_bExistRGFiltered;

	STBizMsg()
	{
		m_serial = 0;
		memset(m_sessionID,0x00,sizeof(m_sessionID));
		memset(m_subNumber,0x00,sizeof(m_subNumber));
		memset(m_szServiceContextIDStr,0x00,sizeof(m_szServiceContextIDStr));
		memset(m_ServiceFlowID,0x00,sizeof(m_ServiceFlowID));
		memset(m_ProductOfferId,0x00,sizeof(m_ProductOfferId));
		memset(m_mvnoId,0x00,sizeof(m_mvnoId));
		memset(m_callingNumber,0x00,sizeof(m_callingNumber));
		memset(m_calledNumber,0x00,sizeof(m_calledNumber));
		memset(m_eventType, 0x00, sizeof(m_eventType));
		memset(m_xdrsource, 0x00, sizeof(m_xdrsource));
		memset(m_taskId,0x00,sizeof(m_taskId));
		memset(m_spid,0x00,sizeof(m_spid));
		m_serviceContextID = 0;
		m_offline = 0;
		m_requestType = 0;
		m_requestNumber = 0;

		m_serviceflag = 0;
		m_freeFlag = 0;
		m_sendASRFlag = 0;						//标志批量释放会话时，是否发送ASR
		m_longCDR = 0;							//标识超长话单
		m_RARFlag = 0;							//标识RAR消息
		m_testFlag = 0;							//模拟拨测消息标识
	    m_cdrCallType = 0;
		m_roamtype = 0;
		m_longtype = 0;

		m_trace_flag = 0; 						//跟踪号码标志，1开启跟踪，0未开启跟踪
		m_if4Gpp = 0;

		m_naoc_type = 0;
		m_nTUSU = 0;
		m_version = 0;
		m_resultcode = 0;

		m_ratingGroup = 0;
		m_userType    = 0;
		m_ifree = 0;
		m_eptType = 0;
		timestamps = 0;
		timestampCCR = 0;
		data.clear();//存放数据
		billcycle = 0;
		m_base = NULL;
		m_extend = NULL;
		m_ratingMsg = NULL;
		m_visit = NULL;
		m_userinfo = NULL;
		m_smpara = NULL;
		m_anspara = NULL;
		m_dbm = NULL;
		//m_producer =NULL;
		m_vectorMsg.clear();
		m_cdrTopic = NULL;
		m_topology = "";
		m_uid = "";
		m_testcdrTopic = NULL;
		m_anstopic = NULL;
		//m_plugin = NULL;
		m_topictype = 0;
		m_sourceId.clear();
		m_sBatchId.clear();
		addressIpv6.clear();
		m_strCheckKey.clear();
		m_strservingCNPlmnId.clear();
		m_ilatnId = 0;
		m_payMentMode.clear();
		m_volteFlag = 0;
		m_inVolteSwitchCdr = 0;
		m_offlineCdrSwitch = 0;
		m_offlineCdrFlag = 0;
		m_hexLocInfoTac.clear();
		termAllRgRecv = false;
		memset(m_spid,0x00,sizeof(m_sessionUuid));
		m_updateFlag = false;
		m_iRollFlag = false;
		m_bRollUser = false;
		m_ReDealAns = false;
        m_bImsFilter = false;    
	    m_bExistMSCCFiltered = false;
		m_ReDealAns = false;
		m_iEptFlag = 0;
	}

	~STBizMsg()
	{
		m_base     = NULL;
		m_extend   = NULL;
		m_userinfo = NULL;
		m_dbm      = NULL;
		//m_producer = NULL;
	}

};


#endif

