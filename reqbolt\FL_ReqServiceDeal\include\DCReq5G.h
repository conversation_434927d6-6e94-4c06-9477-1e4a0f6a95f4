/*******************************************
*Copyrights ? 2020，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReq5G.h
*Indentifier：
*
*Description：
*		5G业务处理类
*Version：
*		V1.0
*Author:
*
*Finished：
*
*History:
********************************************/
#ifndef __DC_REQ_5G_TEL_H__
#define __DC_REQ_5G_TEL_H__
#include "DCReqDATA.h"
#include <uuid/uuid.h>
#include "DCReqIndex.h"
#include "DCRbMsgDef.h"
#include "DCOcpMsgDef.h"
#include "time.h"
#include <list>


using namespace ocs;

struct ST_ParSmRgCondtion
{
	//int iOperListId;
	int iGroupId;
	char szItemCode[32 + 1];
	char szOperators[2 + 1];
	char szItemValue[100 + 1];
	char szFilterType[2 + 1];
	char szCondtionType[2 + 1];

	ST_ParSmRgCondtion()
	{
		//iOperListId = 0;
		iGroupId = 0;
		memset(szItemCode, 0x00, sizeof(szItemCode));
		memset(szOperators, 0x00, sizeof(szOperators));
		memset(szItemValue, 0x00, sizeof(szItemValue));
		memset(szFilterType, 0x00, sizeof(szFilterType));
		memset(szCondtionType, 0x00, sizeof(szCondtionType));
	}
};

class DCReq5G : public DCReqDATA
{
public:
	DCReq5G();
	virtual ~DCReq5G();

private:

	virtual int SwitchReqType(STBizMsg* bizMsg);

	int send5GInitCCA(STBizMsg* bizMsg, SCCRBase* base);

	int Init(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg);

	int Update(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg);

	int Term(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg);

	int OfflineAndXDR(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg);

	bool FilterUSU(TSMPara* pSMparam, ocs::SCCRDataUnit& USU);

	int FilterMultiUSU(TSMPara* pSMparam, ocs::SUSU* MSCC);

	bool FilterMSCC(TSMPara* smpara, ocs::SUSU* MSCC, ocs::rbext& ext);

	bool IsExistFilter(SCCRBase* base);

	int composeRER(STBizMsg* bizMsg, ocs::SUSU* MSCC, SSessionCacheData5G& cacheData5G, std::vector<long>& v_rg, bool& bUpdateNoChild);

	int ComposeOfflineMsg(STBizMsg* bizMsg);

	int InsertChildSession(STBizMsg* bizMsg, SUSU *MSCC, SSessionCacheData5G& cacheData5G);

	int UpdateChildSession(STBizMsg* bizMsg, SUSU *MSCC, SSessionCacheData5G& cacheData5G, bool bAllUSUFiltered);

	int GetDynamicStepInfo5G(DCDBManer* pdbm, long serv_id, int nLatn, long ratingGroup, long lnStartTime, STDynamicStepInfo5G& stDynamicStepInfo5G);

	int PushUSV(std::vector<ocs::SCCRDataUnit>& vecusu, ocs::rbdomain& domain, ocs::usu& u);

	int TotalUSUAdd(ocs::SCCRDataUnit& TUSU, const std::vector<ocs::SCCRDataUnit>& vecusu);

	int ExtUSUInsert(const std::vector<ocs::SCCRDataUnit>& vecusu, std::map<std::string, std::string>& ext, ocs::SCCRBase* base, int roamType, const char* visitArea, STBizMsg* bizMsg);

	int ExtUSUInsert(ocs::SCCRDataUnit& usu, std::map<std::string, std::string>& ext, ocs::SCCRBase* base,  int roamType, const char* visitArea, STBizMsg* bizMsg, int iSeq);

	void TimestampToDate(time_t timestamp, char *pszDate);

	int GetParSmFilterRgCondition(STBizMsg* bizMsg, int iOperListId, std::map<int, std::vector<ST_ParSmRgCondtion> >& parSmRgCondition);
	
	int Deal5GRgFilterRule(std::map<int, std::vector<ST_ParSmRgCondtion> >& parSmRgCondition, STBizMsg* bizMsg);

	bool JudeRg5GFilterRule(ST_ParSmRgCondtion& rgCondition, STBizMsg* bizMsg);

	bool CompareString(const char *v_a, const char *v_b, int &v_Operator);

	int SplitStrInt(const char *pszStr, const char cSplit, std::list<int>& ltStr);

	int JudgeExpress(char cValue, const char* pszExpress);

	int SplitStr(const char *pszStr, const char cSplit, std::vector<std::string>& vecStr);
	
private:
	DCReqIndex m_index;
};

#endif

