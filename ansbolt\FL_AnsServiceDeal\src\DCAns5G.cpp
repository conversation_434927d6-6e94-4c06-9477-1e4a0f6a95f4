#include "DCAns5G.h"
#include "ErrorCode.h"
#include "DCAnsPara.h"
#include "TConfig.h"
#include "DCRbMsgDef.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCCommonIF.h"
#include "UStaMsg.h"
#include "DCKpiSender.h"
DCAns5G::DCAns5G()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "", "");
}

DCAns5G::~DCAns5G()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "", "");
}

int DCAns5G::ComposeCCA(STBizMsg* bizMsg)
{
	int iRet = RET_SUCCESS;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "begin compose CCA");
	if (SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
	{
		bizMsg->m_requestType = SM_SESSION_UPDATE_FIRST_CODE;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request type[%d]", bizMsg->m_requestType);

	if (bizMsg->m_offline == 1 || SM_SESSION_XDR_CODE == bizMsg->m_requestType)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "invalid offline msg, requestType[%d] offline[%d]", bizMsg->m_requestType, bizMsg->m_offline);
		return RET_SUCCESS;
	}

	for (int i = strlen(bizMsg->m_sessionID) - 1; i >= 0; i--)
	{
		if (bizMsg->m_sessionID[i] == ';')
		{
			char szRatingGroup[20] = {0};
			strcpy(szRatingGroup, bizMsg->m_sessionID + i + 1);
			bizMsg->m_sessionID[i] = '\0';
			bizMsg->m_ratingGroup = atol(szRatingGroup);
			break;
		}
	}

	switch (bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
		{
			iRet = InitAns(bizMsg);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", iRet);
			return  iRet;
		}
		break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
		{
			iRet = Update(bizMsg);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", iRet);
			return	iRet;
			break;
		}
		case SM_SESSION_TERMINATION_CODE:
		{
			iRet = Term(bizMsg);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", iRet);
			return  iRet;
			break;
		}
		case SM_SESSION_EVENT_CODE:
		{
			return RET_SUCCESS;						//针对事件类RBA，为异常类型，丢弃该RBA消息
			break;
		}
		default:
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "unknow session state, requestType[%d]", bizMsg->m_requestType);
			return RB_SM_UNABLE_TO_COMPLY;
			break;
		}
	}

	return iRet;
}

int DCAns5G::GetUSUAmountFromExt(ocs::SCCRDataUnit& USU, const char* szUsuamount)
{
	char cnull = '\0';
	char* p = (char*)szUsuamount;
	USU.duration = atoi(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%d]", USU.duration);

	p = strchr(p, '|');
	if(p) 
	{
		p++;
	}
	else 
	{
		p = &cnull;
	}
	USU.unitTotal = atol(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitTotal);

	p = strchr(p, '|');
	if(p)
	{
		p++;
	}
	else
	{
		p = &cnull;
	}
	USU.unitInput = atol(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitInput);

	p = strchr(p, '|');
	if(p)
	{
		p++;
	}
	else 
	{
		p = &cnull;
	}
	USU.unitOutput = atol(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitOutput);

	return 0;
}

int DCAns5G::InitAns(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCAns5G::Update(STBizMsg* bizMsg)
{
	int iRet = RET_SUCCESS;
	ocs::rbext* ext = (ocs::rbext*)bizMsg->m_extend;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	DCAnsPara* smpara = (DCAnsPara*)bizMsg->m_anspara;

	std::map<long, int> mapAllRG;
	ocs::SCCRDataUnit USU;
	ocs::SCCRDataUnit TUSU;
	for (std::map<string,string>::iterator itExt = ext->kv.begin(); itExt != ext->kv.end(); itExt++)
	{
		if (strncmp(itExt->first.c_str(), "RG", 2) == 0)
		{
			mapAllRG.insert(make_pair(atol(itExt->second.c_str()), 0));
		}
		else if (strncmp(itExt->first.c_str(), "USUAMOUNT", 9) == 0)
		{
			GetUSUAmountFromExt(USU, itExt->second.c_str());
			TUSU.duration += USU.duration;
			TUSU.unitTotal += USU.unitTotal;
			TUSU.unitInput += USU.unitInput;
			TUSU.unitOutput += USU.unitOutput;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg size[%d]", mapAllRG.size());

	UDBSQL* pQuery = NULL;
	//授权使用量
	long lnReLastGsuTime = 0;
	long lnReLastGsuByte = 0;
	int gUnit = 0;
	long long gNum = 0;
	if (base->gsv.empty())
	{ // 批价对于高额不会返回gsv, 实扣算成功, 返回网元正常
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "empty gsv info");
		pQuery = dbm->GetSQL(_5G_ANS_Update_ChildSession_NoGsv);
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, RECVRB_ACTION); // SM_INT_SESSION_STATUS
			pQuery->BindParam(2, 2001); // SM_INT_RESULT_CODE
			pQuery->BindParam(3, (long)TUSU.duration);   // SM_LNG_ALL_USU_TIME
			pQuery->BindParam(4, TUSU.unitTotal);  // SM_LNG_ALL_USU_TOTAL_OCT 
			pQuery->BindParam(5, TUSU.unitInput);  // SM_LNG_ALL_USU_INPUT_OCT 
			pQuery->BindParam(6, TUSU.unitOutput); // SM_LNG_ALL_USU_OUTPUT_OCT 
			pQuery->BindParam(7, bizMsg->m_sessionID);
			pQuery->BindParam(8, bizMsg->m_childsessionID);
			pQuery->Execute();
			pQuery->Connection()->Commit();
		}
		catch (UDBException& e)
		{
			std::string strSql;
			pQuery->GetSqlString(strSql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", strSql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "QueryALLRGUpdate execption[%s]", e.ToString());
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}
	else
	{
		gUnit = base->gsv[0].unit;
		gNum = base->gsv[0].amount;
		DCBIZLOG(DCLOG_LEVEL_TRACE, SM_DATA_TYPE, "", "gunit[%d], gnum[%d]", gUnit, gNum);
		if (RB_UNIT_CODE_TOTAL_BYTES == gUnit)
		{
			lnReLastGsuByte = gNum;
		}
		else if (RB_UNIT_CODE_SECOND == gUnit)
		{
			lnReLastGsuTime = gNum;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gsu object[%ld],gsu time[%d]", lnReLastGsuByte, lnReLastGsuTime);
		pQuery = dbm->GetSQL(_5G_ANS_Update_ChildSession);
		try
		{
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1, RECVRB_ACTION); // SM_INT_SESSION_STATUS
			pQuery->BindParam(2, 2001); // SM_INT_RESULT_CODE
			pQuery->BindParam(3, gUnit); // RE_INT_LAST_GSU_UNIT
			pQuery->BindParam(4, lnReLastGsuByte); // RE_LNG_LAST_GSU_TOTAL_OCT
			pQuery->BindParam(5, lnReLastGsuTime); // RE_LNG_LAST_GSU_TIME
			pQuery->BindParam(6, (long)TUSU.duration);   // SM_LNG_ALL_USU_TIME
			pQuery->BindParam(7, TUSU.unitTotal);  // SM_LNG_ALL_USU_TOTAL_OCT 
			pQuery->BindParam(8, TUSU.unitInput);  // SM_LNG_ALL_USU_INPUT_OCT 
			pQuery->BindParam(9, TUSU.unitOutput); // SM_LNG_ALL_USU_OUTPUT_OCT 
			pQuery->BindParam(10, bizMsg->m_sessionID);
			pQuery->BindParam(11, bizMsg->m_childsessionID);
			pQuery->Execute();
			pQuery->Connection()->Commit();
		}
		catch (UDBException& e)
		{
			std::string strSql;
			pQuery->GetSqlString(strSql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", strSql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "QueryALLRGUpdate execption[%s]", e.ToString());
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	std::map<long long, STCodeOfr> MSCCResult;
	char szValue[BIZ_TEMP_LEN_2048]  = {0};
	int iRecvRGNum = 0;
	pQuery = dbm->GetSQL(_5G_SelectSessionStoreRbansRg); //q_5g_session_rbansrg
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		while (pQuery->Next())
		{
			STCodeOfr stCodeOfr;
			pQuery->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSessionStatus[%d]", stCodeOfr.nSessionStatus);
			if (stCodeOfr.nSessionStatus != RECVRB_ACTION)
			{
				continue;
			}

			pQuery->GetValue(2, szValue);//OCP_LNG_RATING_GROUP
			long lnRatingGroup = atol(szValue);

			std::map<long, int>::iterator itRG = mapAllRG.find(lnRatingGroup);
			if (itRG != mapAllRG.end())
			{
				iRecvRGNum++;
			}
			else
			{
				continue;
			}

			pQuery->GetValue(3, szValue);
			stCodeOfr.resultcode = atoi(szValue);  // SM_INT_RESULT_CODE
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg result code[%s]", szValue);

			pQuery->GetValue(4, stCodeOfr.gUnit);   // RE_INT_LAST_GSU_UNIT
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gUnit[%d]", stCodeOfr.gUnit);

			pQuery->GetValue(5, stCodeOfr.gTotal);  // RE_LNG_LAST_GSU_TOTAL_OCT
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gTotal[%ld]", stCodeOfr.gTotal);

			pQuery->GetValue(6, stCodeOfr.gTime);   // RE_LNG_LAST_GSU_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gTime[%ld]", stCodeOfr.gTime);

			pQuery->GetValue(7, stCodeOfr.validityTime);  // SM_LNG_MSCC_VALIDITY_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "validityTime[%d]", stCodeOfr.validityTime);

			pQuery->GetValue(8, stCodeOfr.quotaHoldingTime); // SM_LNG_QUOTA_HOLDING_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quotaHoldingTime[%d]", stCodeOfr.quotaHoldingTime);

			pQuery->GetValue(9, stCodeOfr.quotaConsumeTime); // SM_LNG_QUOTA_CONSUME_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quotaConsumeTime[%d]", stCodeOfr.quotaConsumeTime);

			pQuery->GetValue(10, stCodeOfr.timeQuotaThreshold); // SM_LNG_TIME_QUOTA_THRESHOLD
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "timeQuotaThreshold[%d]", stCodeOfr.timeQuotaThreshold);

			pQuery->GetValue(11, stCodeOfr.volumeQuotaThreshold); // SM_LNG_VOLUME_QUOTA_THRESHOLD
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold[%d]", stCodeOfr.volumeQuotaThreshold);

			pQuery->GetValue(12, stCodeOfr.volumeQuotaThreshold2); // SM_LNG_VOLUME_QUOTA_THRESHO_1
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold2[%d]", stCodeOfr.volumeQuotaThreshold2);

			stCodeOfr.finalAction = base->final_flag;
			MSCCResult.insert(pair<long long, STCodeOfr>(lnRatingGroup, stCodeOfr));
		}
	}
	catch (UDBException& e)
	{
		std::string strSql;
		pQuery->GetSqlString(strSql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", strSql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "QueryALLRGUpdate execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	if (iRecvRGNum < mapAllRG.size())
	{ // 未接收到全部RG，不处理
		return RET_SUCCESS;
	}

	//替换成5G结构体
	ocs::SCCA5G cca;
	cca.sessionID = bizMsg->m_sessionID;
	cca.timestamp = ""; // 由代理赋值
	cca.resultCode = 2001;
	cca.requestType = bizMsg->m_requestType;
	if (cca.requestType == SM_SESSION_UPDATE_FIRST_CODE)
	{
		cca.requestType = SM_SESSION_INITIAL_CODE;
	}
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 7;
	cca.finalUnitAction = base->final_flag;
	for (std::map<long long, STCodeOfr>::iterator itMSCC = MSCCResult.begin(); itMSCC != MSCCResult.end(); itMSCC++)
	{
		ocs::AUSU gsu;
		gsu.ratinggroup	= itMSCC->first;
		gsu.Unit = itMSCC->second.gUnit;
		sprintf(szValue, "%d", itMSCC->second.finalAction);
		gsu.FinalUnitAction = szValue;

		if (RB_UNIT_CODE_SECOND == gsu.Unit)
		{
			gsu.CCTime = itMSCC->second.gTime;
		}
		else if (RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit)
		{
			gsu.Unittotal = itMSCC->second.gTotal;
		}
		else if (RB_UNIT_CODE_UP_BYTES == gsu.Unit)
		{
			gsu.Unitinput = itMSCC->second.gTotal;
		}
		else if (RB_UNIT_CODE_DOWN_BYTES == gsu.Unit)
		{
			gsu.Unitoutput = itMSCC->second.gTotal;
		}

		if (1 == itMSCC->second.finalAction)
		{ //重定向
			gsu.RedirectAddressType = smpara->GetPSPara()->redirectType;
			gsu.RedirectServerAddress = smpara->GetPSPara()->redirectServer;
		}

		//在线模式返回 triggers
		if (gsu.Unit != RB_UNIT_CODE_SECOND)
		{
			gsu.CCTime = itMSCC->second.gTime;
		}
		gsu.ValidityTime = itMSCC->second.validityTime;
		gsu.QuotaHoldingTime = itMSCC->second.quotaHoldingTime;
		gsu.QuotaConsumptionTime = itMSCC->second.quotaConsumeTime;
		gsu.TimeQuotaThreshold = itMSCC->second.timeQuotaThreshold;

		if ((itMSCC->second.volumeQuotaThreshold2 != 0) 
			&& (gsu.Unittotal <= itMSCC->second.volumeQuotaThreshold) 
			&& (RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit))//授权量<=授权量，并且是按流量计费的，取门限值2
		{
			gsu.VolumeQuotaThreshold = itMSCC->second.volumeQuotaThreshold2;
		}
		else
		{
			gsu.VolumeQuotaThreshold = itMSCC->second.volumeQuotaThreshold;
		}

		if (smpara->GetPSPara()->nFinalUintResultCode && base->final_flag)
		{
			gsu.ResultCode = smpara->GetPSPara()->nFinalUintResultCode;
		}
		else
		{
			gsu.ResultCode = itMSCC->second.resultcode ? itMSCC->second.resultcode : 2001;
		}

		cca.MUU.push_back(gsu);
	}

	string msg;
	iRet = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (iRet != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce cdr_5g failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCKpiMon* ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
	char monGroup[50] = {0};
	DCKpiSender::instance()->GetFullGroup("TraceKpi", bizMsg->m_ilatnId, monGroup);
	DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "SMOnA", NULL, 1); //接收CHFProxy的online消息量
	
	return RET_SUCCESS;
}

int DCAns5G::Term(STBizMsg* bizMsg)
{
	int iRet = RET_SUCCESS;
	ocs::rbext* ext = (ocs::rbext*)bizMsg->m_extend;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	DCAnsPara* smpara = (DCAnsPara*)bizMsg->m_anspara;
	std::map<long, int> mapAllRG;
	for (std::map<string,string>::iterator itExt = ext->kv.begin(); itExt != ext->kv.end(); itExt++)
	{
		if (strncmp(itExt->first.c_str(), "RG", 2) == 0)
		{
			mapAllRG.insert(make_pair(atol(itExt->second.c_str()), 0));
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg size[%d]", mapAllRG.size());

	UDBSQL* pQuery = dbm->GetSQL(_5G_ANS_Term_ChildSession);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, RECVRB_ACTION); // SM_INT_SESSION_STATUS
		pQuery->BindParam(2, 2001); // SM_INT_RESULT_CODE
		pQuery->BindParam(3, bizMsg->m_sessionID);
		pQuery->BindParam(4, bizMsg->m_childsessionID);
		pQuery->Execute();
		pQuery->Connection()->Commit();
	}
	catch (UDBException& e)
	{
		std::string strSql;
		pQuery->GetSqlString(strSql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", strSql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "QueryALLRGUpdate execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	std::map<long long, STCodeOfr> MSCCResult;
	char szValue[BIZ_TEMP_LEN_2048]  = {0};
	int iRecvRGNum = 0;
	pQuery = dbm->GetSQL(_5G_SelectSessionStoreRbansRg); //q_5g_session_rbansrg
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		while (pQuery->Next())
		{
			STCodeOfr stCodeOfr;
			pQuery->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSessionStatus[%d]", stCodeOfr.nSessionStatus);
			if (stCodeOfr.nSessionStatus != RECVRB_ACTION)
			{
				continue;
			}

			pQuery->GetValue(2, szValue);//OCP_LNG_RATING_GROUP
			long lnRatingGroup = atol(szValue);

			std::map<long, int>::iterator itRG = mapAllRG.find(lnRatingGroup);
			if (itRG != mapAllRG.end())
			{
				iRecvRGNum++;
			}
			else
			{
				continue;
			}

			pQuery->GetValue(3, szValue);
			stCodeOfr.resultcode = atoi(szValue);  // SM_INT_RESULT_CODE
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg result code[%s]", szValue);

			MSCCResult.insert(pair<long long, STCodeOfr>(lnRatingGroup, stCodeOfr));
		}
	}
	catch (UDBException& e)
	{
		std::string strSql;
		pQuery->GetSqlString(strSql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", strSql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "QueryALLRGUpdate execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	if (iRecvRGNum < mapAllRG.size())
	{ // 未接收到全部RG，不处理
		return RET_SUCCESS;
	}

	//替换成5G结构体
	ocs::SCCA5G cca;
	cca.sessionID = bizMsg->m_sessionID;
	cca.timestamp = ""; // 由代理赋值
	cca.resultCode = 2001;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 7;
	cca.finalUnitAction = base->final_flag;
	for (std::map<long long, STCodeOfr>::iterator itMSCC = MSCCResult.begin(); itMSCC != MSCCResult.end(); itMSCC++)
	{
		ocs::AUSU gsu;
		gsu.ratinggroup	= itMSCC->first;
		gsu.ResultCode = itMSCC->second.resultcode ? itMSCC->second.resultcode : 2001;
		cca.MUU.push_back(gsu);
	}

	string msg;
	iRet = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (iRet != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce cdr_5g failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCKpiMon* ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
	char monGroup[50] = {0};
	DCKpiSender::instance()->GetFullGroup("TraceKpi", bizMsg->m_ilatnId, monGroup);
	DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "SMOnA", NULL, 1); //接收CHFProxy的online消息量
	DelSession(bizMsg);
	return RET_SUCCESS;
}

