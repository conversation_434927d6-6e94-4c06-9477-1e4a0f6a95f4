#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCPluginManer.h"
#include "DCMqConsumeServer.h"
#include "DCOcpMsgDef.h"
#include "DCSeriaOp.h"
#include "UHead.h"

using namespace ocs;
using namespace std;

DCMqConsumeServer *m_consumer  =NULL;
DCPluginManer m_pm; 

int Initialize()
{
	int ret = 0;
	char *buf =getenv("OCS_CONFIG");
	if(NULL==buf)
	{
	
	}

	
	//日志初始化
	ret = DCLOGINIT("reqbolt","reqbolt",6,"/public/ocs_ah/log/reqlog");
	if(ret)
	{
	  return -1;
	}

	
	//mq初始化
    char *brokers = "192.168.161.177:9876";    
	char *pTopic = "CCRSMS";    
	char *pDefaultGroup = "ConsumerInGroup";    
	
	m_consumer = new DCMqConsumeServer(MQ_ROCKET);  
	ret = m_consumer->Init(brokers, pDefaultGroup);	
	if(ret !=0)	
	{		
		return -1;	
	}
    m_consumer->Subscribe(pTopic, "*");    
	m_consumer->Start();
	
	//插件初始化
	ret = m_pm.init(buf, "reqbolt",DFM_USE_DBM|DFM_USE_REFRESH|DFM_USE_NOFLOW);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin failed");
		return 1;
	}

	ret = m_pm.load_flow("reqbolt");
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init plugin reqbolt failed");
		return 1;
	}	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init plugin successful");
 

	return 0;
}

int main(int argc,char **argv) 
{

	int ret = Initialize();
	if(ret)
	{
		return ret;
	}
	
	UHead uhd;
	SCCRBase base;
	//SCCRSMS sms;
	SCCRDATA data;
	uhd.uid="143807011476877049";
	base.sessionId="scp002.chinatelecom.com;1207666037;191744830;3681;65637";
	base.originHost="dcc_cs.chinatelecom.com";
	base.originRealm="dcc_cs.chinatelecom.com";
	base.szContextOriginal="<EMAIL>";	
	base.requestType=atoi(argv[1]);	
	base.requestAction=0;			
	base.requestNumber=0;	
	base.timestamp= 3676262492;
	base.serial   = 2;
	base.ServiceInformation = 874;

	base.subscriptionType = 0;
	base.subscriptionData = "13902820001";

	data.userLocationInfo = "0x8264f01101ff6401100039301";
	data.gppChargingID = 21;
	data.chargingID = "ef800021";
	data.PDSNAddress= "**************";
	data.RATType = 59;

	SUSU USU;
	USU.ratingGroup = 160030;
	USU.USU0.duration = 0;
    USU.USU0.unitInput =10240;
    USU.USU0.unitOutput = 10240;
    USU.USU0.unitTotal =20480;
	base.MSCC.push_back(USU);


	// 序列化
	DCSeriaEncoder en(ESeriaBinString); 	// 序列化
	en.encode(uhd);
	en.encode(base);
	en.encode(data);

	std::string recvCCRMsg = HexEncode(en.data(), en.size());	
	std::string sendmsg;
	
	DCBaseFlow* flow = m_pm.get_flow("reqbolt");
	if(!flow)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find flow[FL_ReqBaseFlow]");
		return 1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get  FL_ReqBaseFlow successful");
	
	ret = flow->call(&recvCCRMsg, &sendmsg);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","call  flow successful");

	en.clear();

	sleep(5);

	while(1)
	{
	
		DCMqMessage* msg = NULL;
		msg = m_consumer->Consume();
		if(msg != NULL)	
		{	
			recvCCRMsg.clear();
			recvCCRMsg.assign(msg->getBody(),msg->getLen());
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","consume msgfailed");

		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","consume msg successful");
		
		/*std::string sendmsg;
		
		DCBaseFlow* flow = m_pm.get_flow("FL_ReqBaseFLOW");
		if(!flow)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find flow[FL_ReqBaseFLOW]");
			return 1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get  FL_ReqBaseFLOW successful");

	    ret = flow->call(&recvCCRMsg, &sendmsg);
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","call  flow successful");*/
		
		delete msg;
		msg = NULL;
	}
	return 0;
}
