/*******************************************
*Copyrights ? 2020，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAns5G.h
*Indentifier：
*
*Description：
*		5G处理类
*Version：
*		V1.0
*Author:
*
*Finished：
*
*History:
********************************************/
#ifndef __DC_ANS_5G_H__
#define __DC_ANS_5G_H__

#include "DCAnsDATA.h"


class DCAns5G:public DCAnsDATA
{
	public:

		DCAns5G();
		virtual ~DCAns5G();

	protected:

		virtual int ComposeCCA(STBizMsg* bizMsg);

		int GetUSUAmountFromExt(ocs::SCCRDataUnit& USU, const char* szUsuamount);

		int InitAns(STBizMsg* bizMsg);

		int Update(STBizMsg* bizMsg);

		int Term(STBizMsg* bizMsg);
};

#endif

