#include "CKV.h"
#include "KVProto.h"
#include "ByteSwap.h"
#include <assert.h>
#include <stdio.h>
#include <stdlib.h>

using namespace std;

CKVVal::CKVVal()
{
	memset(m_key, 0, sizeof(m_key));
	memset(m_val, 0, sizeof(m_val));
	m_type = 0;
}

CKVVal::CKVVal(const char* key)
{
    strncpy(m_key, key, sizeof(m_key)-1);
    m_key[sizeof(m_key)-1] = 0x0;

	memset(m_val, 0, sizeof(m_val));
	m_type = 0;
}

CKVVal::~CKVVal()
{
	if(m_type == t_string)
	{
		char* p = 0;
		memcpy(&p, m_val, sizeof(char*));
		delete [] p;
	}
}

void CKVVal::key(const char* key)
{
	strncpy(m_key, key, sizeof(m_key)-1);
    m_key[sizeof(m_key)-1] = 0x0;
}

void CKVVal::value(const char* val)
{
    clear();
    char* p = new char[strlen(val)+1];
    strcpy(p, val);
    memcpy(m_val, &p, sizeof(char*));
    m_type = t_string;
}

void CKVVal::value(int8_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(int8_t));
    m_type = t_int8;
}

void CKVVal::value(uint8_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(uint8_t));
    m_type = t_uint8;
}

void CKVVal::value(int16_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(int16_t));
    m_type = t_int16;
}

void CKVVal::value(uint16_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(uint16_t));
    m_type = t_uint16;
}

void CKVVal::value(int32_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(int32_t));
    m_type = t_int32;
}

void CKVVal::value(uint32_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(uint32_t));
    m_type = t_uint32;
}

void CKVVal::value(int64_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(int64_t));
    m_type = t_int64;
}

void CKVVal::value(uint64_t val)
{
	clear();
    memcpy(m_val, &val, sizeof(uint64_t));
    m_type = t_uint64;
}

void CKVVal::value(float val)
{
	clear();
    memcpy(m_val, &val, sizeof(float));
    m_type = t_float;
}

void CKVVal::value(double val)
{
	clear();
    memcpy(m_val, &val, sizeof(double));
    m_type = t_double;
}

void CKVVal::value(void* ptr)
{
	clear();
    memcpy(m_val, ptr, sizeof(void*));
    m_type = t_addr;
}

void CKVVal::strto(int type)
{
    if(m_type == t_string && type != t_string)
	{
		char* p = NULL;
        memcpy(&p, m_val, sizeof(char*));

        switch(type)
        {
		case t_int8:
		{
            int8_t val = (int8_t)atoi(p);
            memcpy(m_val, &val, sizeof(int8_t));
			break;
		}
		case t_uint8:
		{
			uint8_t val = (uint8_t)atoi(p);
            memcpy(m_val, &val, sizeof(int8_t));
			break;
		}
		case t_int16:
		{
			int16_t val = (int16_t)atoi(p);
            memcpy(m_val, &val, sizeof(int16_t));
			break;
		}
		case t_uint16:
		{
			uint16_t val = (uint16_t)atoi(p);
            memcpy(m_val, &val, sizeof(int16_t));
			break;
		}
		case t_int32:
		{
			int32_t val = (int32_t)strtol(p, NULL, 10);
            memcpy(m_val, &val, sizeof(int32_t));
			break;
		}
		case t_uint32:
		{
			uint32_t val = (uint32_t)strtoul(p, NULL, 10);
            memcpy(m_val, &val, sizeof(int32_t));
			break;
		}
		case t_int64:
		{
            int64_t val = (int64_t)strtol(p, NULL, 10);
			memcpy(m_val, &val, sizeof(int64_t));
			break;
		}
		case t_uint64:
		case t_addr:
		{
            uint64_t val = (uint64_t)strtoul(p, NULL, 10);
            memcpy(m_val, &val, sizeof(int64_t));
			break;
		}
		case t_float:
		{
            float val = strtof(p, NULL);
			memcpy(m_val, &val, sizeof(float));
			break;
		}
		case t_double:
		{
			double val = strtod(p, NULL);
			memcpy(m_val, &val, sizeof(double));
			break;
		}
		default: return;
        }
        m_type = type;
        delete p;
	}
}

std::string CKVVal::toString() const
{
    char szbuf[24];
    char* p =szbuf;
	*p = 0;

    switch(m_type)
    {
	case t_int8 :
	{
        sprintf(szbuf, "%d", getInt8());
        break;
	}
	case t_uint8 :
	{
		sprintf(szbuf, "%d", getUInt8());
        break;
	}
	case t_int16 :
	{
        sprintf(szbuf, "%d", getInt16());
        break;
	}
	case t_uint16 :
	{
		sprintf(szbuf, "%d", getUInt16());
        break;
	}
	case t_int32 :
	{
		sprintf(szbuf, "%d", getInt32());
        break;
	}
	case t_uint32 :
	{
		sprintf(szbuf, "%u", getUInt32());
        break;
	}
    case t_int64 :
	{
		sprintf(szbuf, "%lld", getInt64());
        break;
	}
	case t_uint64 :
	{
		sprintf(szbuf, "%llu", getUInt64());
        break;
	}
	case t_float :
	{
		sprintf(szbuf, "%f", getFloat());
        break;
	}
	case t_double :
	{
		sprintf(szbuf, "%f", getDouble());
        break;
	}
	case t_addr :
	{
		sprintf(szbuf, "%p", getPtr());
        break;
	}
	case t_string :
	{
		p = (char*)str();
		break;
	}
	default: break;
    }
    return string(p);
}

const char* CKVVal::str() const
{
    if(m_type == t_string)
	{
		char* p = NULL;
        memcpy(&p, m_val, sizeof(char*));
        return p;
	}
	else
	{
        return NULL;
	}
}

int8_t CKVVal::getInt8() const
{
	int8_t val = 0;
    if(m_type == t_int8)
	{
        memcpy(&val, m_val, sizeof(int8_t));
	}
	return val;
}

uint8_t CKVVal::getUInt8() const
{
	uint8_t val = 0;
    if(m_type == t_uint8)
	{
        memcpy(&val, m_val, sizeof(uint8_t));
	}
	return val;
}

int16_t CKVVal::getInt16() const
{
	int16_t val = 0;
    if(m_type == t_int16)
	{
        memcpy(&val, m_val, sizeof(int16_t));
	}
	return val;
}

uint16_t CKVVal::getUInt16() const
{
	uint16_t val = 0;
    if(m_type == t_uint16)
	{
        memcpy(&val, m_val, sizeof(uint16_t));
	}
	return val;
}

int32_t CKVVal::getInt32() const
{
	int32_t val = 0;
    if(m_type == t_int32)
	{
        memcpy(&val, m_val, sizeof(int32_t));
	}
	return val;
}

uint32_t CKVVal::getUInt32() const
{
	uint32_t val = 0;
    if(m_type == t_uint32)
	{
        memcpy(&val, m_val, sizeof(uint32_t));
	}
	return val;
}

int64_t CKVVal::getInt64() const
{
	int64_t val = 0;
    if(m_type == t_int64)
	{
        memcpy(&val, m_val, sizeof(int64_t));
	}
	return val;
}

uint64_t CKVVal::getUInt64() const
{
	uint64_t val = 0;
    if(m_type == t_uint64)
	{
        memcpy(&val, m_val, sizeof(uint64_t));
	}
	return val;
}

float CKVVal::getFloat() const
{
	float val = 0;
    if(m_type == t_float)
	{
        memcpy(&val, m_val, sizeof(float));
	}
	return val;
}

double CKVVal::getDouble() const
{
	double val = 0;
    if(m_type == t_double)
	{
        memcpy(&val, m_val, sizeof(double));
	}
	return val;
}

void* CKVVal::getPtr() const
{
	void* val = NULL;
	if(m_type == t_addr)
	{
        memcpy(&val, m_val, sizeof(void*));
	}
	return val;
}

CKVVal* CKVVal::clone() const
{
	CKVVal* pval = new CKVVal();
	if(!pval)
	{
		return NULL;
	}
	memcpy(pval->m_key, m_key, sizeof(m_key));
	pval->m_type = m_type;
    if(m_type == t_string)
	{
		char* p = NULL;
		char* q = NULL;
        memcpy(&p, m_val, sizeof(char*));
		q = new char[strlen(p)+1];
		strcpy(q, p);
		memcpy(pval->m_val, &q, sizeof(char*));
	}
	else if(m_type == t_group)
	{
        assert(0);
	}
	else
	{
        memcpy(pval->m_val, m_val, sizeof(m_val));
	}
	return pval;
}

void CKVVal::clear()
{
    if(m_type == t_string)
	{
		char* p = 0;
		memcpy(&p, m_val, sizeof(char*));
		delete [] p;
	}
	m_type = 0;
    memset(m_val, 0, sizeof(m_val));
}

int CKVVal::print(FILE* fp, int depth) const
{
	if(!fp) return -1;
    for(int i = 0; i < depth; i++)
	{
        fprintf(fp, "    ");
	}

	char szbuf[24];
    char* p =szbuf;
	*p = 0;

    switch(m_type)
    {
	case t_int8 :
	{
        sprintf(szbuf, "%d", getInt8());
        break;
	}
	case t_uint8 :
	{
		sprintf(szbuf, "%d", getUInt8());
        break;
	}
	case t_int16 :
	{
        sprintf(szbuf, "%d", getInt16());
        break;
	}
	case t_uint16 :
	{
		sprintf(szbuf, "%d", getUInt16());
        break;
	}
	case t_int32 :
	{
		sprintf(szbuf, "%d", getInt32());
        break;
	}
	case t_uint32 :
	{
		sprintf(szbuf, "%u", getUInt32());
        break;
	}
    case t_int64 :
	{
		sprintf(szbuf, "%lld", getInt64());
        break;
	}
	case t_uint64 :
	{
		sprintf(szbuf, "%llu", getUInt64());
        break;
	}
	case t_float :
	{
		sprintf(szbuf, "%f", getFloat());
        break;
	}
	case t_double :
	{
		sprintf(szbuf, "%f", getDouble());
        break;
	}
	case t_addr :
	{
		sprintf(szbuf, "%p", getPtr());
        break;
	}
	case t_string :
	{
		p = (char*)str();
		break;
	}
	default: break;
    }

	fprintf(fp, "%s = %s\n", m_key, p);
	return 0;
}

bool CKVVal::val_equal(const CKVVal& rh) const
{
	int bf = false;
    if(m_type == rh.m_type)
	{
        if(m_type == t_string)
		{
            bf = !strcmp(str(), rh.str());
		}
		else
		{
            bf = !memcmp(m_val, rh.m_val, sizeof(m_val));
		}
	}
	return bf;
}

bool CKVVal::val_less(const CKVVal& rh) const
{
	int bf = false;
	if(m_type == rh.m_type)
	{
		switch(m_type)
		{
		case t_int8: bf = getInt8() < rh.getInt8();
			break;
		case t_uint8: bf = getUInt8() < rh.getUInt8();
			break;
		case t_int16: bf = getInt16() < rh.getInt16();
			break;
		case t_uint16: bf = getUInt16() < rh.getUInt16();
			break;
		case t_int32: bf = getInt32() < rh.getInt32();
			break;
		case t_uint32: bf = getUInt32() < rh.getUInt32();
			break;
		case t_int64: bf = getInt64() < rh.getInt64();
			break;
		case t_uint64: bf = getUInt64() < rh.getUInt64();
			break;
		case t_string:  bf = strcmp(str(), rh.str()) < 0;
			break;
		case t_float: bf = getFloat() < rh.getFloat();
			break;
		case t_double: bf = getDouble() < rh.getDouble();
			break;
		case t_addr: bf = getPtr() < rh.getPtr();
			break;
		}
	}
	return bf;
}

void CKVVal::val_plus(const CKVVal& rh)
{
	if(m_type == rh.m_type)
	{
		switch(m_type)
		{
		case t_int8:
			{
				int8_t rc = getInt8() + rh.getInt8();
				memcpy(m_val, &rc, sizeof(int8_t));
				break;
			}
		case t_uint8:
			{
				uint8_t rc = getUInt8() + rh.getUInt8();
				memcpy(m_val, &rc, sizeof(int8_t));
				break;
			}
		case t_int16:
			{
				int16_t rc = getInt16() + rh.getInt16();
				memcpy(m_val, &rc, sizeof(int16_t));
				break;
			}
		case t_uint16:
			{
				uint16_t rc = getUInt16() + rh.getUInt16();
				memcpy(m_val, &rc, sizeof(int16_t));
				break;
			}
		case t_int32:
			{
				int32_t rc = getInt32() + rh.getInt32();
				memcpy(m_val, &rc, sizeof(int32_t));
				break;
			}
		case t_uint32:
			{
				uint32_t rc = getUInt32() + rh.getUInt32();
				memcpy(m_val, &rc, sizeof(int32_t));
				break;
			}
		case t_int64:
			{
				int64_t rc = getInt64() + rh.getInt64();
				memcpy(m_val, &rc, sizeof(int64_t));
				break;
			}
		case t_uint64:
			{
				uint64_t rc = getUInt64() + rh.getUInt64();
				memcpy(m_val, &rc, sizeof(int64_t));
				break;
			}
		case t_float:
			{
				float rc = getFloat() + rh.getFloat();
				memcpy(m_val, &rc, sizeof(float));
				break;
			}
		case t_double:
			{
				double rc = getDouble() + rh.getDouble();
				memcpy(m_val, &rc, sizeof(double));
				break;
			}
		}
	}
}

void CKVVal::val_assign(const CKVVal& rh)
{
	if(m_type == rh.m_type)
	{
		if(m_type == t_string)
		{
            value(rh.str());
		}
		else
		{
            memcpy(m_val, rh.m_val, sizeof(m_val));
		}
	}
}


CKVNode::CKVNode()
	:CKVVal()
{
	m_type = t_group;
    memcpy(m_val, &m_child, sizeof(void*));
}

CKVNode::CKVNode(const char* key)
	:CKVVal(key)
{
	m_type = t_group;
    memcpy(m_val, &m_child, sizeof(void*));
}

CKVNode::~CKVNode()
{
    CKVNode::clear();
}

void CKVNode::add(CKVVal* kv)
{
	if(kv) m_child.push_back(kv);
}

CKVVal* CKVNode::get(const char* key) const
{
    vector<CKVVal*>::const_iterator it;
    for(it = m_child.begin(); it != m_child.end(); it++)
	{
        if(!strcmp((*it)->key(), key))
		{
            return *it;
		}
	}
	return NULL;
}

void CKVNode::erase(const char* key)
{
	vector<CKVVal*>::iterator it;
    for(it = m_child.begin(); it != m_child.end(); it++)
	{
        if(!strcmp((*it)->key(), key))
		{
            delete *it;
            m_child.erase(it);
            break;
		}
	}
}

CKVVal* CKVNode::remove(const char* key)
{
	vector<CKVVal*>::iterator it;
	CKVVal* pval = NULL;
    for(it = m_child.begin(); it != m_child.end(); it++)
	{
        if(!strcmp((*it)->key(), key))
		{
            pval = *it;
            m_child.erase(it);
            break;
		}
	}
	return pval;
}

void CKVNode::add(std::vector<CKVVal*>& child)
{
	for(std::vector<CKVVal*>::iterator it = child.begin();
		it != child.end(); it++)
	{
		m_child.push_back(*it);
	}
}

CKVVal* CKVNode::clone() const
{
	if(m_type != t_group)
	{
        return CKVVal::clone();
	}

	CKVNode* pval = new CKVNode();
	if(!pval)
	{
		return NULL;
	}
    memcpy(pval->m_key, m_key, sizeof(m_key));
    for(vector<CKVVal*>::const_iterator it = m_child.begin();
    	it != m_child.end(); it++)
	{
		CKVVal* p = (*it)->clone();
		pval->m_child.push_back(p);
	}
	return pval;
}

void CKVNode::clear()
{
	for(vector<CKVVal*>::iterator it = m_child.begin();
    	it != m_child.end(); it++)
	{
        delete *it;
	}
	m_child.clear();
}

int CKVNode::print(FILE* fp, int depth) const
{
	if(!fp) return -1;
    for(int i = 0; i < depth; i++)
	{
        fprintf(fp, "    ");
	}
    fprintf(fp, "<%s>\n", m_key);

    for(vector<CKVVal*>::const_iterator it = m_child.begin();
    	it != m_child.end(); it++)
	{
		(*it)->print(fp, depth+1);
	}
	for(int i = 0; i < depth; i++)
	{
        fprintf(fp, "    ");
	}
	fprintf(fp, "</%s>\n", m_key);
	return 0;
}


CKVVal* make_ckv(const char* key, const char* val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, int8_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, uint8_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, int16_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, uint16_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, int32_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, uint32_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, int64_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, uint64_t val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, float val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, double val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

CKVVal* make_ckv(const char* key, void* val)
{
	CKVVal* pval = new CKVVal(key);
    if(!pval) return NULL;
	pval->value(val);
	return pval;
}

int sparse_compose_ckv(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVVal* val)
{
	unsigned int size = 0;
	unsigned int pos =  5;
	unsigned int klen = strlen(val->m_key)+1;
    unsigned int vlen = 0;
	real_size = 0;

    if(val->m_type == t_string)
	{
		vlen = KVSize(KVType(val->m_type), val->str());
	}
	else
	{
		vlen = KVSize(KVType(val->m_type), NULL);
	}

	size = 5 + klen + vlen;
    if(size > max_size)
	{
		return -2;
	}

	real_size = size;
	size = hton32(size);
	memcpy(pbuf, &size, sizeof(int32_t));

	pbuf[4] = (t_string<<4)|val->m_type;

	memcpy(pbuf+pos, val->m_key, klen);
	pos+=klen;

    if(val->m_type == t_string)
	{
        memcpy(pbuf+pos, val->str(), vlen);
	}
	else
	{
        switch(vlen)
        {
		case 1:
		{
            memcpy(pbuf+pos, val->m_val, vlen);
            break;
		}
        case 2:
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pbuf+pos, val->m_val, vlen);
			#else
			uint16_t iv;
            memcpy(&iv, val->m_val, vlen);
            iv = hton16(iv);
			memcpy(pbuf+pos, &iv, vlen);
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 4:
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pbuf+pos, val->m_val, vlen);
			#else
			uint32_t iv;
            memcpy(&iv, val->m_val, vlen);
            iv = hton32(iv);
			memcpy(pbuf+pos, &iv, vlen);
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 8:
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pbuf+pos, val->m_val, vlen);
			#else
			uint64_t iv;
            memcpy(&iv, val->m_val, vlen);
            iv = hton64(iv);
			memcpy(pbuf+pos, &iv, vlen);
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
        }
	}
	return 0;
}

int sparse_decompose_ckv(const char* psrc, unsigned int size, CKVVal* pval)
{
	if(!psrc || !pval || size<6)
	{
        return -1;
	}
	unsigned int kvsize = 0;
	unsigned int pos =  5;
	unsigned int klen = 0;
	unsigned int vlen = 0;
	char type = 0;

	memcpy(&kvsize, psrc, 4);
	kvsize = ntoh32(kvsize);
	if(kvsize != size)
	{
        return -1;
	}

	type = psrc[4]&0x0F;
	klen = strlen(psrc+pos)+1;
	pval->key(psrc+pos);
	pos+=klen;
    vlen = KVSize(KVType(type), psrc+pos);
	pval->m_type = type;

	if(5+klen+vlen != kvsize)
	{
        return -4;
	}

    if(type == t_string)
	{
        char* pv = new char[vlen];
        memcpy(pv, psrc+pos, vlen);
        memcpy(pval->m_val, &pv, sizeof(char*));
	}
	else
	{
        switch(vlen)
        {
		case 1 :
		{
            memcpy(pval->m_val, psrc+pos, vlen);
            break;
		}
		case 2 :
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pval->m_val, psrc+pos, vlen);
			#else
			uint16_t iv;
            memcpy(&iv, psrc+pos, vlen);
            iv = ntoh16(iv);
            memcpy(pval->m_val, &iv, sizeof(uint16_t));
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 4 :
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pval->m_val, psrc+pos, vlen);
			#else
			uint32_t iv;
            memcpy(&iv, psrc+pos, vlen);
            iv = ntoh32(iv);
            memcpy(pval->m_val, &iv, sizeof(uint32_t));
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 8 :
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pval->m_val, psrc+pos, vlen);
			#else
			uint64_t iv;
            memcpy(&iv, psrc+pos, vlen);
            iv = ntoh64(iv);
            memcpy(pval->m_val, &iv, sizeof(uint64_t));
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
        }
	}
	return 0;
}

int sparse_compose(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVNode* node)
{
	real_size = 0;
	if(!pbuf || !node)
	{
        return -1;
	}
    if(node->m_child.empty())
	{
		return 0;
	}

	/*** 4+1+key+value ***/
	int ret = 0;
	unsigned int size = 0;
	unsigned int pos = 5;
    unsigned int len = strlen(node->m_key)+1;

    if(5+len > max_size)
	{
		return -2;
	}

    pbuf[4] = (t_string<<4)|t_group;
    memcpy(pbuf+pos, node->m_key, len);
    pos += len;

    for(vector<CKVVal*>::const_iterator it = node->m_child.begin();
    	it != node->m_child.end(); it++)
	{
		CKVVal* p = *it;
		if(p->type() == t_group)
		{
			ret = sparse_compose(pbuf+pos, max_size - pos, len, static_cast<CKVNode*>(p));
			if(ret < 0)
			{
				return ret;
			}
            pos+=len;
		}
		else
		{
			ret = sparse_compose_ckv(pbuf+pos, max_size - pos, len, p);
			if(ret < 0)
			{
				return ret;
			}
			pos+=len;
		}
	}
	real_size = pos;
	size = hton32(real_size);
    memcpy(pbuf, &size, sizeof(int32_t));
    return 0;
}

int sparse_decompose(const char* psrc, unsigned int size, CKVNode* node)
{
    if(!psrc || !node || size < 6)
	{
        return -1;
	}
	int ret = 0;
    unsigned int gsize = 0;
    unsigned int pos = 5;
    unsigned int len = 0;
    char type = 0;
    memcpy(&gsize, psrc, 4);
    gsize = ntoh32(gsize);
    type = psrc[4];

    if(gsize != size || type != (char)((t_string<<4)|t_group) )
	{
		return -1;
	}

	const char* key = psrc+pos;
	len = strlen(key)+1;
	node->key(key);
	pos += len;

	while(1)
	{
		if(pos > gsize)
		{
			return -2;
		}

		if(pos == gsize)
		{
			break;
		}

		memcpy(&len, psrc+pos, 4);
    	len = ntoh32(len);
    	type = psrc[pos+4];

    	if(pos + len > gsize)
		{
            return -2;
		}

    	if((type&0x0F) == t_group)
		{
			CKVNode* pNode = new CKVNode();
			ret = sparse_decompose(psrc+pos, len, pNode);
    	    if(ret < 0)
			{
				delete pNode;
				return ret;
			}
			node->add(pNode);
		}
    	else
		{
			CKVVal* pVal = new CKVVal();
    	    ret = sparse_decompose_ckv(psrc+pos, len, pVal);
    	    if(ret < 0)
			{
				delete pVal;
				return ret;
			}
			node->add(pVal);
		}
		pos += len;
	}
	return 0;
}


int compact_compose_ckv(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVVal* val)
{
	/* 1+key+value */
	unsigned int size = 0;
	unsigned int pos =  1;
	unsigned int klen = strlen(val->m_key)+1;
    unsigned int vlen = 0;
	real_size = 0;

    if(val->m_type == t_string)
	{
		vlen = KVSize(KVType(val->m_type), val->str());
	}
	else
	{
		vlen = KVSize(KVType(val->m_type), NULL);
	}

	size = 1 + klen + vlen;
    if(size > max_size)
	{
		return -2;
	}

	real_size = size;

	pbuf[0] = (t_string<<4)|val->m_type;

	memcpy(pbuf+pos, val->m_key, klen);
	pos+=klen;

    if(val->m_type == t_string)
	{
        memcpy(pbuf+pos, val->str(), vlen);
	}
	else
	{
        switch(vlen)
        {
		case 1:
		{
            memcpy(pbuf+pos, val->m_val, vlen);
            break;
		}
        case 2:
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pbuf+pos, val->m_val, vlen);
			#else
			uint16_t iv;
            memcpy(&iv, val->m_val, vlen);
            iv = hton16(iv);
			memcpy(pbuf+pos, &iv, vlen);
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 4:
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pbuf+pos, val->m_val, vlen);
			#else
			uint32_t iv;
            memcpy(&iv, val->m_val, vlen);
            iv = hton32(iv);
			memcpy(pbuf+pos, &iv, vlen);
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 8:
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pbuf+pos, val->m_val, vlen);
			#else
			uint64_t iv;
            memcpy(&iv, val->m_val, vlen);
            iv = hton64(iv);
			memcpy(pbuf+pos, &iv, vlen);
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
        }
	}
	return 0;
}

int compact_decompose_ckv(const char* psrc, unsigned int max_size, unsigned int &size,  CKVVal* pval)
{
	/* 1+key+value */
	size = 0;
	if(!psrc || !pval)
	{
        return -1;
	}
	unsigned int pos =  1;
	unsigned int klen = 0;
	unsigned int vlen = 0;
	char type = psrc[0]&0x0F;

	klen = strlen(psrc+pos)+1;
	pval->key(psrc+pos);
	pos+=klen;
    vlen = KVSize(KVType(type), psrc+pos);
	pval->m_type = type;

	size = 1 + klen + vlen;
	if(size > max_size)
	{
		size = 0;
        return -4;
	}

    if(type == t_string)
	{
        char* pv = new char[vlen];
        memcpy(pv, psrc+pos, vlen);
        memcpy(pval->m_val, &pv, sizeof(char*));
	}
	else
	{
        switch(vlen)
        {
		case 1 :
		{
            memcpy(pval->m_val, psrc+pos, vlen);
            break;
		}
		case 2 :
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pval->m_val, psrc+pos, vlen);
			#else
			uint16_t iv;
            memcpy(&iv, psrc+pos, vlen);
            iv = ntoh16(iv);
            memcpy(pval->m_val, &iv, sizeof(uint16_t));
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 4 :
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pval->m_val, psrc+pos, vlen);
			#else
			uint32_t iv;
            memcpy(&iv, psrc+pos, vlen);
            iv = ntoh32(iv);
            memcpy(pval->m_val, &iv, sizeof(uint32_t));
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
		case 8 :
		{
			#ifdef __BYTE_BIG_ENDIAN__
			memcpy(pval->m_val, psrc+pos, vlen);
			#else
			uint64_t iv;
            memcpy(&iv, psrc+pos, vlen);
            iv = ntoh64(iv);
            memcpy(pval->m_val, &iv, sizeof(uint64_t));
			#endif // __BYTE_BIG_ENDIAN__
			break;
		}
        }
	}
	return 0;
}

int compact_compose(char* pbuf, unsigned int max_size, unsigned int &real_size, const CKVNode* node)
{
	real_size = 0;
	if(!pbuf || !node)
	{
        return -1;
	}
    if(node->m_child.empty())
	{
		return 0;
	}
    /* 1+4+key+value */
	int ret = 0;
	unsigned int size = 0;
	unsigned int pos = 5;
    unsigned int len = strlen(node->m_key)+1;

    if(5+len > max_size)
	{
		return -2;
	}

	pbuf[0] = (t_string<<4)|t_group;
    memcpy(pbuf+pos, node->m_key, len);
    pos += len;

	for(vector<CKVVal*>::const_iterator it = node->m_child.begin();
    	it != node->m_child.end(); it++)
	{
		CKVVal* p = *it;
		if(p->type() == t_group)
		{
			ret = compact_compose(pbuf+pos, max_size - pos, len, static_cast<CKVNode*>(p));
			if(ret < 0)
			{
				return ret;
			}
            pos+=len;
		}
		else
		{
			ret = compact_compose_ckv(pbuf+pos, max_size - pos, len, p);
			if(ret < 0)
			{
				return ret;
			}
			pos+=len;
		}
	}
	real_size = pos;
	size = hton32(real_size);
    memcpy(pbuf+1, &size, sizeof(int32_t));
	return 0;
}

int compact_decompose(const char* psrc, unsigned int size,  CKVNode* node)
{
	if(!psrc || !node || size < 6)
	{
        return -1;
	}
	int ret = 0;
    unsigned int gsize = 0;
    unsigned int pos = 5;
    unsigned int len = 0;
    char type = psrc[0];
    memcpy(&gsize, psrc+1, 4);
    gsize = ntoh32(gsize);

    if(gsize != size || type != (char)((t_string<<4)|t_group) )
	{
		return -1;
	}

	const char* key = psrc+pos;
	len = strlen(key)+1;
	node->key(key);
	pos += len;

	while(1)
	{
		if(pos > gsize)
		{
			return -2;
		}

		if(pos == gsize)
		{
			break;
		}

    	type = psrc[pos];
    	if((type&0x0F) == t_group)
		{
			memcpy(&len, psrc+pos+1, 4);
    		len = ntoh32(len);

			CKVNode* pNode = new CKVNode();
			ret = compact_decompose(psrc+pos, len, pNode);
    	    if(ret < 0)
			{
				delete pNode;
				return ret;
			}
			node->add(pNode);
		}
    	else
		{
			CKVVal* pVal = new CKVVal();
    	    ret = compact_decompose_ckv(psrc+pos, size-pos, len, pVal);
    	    if(ret < 0)
			{
				delete pVal;
				return ret;
			}
			node->add(pVal);
		}
		pos += len;
	}
	return 0;
}

int compose_body(int flag, char* body, unsigned int max_size, unsigned int &body_size, const CKVNode* root)
{
	int ret = 0;
	unsigned int gsize = 0;
	body_size = 0;
	if(flag == 0)
	{
        for(std::vector<CKVVal*>::iterator it = const_cast<CKVNode*>(root)->getChild().begin();
        	it != const_cast<CKVNode*>(root)->getChild().end();
        	it++)
		{
			ret = sparse_compose(body+body_size, max_size-body_size, gsize, static_cast<CKVNode*>(*it));
			if(ret < 0)
			{
				return ret;
			}
			body_size += gsize;
		}
		return 0;
	}

	if(flag == 1)
	{
        for(std::vector<CKVVal*>::iterator it = const_cast<CKVNode*>(root)->getChild().begin();
        	it != const_cast<CKVNode*>(root)->getChild().end();
        	it++)
		{
			ret = compact_compose(body+body_size, max_size-body_size, gsize, static_cast<CKVNode*>(*it));
			if(ret < 0)
			{
				return ret;
			}
			body_size += gsize;
		}
		return 0;
	}
	return -1;
}

int decompose_body(int flag, const char* body, unsigned int body_size, CKVNode* root)
{
	unsigned int pos = 0;
	uint32_t gsize = 0;
	int ret = 0;
	CKVNode* node = NULL;
    if(flag == 0)
	{
		while(pos < body_size)
		{
			memcpy(&gsize, body+pos, sizeof(uint32_t));
			gsize = ntoh32(gsize);

			node = new CKVNode();
			ret = sparse_decompose(body+pos, gsize, node);
			if(ret < 0)
			{
                delete node;
				return ret;
			}
			root->add(node);
			pos += gsize;
		}
		if(pos != body_size)
		{
            return -2;
		}
		return 0;
	}

	if(flag == 1)
	{
		while(pos < body_size)
		{
			memcpy(&gsize, body+pos+1, sizeof(uint32_t));
			gsize = ntoh32(gsize);

			node = new CKVNode();
			ret = compact_decompose(body+pos, gsize, node);
			if(ret < 0)
			{
                delete node;
				return ret;
			}
			root->add(node);
			pos += gsize;
		}
		if(pos != body_size)
		{
            return -2;
		}
		return 0;
	}
	return -1;
}

int compose_group(int flag, char* body, unsigned int max_size, unsigned int &body_size, const CKVNode* group)
{
	switch(flag)
	{
	case 0:
		return sparse_compose(body, max_size, body_size, group);
	case 1:
		return compact_compose(body, max_size, body_size, group);
	}
	return -1;
}

int decompose_group(int flag, const char* body, unsigned int body_size, CKVNode* group)
{
	switch(flag)
	{
	case 0:
		return sparse_decompose(body, body_size, group);
	case 1:
		return compact_decompose(body, body_size, group);
	}
	return -1;
}
