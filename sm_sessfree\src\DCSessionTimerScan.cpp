#include "DCBasePlugin.h"
#include "DCLogMacro.h"
#include "DCEptMsgDef.h"
#include "DCDBManer.h"
#include "DCSeriaOp.h"
#include "DCSMPara.h"
#include "UHead.h"
#include <vector>
#include <map>
#include <string>
#include <string.h>
#include <sys/time.h>
//#include "DCFLocalClient.h"
#include "DCFLocalClientNew.h"
#include "DCFClient.h"
#include "NCHFData.h"
#include "DCKpiSender.h"
#include "DCPerfStatistic.h"
#include "DCPluginManer.h"
#include "DCSMPara.h"
#include "DCGrayscaleRoute.h"
#include <time.h>

using namespace std;
using namespace dcf_new;
struct SSessionTimerData
{
    int OfflineFlag;
    int FreeFlag;
    int Trace;
    int  SessionFlag;
    int  ASRFlag;
    int  SendFlag;
    int servicecontextid;
	int RgNum;	
	int MsgMode;

	long latnId;
    long DataSerial;
    long RequestType;
    long RequestNum;
    long RouteRecord;
    long ValidTime;
	long NextCCRTime;
	long MainNextCCRTime;
	long ChildNextCCRTime;
	long RatingGroup;
    long ValidTimeMain;
	
	char uid[32];
    char HostId[64];
    char SessionId[128];
    char ChildSessionId[128];
    char SessionStart[16];
	char CurrCCRTime[16];
    char SubNbr[64];
	char productofferid[128];
	char strcontextid[256];
    char NotifyUrl[512];
	SSessionTimerData()
	{
		OfflineFlag = 0;
		FreeFlag =0;
		Trace =0;
		SessionFlag =0;
		ASRFlag =0;
		SendFlag =0;
		servicecontextid =0;
		RgNum =0;	
		MsgMode = 0;

		latnId =0;
		DataSerial =0;
		RequestType =0;
		RequestNum =0;
		RouteRecord =0;
		ValidTime =0;
		NextCCRTime =0;
		MainNextCCRTime =0;
		ChildNextCCRTime =0;
		RatingGroup =0;
		ValidTimeMain =0;
			
		memset(uid,0,sizeof(uid));
		memset(HostId,0,sizeof(HostId));
		memset(SubNbr,0,sizeof(SubNbr));
		memset(SessionId,0,sizeof(SessionId));
		memset(SessionStart,0,sizeof(SessionStart));
		memset(CurrCCRTime,0,sizeof(CurrCCRTime));
		memset(productofferid,0,sizeof(productofferid));
		memset(ChildSessionId,0,sizeof(ChildSessionId));
		memset(strcontextid,0,sizeof(strcontextid));
		memset(NotifyUrl,0,sizeof(NotifyUrl));	
	}
};

class DCSessionTimerScan : public DCBasePlugin
{
public:
    enum ClientType
    {
        CLIENT_TOPIC = 0,
        CLIENT_PROXY = 1,
    };
	DCSessionTimerScan(const char* category, const char* func, const char* version)
		:DCBasePlugin(category,func,version)
		,m_pclient(NULL)
		,m_mTopic(NULL)
		,m_ntfEn(ESeriaBinary)
		,m_eptEn(ESeriaBinString)
	{
		m_hseq = getpid()%1000000*100;
		m_tseq = time(NULL);
		m_sseq[0] = 0x0;
	}

	virtual ~DCSessionTimerScan()
	{
	}

	virtual const char* desc()
	{
        return "session timeout scan plugin";
	}
protected:
	virtual int init();

	virtual int process(void* input, void* output);

    int scan_voice_session();

    int scan_sms_session();

    int scan_ismp_session();

    int scan_data_session();

    int scan_pgw_session();
	int scan_5G_session();
    int scan_dsl_session();
	int  SplitString(const char* pszStr, const char cSeparator, std::list<std::string>& vecStr);
    int produce(ocs::UHead& uhd, ocs::SEPTMsg& msg);

	int SendSessionFree(int iServiceContextId, SSessionTimerData& stfield);
	int SendNotifyToCHFProxy(SSessionTimerData& stfield);

    static void SendToProxyCallBack(int rc, const DCFSMsg* msg);

    int UpSendFlag(int service_context_id, SSessionTimerData& stfield, bool bChild);

    int IsNeedAsr(int service_context_id, SSessionTimerData& stfield);

    int IsNoneChild(int service_context_id, SSessionTimerData& stfield);
	int QueryOperList(string& strServiceContextId, string &szOperListId, string& szOperType);

    int CheckLatnId(string strPhone, int iLatnId);
	int GetGrayServName(const string& RouteProcess, string& serviceName, std::map<std::string, std::string >& EptExt);

private:
	DCPluginManer m_pm;
    vector<SSessionTimerData> 	m_data;
    DCFClient*              m_pDcfClient;    // 直接发送消息
    DCFLocalClient*         m_pclient;       // 发送消息到图
    map<string, string>* m_mTopic;
	map<string, string>* m_mapParam;
	map<string, int>  	 m_mapAsr;
	DCSeriaEncoder					m_ntfEn;
	DCSeriaEncoder					m_eptEn;
	DCSeriaPrinter      m_pPrint;
	int			m_hseq;
	long			m_tseq;
	char 	       m_sseq[20];
	time_t lasttime;
    //灰度发布
	string sSubscriber;
	string sSmRouteProcess;
	int nGrayRefreshIntr;
};

int DCSessionTimerScan::init()
{
	UDBSQL* pquery = NULL;
	pquery = dbm()->GetSQL("q_voice_sessiontimer");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_voice_sessiontimer sql");
		return -1;
	}
	pquery = dbm()->GetSQL("q_sms_sessiontimer");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_sms_sessiontimer sql");
		return -1;
	}
	pquery = dbm()->GetSQL("q_ismp_sessiontimer");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_ismp_sessiontimer sql");
		return -1;
	}
	pquery = dbm()->GetSQL("q_dsl_sessiontimer");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_dsl_sessiontimer sql");
		return -1;
	}
	pquery = dbm()->GetSQL("q_data_sessiontimer");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_data_sessiontimer sql");
		return -1;
	}
	pquery = dbm()->GetSQL("u_data_timer_rar");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find u_data_timer_rar sql");
		return -1;
	}
	pquery = dbm()->GetSQL("q_pgw_sessiontimer");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_pgw_sessiontimer sql");
		return -1;
	}
	pquery = dbm()->GetSQL("q_5g_sessiontimer_child_VT");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_5g_sessiontimer_child_VT sql");
		return -1;
	}

	pquery = dbm()->GetSQL("q_5g_sessiontimer_child_2VT");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_5g_sessiontimer_child_2VT sql");
		return -1;
	}

	pquery = dbm()->GetSQL("q_5g_sessiontimer_child_ASR");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_5g_sessiontimer_child_ASR sql");
		return -1;
	}

	pquery = dbm()->GetSQL("u_pgw_timer_rar");
    if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find u_pgw_timer_rar sql");
		return -1;
	}

	pquery = dbm()->GetSQL("q_vir_oper_list");
	if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_vir_oper_list sql");
		return -1;
	}
	
	pquery = dbm()->GetSQL("u_5g_timer_rar_child");
	if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find u_5g_timer_rar_child sql");
		return -1;
	}
	
	pquery = dbm()->GetSQL("u_5g_timer_rar_allchild");
	if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find u_5g_timer_rar_allchild sql");
		return -1;
	}

	pquery = dbm()->GetSQL("q_5g_timer_childnum");
	if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_5g_timer_childnum sql");
		return -1;
	}

	pquery = dbm()->GetSQL("q_tb_prd_prd_inst_latnid");
	if(!pquery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find q_tb_prd_prd_inst_latnid sql");
		return -1;
	}

    m_pDcfClient = (DCFClient*)gethandle("SERVICEPROXY");
    if (!m_pDcfClient)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPOXY handle");
        return -1;
    }
	m_pclient = (DCFLocalClient*)gethandle("SERVICEPRD");
    if(!m_pclient)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPRD handle");
		return -1;
	}

	m_mTopic = (std::map<std::string, std::string>*)gethandle("SERVICETOPIC");
	if(!m_mTopic)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICETOPIC handle");
		return -1;
	}

	m_mapParam = (std::map<std::string, std::string>*)gethandle("SERVICEPARAM");
	if (!m_mapParam)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPARAM handle");
		return -1;
	}

	m_pm.get_statistic("SessfreeApp")->get_position("Deal5GSession");
	//灰度配置数据初始化
	std::map<std::string, std::string>::iterator itParam = m_mapParam->find("sSubscriber");
	if (itParam != m_mapParam->end())
	{
		sSubscriber = itParam->second;
	}
	itParam = m_mapParam->find("sSmRouteProcess");
	if (itParam != m_mapParam->end())
	{
		sSmRouteProcess = itParam->second;
	}
	itParam = m_mapParam->find("nGrayRefreshIntr");
	if (itParam != m_mapParam->end())
	{
		nGrayRefreshIntr = atoi(itParam->second.c_str());
	}
	
	int iret = DCGrayscaleRoute::instance()->init(dbm(),sSubscriber.c_str()); //刷新数据
	if (iret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCGrayscaleRoute init data failed,Subscriber=[%s]",sSubscriber.c_str());
		return -1;
	}

    return 0;
}

int DCSessionTimerScan::SplitString(const char* pszStr, const char cSeparator, std::list<std::string>& vecStr)
{
	if (!pszStr)
	{
		return 0;
	}

	std::string strField;
	strField.clear();
	for (const char* p = pszStr; *p; p++)
	{
		if ((*p) != cSeparator)
		{
			strField.push_back(*p);
			continue;
		}

		vecStr.push_back(strField);
		strField.clear();
	}

	vecStr.push_back(strField);

	return 0;
}

int DCSessionTimerScan::process(void* input, void* output)
{
	scan_voice_session();
	scan_sms_session();
	scan_ismp_session();
	scan_data_session();
	scan_pgw_session();
	scan_5G_session();
	scan_dsl_session();
	return 0;
}

int DCSessionTimerScan::scan_voice_session()
{
	UDBSQL* pQuery = dbm()->GetSQL("q_voice_sessiontimer");
	long et = time(NULL);
	int nAmountLimit = drf()->getval<DCParaCom>("smpara")->nAmountLimit;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "voice", "current time:%ld, nAmountLimit:%d", et,nAmountLimit);
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	SSessionTimerData stfield;
	std::string skey;
	std::string szOperListId;
	std::string szOperType;
	int ncount = 0;
	m_data.clear();
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, et);
		pQuery->Execute();
		while (pQuery->Next())
		{
			memset(&stfield, 0, sizeof(stfield));

			pQuery->GetValue(1, stfield.HostId);
			pQuery->GetValue(2, stfield.SessionId);
			pQuery->GetValue(3, stfield.DataSerial);
			pQuery->GetValue(4, stfield.RequestType);
			pQuery->GetValue(5, stfield.RouteRecord);
			pQuery->GetValue(6, stfield.FreeFlag);
			pQuery->GetValue(7, stfield.Trace);
			pQuery->GetValue(8, stfield.SubNbr);
			pQuery->GetValue(9, stfield.SessionStart);
			pQuery->GetValue(10, stfield.CurrCCRTime);
			pQuery->GetValue(11, stfield.NextCCRTime);
			pQuery->GetValue(12, stfield.strcontextid);
			pQuery->GetValue(13, stfield.latnId);
			
			stfield.ASRFlag = 4;	// 3:RAR, 4:ASR

			if((stfield.FreeFlag ==1) &&(drf()->getval<DCParaCom>("smpara")->nFreeAddCdr))
			{
				 stfield.FreeFlag = 2;
			}

			m_data.push_back(stfield);
		}
		pQuery->Close();
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "voice", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }
	for(unsigned int i = 0; i < m_data.size(); i++)
	{
		// produce by topology + service
		msg.type = 3;
		msg.ASRFlag = m_data[i].ASRFlag+m_data[i].FreeFlag*10;
		msg.sessionID = m_data[i].SessionId;
		msg.trace = m_data[i].Trace;
		msg.sreq = m_data[i].RequestType;
		msg.serial = m_data[i].DataSerial;
		msg.requestnumber = m_data[i].RouteRecord;
		msg.servicecontextid = VOICE;
		msg.topology = m_data[i].HostId;
		msg.stamp = time(NULL);
		msg.result = NE_SESSION_TIMEOUT;
		msg.szServiceContextIDStr = m_data[i].strcontextid;
		char szTemp[12] = {0};
		sprintf(szTemp,"%ld",m_data[i].latnId);
		msg.EptExt["LatnId"] = szTemp;
		QueryOperList(msg.szServiceContextIDStr, szOperListId, szOperType);
		msg.EptExt["opertype"] = szOperType;
		msg.EptExt["operListId"] = szOperListId;
		msg.EptExt["BillingNum"] = m_data[i].SubNbr;
		m_tseq++;
		if(m_tseq >= 10000000000L) m_tseq = 1;
		sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
		uhd.uid = m_sseq;
	
		skey = m_sseq;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, m_data[i].Trace);
		
		produce(uhd, msg);

		DCBIZLOG(DCLOG_LEVEL_ERROR, NE_SESSION_TIMEOUT, m_data[i].SessionId, \
				"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
				"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], "
				"SessionStart[%s], CurrCCRTime[%s], NextCCRTime[%ld], voice", \
				m_data[i].SubNbr, m_data[i].FreeFlag, m_data[i].ASRFlag, m_data[i].HostId, \
				m_data[i].RequestType, m_data[i].DataSerial, m_data[i].RouteRecord, \
				m_data[i].SessionStart, m_data[i].CurrCCRTime, m_data[i].NextCCRTime);
		
		ncount++;
		if( (nAmountLimit > 0) && (ncount%nAmountLimit==0))
		{
			sleep(1);
		}
	}
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	m_data.clear();
	return 0;
}

int DCSessionTimerScan::scan_sms_session()
{
	UDBSQL* pQuery = dbm()->GetSQL("q_sms_sessiontimer");
	long et = time(NULL);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "sms", "current time:%ld", et);
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	SSessionTimerData stfield;
	std::string skey;
	std::string szOperListId;
	std::string szOperType;
	m_data.clear();
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, et);
		pQuery->Execute();
		while (pQuery->Next())
		{
			memset(&stfield, 0, sizeof(stfield));

			pQuery->GetValue(1, stfield.HostId);
			pQuery->GetValue(2, stfield.SessionId);
			pQuery->GetValue(3, stfield.DataSerial);
			pQuery->GetValue(4, stfield.RequestType);
			pQuery->GetValue(5, stfield.RouteRecord);
			pQuery->GetValue(6, stfield.FreeFlag);
			pQuery->GetValue(7, stfield.Trace);
			pQuery->GetValue(8, stfield.SubNbr);
			pQuery->GetValue(9, stfield.SessionStart);
			pQuery->GetValue(10, stfield.CurrCCRTime);
			pQuery->GetValue(11, stfield.NextCCRTime);
			pQuery->GetValue(12, stfield.strcontextid);
			pQuery->GetValue(13, stfield.latnId);

			stfield.ASRFlag = 4;	// 3:RAR, 4:ASR

			if((stfield.FreeFlag ==1) &&(drf()->getval<DCParaCom>("smpara")->nFreeAddCdr))
			{
				 stfield.FreeFlag = 2;
			}

			m_data.push_back(stfield);
		}
		pQuery->Close();
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "sms", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }
	
	for(unsigned int i = 0; i < m_data.size(); i++)
	{
		// produce by topology + service
		msg.type = 3;
		msg.ASRFlag = m_data[i].ASRFlag+m_data[i].FreeFlag*10;
		msg.sessionID = m_data[i].SessionId;
		msg.trace = m_data[i].Trace;
		msg.sreq = m_data[i].RequestType;
		msg.serial = m_data[i].DataSerial;
		msg.requestnumber = m_data[i].RouteRecord;
		msg.servicecontextid = SMS;
		msg.topology = m_data[i].HostId;
		msg.stamp = time(NULL);
		msg.result = NE_SESSION_TIMEOUT;
		msg.szServiceContextIDStr = m_data[i].strcontextid;
		QueryOperList(msg.szServiceContextIDStr, szOperListId, szOperType);
		msg.EptExt["opertype"] = szOperType;
		msg.EptExt["operListId"] = szOperListId;
		char szTemp[12] = {0};
		sprintf(szTemp,"%ld",m_data[i].latnId);
		msg.EptExt["LatnId"] = szTemp;
		msg.EptExt["BillingNum"] = m_data[i].SubNbr;
		m_tseq++;
		if(m_tseq >= 10000000000L) m_tseq = 1;
		sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
		uhd.uid = m_sseq;
	
		skey = m_sseq;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, m_data[i].Trace);

		produce(uhd, msg);

		DCBIZLOG(DCLOG_LEVEL_ERROR, NE_SESSION_TIMEOUT, m_data[i].SessionId, \
				"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
				"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], "
				"SessionStart[%s], CurrCCRTime[%s], NextCCRTime[%ld],sms ", \
				m_data[i].SubNbr, m_data[i].FreeFlag, m_data[i].ASRFlag, m_data[i].HostId, \
				m_data[i].RequestType, m_data[i].DataSerial, m_data[i].RouteRecord, \
				m_data[i].SessionStart, m_data[i].CurrCCRTime, m_data[i].NextCCRTime);
	}
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	m_data.clear();
	return 0;
}

int DCSessionTimerScan::scan_ismp_session()
{
	UDBSQL* pQuery = dbm()->GetSQL("q_ismp_sessiontimer");
	long et = time(NULL);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "ismp", "current time:%ld", et);
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	SSessionTimerData stfield;
	std::string skey;
	std::string szOperListId;
	std::string szOperType;
	m_data.clear();
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, et);
		pQuery->Execute();
		while (pQuery->Next())
		{
			memset(&stfield, 0, sizeof(stfield));

			pQuery->GetValue(1, stfield.HostId);
			pQuery->GetValue(2, stfield.SessionId);
			pQuery->GetValue(3, stfield.DataSerial);
			pQuery->GetValue(4, stfield.RequestType);
			pQuery->GetValue(5, stfield.RouteRecord);
			pQuery->GetValue(6, stfield.FreeFlag);
			pQuery->GetValue(7, stfield.Trace);
			pQuery->GetValue(8, stfield.SubNbr);
			pQuery->GetValue(9, stfield.SessionStart);
			pQuery->GetValue(10, stfield.CurrCCRTime);
			pQuery->GetValue(11, stfield.NextCCRTime);
			pQuery->GetValue(12, stfield.strcontextid);
			pQuery->GetValue(13, stfield.latnId);

			stfield.ASRFlag = 4;	// 3:RAR, 4:ASR

			if((stfield.FreeFlag ==1) &&(drf()->getval<DCParaCom>("smpara")->nFreeAddCdr))
			{
				 stfield.FreeFlag = 2;
			}

			m_data.push_back(stfield);
		}
		pQuery->Close();
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "ismp", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }
	for(unsigned int i = 0; i < m_data.size(); i++)
	{
		// produce by topology + service
		msg.type = 3;
		msg.ASRFlag = m_data[i].ASRFlag+m_data[i].FreeFlag*10;
		msg.sessionID = m_data[i].SessionId;
		msg.trace = m_data[i].Trace;
		msg.sreq = m_data[i].RequestType;
		msg.serial = m_data[i].DataSerial;
		msg.requestnumber = m_data[i].RouteRecord;
		msg.servicecontextid = ISMP;
		msg.topology = m_data[i].HostId;
		msg.stamp = time(NULL);
		msg.result = NE_SESSION_TIMEOUT;
		msg.szServiceContextIDStr = m_data[i].strcontextid;
		QueryOperList(msg.szServiceContextIDStr, szOperListId, szOperType);
		msg.EptExt["opertype"] = szOperType;
		msg.EptExt["operListId"] = szOperListId;
		char szTemp[12] = {0};
		sprintf(szTemp,"%ld",m_data[i].latnId);
		msg.EptExt["LatnId"] = szTemp;
		msg.EptExt["BillingNum"] = m_data[i].SubNbr;

		m_tseq++;
		if(m_tseq >= 10000000000L) m_tseq = 1;
		sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
		uhd.uid = m_sseq;
	
		skey = m_sseq;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, m_data[i].Trace);

		produce(uhd, msg);

		DCBIZLOG(DCLOG_LEVEL_ERROR, NE_SESSION_TIMEOUT, m_data[i].SessionId, \
				"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
				"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], "
				"SessionStart[%s], CurrCCRTime[%s], NextCCRTime[%ld], ismp", \
				m_data[i].SubNbr, m_data[i].FreeFlag, m_data[i].ASRFlag, m_data[i].HostId, \
				m_data[i].RequestType, m_data[i].DataSerial, m_data[i].RouteRecord, \
				m_data[i].SessionStart, m_data[i].CurrCCRTime, m_data[i].NextCCRTime);
	}
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	m_data.clear();
	return 0;
}

int DCSessionTimerScan::scan_data_session()
{
	UDBSQL* pQuery = dbm()->GetSQL("q_data_sessiontimer");
	long et = time(NULL);
	int nAmountLimit = drf()->getval<DCParaCom>("smpara")->nAmountLimit;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "data", "current time:%ld, nAmountLimit:%d", et,nAmountLimit);
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	SSessionTimerData stfield;
	std::string skey;
	std::string szOperListId;
	std::string szOperType;
	char value[256] = {0};
	int ncount = 0;
	m_data.clear();
	m_mapAsr.clear();
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, et);
		pQuery->Execute();
		while (pQuery->Next())
		{
			memset(&stfield, 0, sizeof(stfield));

			pQuery->GetValue(1, stfield.HostId);
			pQuery->GetValue(2, stfield.SessionId);
			pQuery->GetValue(3, stfield.DataSerial);
			pQuery->GetValue(4, stfield.RequestType);
			pQuery->GetValue(5, stfield.RouteRecord);
			pQuery->GetValue(6, stfield.FreeFlag);
			pQuery->GetValue(7, stfield.Trace);
			pQuery->GetValue(8, stfield.SubNbr);
			pQuery->GetValue(9, stfield.SessionStart);
			pQuery->GetValue(10, stfield.CurrCCRTime);
			pQuery->GetValue(11, stfield.NextCCRTime);
			pQuery->GetValue(12, stfield.ValidTime);
			pQuery->GetValue(13, stfield.SessionFlag);
			pQuery->GetValue(14, stfield.SendFlag);
			pQuery->GetValue(15, stfield.RatingGroup);
			pQuery->GetValue(16, stfield.productofferid);
			pQuery->GetValue(17, stfield.strcontextid);
            pQuery->GetValue(18, stfield.servicecontextid);
			pQuery->GetValue(19, stfield.latnId);

			if((stfield.FreeFlag ==1) &&(drf()->getval<DCParaCom>("smpara")->nFreeAddCdr))
			{
				 stfield.FreeFlag = 2;
			}

                     if(stfield.SendFlag ==0 && stfield.SessionFlag == 1 && stfield.RequestType !=1 && stfield.ValidTime + stfield.NextCCRTime > et)
			{
				stfield.ASRFlag = 3; 	//RAR
			}
			else if(stfield.SessionFlag == 1 && stfield.ValidTime + stfield.NextCCRTime < et)
			{
				stfield.ASRFlag = 4;	// 4:ASR
			}
			else if(0==stfield.RatingGroup && stfield.SessionFlag == 0 && stfield.ValidTime + stfield.ValidTime + stfield.NextCCRTime < et)
			{
				stfield.ASRFlag = 5;	// 5:Only Main Session
			}
                     else if(stfield.SessionFlag == 0 && stfield.NextCCRTime + 86400 < et)
                     {
                            stfield.ASRFlag = 6;	// 6: maybe not free main session
                     }

			if(stfield.ASRFlag == 3 || stfield.ASRFlag == 4)
			{
				strcpy(stfield.ChildSessionId, stfield.SessionId);
                char* p = strrchr(stfield.SessionId, ';');
                if(p) *p = 0x0;
			}

			if(stfield.ASRFlag)
			{
				m_data.push_back(stfield);
				m_mapAsr[stfield.SessionId] = 0;
			}
		}
		pQuery->Close();
	}
	catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "data", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }
	for(unsigned int i = 0; i < m_data.size(); i++)
	{
		m_tseq++;
		if(m_tseq >= 10000000000L) m_tseq = 1;
		sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
		uhd.uid = m_sseq;
		msg.sessionID = m_data[i].SessionId;
           
		skey = m_sseq;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, m_data[i].Trace);
		value[0] = '\0';
		// update SendFlag = 2
		if(m_data[i].ASRFlag == 3 || m_data[i].ASRFlag == 4)
		{
			//所以把rg和pid合在topology传过去
			sprintf(value,";%ld;%s",m_data[i].RatingGroup,m_data[i].productofferid);
			UpSendFlag(DATA, m_data[i], true);
		}
        
		if(m_data[i].ASRFlag == 6)
		{
			// judge isn't only main session
			if(IsNoneChild(DATA, m_data[i]) == 0)
			{
				continue;
			}
			m_data[i].ASRFlag = 5;
		}
		
		if(m_data[i].ASRFlag == 4 && IsNeedAsr(DATA, m_data[i]) == 0)
		{
                    continue;
		}
		if(m_mapAsr[m_data[i].SessionId])
			continue;
		// produce by topology + service
		msg.type = 3;
		//msg.ASRFlag = (m_data[i].ASRFlag == 5?4:m_data[i].ASRFlag) + m_data[i].FreeFlag*10;
		msg.ASRFlag = m_data[i].ASRFlag + m_data[i].FreeFlag*10;
		msg.sessionID = m_data[i].SessionId;
		msg.trace = m_data[i].Trace;
		msg.sreq = m_data[i].RequestType;
		msg.serial = m_data[i].DataSerial;
		msg.requestnumber = m_data[i].RouteRecord;
		msg.servicecontextid = m_data[i].servicecontextid;
		msg.topology = m_data[i].HostId;
		msg.topology.append(value);
		
		msg.stamp = time(NULL);
		msg.result = NE_SESSION_TIMEOUT;
		msg.szServiceContextIDStr = m_data[i].strcontextid;
		QueryOperList(msg.szServiceContextIDStr, szOperListId, szOperType);
		msg.EptExt["opertype"] = szOperType;
		msg.EptExt["operListId"] = szOperListId;
		char szTemp[12] = {0};
		sprintf(szTemp,"%ld",m_data[i].latnId);
		msg.EptExt["LatnId"] = szTemp;
		msg.EptExt["BillingNum"] = m_data[i].SubNbr;
		produce(uhd, msg);
		m_mapAsr[m_data[i].SessionId] = 1;
		DCBIZLOG(DCLOG_LEVEL_ERROR, NE_SESSION_TIMEOUT, m_data[i].SessionId, \
				"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
				"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], "
				"SessionStart[%s], CurrCCRTime[%s], NextCCRTime[%ld], data", \
				m_data[i].SubNbr, m_data[i].FreeFlag, m_data[i].ASRFlag, m_data[i].HostId, \
				m_data[i].RequestType, m_data[i].DataSerial, m_data[i].RouteRecord, \
				m_data[i].SessionStart, m_data[i].CurrCCRTime, m_data[i].NextCCRTime);
		
		ncount++;
		if( (nAmountLimit > 0) && (ncount%nAmountLimit==0))
		{
			sleep(1);
		}
	}
	m_data.clear();
	m_mapAsr.clear();
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	return 0;
}

int DCSessionTimerScan::scan_pgw_session()
{
	UDBSQL* pQuery = dbm()->GetSQL("q_pgw_sessiontimer");
	long et = time(NULL);
	int nAmountLimit = drf()->getval<DCParaCom>("smpara")->nAmountLimit;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "pgw", "current time:%ld, nAmountLimit:%d", et, nAmountLimit);
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	SSessionTimerData stfield;
	std::string skey;
	std::string szOperListId;
	std::string szOperType;
	char value[256] = {0};
	int ncount = 0;
	m_data.clear();
	m_mapAsr.clear();
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, et);
		pQuery->Execute();
		while (pQuery->Next())
		{
			memset(&stfield, 0, sizeof(stfield));

			pQuery->GetValue(1, stfield.HostId);
			pQuery->GetValue(2, stfield.SessionId);
			pQuery->GetValue(3, stfield.DataSerial);
			pQuery->GetValue(4, stfield.RequestType);
			pQuery->GetValue(5, stfield.RouteRecord);
			pQuery->GetValue(6, stfield.FreeFlag);
			pQuery->GetValue(7, stfield.Trace);
			pQuery->GetValue(8, stfield.SubNbr);
			pQuery->GetValue(9, stfield.SessionStart);
			pQuery->GetValue(10, stfield.CurrCCRTime);
			pQuery->GetValue(11, stfield.NextCCRTime);
			pQuery->GetValue(12, stfield.ValidTime);
			pQuery->GetValue(13, stfield.SessionFlag);
			pQuery->GetValue(14, stfield.SendFlag);
			pQuery->GetValue(15, stfield.RatingGroup);
			pQuery->GetValue(16, stfield.productofferid);
			pQuery->GetValue(17, stfield.strcontextid);
			pQuery->GetValue(18, stfield.latnId);

			if((stfield.FreeFlag ==1) &&(drf()->getval<DCParaCom>("smpara")->nFreeAddCdr))
			{
				 stfield.FreeFlag = 2;
			}

            if(stfield.SendFlag ==0 && stfield.SessionFlag == 1 && stfield.RequestType !=1 && stfield.ValidTime + stfield.NextCCRTime > et)
			{
				stfield.ASRFlag = 3; 	//RAR
			}
			else if(stfield.SessionFlag == 1 && stfield.ValidTime + stfield.NextCCRTime < et)
			{
				stfield.ASRFlag = 4;	// 4:ASR
			}
			else if(0==stfield.RatingGroup && stfield.SessionFlag == 0 && stfield.ValidTime + stfield.ValidTime + stfield.NextCCRTime < et)
			{
				stfield.ASRFlag = 5;	// 5:ASR Only Main Session
			}
                     else if(stfield.SessionFlag == 0 && stfield.NextCCRTime + 86400 < et)
                     {
                            stfield.ASRFlag = 6;	// 6: maybe not free main session
                     }

			if(stfield.ASRFlag == 3 || stfield.ASRFlag == 4)
			{
				strcpy(stfield.ChildSessionId, stfield.SessionId);
                char* p = strrchr(stfield.SessionId, ';');
                if(p) *p = 0x0;
			}

			if(stfield.ASRFlag)
			{
				m_data.push_back(stfield);
				m_mapAsr[stfield.SessionId] = 0;
			}
		}
		pQuery->Close();
	}
	catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "pgw", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }
	for(unsigned int i = 0; i < m_data.size(); i++)
	{
		m_tseq++;
		if(m_tseq >= 10000000000L) m_tseq = 1;
		sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
		uhd.uid = m_sseq;
	       msg.sessionID = m_data[i].SessionId;
           
		skey = m_sseq;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, m_data[i].Trace);
		value[0] = '\0';
		// update SendFlag = 2
		if(m_data[i].ASRFlag == 3 || m_data[i].ASRFlag == 4)
		{
			//所以把rg和pid合在topology传过去
			sprintf(value,";%ld;%s",m_data[i].RatingGroup,m_data[i].productofferid);
			UpSendFlag(PGW, m_data[i], true);
		}

		if(m_data[i].ASRFlag == 6)
		{
			// judge isn't only main session
			if(IsNoneChild(PGW, m_data[i]) == 0)
			{
                            continue;
                    }
                    m_data[i].ASRFlag = 5;
              }
		
		if(m_data[i].ASRFlag == 4 && IsNeedAsr(PGW, m_data[i]) == 0)
		{
                    continue;
		}
		if(m_mapAsr[m_data[i].SessionId])
			continue;
		// produce by topology + service
		msg.type = 3;
		//msg.ASRFlag = (m_data[i].ASRFlag == 5?4:m_data[i].ASRFlag) + m_data[i].FreeFlag*10;
		msg.ASRFlag = m_data[i].ASRFlag + m_data[i].FreeFlag*10;
		msg.sessionID = m_data[i].SessionId;
		msg.trace = m_data[i].Trace;
		msg.sreq = m_data[i].RequestType;
		msg.serial = m_data[i].DataSerial;
		msg.requestnumber = m_data[i].RouteRecord;
		msg.servicecontextid = PGW;
		msg.topology = m_data[i].HostId;
		msg.topology.append(value);
		
		msg.stamp = time(NULL);
		msg.result = NE_SESSION_TIMEOUT;
		msg.szServiceContextIDStr = m_data[i].strcontextid;
		QueryOperList(msg.szServiceContextIDStr, szOperListId, szOperType);
		msg.EptExt["opertype"] = szOperType;
		msg.EptExt["operListId"] = szOperListId;
		char szTemp[12] = {0};
		sprintf(szTemp,"%ld",m_data[i].latnId);
		msg.EptExt["LatnId"] = szTemp;
		msg.EptExt["BillingNum"] = m_data[i].SubNbr;
		produce(uhd, msg);
		if(m_data[i].ASRFlag == 4)
			m_mapAsr[m_data[i].SessionId] = 1;
		DCBIZLOG(DCLOG_LEVEL_ERROR, NE_SESSION_TIMEOUT, m_data[i].SessionId, \
				"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
				"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], "
				"SessionStart[%s], CurrCCRTime[%s], NextCCRTime[%ld], pgw", \
				m_data[i].SubNbr, m_data[i].FreeFlag, m_data[i].ASRFlag, m_data[i].HostId, \
				m_data[i].RequestType, m_data[i].DataSerial, m_data[i].RouteRecord, \
				m_data[i].SessionStart, m_data[i].CurrCCRTime, m_data[i].NextCCRTime);
				
		ncount++;
		if ((nAmountLimit > 0) && (ncount%nAmountLimit==0))
		{
			sleep(1);
		}
	}
	m_data.clear();
	m_mapAsr.clear();
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	return 0;
}

int DCSessionTimerScan::scan_5G_session()
{
	long lnCurrTimeStamp = time(NULL);
	int iCount = 0;
	if (drf()->getval<DCParaCom>("smpara")->iFastReleaseFlag == 1)
	{
		lnCurrTimeStamp = 9999999999;
	}
	
	int nAmountLimit = drf()->getval<DCParaCom>("smpara")->nAmountLimit;
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "5G", "current time:%ld, nAmountLimit:%d", lnCurrTimeStamp, nAmountLimit);
	DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "all child release start");
	int iRet = 0;
	UDBSQL* pQuery = NULL;
	int iSendFlag = 0;
	while (true)
	{
		m_data.clear();
		m_mapAsr.clear();
		bool bEmptySession = true;
		if (iSendFlag == 0)
		{
			pQuery = dbm()->GetSQL("q_5g_sessiontimer_child_VT");
			if (!pQuery)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[q_5g_sessiontimer_child_VT]");
				m_data.clear();
				m_mapAsr.clear();
				return -1;
			}
		}
		else if (iSendFlag == 1)
		{
			pQuery = dbm()->GetSQL("q_5g_sessiontimer_child_2VT");
			if (!pQuery)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[q_5g_sessiontimer_child_2VT]");
				m_data.clear();
				m_mapAsr.clear();
				return -1;
			}
		}
		else if (iSendFlag == 2)
		{
			pQuery = dbm()->GetSQL("q_5g_sessiontimer_child_ASR");
			if (!pQuery)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[q_5g_sessiontimer_child_ASR]");
				m_data.clear();
				m_mapAsr.clear();
				return -1;
			}
		}
		else
		{
			break;
		}

		try
		{
			pQuery->DivTable(NULL);
			pQuery->UnBindParam();
			if (iSendFlag == 0 || iSendFlag == 1)
			{
				pQuery->BindParam(1, lnCurrTimeStamp);
				pQuery->BindParam(2, iSendFlag);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Query Session Current Time[%ld], SendFlag[%d]", lnCurrTimeStamp, iSendFlag);
			}
			else if (iSendFlag == 2)
			{
				pQuery->BindParam(1, iSendFlag);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Query Session SendFlag[%d]", iSendFlag);
			}
			else
			{
				break;
			}

			pQuery->Execute();
			while (pQuery->Next())
			{
				bEmptySession = false;
				SSessionTimerData stfield;
				pQuery->GetValue(1, stfield.SessionId);
				pQuery->GetValue(2, stfield.ChildSessionId);
				pQuery->GetValue(3, stfield.CurrCCRTime);
				pQuery->GetValue(4, stfield.ChildNextCCRTime);
				pQuery->GetValue(5, stfield.ValidTime);
				pQuery->GetValue(6, stfield.SendFlag);
				pQuery->GetValue(7, stfield.RatingGroup);
				pQuery->GetValue(8, stfield.HostId);
				pQuery->GetValue(9, stfield.latnId);
				pQuery->GetValue(10, stfield.SubNbr);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "Current Time[%ld] > Child Next CCR Time[%ld], VT[%ld].", 
																	lnCurrTimeStamp, stfield.ChildNextCCRTime, stfield.ValidTime);
																	
				if (stfield.SendFlag == 0 && lnCurrTimeStamp < stfield.ChildNextCCRTime + stfield.ValidTime) // 大于一倍VT 且 小于两倍VT
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "Current Time[%ld] < Child Next CCR Time[%ld] + VT[%ld].", 
																		lnCurrTimeStamp, stfield.ChildNextCCRTime, stfield.ValidTime);
					stfield.ASRFlag = 3;	// RAR
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "Current Time[%ld] >= Child Next CCR Time[%ld] + VT[%ld].", 
																		lnCurrTimeStamp, stfield.ChildNextCCRTime, stfield.ValidTime);
					stfield.ASRFlag = 4;	// ASR
				}
	
				m_data.push_back(stfield);
			}
	
			pQuery->Close();
		}
		catch (UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SendFlag[%d], Current Time[%ld], exception[%s]", iSendFlag, lnCurrTimeStamp, e.ToString());
			m_data.clear();
			m_mapAsr.clear();
			pQuery->Close();
			return -1;
		}

		map<string, int>::iterator itMainSessionId;
		for (int iIndex = 0; iIndex < m_data.size(); iIndex++)
		{
			if (drf()->getval<DCParaCom>("smpara")->iFastReleaseFlag == 1)
			{
				m_data[iIndex].SendFlag = 2;
			}
			else
			{
				if (m_data[iIndex].SendFlag == 0 || m_data[iIndex].SendFlag == 1)
				{
					iRet = UpSendFlag(DATA_5G, m_data[iIndex], true);
					if (iRet <= 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "update SendFlag failed, SessionId[%s], ChildSessionId[%s]", m_data[iIndex].SessionId, m_data[iIndex].ChildSessionId);
						continue;
					}
				}
				
				if (m_data[iIndex].SendFlag == 1 || (m_data[iIndex].SendFlag == 0 && m_data[iIndex].ASRFlag == 4))
				{
					continue;
				}
			}

			if (m_data[iIndex].SendFlag == 2)
			{
				itMainSessionId = m_mapAsr.find(m_data[iIndex].SessionId);
				if (itMainSessionId != m_mapAsr.end())
				{
					continue;
				}

				m_mapAsr[m_data[iIndex].SessionId] = 1;
				if (drf()->getval<DCParaCom>("smpara")->iFastReleaseFlag == 1)
				{
					iRet = UpSendFlag(DATA_5G, m_data[iIndex], false);
					if (iRet <= 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "update SendFlag failed, SessionId[%s], ChildSessionId[%s]", m_data[iIndex].SessionId, m_data[iIndex].ChildSessionId);
						continue;
					}

					SendSessionFree(DATA_5G, m_data[iIndex]);
					
					iCount++;
					if ((nAmountLimit > 0) && (iCount % nAmountLimit == 0))
					{
						iCount = 0;
						sleep(1);
					}
				}
				else
				{
					if (IsNeedAsr(DATA_5G, m_data[iIndex]) == 1)
					{
						iRet = UpSendFlag(DATA_5G, m_data[iIndex], false);
						if (iRet <= 0)
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "update SendFlag failed, SessionId[%s], ChildSessionId[%s]", m_data[iIndex].SessionId, m_data[iIndex].ChildSessionId);
							continue;
						}
					
						if (drf()->getval<DCParaCom>("smpara")->iNotifyFlag == 0)
						{
							SendNotifyToCHFProxy(m_data[iIndex]);
						}
					
						SendSessionFree(DATA_5G, m_data[iIndex]);
						
						iCount++;
						if ((nAmountLimit > 0) && (iCount % nAmountLimit == 0))
						{
							sleep(1);
						}
					}
					else
					{
						m_data[iIndex].SendFlag = 0;
						m_data[iIndex].ASRFlag = 3;
						iRet = UpSendFlag(DATA_5G, m_data[iIndex], false);
						if (iRet <= 0)
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "update SendFlag failed, SessionId[%s], ChildSessionId[%s]", m_data[iIndex].SessionId, m_data[iIndex].ChildSessionId);
							continue;
						}
					}
				}
			}
			else
			{
				if (drf()->getval<DCParaCom>("smpara")->iNotifyFlag == 0 || drf()->getval<DCParaCom>("smpara")->iNotifyFlag == 1)
				{
					SendNotifyToCHFProxy(m_data[iIndex]);
				}
			}
		}

		if (bEmptySession)
		{
			iSendFlag++;
		}
	}

	m_data.clear();
	m_mapAsr.clear();

	DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "all child release end");

	if (iCount > 0)
	{
		sleep(iCount / 10000);
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "wait session delete");
	}

	if (DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0)
	{
		std::string strStat = "";
		bool bSample = true;
		m_pm.get_statistic("APP")->to_json_string(strStat, false, bSample);
		dbm()->get_statistic()->to_json_string(strStat, false, bSample);
		DCPERFLOG(0, "%s", strStat.c_str());
	}

	m_data.clear();
	m_mapAsr.clear();
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	return 0;
}

int DCSessionTimerScan::scan_dsl_session()
{
	UDBSQL* pQuery = dbm()->GetSQL("q_dsl_sessiontimer");
	long et = time(NULL);
	int nAmountLimit = drf()->getval<DCParaCom>("smpara")->nAmountLimit;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "dsl", "current time:%ld, nAmountLimit:%d", et,nAmountLimit);
	ocs::UHead uhd;
	ocs::SEPTMsg msg;
	SSessionTimerData stfield;
	std::string skey;
	int ncount = 0;
	m_data.clear();
    try
    {
    	pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, et);
		pQuery->Execute();
		while (pQuery->Next())
		{
			memset(&stfield, 0, sizeof(stfield));

			pQuery->GetValue(1, stfield.HostId);
			pQuery->GetValue(2, stfield.SessionId);
			pQuery->GetValue(3, stfield.DataSerial);
			pQuery->GetValue(4, stfield.RequestType);
			pQuery->GetValue(5, stfield.RouteRecord);
			pQuery->GetValue(6, stfield.FreeFlag);
			pQuery->GetValue(7, stfield.Trace);
			pQuery->GetValue(8, stfield.SubNbr);
			pQuery->GetValue(9, stfield.SessionStart);
			pQuery->GetValue(10, stfield.CurrCCRTime);
			pQuery->GetValue(11, stfield.NextCCRTime);
			pQuery->GetValue(12, stfield.strcontextid);
			pQuery->GetValue(13, stfield.latnId);
			if(stfield.NextCCRTime > et)
				continue;
			stfield.ASRFlag = 4;	// 3:RAR, 4:ASR

			if((stfield.FreeFlag ==1) &&(drf()->getval<DCParaCom>("smpara")->nFreeAddCdr))
			{
				 stfield.FreeFlag = 2;
			}

			m_data.push_back(stfield);
		}
		pQuery->Close();
    }catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "dsl", "dbexp:%s", e.ToString());
		m_data.clear();
		return -1;
    }
	for(unsigned int i = 0; i < m_data.size(); i++)
	{
		// produce by topology + service
		msg.type = 3;
		msg.ASRFlag = m_data[i].ASRFlag+m_data[i].FreeFlag*10;
		msg.sessionID = m_data[i].SessionId;
		msg.trace = m_data[i].Trace;
		msg.sreq = m_data[i].RequestType;
		msg.serial = m_data[i].DataSerial;
		msg.requestnumber = m_data[i].RouteRecord;
		msg.servicecontextid = DSL;
		msg.topology = m_data[i].HostId;
		msg.stamp = time(NULL);
		msg.result = NE_SESSION_TIMEOUT;
		msg.szServiceContextIDStr = m_data[i].strcontextid;
		char szTemp[12] = {0};
		sprintf(szTemp,"%ld",m_data[i].latnId);
		msg.EptExt.insert(pair<string,string>("LatnId",szTemp));
		msg.EptExt["BillingNum"] = m_data[i].SubNbr;
		m_tseq++;
		if(m_tseq >= 10000000000L) m_tseq = 1;
		sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
		uhd.uid = m_sseq;
	
		skey = m_sseq;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, m_data[i].Trace);

		produce(uhd, msg);

		DCBIZLOG(DCLOG_LEVEL_ERROR, NE_SESSION_TIMEOUT, m_data[i].SessionId, \
				"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
				"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], "
				"SessionStart[%s], CurrCCRTime[%s], NextCCRTime[%ld],dsl ", \
				m_data[i].SubNbr, m_data[i].FreeFlag, m_data[i].ASRFlag, m_data[i].HostId, \
				m_data[i].RequestType, m_data[i].DataSerial, m_data[i].RouteRecord, \
				m_data[i].SessionStart, m_data[i].CurrCCRTime, m_data[i].NextCCRTime);
				
		ncount++;
		if( (nAmountLimit > 0) && (ncount%nAmountLimit==0))
		{
			sleep(1);
		}
	}
	m_data.clear();
	DCLOG_SETCTL(DCLOG_MASK_TRACE, 0);
	return 0;
}


int DCSessionTimerScan::UpSendFlag(int service_context_id, SSessionTimerData& stfield, bool bChild)
{
	UDBSQL* pDML = NULL;
	int iRowCount = 0;
	int iSendFlag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "UpSendFlag: service_context_id[%d], ASRFlag[%d], SendFlag[%d]", service_context_id, stfield.ASRFlag, stfield.SendFlag);
	try
	{
		if (service_context_id == PGW )
		{
			pDML = dbm()->GetSQL("u_pgw_timer_rar");
		}
		else if (service_context_id == DATA || service_context_id == CCG)
		{
			pDML = dbm()->GetSQL("u_data_timer_rar");
		}
		else if (service_context_id == DATA_5G)
		{
			if (bChild)
			{
				pDML = dbm()->GetSQL("u_5g_timer_rar_child");
			}
			else
			{
				pDML = dbm()->GetSQL("u_5g_timer_rar_allchild");
			}
		}
		else
		{
			return 0;
		}

		if (stfield.SendFlag == 0 || stfield.SendFlag == 1)
		{
			if (stfield.ASRFlag == 3)
			{
				iSendFlag= 1;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "UpSendFlag: service_context_id[%d], ASRFlag[%d], set SendFlag[%d]", service_context_id, stfield.ASRFlag, iSendFlag);
			}
			else if (stfield.ASRFlag == 4)
			{
				iSendFlag = 2;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "UpSendFlag: service_context_id[%d], ASRFlag[%d], set SendFlag[%d]", service_context_id, stfield.ASRFlag, iSendFlag);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "find invalid data, service_context_id[%d], ASRFlag[%d], SendFlag[%d]", service_context_id, stfield.ASRFlag, stfield.SendFlag);
				return 0;
			}
		}
		else if (stfield.SendFlag == 2 && service_context_id == DATA_5G)
		{
			iSendFlag = 3;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ASRFlag[%d], set send_flag[%d]", stfield.ASRFlag, iSendFlag);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "find invalid data, service_context_id[%d], ASRFlag[%d], SendFlag[%d]", service_context_id, stfield.ASRFlag, stfield.SendFlag);
			return 0;
		}

		pDML->DivTable(stfield.SessionId);
		pDML->UnBindParam();
		pDML->BindParam(1, iSendFlag);
		if (service_context_id == DATA_5G)
		{
			if (bChild)
			{
				pDML->BindParam(2, stfield.SessionId);
				pDML->BindParam(3, stfield.ChildSessionId);
			}
			else
			{
				pDML->BindParam(2, stfield.SessionId);
			}
		}
		else
		{
			pDML->BindParam(2, stfield.ChildSessionId);
		}
		pDML->Execute();
		iRowCount = pDML->GetRowCount();
		pDML->Connection()->Commit();
		pDML->Close();
	}
	catch(UDBException& e)
	{
		pDML->Connection()->Rollback();
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "dbexp:%s", e.ToString());
		return -1;
	}
	return iRowCount;
}

int DCSessionTimerScan::IsNeedAsr(int service_context_id, SSessionTimerData& stfield)
{
	int ret = 0;
	char value[256] = {0};
	int Send_Flag = 0;
	int nChildNum = 0;
	int nChildTimeoutNum = 0;
	UDBSQL* qChildNum = NULL;

	try
    {
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "real session[%s]", stfield.SessionId);
		sprintf(value, "%s", stfield.SessionId);
		
		if(service_context_id == PGW )
		{
			qChildNum = dbm()->GetSQL("q_pgw_timer_childnum");
		}
		else if(service_context_id == DATA || service_context_id == CCG)
		{
			qChildNum = dbm()->GetSQL("q_data_timer_childnum");
		}
		else if(service_context_id == DATA_5G)
		{
			qChildNum = dbm()->GetSQL("q_5g_timer_childnum");
		}
		else
		{
			return 0;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "IsNeedAsr sql bind[%s]", value);
		qChildNum->DivTable(stfield.SessionId);
		qChildNum->UnBindParam();
		qChildNum->BindParam(1, value);
		qChildNum->Execute();

        while(qChildNum->Next())
		{
			qChildNum->GetValue(1, Send_Flag);
			nChildNum++;
			if(Send_Flag == 2) nChildTimeoutNum++;
		}
		qChildNum->Close();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "child num,timeout num[%d,%d]",nChildNum,nChildTimeoutNum);
        if(nChildNum != nChildTimeoutNum)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "childnum != timeoutnum, don't send asr.");
			return 0;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "need asr");
			return 1;
		}
    }
    catch(UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "dbexp:%s", e.ToString());
		return 0;
    }
    return 0;
}

int DCSessionTimerScan::IsNoneChild(int service_context_id, SSessionTimerData& stfield)
{
	int ret = 0;
	char value[256] = {0};
	int nChildNum = 0;
	UDBSQL* qChildNum = NULL;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "real session[%s]", stfield.SessionId);
	sprintf(value, "%s", stfield.SessionId);

	try
	{
		if(service_context_id == PGW )
		{
			qChildNum = dbm()->GetSQL("q_pgw_timer_childnum");
		}
		else if(service_context_id == DATA || service_context_id == CCG)
		{
			qChildNum = dbm()->GetSQL("q_data_timer_childnum");
		}
		else if(service_context_id == DATA_5G)
		{
			qChildNum = dbm()->GetSQL("q_5g_timer_childnum");
		}
		else
		{
			return 0;
		}
		qChildNum->DivTable(stfield.SessionId);
		qChildNum->UnBindParam();
		qChildNum->BindParam(1, value);
		qChildNum->Execute();

		if(qChildNum->Next())
		{
			qChildNum->Close();
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "have child");
			return 0;
		}
		qChildNum->Close();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, stfield.SessionId, "none child");
		return 1;
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "dbexp:%s", e.ToString());
		return 0;
	}
	return 0;
}

void DCSessionTimerScan::SendToProxyCallBack(int rc, const DCFSMsg* msg)
{
    if (rc)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "send to proxy failed, uuid[%s], content[%s]", msg->id.c_str(), msg->body.c_str());
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send to proxy success, uuid[%s], content[%s]", msg->id.c_str(), msg->body.c_str());
    }
}

int DCSessionTimerScan::produce(ocs::UHead& uhd, ocs::SEPTMsg& msg)
{
	DCBIZLOG(DCLOG_LEVEL_INFO, 1001, "", "DCSessionTimerScan::produce SessionId[%s] Uid[%s] HOSTID:%s",msg.sessionID.c_str(),uhd.uid.c_str() ,msg.topology.c_str());
	int ret = 0;
    struct timeval tmv;
    char buf[20];
	char value[256];
	const char *topology = NULL;
	uhd.car = "FREE";
	strcpy(value,msg.topology.c_str());
	msg.version = 2;
	if(msg.topology != "")
	topology = strtok(value,";");
	else
	topology = "";
    std::map<std::string, std::string>::iterator it = m_mTopic->find(topology);
    if(it == m_mTopic->end())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001,  msg.sessionID.c_str(), "not find topic for topology[%s]", topology);
		return -1;
	}
	std::size_t  sep = it->second.find('|');
	std::string  serviceName = it->second.substr(0, sep);
	msg.anstopic = it->second.substr(sep+1);

	try
	{
		m_eptEn.clear();
		m_eptEn.encode(uhd);
		m_eptEn.encode(msg);
		m_pPrint.clear();
		m_pPrint.print(uhd);
		m_pPrint.print(msg);
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "DCSessionTimerScan seedMsg:%s", m_pPrint.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","encode failed");
		return -1;
	}
	std::string sendmsg = HexEncode(m_eptEn.data(),m_eptEn.size());

	// 头部加固定16位微妙时间戳
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	sendmsg.insert(0, buf);
    GetGrayServName(sSmRouteProcess, serviceName, msg.EptExt);
	
    ret = m_pclient->invokeAsync(serviceName, uhd.uid, sendmsg); 
    if (ret < 0)
    {
    	int iNum = 0;
		while(ret < 0)
		{
			iNum++;
			m_pclient = (DCFLocalClient*)gethandle("SERVICEPRD");
			DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "", "not find SERVICEPRD handle, reconnect times[%d]", iNum);
			ret = m_pclient->invokeAsync(serviceName, uhd.uid, sendmsg); 
			if(iNum > 3)
			{
				break;
			}
			sleep(1);
		}
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "send [%s]failed", serviceName.c_str());		
        return -1;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "send to service:%s, uuid:%s, content:%s", serviceName.c_str(), uhd.uid.c_str(), sendmsg.c_str());
    }

	return 0;
}

int DCSessionTimerScan::SendSessionFree(int        iServiceContextId, SSessionTimerData& stfield)
{
	char value[256] = { 0 };
	char szOperListId[10] = {0};;
	char szOperType[20] = {0};
	string strServiceContextId;

	m_pm.get_statistic("APP")->reset(); 
	DCPerfTimeStats* tstat = m_pm.get_statistic("APP")->get_position("Deal5GSession");
	DCPerfTimeVCollect collect(tstat, true);

	switch (iServiceContextId)
	{
		case DATA_5G:
		{
			strServiceContextId = "5G";
			break;
		}
	}
	
	UDBSQL* pQuery = dbm()->GetSQL("q_vir_oper_list");
	try
	{
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, strServiceContextId.c_str());
		pQuery->Execute();
		if (pQuery->Next())
		{
			pQuery->GetValue(1, szOperListId);
			pQuery->GetValue(2, szOperType);
		}

		pQuery->Close();
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "5G", "q_vir_oper_list:%s", e.ToString());
		pQuery->Close();
		return -1;
	}

	ocs::UHead uhd;
    ocs::SEPTMsg msg;
	m_tseq++;
	if (m_tseq >= 10000000000L) 
	{
		m_tseq = 1;
	}
	sprintf(m_sseq, "%08d%010ld", m_hseq, m_tseq);
	uhd.uid = m_sseq;
	string skey = m_sseq;
	DCLOG_SETKEY(skey.c_str());
	DCLOG_SETCTL(DCLOG_MASK_TRACE, stfield.Trace);

	msg.type = 3;
	msg.ASRFlag = stfield.ASRFlag + stfield.FreeFlag * 10;
	msg.sessionID = stfield.SessionId;
	msg.trace = stfield.Trace;
	msg.sreq = stfield.RequestType;
	msg.serial = stfield.DataSerial;
	msg.requestnumber = stfield.RouteRecord;
	msg.servicecontextid = DATA_5G;
	msg.topology = stfield.HostId;
	sprintf(value, ";%ld;%s", stfield.RatingGroup, stfield.productofferid);
	msg.topology.append(value);
	msg.stamp = time(NULL);
	msg.result = NE_SESSION_TIMEOUT;
	msg.szServiceContextIDStr = "5G";
	char szTemp[12] = { 0 };
	sprintf(szTemp, "%ld", stfield.latnId);
	msg.EptExt["LatnId"] = szTemp;
	msg.EptExt["operListId"] = szOperListId;
	msg.EptExt["switchId"] = szOperListId;
	msg.EptExt["opertype"] = szOperType;
	msg.EptExt["BillingNum"] = stfield.SubNbr;

	produce(uhd, msg);
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, NE_SESSION_TIMEOUT, stfield.SessionId, \
	"SessionTimeOut: SubNbr[%s], FreeFlag[%d], ASRFlag[%d], HostId[%s], "
	"RequestType[%ld], DataSerial[%ld], RouteRecord[%ld], CurrCCRTime[%s], MainNextCCRTime[%ld], ChildNextCCRTime[%ld], 5g", \
	stfield.SubNbr, stfield.FreeFlag, stfield.ASRFlag, stfield.HostId, \
	stfield.RequestType, stfield.DataSerial, stfield.RouteRecord, stfield.CurrCCRTime, stfield.MainNextCCRTime, stfield.ChildNextCCRTime);

	collect.stop();

	return 0;
}


int DCSessionTimerScan::SendNotifyToCHFProxy(SSessionTimerData& stfield)
{
	ocs::DCHDATA hData;
	hData.type = "nchf_convergedcharging_notify";
	hData.body.clear();
	if (stfield.ASRFlag == 3)  // RAR重授权消息
	{
		hData.body =  "REAUTHORIZATION;"; 
	}
	else if (stfield.ASRFlag == 4 || stfield.ASRFlag == 5) // ASR终止会话消息
	{
		hData.body =  "ABORT_CHARGING;"; 
	}
	
	string strQuotaManager = stfield.OfflineFlag == 1 ? "OFFLINE_CHARGING" : "ONLINE_CHARGING";
	char szTmp[1024] = {0};
	sprintf(szTmp, "%s;%ld;%s;%s", stfield.productofferid, stfield.RatingGroup, strQuotaManager.c_str(), stfield.NotifyUrl);
	hData.body += szTmp;

	try
	{
		m_ntfEn.clear();
		m_ntfEn.encode(hData);
		m_pPrint.clear();
		m_pPrint.print(hData);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DCSessionTimerScan seedMsg:%s", m_pPrint.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "encode failed");
		return -1;
	}

	std::string encodeMsg = HexEncode(m_ntfEn.data(), m_ntfEn.size());

    DCFSMsg* fmsg = new DCFSMsg();
    fmsg->ptr = NULL;
    fmsg->id = stfield.SessionId;
    fmsg->body.swap(encodeMsg);

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send a msg to proxy, sessionId[%s], content:%s", stfield.SessionId, encodeMsg.c_str());
    int iRet = m_pDcfClient->send_async(fmsg, DCSessionTimerScan::SendToProxyCallBack);
    if (iRet < 0)
    {
        delete fmsg;
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "send a msg to proxy failed");
        return -1;
    }

    return 0;
}

int DCSessionTimerScan::CheckLatnId(string strPhone, int iLatnId)
{
	char value[256]	= {0};
	long lProdId = 0;
	int iPrdLatnId = 0;
	list<string> vec5GMobilePrdId;
	SplitString((*m_mapParam)["5GMobilePrdId"].c_str(), '|', vec5GMobilePrdId);
	UDBSQL* pQuery = dbm()->GetSQL("q_tb_prd_prd_inst_latnid");
	try
	{
		pQuery->UnBindParam();
		pQuery->BindParam(1, strPhone.c_str());	
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "phone[%s] latnid[%d]", strPhone.c_str(), iLatnId);
		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1, value);
			lProdId = atol(value);
			bool bFindPrdId = false;
			list<string>::iterator it5GPrdId = vec5GMobilePrdId.begin();
			for (; it5GPrdId != vec5GMobilePrdId.end(); it5GPrdId++)
			{
				if (lProdId == atol(it5GPrdId->c_str()))
				{
					bFindPrdId = true;
					break;
				}
			}

			if (!bFindPrdId)
			{
				continue;
			}

			pQuery->GetValue(2, value);
			iPrdLatnId = atoi(value);
		}		
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetUserAccountInfo exception[%s],sql[%s],errcode[%d] ", e.GetErrorInfo(), e.GetSqlInfo(), e.GetSqlCode());
		return -1;
	}

	if (iLatnId != iPrdLatnId)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "phone[%s], prodId[%ld], sm session latnid[%d], prd inst latnid[%d]", 
			strPhone.c_str(), lProdId, iLatnId, iPrdLatnId);
		return -1;
	}

	return 0;
}

int DCSessionTimerScan::QueryOperList(string& strServiceContextId, string &szOperListId, string& szOperType)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "begin to query OperListId and OperType ServiceContextId:%s", strServiceContextId.c_str());
	UDBSQL* pQuery = dbm()->GetSQL("q_vir_oper_list");
	try
	{
		pQuery->DivTable(NULL);
		pQuery->UnBindParam();
		pQuery->BindParam(1, strServiceContextId.c_str());
		pQuery->Execute();
		if (pQuery->Next())
		{
			pQuery->GetValue(1, szOperListId);
			pQuery->GetValue(2, szOperType);
		}

		pQuery->Close();
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1001, "OCS", "q_vir_oper_list:%s", e.ToString());
		pQuery->Close();
		return -1;
	}
	return 0;
}
//根据灰度路由规则表获取服务名
int DCSessionTimerScan::GetGrayServName(const string& RouteProcess, string& serviceName, std::map<std::string, std::string>& EptExt)
{
	//路由规则获取服务名称
	string sServiceName = "";
	string sLatnId;
	string sBillingNum;
	map<string,string>::iterator iterMap;
	map<string,string> inMapParam;
	iterMap = EptExt.find("LatnId");
	if(iterMap != EptExt.end())
	{
		sLatnId = iterMap->second;
	}
	iterMap = EptExt.find("BillingNum");
	if(iterMap != EptExt.end())
	{
		sBillingNum = iterMap->second;
	}

	inMapParam.insert(map<string,string>::value_type("LatnID", sLatnId));
	inMapParam.insert(map<string,string>::value_type("BillingNum", sBillingNum));

	int ret = DCGrayscaleRoute::instance()->GetRouteServiceName(RouteProcess.c_str(), inMapParam, sServiceName);
	if (ret < 0 || 0 == sServiceName.length())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get RouteServiceName failed.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get RouteServiceName[%s]",sServiceName.c_str());
	}
	serviceName = sServiceName;
	return 0;
}


DYN_PLUGIN_CREATE(DCSessionTimerScan, "FC_SessFree", "FC_SessFreeScan", "1.0.0")

