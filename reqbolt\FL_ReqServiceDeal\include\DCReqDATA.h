/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqDATA.h
*Indentifier：
*
*Description：
*		数据业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __TBIZ_REQ_DATA_H__
#define __TBIZ_REQ_DATA_H__

#include "DCReq.h"
#include "OCPMsgDef.h"
#include "DCBizMsgDef.h"
#include "SMParaStruct.h"

class DCReqDATA : public DCReq
{
	public:

		DCReqDATA();
		virtual ~DCReqDATA();

	public:

		virtual int Work(void *data);

	protected:
	virtual int SwitchReqType(STBizMsg* bizMsg);
		
	int QueryOss(STBizMsg* bizMsg,long servid,char *PLCA_LOC,char *PLCA_CELLID,char *PLCA_MSC,char *PLCA_RAT_TYPE);
	int GetServerid(SUserInfo* userInfo, STBizMsg* bizMsg);
	int sendInitCCA(STBizMsg* bizMsg);
	int sendTermCCAWithOutRG(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);
    int sendTermCCAWithOutRG(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg);

};

#endif

