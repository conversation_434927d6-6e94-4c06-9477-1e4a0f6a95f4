/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdrNormalSMS.h
*Indentifier：
*		
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef _DCBIZ_CDR_NORMAL_SMS_H_
#define _DCBIZ_CDR_NORMAL_SMS_H_

#include "DCBizCdrNormal.h"
#include "TCDRDict.h"


class DCBizCdrNormalSMS:public DCBizCdrNormal
{
	public:

		DCBizCdrNormalSMS();
		~DCBizCdrNormalSMS();
		
	protected:	

		virtual int ComposeSMS(STBizMsg* bizMsg);

		int ComposeSmsCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr);

};

#endif


