#include "DCBizCdrNormalPGW.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "DCOcpMsgDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"

using namespace ocs;

static void selftimeAdd(char* begin_time,int seconds)
{
  int year,mon,day,hh,mm,ss;
  char temp[5] = {0};
  char szOldTime[15] = {0};
  struct tm tm1;
  struct tm tm2;
  time_t ltime;

  strcpy(szOldTime,begin_time);
  strncpy(temp,begin_time,4);

  year=atol(temp);
  year=year-1900;
  memset(temp,'\0',5);
  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;
  strncpy(temp,begin_time+6,2);

  day=atol(temp);
  strncpy(temp,begin_time+8,2);

  hh=atol(temp);
  strncpy(temp,begin_time+10,2);

  mm=atol(temp);
  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  memset(&tm1,0,sizeof(tm));

  tm1.tm_sec=ss;
  tm1.tm_min=mm;
  tm1.tm_hour=hh;
  tm1.tm_mday=day;
  tm1.tm_mon=mon;
  tm1.tm_year=year;

  ltime=mktime(&tm1);
  ltime+=seconds;

  localtime_r(&ltime,&tm2);

  sprintf(begin_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2.tm_year+1900,tm2.tm_mon+1,
            tm2.tm_mday,tm2.tm_hour,tm2.tm_min,tm2.tm_sec);

}

DCBizCdrNormalPGW::DCBizCdrNormalPGW()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormalPGW::~DCBizCdrNormalPGW()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

//非超长出单或者超长截单后剩余量出单
int DCBizCdrNormalPGW::PretreatPGWRGLeft(STBizMsg* bizMsg)
{
	SCCRDataUnit TUSU;
	SCCRDataUnit USU;
	char ChildsessionID[BIZ_TEMP_LEN_256] = {0};
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;

	vector<SCDRField*>::iterator iter;

	//规整话单字段
	for(map<string, DataCDRInfo>::iterator iter2 = m_mapCdrInfo.begin(); iter2 !=  m_mapCdrInfo.end(); ++iter2)
	{
	    //int LongCdrFlag = 0;
		memset(ChildsessionID,0,sizeof(ChildsessionID));

		//使用量信息
		TUSU.duration = iter2->second.duration;
		TUSU.unitTotal = iter2->second.total;
		TUSU.unitInput = iter2->second.input;
		TUSU.unitOutput = iter2->second.output;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total:out[%ld], in[%ld], total[%ld], duration[%d]", TUSU.unitOutput, TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

		//减去已经出过的使用量
		TUSU.unitOutput-=iter2->second.usedoutput;
		TUSU.unitInput-=iter2->second.usedinput;
		TUSU.unitTotal-=iter2->second.usedtotal;
		TUSU.duration-=iter2->second.usedduration;


		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",
			"rating group[%s]:duration[%d],unitTotal[%lld],unitInput[%lld],unitOutput[%lld]",
			iter2->second.rating, TUSU.duration, TUSU.unitTotal, TUSU.unitInput, TUSU.unitOutput);

              if((TUSU.duration<=0) && (TUSU.unitTotal<=0) )
		{
                      iter2->second.output=0;
		        iter2->second.input=0;
		        iter2->second.total=0;
		        iter2->second.duration=0;
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "skip cdr, rg[%s], duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]",  iter2->second.rating,TUSU.duration, TUSU.unitTotal, TUSU.unitInput, TUSU.unitOutput);
		       continue;
		}

              //流量计费时长修正
              if(iter2->second.nChargeType != 1)
              {
                    long nEndTime = DCCommonIF::dateToSec(iter2->second.sessionCurrent);
		      long nBeginTime = DCCommonIF::dateToSec(iter2->second.sessionStart);
		      long nDuration = nEndTime - nBeginTime ;
                    TUSU.duration = nDuration < 0 ? 0 : nDuration;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "fix duration, rg[%s], duration[%d]", iter2->second.rating, TUSU.duration);
              }

              iter2->second.output=TUSU.unitOutput;
		iter2->second.input=TUSU.unitInput;
		iter2->second.total=TUSU.unitTotal;
		iter2->second.duration=TUSU.duration;

		//流量基础单位转换(KB)
		if(1 == smpara->GetPSPara()->iRGUNITSwitch)
		{
                      TUSU.unitInput = (TUSU.unitInput + 1023)/1024;
		        TUSU.unitOutput = (TUSU.unitOutput + 1023)/1024;
                      TUSU.unitTotal = (TUSU.unitTotal + 1023)/1024;

			iter2->second.output = TUSU.unitOutput;
			iter2->second.input = TUSU.unitInput;
			iter2->second.total = TUSU.unitTotal;
		}

		//累积量信息单位
		if(1 == smpara->GetPSPara()->iAccuUNITSwitch)
		{
			ChangeAmoutUnit(iter2->second.accumuInfo, smpara);
		}

		//账目类型和费用字段
		ParaseFeeItemByRG(bizMsg,iter2->second);


		//规整字段信息
		//charge type
		if(1 == iter2->second.nChargeType)//时长
		{
				iter2->second.nChargeType = 2;
		}
		else//流量
		{
				iter2->second.nChargeType = 3;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","chargeType[%d]:", iter2->second.nChargeType);
		//romatype
		int roamType =  iter2->second.nRomatype;
		switch(roamType) //漫游
		{
			case 0://非漫游
				roamType = 0;
				break;
			case 1://省内漫游
				roamType = 1;
				break;
			case 3://省际漫游来访
				roamType = 2;
				break;
			case 4://省际漫游出访
				roamType = 5;
				break;
			case 5://国际漫游来访
				roamType = 7;
				break;
			case 6://国际漫游出访
				roamType = 4;
				break;
			case 7://省内边界漫游
				roamType = 8;
				break;
			case 8://省际边界漫游
				roamType = 9;
				break;
			case 9://港澳台漫游
				roamType = 3;
				break;
			default:
				break;
		}
		iter2->second.nRomatype = roamType;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","roamType[%d]:", iter2->second.nRomatype);

		//info 日志
		DCDATLOG("SM00015:%d%s%s%ld%s%s%s%s%s", bizMsg->m_longCDR,iter2->second.sessionStart, iter2->second.sessionCurrent,\
								iter2->second.total,iter2->second.planInfo, iter2->second.tarifInfo, iter2->second.chargeInfo, iter2->second.accumuInfo, iter2->second.orichargeInfo);

	}

	//生成话单
	PutCdr_PGW(bizMsg);

	return 0;
}


//超长截单
int DCBizCdrNormalPGW::PretreatPGWRGCut(STBizMsg* bizMsg,int longtype)
{
	int ret = 0;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	char value[BIZ_TEMP_LEN_256] = {0};
	char rating[BIZ_TEMP_LEN_1024] = {0};


	char ChildsessionID[BIZ_TEMP_LEN_256] 	= {0};
	char sessionCurrent[BIZ_DATA_LEN_32]  	= {0};
	char szNewLocInfo[BIZ_DATA_LEN_128]		= {0};


	vector<SCDRField*>::iterator iter;

	// 从缓存对象中获取数据:子会话信息
	//SSessionCacheDATA* bizCache = (SSessionCacheDATA*)bizMsg->m_cache;

	map<string, DataCDRInfo>::iterator iter2;

	for(iter2 = m_mapCdrInfo.begin(); iter2 != m_mapCdrInfo.end(); iter2++)
	{
		SCCRDataUnit TUSU;
	    int LongCdrFlag = 0;
		//使用量信息
		TUSU.duration = iter2->second.duration;
		TUSU.unitTotal = iter2->second.total;
		TUSU.unitInput = iter2->second.input;
		TUSU.unitOutput = iter2->second.output;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "USU duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]", TUSU.duration, TUSU.unitTotal,TUSU.unitInput,TUSU.unitOutput);

		//扣费信息
		strcpy(sessionCurrent,iter2->second.sessionCurrent);
		strcpy(szNewLocInfo, iter2->second.szNewLocInfo);

		//减去已经出过的使用量
		TUSU.unitOutput -=iter2->second.usedoutput;
		TUSU.unitInput -=iter2->second.usedinput;
		TUSU.unitTotal -=iter2->second.usedtotal;
		TUSU.duration -=iter2->second.usedduration;

		iter2->second.outputtemp=TUSU.unitOutput;
		iter2->second.inputtemp=TUSU.unitInput;
		iter2->second.totaltemp=TUSU.unitTotal;
		iter2->second.durationtemp=TUSU.duration;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "used:out[%ld], in[%ld], total[%ld], duration[%ld] unuser:out[%ld], in[%ld], total[%ld], duration[%ld]",
			iter2->second.usedoutput,iter2->second.usedinput,iter2->second.usedtotal,iter2->second.usedduration,
			TUSU.unitOutput,TUSU.unitInput,TUSU.unitTotal,TUSU.duration);

		if(iter2->second.szProductOfferId[0])
		{
			sprintf(iter2->second.sessionId, "%s;%s", bizMsg->m_sessionID, iter2->second.szProductOfferId);
			sprintf(ChildsessionID, "%s;%s", bizMsg->m_sessionID, iter2->second.szProductOfferId);
			strcpy(iter2->second.rating, iter2->second.szProductOfferId);
		}
		else
		{
			sprintf(ChildsessionID, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);
			sprintf(iter2->second.sessionId, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "find child used,session[%s]",iter2->second.sessionId);


		if((TUSU.duration<=0) && (TUSU.unitTotal<=0) )
		{
                     iter2->second.output= 0;
		       iter2->second.input= 0;
		       iter2->second.total= 0;
		       iter2->second.duration= 0;

			if(4 == bizMsg->m_longCDR)
			{
				updateLocInfo(bizMsg,iter2->second.sessionId,sessionCurrent, szNewLocInfo);
			}
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "skip cdr, rg[%s], duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]",  iter2->second.rating,TUSU.duration, TUSU.unitTotal, TUSU.unitInput, TUSU.unitOutput);
			if(bizMsg->m_longCDR == 3)
			{
				continue;
			}
			else
			{
				return 0;
			}
		}

		if((TUSU.duration<=0) && (TUSU.unitTotal<=0) )
		{
                     iter2->second.output= 0;
		       iter2->second.input= 0;
		       iter2->second.total= 0;
		       iter2->second.duration= 0;
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "skip cdr, rg[%s], duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]",  iter2->second.rating,TUSU.duration, TUSU.unitTotal, TUSU.unitInput, TUSU.unitOutput);
			continue;
		}

              //流量计费时长修正
              if(iter2->second.nChargeType != 1)
              {
                    long nEndTime = DCCommonIF::dateToSec(iter2->second.sessionCurrent);
		      long nBeginTime = DCCommonIF::dateToSec(iter2->second.sessionStart);
		      long nDuration = nEndTime - nBeginTime ;
                    TUSU.duration = nDuration < 0 ? 0 : nDuration;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "fix duration, rg[%s], duration[%d]", iter2->second.rating, TUSU.duration);
              }

              iter2->second.output=TUSU.unitOutput;
		iter2->second.input=TUSU.unitInput;
		iter2->second.total=TUSU.unitTotal;
		iter2->second.duration=TUSU.duration;

		//账目类型和费用字段
		ParaseFeeItemByRG(bizMsg,iter2->second);


		//流量基础单位转换(KB)
		if(1 == smpara->GetPSPara()->iRGUNITSwitch)
		{
                     iter2->second.input = (iter2->second.input + 1023)/1024;
			iter2->second.output = (iter2->second.output + 1023)/1024;
                     iter2->second.total = (iter2->second.total + 1023)/1024;
		}


		//累积量信息单位
		if(1 == smpara->GetPSPara()->iAccuUNITSwitch)
		{
			ChangeAmoutUnit(iter2->second.accumuInfo, smpara);
		}


		//规整字段信息
		//charge type
		if(1 == iter2->second.nChargeType)//时长
		{
				iter2->second.nChargeType = 2;
		}
		else//流量
		{
				iter2->second.nChargeType = 3;
		}



		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","chargeType[%d]:", iter2->second.nChargeType);

		//romatype
		int roamType =  iter2->second.nRomatype;
		switch(roamType) //漫游
		{
			case 0://非漫游
				roamType = 0;
				break;
			case 1://省内漫游
				roamType = 1;
				break;
			case 3://省际漫游来访
				roamType = 2;
				break;
			case 4://省际漫游出访
				roamType = 5;
				break;
			case 5://国际漫游来访
				roamType = 7;
				break;
			case 6://国际漫游出访
				roamType = 4;
				break;
			case 7://省内边界漫游
				roamType = 8;
				break;
			case 8://省际边界漫游
				roamType = 9;
				break;
			case 9://港澳台漫游
				roamType = 3;
				break;
			default:
				break;
		}
		iter2->second.nRomatype = roamType;

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","roamType[%d]:", iter2->second.nRomatype);

		//info 日志
		DCDATLOG("SM00015:%d%s%s%ld%s%s%s%s%s", bizMsg->m_longCDR,iter2->second.sessionStart, iter2->second.sessionCurrent,\
								iter2->second.total,iter2->second.planInfo, iter2->second.tarifInfo, iter2->second.chargeInfo, iter2->second.accumuInfo, iter2->second.orichargeInfo);
	}



	//生成话单
	PutCdr_PGW(bizMsg);
	UDBSQL *pExe = dbm->GetSQL(PGW_CDR_UpdateCdrSession);
	for(iter2 = m_mapCdrInfo.begin(); iter2 != m_mapCdrInfo.end(); iter2++)
	{
		if(!iter2->second.outputtemp && !iter2->second.inputtemp && !iter2->second.totaltemp && !iter2->second.durationtemp)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU=0,skip", "");
			continue;
		}
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				// 更新使用量信息到CDR_PGW_SESSION_STORE
				pExe->DivTable(bizMsg->m_sessionID);
				pExe->UnBindParam();
				pExe->BindParam(1, (long)iter2->second.outputtemp);
				pExe->BindParam(2, (long)iter2->second.inputtemp);
				pExe->BindParam(3, (long)iter2->second.totaltemp);
				pExe->BindParam(4, (long)iter2->second.durationtemp);
				pExe->BindParam(5, iter2->second.sessionCurrent);
				pExe->BindParam(6, iter2->second.seqNum);
				pExe->BindParam(7, "");
				pExe->BindParam(8, iter2->second.sessionId);
				string sql;
				pExe->GetSqlString(sql);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sql[%s]", sql.c_str());
				pExe->Execute();
				pExe->Connection()->Commit();
				success = true;
			}
			catch (UDBException &e)
			{
				pExe->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return RB_SM_UNABLE_TO_COMPLY;
					}	
				}
				return RB_SM_UNABLE_TO_COMPLY;
			}
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update SM_PGW_SESSION_STORE out[%ld], in[%ld], total[%ld], duration[%ld]"
			, (long)iter2->second.outputtemp,(long)iter2->second.inputtemp,(long)iter2->second.totaltemp,(long)iter2->second.durationtemp);

		//清空相关信息并且更新序列
		updateCdrInfo(bizMsg,iter2->second.sessionId);
	}
	//释放
	//m_mapCdrInfo.clear();


	//按时长截单的，更新会话开始时间为当前时间

	if(1 == bizMsg->m_longCDR||2 == bizMsg->m_longCDR || 20  == bizMsg->m_requestType ||  5 == bizMsg->m_longCDR)
	{

		UDBSQL *pExe1 = dbm->GetSQL(PGW_CDR_LONG_UpdateSessionTime);
		long lCurrentTime = atol(sessionCurrent);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				pExe1->DivTable(bizMsg->m_sessionID);
				pExe1->UnBindParam();
				pExe1->BindParam(1, lCurrentTime);
				pExe1->BindParam(2, ChildsessionID);
				pExe1->Execute();
				pExe1->Connection()->Commit();
				success = true;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update RE_LNG_CALL_START_TIME=[%ld]", lCurrentTime);
			}
			catch (UDBException &e)
			{
				pExe1->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
				return RB_SM_UNABLE_TO_COMPLY;
			}
		}
	}
	else if(4 == bizMsg->m_longCDR)
	{
		updateLocInfo(bizMsg,ChildsessionID,sessionCurrent,szNewLocInfo);
	}

	return 0;
}

int DCBizCdrNormalPGW::updateLocInfo(STBizMsg* bizMsg,char *sessinID,char *startTime, char* szNewLocInfo)
{
	int ret=0;
	long lCurrentTime = atol(startTime);
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	UDBSQL *pExe1 = dbm->GetSQL(PGW_CDR_LOCA_UpdateSessionTime);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NewLoc Info %s",szNewLocInfo);

	vector<string> vec;
	SplitString(szNewLocInfo, '|', vec);
	vec.resize(16);
       string ext = vec[8]+string("|")+vec[9];      //EVT|RTI

    //扩展字段兼容现有数据，默认使用6出单
    if (vec[10].empty())
    {
   		vec[10] = "6";
    }

	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExe1->DivTable(bizMsg->m_sessionID);
			pExe1->UnBindParam();
			pExe1->BindParam(1, lCurrentTime);
			pExe1->BindParam(2, vec[0].c_str());	   // OCP_STR_IP_SGSN_ADDR
			pExe1->BindParam(3, vec[1].c_str());	   // CDR_STR_CELLID
			pExe1->BindParam(4, vec[2].c_str());	   // CDR_STR_MSC
			pExe1->BindParam(5, vec[3].c_str());	   // CDR_STR_LAC
			pExe1->BindParam(6, vec[4].c_str());	   // CDR_USER_LOCATION_INFO
			pExe1->BindParam(7, atoi(vec[5].c_str())); // RE_INT_ROAM_TYPE
			pExe1->BindParam(8, vec[6].c_str());	   // RE_STR_SUB_VISIT_AREA
			pExe1->BindParam(9, vec[7].c_str());	   // OCP_STR_IP_GGSN_ADDR
			pExe1->BindParam(10, vec[4].c_str());	   // OCP_STR_LAC_CODE
			pExe1->BindParam(11, ext.c_str());		   // OCP_OLD_LOCATION_EXT
			pExe1->BindParam(12, vec[10].c_str());	   // OCP_STR_3GPP_RAT_TYPE
			pExe1->BindParam(13, sessinID);
			pExe1->Execute();
			pExe1->Connection()->Commit();
			success = true;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child ok,all usu time 0");
		}
		catch (UDBException &e)
		{
			pExe1->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update DATA sqlCode[%s] execption[%s], error code[%d]", PGW_CDR_LOCA_UpdateSessionTime, e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return -1;
				}
			}	
			return -1;
		}
	}

	return 0;
}


//查询需要预处理的话单字段
int DCBizCdrNormalPGW::SelectPretreatColumn(STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_256] = {0};
	char szCdrSessionId[BIZ_TEMP_LEN_128] = {0};
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	m_mapCdrInfo.clear();

	UDBSQL *pQuery = NULL;
	int ret = 0;

	//超长话单的只需查一条子会话
	if(1 == bizMsg->m_longCDR || 2 == bizMsg->m_longCDR || 4 == bizMsg->m_longCDR || 5 == bizMsg->m_longCDR)
	{
		memset(value,0,sizeof(value));
		if(strlen(bizMsg->m_ProductOfferId)>0)
		{
			sprintf(value, "%s;%s", bizMsg->m_sessionID, bizMsg->m_ProductOfferId);
                     strcpy(bizMsg->m_childsessionID, value);
		}
		else if(bizMsg->m_ratingGroup > 0)
		{
			sprintf(value, "%s;%lld", bizMsg->m_sessionID, bizMsg->m_ratingGroup);
                     strcpy(bizMsg->m_childsessionID, value);
		}
		else
		{
			sprintf(value, "%s", bizMsg->m_childsessionID);
		}

		pQuery = dbm->GetSQL(PGW_CDR_SelectSession);

	}
	else
	{
		sprintf(value, "%s%%", bizMsg->m_sessionID);
		value[2+strlen(bizMsg->m_sessionID)]='\0';
		pQuery = dbm->GetSQL(PGW_CDR_SelectSessionALL);

	}

	if(NULL==pQuery)
	{
		return -1;
	}

	try
	{
		//查询子会话
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, value);
		pQuery->Execute();
		while(pQuery->Next())
		{
			//话单信息组装
			DataCDRInfo stCdrInfo;
			memset(&stCdrInfo,0x00,sizeof(DataCDRInfo));


			//RE_INT_ROAM_TYPE
			pQuery->GetValue(1, value);
			stCdrInfo.nRomatype = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.nRomatype [%d]", stCdrInfo.nRomatype );

			//SM_INT_CHARGE_TYPE 替换为RE_INT_LAST_GSU_UNIT
			pQuery->GetValue(2, value);
			stCdrInfo.nChargeType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.nChargeType [%d]", stCdrInfo.nChargeType );

			//CDR_PUB_STR_BALANCEINFO
			pQuery->GetValue(3, value);
			strcpy(stCdrInfo.balanceInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.balanceInfo [%s]", stCdrInfo.balanceInfo );

			//CDR_PUB_STR_BALANCEINFO2
			pQuery->GetValue(4, value);
			strcpy(stCdrInfo.balanceInfo2,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.balanceInfo2 [%s]", stCdrInfo.balanceInfo2 );

			//CDR_PUB_STR_ACCUMLATORINFO
			pQuery->GetValue(5, stCdrInfo.accumuInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.accumuInfo [%s]", stCdrInfo.accumuInfo );

			//CDR_PUB_STR_TARIFFID
			pQuery->GetValue(6, stCdrInfo.tarifInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.tarifInfo [%s]", stCdrInfo.tarifInfo );

			//CDR_PUB_STR_CHARGEINFO
			pQuery->GetValue(7, value);
			strcpy(stCdrInfo.chargeInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.chargeInfo [%s]", stCdrInfo.chargeInfo );

			//SM_LNG_ALL_USU_TIME
			pQuery->GetValue(8, value);
			stCdrInfo.duration = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.duration[%ld]", stCdrInfo.duration);

			//SM_LNG_ALL_USU_TOTAL_OCT
			pQuery->GetValue(9, value);
			stCdrInfo.total = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.total[%ld]", stCdrInfo.total);

			//SM_LNG_ALL_USU_INPUT_OCT
			pQuery->GetValue(10, value);
			stCdrInfo.input = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.input[%ld]", stCdrInfo.input);

			//SM_LNG_ALL_USU_OUTPUT_OCT
			pQuery->GetValue(11, value);
			stCdrInfo.output = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.output[%ld]", stCdrInfo.output);

			//OCP_INT_RATING_GROUP
			pQuery->GetValue(12, value);
			strcpy(stCdrInfo.rating,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "stCdrInfo.rating[%s]", stCdrInfo.rating);

			//OCS_SM_CDR_VERSION_SEQ_ID.nextval
			pQuery->GetValue(13, value);
			stCdrInfo.lCdrVersionSerial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCS_SM_CDR_VERSION_SEQ_ID.nextval[%ld]", stCdrInfo.lCdrVersionSerial);

			//CDR_DISCOUNT_FEE
			pQuery->GetValue(14, value);
			stCdrInfo.discount_fee = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "discount_fee[%ld]", stCdrInfo.discount_fee);

			//CDR_PUB_STR_PRICING_PLAN_ID
			pQuery->GetValue(15, value);
			strcpy(stCdrInfo.planInfo,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "planInfo[%s]", stCdrInfo.planInfo);

			if(1 == bizMsg->m_longCDR || 2 == bizMsg->m_longCDR || 4 == bizMsg->m_longCDR) //超长话单的，会话表中结果码为0，这里需要特殊处理为2001，term时候才更新为2001
			{
				stCdrInfo.nResultCode = 2001;
			}
			else
			{
				//SM_INT_RESULT_CODE
				pQuery->GetValue(17, value);
				stCdrInfo.nResultCode= atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nResultCode[%d]", stCdrInfo.nResultCode);
			}
			pQuery->GetValue(18, value);
			strcpy(stCdrInfo.szProductOfferId,value);//OCP_STR_PRODUCT_OFFER_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "product offer id[%s]", stCdrInfo.szProductOfferId);

			pQuery->GetValue(19, value); //OCP_STR_SESSION_ID
			strcpy(szCdrSessionId,value);
			strcpy(stCdrInfo.sessionId,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SessionId[%s]", szCdrSessionId);

			pQuery->GetValue(21, value);
			strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionCurrent[%s]", value);

			stCdrInfo.Day[0] = value[6];
			stCdrInfo.Day[1] = value[7];
			stCdrInfo.Day[2] = '\0';
			int day = atoi(stCdrInfo.Day);
			sprintf(stCdrInfo.Day,"%d",day);
			pQuery->GetValue(37, value);//CDR_STR_BILLCYCLE
			stCdrInfo.Payment= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "Payment[%ld]", stCdrInfo.Payment);
			if(stCdrInfo.Payment == 0)
			{
				strcpy(value,stCdrInfo.sessionCurrent);
				value[6] = '\0';
				stCdrInfo.Payment= atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "Day[%s]", stCdrInfo.Day);

			//CDR_LNG_ALL_USU_OUTPUT_OCT
			pQuery->GetValue(22, value);
			stCdrInfo.usedoutput= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedoutput[%ld]", stCdrInfo.usedoutput);

			//CDR_LNG_ALL_USU_INPUT_OCT
			pQuery->GetValue(23, value);
			stCdrInfo.usedinput= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedinput[%ld]", stCdrInfo.usedinput);

			//CDR_LNG_ALL_USU_TOTAL_OCT
			pQuery->GetValue(24, value);
			stCdrInfo.usedtotal= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedtotal[%ld]", stCdrInfo.usedtotal);

			//CDR_LNG_ALL_USU_TIME
			pQuery->GetValue(25, value);
			stCdrInfo.usedduration= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usedduration[%ld]", stCdrInfo.usedduration);

			if (PGW == bizMsg->m_serviceContextID && 4 == bizMsg->m_longCDR)
			{
				// OCP_LOCATION_INFO    位置变更信息
				pQuery->GetValue(26, value);
				strcpy(stCdrInfo.szNewLocInfo, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "szNewLocInfo[%s]", stCdrInfo.szNewLocInfo);
			}

			pQuery->GetValue(27, value);
			strncpy(stCdrInfo.sessionStart,value,sizeof(stCdrInfo.sessionStart));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionStart[%s]", value);

			//CDR_PUB_INT_SEQ
			pQuery->GetValue(29, value);
			stCdrInfo.seqNum = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "seqNum[%d]", stCdrInfo.seqNum);

			pQuery->GetValue(30, value);
			stCdrInfo.PayFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "payflag[%d]", stCdrInfo.PayFlag);

			pQuery->GetValue(31, value);
			strcpy(stCdrInfo.ratType, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ratType[%s]", stCdrInfo.ratType);

			pQuery->GetValue(32, value);
			strcpy(stCdrInfo.userMsc, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userMsc[%s]", stCdrInfo.userMsc);

			pQuery->GetValue(33, value);
			strcpy(stCdrInfo.userLocationInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "userLocationInfo[%s]", stCdrInfo.userLocationInfo);

                     //修正rattype的取值
			if(!strcmp(stCdrInfo.ratType,"6") && strncmp(stCdrInfo.userLocationInfo,"46011", 5) && strncmp(stCdrInfo.userLocationInfo,"460#11", 6))
			{
				strcpy(stCdrInfo.ratType,"102");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "fix ratType[%s]", stCdrInfo.ratType);
			}

			pQuery->GetValue(34, value);
			strcpy(stCdrInfo.LacId, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "LacId[%s]", stCdrInfo.LacId);

			pQuery->GetValue(35, value);
			strcpy(stCdrInfo.orichargeInfo, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "orichargeInfo[%s]", value);

			pQuery->GetValue(36, value);
			strcpy(stCdrInfo.imsi, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "imsi[%s]", value);
			//话单信息保存到map
			if(strlen(stCdrInfo.szProductOfferId))
			{
				m_mapCdrInfo.insert(pair<string, DataCDRInfo>(stCdrInfo.szProductOfferId, stCdrInfo));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "insert map:<%s,cdrinfo>", stCdrInfo.szProductOfferId);
			}
			else
			{
				m_mapCdrInfo.insert(pair<string, DataCDRInfo>(stCdrInfo.rating, stCdrInfo));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "insert map:<%s,cdrinfo>", stCdrInfo.rating);
			}

		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "select  execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	if(0 == m_mapCdrInfo.size())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child no exist rg[%ld],reqytpe[%d],reqnum[%d]", bizMsg->m_ratingGroup,bizMsg->m_requestType,bizMsg->m_requestNumber);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session num[%d]", m_mapCdrInfo.size());
	}


	return 0;
}

int DCBizCdrNormalPGW::ComposePGW(STBizMsg* bizMsg)
{
	int ret = 0;

/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		return RET_SUCCESS;
	}
*/
	//查询所有子会话需要预处理的字段,保存在map中
	ret = SelectPretreatColumn(bizMsg);
	if(ret)
	{
		return ret;
	}

	//超长话单
	if(1 == bizMsg->m_longCDR || 2 == bizMsg->m_longCDR || 3 == bizMsg->m_longCDR || 4 == bizMsg->m_longCDR || 5 == bizMsg->m_longCDR || 20  == bizMsg->m_requestType)
	{
		ret = PretreatPGWRGCut(bizMsg,bizMsg->m_longCDR);
		return ret;
	}
	//超长话单剩余部分出单或者非超长出单
	else
	{
		ret = PretreatPGWRGLeft(bizMsg);

		DeleteSession(bizMsg);

		return ret;
	}

	return RET_SUCCESS;

}


//生成话单,按RG,PGW业务
int DCBizCdrNormalPGW::PutCdr_PGW(STBizMsg* bizMsg)
{
	int ret = 0;
	int length = 0;
	char ChildsessionID[BIZ_TEMP_LEN_256] = {0};

	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	//规整话单各个字段，出话单
	vector<SCDRField*> *field;
       char* rg = NULL;

	for(map<string, DataCDRInfo>::iterator iter2 = m_mapCdrInfo.begin(); iter2 != m_mapCdrInfo.end(); ++iter2)
	{
		if((!iter2->second.duration) && (!iter2->second.total) && (!iter2->second.input) && (!iter2->second.output))
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU=0,skip", "");
			continue;
		}

		if((iter2->second.duration<0) || (iter2->second.total<0) || (iter2->second.input<0) || (iter2->second.output<0))
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU<0,skip", "");
			continue;
		}

		vector<STariffAccumCDRInfo> vecTaiAuCdr;
		vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

		//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
		ParaseTariffAccumCdrInfo(bizMsg,iter2->second,vecTaiAuCdr);

		long nLeftDuration = iter2->second.duration;
		long nDuration = iter2->second.duration;

		long nLeftTotal = iter2->second.total;
		long nTotal = iter2->second.total;
		long nInPut = iter2->second.input;
		long nOutPut = iter2->second.output;
		long nLeftInput = nInPut;

		long nStartTime = atol(iter2->second.sessionStart);
		long nCurrTime = atol(iter2->second.sessionCurrent);

		field = TCDRDict::instance()->GetDATAField();
		/*if(bizMsg->m_eptType == 5)
		{
			char* p = strrchr(bizMsg->m_sessionID, ';');
        	if(p) *p = 0x0;
		}*/
		//子会话ID
		memset(ChildsessionID,0,sizeof(ChildsessionID));
        if(strlen(iter2->second.szProductOfferId)>0)
        {
			sprintf(ChildsessionID, "%s;%s", bizMsg->m_sessionID, iter2->second.szProductOfferId);
			rg  = iter2->second.szProductOfferId;
    	}
		else
		{
			sprintf(ChildsessionID, "%s;%s", bizMsg->m_sessionID, iter2->second.rating);
			rg = iter2->second.rating;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "select child[%s]", ChildsessionID);
		/*
		DCCdrIndex cdridx;
		strcpy(bizMsg->m_ratingGroup,rg);
		cdridx.SetIndexInfo(bizMsg,iter2->second.sessionStart,iter2->second.sessionCurrent,iter2->second.imsi);
		*/
		UDBSQL *pQuery = dbm->GetSQL(COM_CDR_PGW);
		try
		{
			string  sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();
			pQuery->BindParam(1,ChildsessionID);

			pQuery->Execute();
			if(pQuery->Next())
			{
				int cdrSeqNum=0;

				if(iter2->second.seqNum > 0)
				{
					cdrSeqNum = iter2->second.seqNum;
				}

				if(vecTaiAuCdr.size() > 0)
				{
					long nSize = vecTaiAuCdr.size();
					long nIndex = 0;
					for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
					{
						nIndex ++ ;
						cdrSeqNum++;
						SCDRData cdr = {0};
						iter2->second.nCutnum = nSize;

						if(nIndex < nSize)
						{
							if(TaiAuCdrIter->measure == 3)
							{
								iter2->second.total = TaiAuCdrIter->billingDuration;
								nLeftTotal -= TaiAuCdrIter->billingDuration;
								if(nLeftTotal < 0)
									nLeftTotal = 0;

								if(iter2->second.total == 0)
								{
									iter2->second.duration = 0;
									iter2->second.input = 0;
									iter2->second.output = 0;
								}
								else if(nLeftTotal == 0)
								{
									iter2->second.duration = nLeftDuration;
									iter2->second.input = nLeftInput;

									if(iter2->second.total < iter2->second.input)
									{
									 	iter2->second.input = iter2->second.total;
									}
									iter2->second.output = iter2->second.total - iter2->second.input;
								}
								else
								{
									iter2->second.duration = Div(nDuration*(long)iter2->second.total, nTotal, '3');
									iter2->second.input = Div(nInPut*(long)iter2->second.total,  nTotal, '3');
									iter2->second.output = iter2->second.total - iter2->second.input;
								}

								nLeftInput -= iter2->second.input;
								if(nLeftInput < 0)
									nLeftInput = 0;

								nLeftDuration -= iter2->second.duration;
								if(nLeftDuration < 0)
									nLeftDuration = 0;
							}
							else if (TaiAuCdrIter->measure == 1)
							{
								iter2->second.duration = TaiAuCdrIter->billingDuration;
								nLeftDuration -= TaiAuCdrIter->billingDuration;
								if(nLeftDuration <0)
									nLeftDuration = 0;
							}
						}
						else
						{
							//最后一个
							if(TaiAuCdrIter->measure == 3)
							{
								iter2->second.duration = nLeftDuration;
								iter2->second.total = nLeftTotal;
								iter2->second.input = nLeftInput > nLeftTotal ? nLeftTotal : nLeftInput;
								iter2->second.output = iter2->second.total - iter2->second.input;
							}
							else if (TaiAuCdrIter->measure == 1)
							{
								iter2->second.duration = nLeftDuration;
							}

						}

						if(nSize > 1)
						{
						 	// 重新计算结束时间
						 	strcpy(iter2->second.sessionCurrent, iter2->second.sessionStart);
						 	selftimeAdd(iter2->second.sessionCurrent, (int)iter2->second.duration);
						}

						if(0 == iter2->second.total &&  0 == iter2->second.duration && 0 == iter2->second.input && 0 == iter2->second.output)
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "USU = 0, continue");
							continue;
						}

						ComposePGWCDR(bizMsg,iter2->second,pQuery,*TaiAuCdrIter,field,cdrSeqNum,rg,cdr);
						int len = strlen(cdr.m_body);
						/*
						cdr.m_body[len - 1] = '\r';
						cdr.m_body[len] = '\n';
						cdr.m_body[len + 1] = '\0';
						*/
						cdr.m_body[len - 1] = '\0';

						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr field:\n[%s]", cdr.m_body);
						//发送话单数据
						UCDRData scdr;
						scdr.body = cdr.m_body;
						ProduceCdr(bizMsg, scdr,iter2->second.PayFlag);

                         if(nSize > 1)
                         {
                               // 设置下次开始时间
                               strcpy(iter2->second.sessionStart, iter2->second.sessionCurrent);
                         }
					}
				}
				else
				{
					cdrSeqNum++;
					SCDRData cdr = {0};
					STariffAccumCDRInfo TaiAuCdrInfo;
                    DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
                    SCommonPara* commonPara = smpara->GetCommonPara();
                    char fence = commonPara->CdrFence;
                    SOriChargeInfo tmpOriChargeInfo[100];//扩大一点。避免因数据异常导致core
                    //增强实扣为0的acct_item_Id
                    DCCommonIF::ParseOriChargeInfo(iter2->second.orichargeInfo,tmpOriChargeInfo,fence);
                    TaiAuCdrInfo.acctItemId = tmpOriChargeInfo[0].acctItemId;
					TaiAuCdrInfo.basefee = tmpOriChargeInfo[0].oriAmount;

					iter2->second.nCutnum = 1;
					ComposePGWCDR(bizMsg,iter2->second,pQuery,TaiAuCdrInfo,field,cdrSeqNum,rg,cdr);
					int len = strlen(cdr.m_body);
					//cdr.m_body[len - 1] = '\r';
					//cdr.m_body[len] = '\n';
					//cdr.m_body[len + 1] = '\0';
					cdr.m_body[len - 1] = '\0';






					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cdr field:\n[%s]", cdr.m_body);
					//发送话单数据
					UCDRData scdr;
					scdr.body = cdr.m_body;
					ProduceCdr(bizMsg, scdr,iter2->second.PayFlag);
				}

				iter2->second.seqNum = cdrSeqNum;

				iter2->second.total = nTotal;
 				iter2->second.input = nInPut;
 				iter2->second.output = nOutPut;
				iter2->second.duration = nDuration;
				sprintf(iter2->second.sessionStart, "%ld", nStartTime);
				sprintf(iter2->second.sessionCurrent, "%ld", nCurrTime);

			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "session no exist","");
				continue;
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "select  execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		}

	}
	return 0;
}

//出单成功后更新会话表的使用量等记录信息
int DCBizCdrNormalPGW::updateCdrInfo(STBizMsg* bizMsg,char *sessinID)
{

	UDBSQL *pExecute = NULL;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	pExecute = dbm->GetSQL(PGW_CDR_UpdateSessionInfo);

	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新子会话总时长
			pExecute->DivTable(bizMsg->m_sessionID);
			pExecute->UnBindParam();
			pExecute->BindParam(1, sessinID);
			string sql;
			pExecute->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sql[%s]", sql.c_str());
			pExecute->Execute();
			pExecute->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			pExecute->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select  execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child ok,set balance=NULL,etc");

}

int DCBizCdrNormalPGW::SplitString(const char * str, const char del, std::vector<std::string> & vec)
{
	vec.clear();
    if(!str || *str == 0x0)
	{
		return -1;
	}

    char *pch;
    char szBuf[1024]={0};
    char szTmp[100]={0};
    bool bIsFound = false;

    strcpy(szBuf,str);

    while(1)
    {
    	pch = strchr(szBuf,del);
        if( NULL == pch )
        {
        	break;
        }

        bIsFound = true;

        memset(szTmp,0,sizeof(szTmp));
        strncpy(szTmp,szBuf,pch - szBuf);

        szTmp[pch - szBuf]='\0';
        vec.push_back(szTmp);
        memmove(szBuf,pch + 1,strlen(szBuf)-(pch-szBuf));
	}
    if((true == bIsFound) || strlen(szBuf) > 0)
    {
        vec.push_back(szBuf);
    }
	return 0;
}

int DCBizCdrNormalPGW::ComposePGWCDR(STBizMsg* bizMsg,DataCDRInfo stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,char* rg,SCDRData &cdr)
{
	int nFieldNum = 0;
	char value[BIZ_TEMP_LEN_1024] = {0};
	long long lCdrVersionSerial = 1;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;

	int i = 1;
	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");
	
	vector<SCDRField*>::iterator iter;
	for(iter=field->begin(); iter!=field->end(); iter++)
	{
		if(iCdrVersionType>0)
		{
			if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)))
			{

				char szVersion[32] = {0};
				int iDay = timestampf();
				sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//组装version信息
				strcpy(value, szVersion);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
				i++;
				continue;
			}
		}

		if(0 == strcmp("RE_LNG_CALL_START_TIME", (*iter)->value))
		{
			strcpy(value, stCdrInfo.sessionStart);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("RE_LNG_CURRENT_CCR_TIME", (*iter)->value))
		{
			strcpy(value, stCdrInfo.sessionCurrent);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		//add by wangpx,清单会话id拼接2位截单序列和2位拆单序列
		if(0 == strcmp("OCP_STR_SESSION_ID", (*iter)->value))
		{
			pQuery->GetValue(i, value);
			int seqNum = stCdrInfo.seqNum < 0 ? 0 : stCdrInfo.seqNum;
			sprintf(value,"%s;%02d;%02d",value,seqNum,cdrSeqNum - seqNum - 1);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_TIME", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.duration);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_BILL_CYCLE", (*iter)->value))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%ld",stCdrInfo.Payment);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s",bizMsg->m_xdrsource);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}
		if(0 == strcmp("SM_LNG_ALL_USU_TOTAL_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",(stCdrInfo.total+1023)/1024);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_INPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.input);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_OUTPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.output);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_BALANCEINFO", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.balanceInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_STR_RATING_GROUP", (*iter)->value))
		{
			sprintf(value, "%s", rg);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_TARIFFID", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.tarifInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("OCP_STR_3GPP_RAT_TYPE", (*iter)->value))
		{
			char tmp[BIZ_TEMP_LEN_256] = {0};
			int dest = 0;
			//pQuery->GetValue(i, tmp);
			strcpy(tmp, stCdrInfo.ratType);
			if(0 == strcmp(tmp,"66"))
			{
				dest = 102;
				sprintf(value, "%d", dest);
			}
			else
			{
				sprintf(value, "%s", tmp);
			}
			//hextod(tmp,dest);
			//sprintf(value, "%d", dest);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_CHARGEINFO", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.chargeInfo);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("CDR_PUB_STR_BALANCEINFO2", (*iter)->value))
		{
			sprintf(value, "%s", stCdrInfo.balanceInfo2);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("RE_INT_ROAM_TYPE", (*iter)->value))
		{
			sprintf(value, "%d",  stCdrInfo.nRomatype);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

		if(0 == strcmp("SM_INT_CHARGE_TYPE", (*iter)->value))
		{
			sprintf(value, "%d", stCdrInfo.nChargeType);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}

              /* 按子会话出单
		if(0 == strcmp("OCP_STR_SESSION_ID", (*iter)->value))
		{
			sprintf(value, "%s",bizMsg->m_sessionID);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}*/

		if(0 == strcmp("SM_INT_RESULT_CODE", (*iter)->value))
		{
			sprintf(value, "%d",stCdrInfo.nResultCode);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if((0 == strcmp("DAY", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%s", stCdrInfo.Day);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			continue;
		}

		if(0 == strcmp("CDR_LTE_FLAG",(*iter)->value))
		{
			strcat(cdr.m_body,"1");
			strcat(cdr.m_body,"|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_LTE_FLAG[1]", "");
			//i++;
			continue;

		}
		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d", stCdrInfo.nChargeType == 2? 1 : 2);  // 出时长、流量单位，与邋HB保持一致
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s]", value);
			//i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			sprintf(value,"%s",TaiAuCdrInfo.sz_accumInfo);
			//sprintf(value,"%s", stCdrInfo.accumuInfo);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_STR_ACCUMLATORINFO[%s]", value);
			//i++;
			continue;
		}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		//OCP_STR_ORIGIN_CALLING_NBR
		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);
			continue;
		}
		if(0 == strcmp("CDR_MCC", (*iter)->value))
		{
			if (strcmp(stCdrInfo.ratType,"6") == 0)
			{
				sprintf(value,"%d",460);
				DealwithField(value);
		 		strcat(cdr.m_body, value);
		 	}
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_MCC[%s]  ", value);
			continue;
		}
		if(0 == strcmp("CDR_MNC", (*iter)->value))
		{
			if (strcmp(stCdrInfo.ratType,"6") == 0)
			{
				sprintf(value,"%d",11);
				DealwithField(value);
		 		strcat(cdr.m_body, value);
		 	}
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_MNC[%s] ", value);
			continue;
		}

		if (0 == strcmp("CDR_STR_BSID", (*iter)->value))
		{
			// 4G业务，ratType为6
			if (strcmp(stCdrInfo.ratType,"6") == 0)
			{
				strcpy(value,stCdrInfo.userMsc);
		 	}
		 	else
		 	{
		 		strcpy(value,stCdrInfo.userLocationInfo);
		 	}
			DealwithField(value);
			strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_STR_BSID[%s]", value);
			continue;
		}

		if (0 == strcmp("CDR_STR_TAI_ID", (*iter)->value))
		{
			if (strcmp(stCdrInfo.ratType,"6") == 0)
			{
				strcpy(value,stCdrInfo.LacId);
		 	}
		 	else
		 	{
		 		strcpy(value,stCdrInfo.userMsc);
		 	}
			DealwithField(value);
			strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "add item CDR_STR_TAI_ID[%s]", value);
			continue;
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());
			continue;
		}

		if((*iter)->flag)//name
		{
			//i++;
			nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
			if(nFieldNum)// 特殊清单字段处理
			{
				if(nFieldNum < 9)
				//清单中加入Fee字段或者ACCT_Item_Type_ID字段
					AddFeeItemByRG(stCdrInfo.stFeeItem,&cdr,nFieldNum);
				else
					AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
				//i--;
				continue;
			}
			else
			{
				pQuery->GetValue(i, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
			}
		}
		else if(0 == (*iter)->flag)//default
		{
			strcpy(value, (*iter)->value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
		}
	}

}



