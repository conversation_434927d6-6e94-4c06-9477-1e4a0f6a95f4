/*******************************************
*Copyrights ? 2007，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		OCPMSGDEF.h
*Indentifier：
*		DIC-PRO-SRC-020620-TST(这里只作为示例)
*Description：
*		SM项目组内部定义OCP message
*Version：
*		V1.0
*Author:
*		fu.c
*Finished：
*		2009年8月27日
*History:
*		XXX	2007/11/27	V1.0 文件创建
********************************************/
#ifndef __OCPMSGDEF_H__
#define __OCPMSGDEF_H__


//the message id of ocp
#define SM_OCP_MSG_ID_RAR									2	//RAR消息ID
#define SM_OCP_MSG_ID_RAA									3	//RAA消息ID
#define SM_OCP_MSG_ID_ASR                      				4
#define SM_OCP_MSG_ID_ASA                     				5
#define SM_OCP_ERR_MSG_ID                      				99
#define SM_OCP_MSG_CODE                          			272

//语音会话
#define SM_OCP_MSG_ID_IN_INIT_REQ      				100
#define SM_OCP_MSG_ID_IN_INIT_ANS      				101
#define SM_OCP_MSG_ID_IN_UPDATE_REQ    			102
#define SM_OCP_MSG_ID_IN_UPDATE_ANS    			103
#define SM_OCP_MSG_ID_IN_TERM_REQ      				104
#define SM_OCP_MSG_ID_IN_TERM_ANS      			105
#define SM_OCP_MSG_ID_IN_EVENT_BALANCE_REQ      	106
#define SM_OCP_MSG_ID_IN_EVENT_BALANCE_ANS      	107

//事件 点对点
#define SM_OCP_MSG_ID_P2P_SMS_REQ          			110
#define SM_OCP_MSG_ID_P2P_SMS_ANS          			111
#define SM_OCP_MSG_ID_P2P_SMS_BACK_REQ          	112
#define SM_OCP_MSG_ID_P2P_SMS_BACK_ANS          	113

//PS
#define SM_OCP_MSG_ID_DATA_INIT_REQ      			120
#define SM_OCP_MSG_ID_DATA_INIT_ANS      			121
#define SM_OCP_MSG_ID_DATA_UPDATE_REQ    		122
#define SM_OCP_MSG_ID_DATA_UPDATE_ANS    		123
#define SM_OCP_MSG_ID_DATA_TERM_REQ      			124
#define SM_OCP_MSG_ID_DATA_TERM_ANS      			125
#define SM_OCP_MSG_ID_DATA_XDR_ANS      			126

//ISMP
#define SM_OCP_MSG_ID_ISMP_INIT_REQ      			130
#define SM_OCP_MSG_ID_ISMP_INIT_ANS      			131
#define SM_OCP_MSG_ID_ISMP_UPDATE_REQ    			132
#define SM_OCP_MSG_ID_ISMP_UPDATE_ANS    			133
#define SM_OCP_MSG_ID_ISMP_TERM_REQ      			134
#define SM_OCP_MSG_ID_ISMP_TERM_ANS      			135
#define SM_OCP_MSG_ID_ISMP_EVENT_REQ      			136
#define SM_OCP_MSG_ID_ISMP_EVENT_ANS      			137
#define SM_OCP_MSG_ID_ISMP_EVENT_REFUND_REQ      138
#define SM_OCP_MSG_ID_ISMP_EVENT_REFUND_ANS      139
#define SM_OCP_MSG_ID_ISMP_EVENT_BALANCE_REQ 	140
#define SM_OCP_MSG_ID_ISMP_EVENT_BALANCE_ANS	141

//IMS业务
#define SM_OCP_MSG_ID_IMS_INIT_REQ      		150
#define SM_OCP_MSG_ID_IMS_INIT_ANS      		151
#define SM_OCP_MSG_ID_IMS_UPDATE_REQ    		152
#define SM_OCP_MSG_ID_IMS_UPDATE_ANS    		153
#define SM_OCP_MSG_ID_IMS_TERM_REQ      		154
#define SM_OCP_MSG_ID_IMS_TERM_ANS      		155

//ratable query
#define SM_OCP_MSG_ID_ISMP_RATABLE_REQ 	142
#define SM_OCP_MSG_ID_ISMP_RATABLE_ANS	143

//DSL
#define SM_OCP_MSG_ID_DSL_INIT_REQ     				160
#define SM_OCP_MSG_ID_DSL_INIT_ANS     			161
#define SM_OCP_MSG_ID_DSL_UPDATE_REQ     			162
#define SM_OCP_MSG_ID_DSL_UPDATE_ANS     			163
#define SM_OCP_MSG_ID_DSL_TERM_REQ     			164
#define SM_OCP_MSG_ID_DSL_TERM_ANS     			165

//SR相关定义
#define SM_OCP_MSG_ID_SR_CONGEST_CCR                     166
#define SM_OCP_MSG_ID_SR_CONGEST_CCA                     167

//激活AVP
#define SM_OCP_MSG_ID_SR_ACTive_CCR                     168
#define SM_OCP_MSG_ID_SR_ACTive_CCA                     169

//去激活AVP
#define SM_OCP_MSG_ID_SR_UnACTive_CCR                     170
#define SM_OCP_MSG_ID_SR_UnACTive_CCA                     171

//负载均衡通知
#define SM_OCP_MSG_ID_SR_LOADOVER_CCR                     172
#define SM_OCP_MSG_ID_SR_LOADOVER_CCA                     173


//Requested-Action
#define SM_REQUESTED_ACTION_DEBIT 	0
#define SM_REQUESTED_ACTION_REFUND 	1
#define SM_REQUESTED_ACTION_CHECK 	2
#define SM_REQUESTED_ACTION_QUERY_RATABLE_1 	113
#define SM_REQUESTED_ACTION_QUERY_RATABLE_2 	115

//session state
#define SM_SESSION_INITIAL_CODE  			1
#define SM_SESSION_INITIAL_STRING 			"1"
#define SM_SESSION_UPDATE_CODE 			2
#define SM_SESSION_UPDATE_STRING 			"2"
#define SM_SESSION_TERMINATION_CODE 		3
#define SM_SESSION_TERMINATION_STRING 	"3"
#define SM_SESSION_EVENT_CODE 			4
#define SM_SESSION_EVENT_STRING 			"4"
#define SM_SESSION_XDR_CODE 	5
#define SM_SESSION_EVENT_REFUND_CODE 	5
#define SM_SESSION_EVENT_REFUND_STRING 	"5"
#define SM_SESSION_EVENT_BALANCE_CODE 	6
#define SM_SESSION_EVENT_BALANCE_STRING "6"
#define SM_SESSION_UPDATE_FIRST_CODE 	7
#define SM_SESSION_UPDATE_FIRST_STRING "7"


#define RB_OCP_MSG_ID      "1"
#define OCP_MSG_ID_IN_INIT_ANS_STR          "101"
#define OCP_MSG_ID_IN_UPDATE_ANS_STR     	"103"
#define OCP_MSG_ID_IN_TERM_ANS_STR       	"105"
#define OCP_MSG_ID_IN_EVENT_BALANCE_ANS_STR "107"

#define SM_OCP_MSG_ID_P2P_SMS_ANS_STR	"111"
#define SM_OCP_MSG_ID_P2P_SMS_BACK_ANS_STR          "113"

#define SM_OCP_MSG_ID_DATA_INIT_ANS_STR      		"121"
#define SM_OCP_MSG_ID_DATA_UPDATE_ANS_STR    		"123"
#define SM_OCP_MSG_ID_DATA_TERM_ANS_STR      		"125"


#define SM_OCP_MSG_ID_ISMP_INIT_ANS_STR 			"131"
#define SM_OCP_MSG_ID_ISMP_UPDATE_ANS_STR    			"133"
#define SM_OCP_MSG_ID_ISMP_TERM_ANS_STR      			"135"
#define SM_OCP_MSG_ID_ISMP_EVENT_ANS_STR      			"137"
#define SM_OCP_MSG_ID_ISMP_EVENT_REFUND_ANS_STR      "139"
#define SM_OCP_MSG_ID_ISMP_EVENT_BALANCE_ANS_STR	"141"
#endif

