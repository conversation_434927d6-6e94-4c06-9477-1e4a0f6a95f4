/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdrNormalDSL.h
*Indentifier：
*		
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef _DCBIZ_CDR_NORMAL_DSL_H_
#define _DCBIZ_CDR_NORMAL_DSL_H_

#include "DCBizCdrNormal.h"
#include "TCDRDict.h"


class DCBizCdrNormalDSL:public DCBizCdrNormal
{
	public:

		DCBizCdrNormalDSL();
		~DCBizCdrNormalDSL();
		
	protected:		
		
		int ComposeDSL(STBizMsg* bizMsg);
		int PretreatDSL(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo);		
		int ComposeDSLCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr);
};

#endif


