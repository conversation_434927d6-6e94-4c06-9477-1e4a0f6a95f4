﻿#include "DCReqPGW.h"
#include "BizLenDef.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "TConfig.h"
#include "DCSeriaOp.h"
#include "DCLogMacro.h"
#include "BizCdrDef.h"
#include "func_sqlindex.h"
#include "DCRbMsgDef.h"
#include "REMsgTypeDef.h"
#include "BizCdrDefTEL.h"
#include "DCCommonIF.h"
#include "UHead.h"
#include <time.h>


static long time2sec(long date)
{
    //20101001231105
    time_t tsec = 0;
    if((date/10100101010101)>=1)
    {
        struct tm strtime = {0};
        strtime.tm_year = date/10000000000-1900;
        strtime.tm_mon = (date%10000000000)/100000000-1;
        strtime.tm_mday = (date%100000000)/1000000;
        strtime.tm_hour = (date%1000000)/10000;
        strtime.tm_min = (date%10000)/100;
        strtime.tm_sec = date%100;

        tsec = mktime(&strtime);
    }
    else
    {
        ;
    }
     return tsec;
}

DCReqPGW::DCReqPGW()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

DCReqPGW::~DCReqPGW()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "", "");
}

int DCReqPGW::SwitchReqType(STBizMsg* bizMsg)
{
	if(!bizMsg->m_base)
	{
		return -1;
	}
	int ret = RET_SUCCESS;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	SCCRDATA* data =(SCCRDATA*)bizMsg->m_extend;


	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				ret = Init(base, data, bizMsg);
			}
			break;
		case SM_SESSION_UPDATE_CODE:
			{
				ret = Update(base, data, bizMsg);
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				ret = Term(base, data, bizMsg);
			}
			break;
		case SM_SESSION_XDR_CODE:
		{
			ret = XdrEvent(base, data, bizMsg);
		}
		break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "invalid request type[%s]", bizMsg->m_requestType);
				ret = SM_OCP_INVALID_AVP_VALUE_USELESS_CCR;
			}
			break;
	}

	return ret;
}

int DCReqPGW::Init(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int resultCode 						= 0;
	int traceNumOnff					= 0;
	long nextCCTime						= 0;
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	int ServiceScenarious               = 700;
	int nFreeFlag                       = 0;
	TSMPara *smpara                     = bizMsg->m_smpara;
	TSERVICE_QUOTA_CONF *conf			= NULL;
	SUserInfo *userInfo					= (SUserInfo*)bizMsg->m_userinfo;
	DCDBManer* dbm 						= (DCDBManer*)bizMsg->m_dbm;
	AREA_INFO *subVisit					= bizMsg->m_visit;
	int roamType						= bizMsg->m_roamtype;
	string iCreatFlag;
	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}

	//ServiceScenarious
    if(0==data->RATType.compare("32876"))
    {
       ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
    }
	else if(0==data->RATType.compare("59"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
	}
	else if(0==data->RATType.compare("33"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}
	else if((0 == data->RATType.length()) && (data->userLocationInfo.length() != 0) && (0 == data->userLocationInfo.compare(5,2,"EE")))
	{
	    ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
	}
	else if((0 == data->RATType.length()) && (0 == data->userLocationInfo.length()))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}
	else
	{
	    ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}

	long valid_time = 0;
	if((conf = smpara->GetServiceQuotaConf(PGW_SESSION_RELEASE_RATING)) == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "no find default -5,default valid-time is 300s","");
		valid_time=300;
	}
	else
	{
		valid_time=conf->VALID_TIME;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get valid time:%d",conf->VALID_TIME);
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + valid_time;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",bizMsg->timestampCCR);


	UDBSQL *pExec =  dbm->GetSQL(PGW_CCR_Init_InsertSession);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1,  SM_CDR_SEQ_NUM);
		pExec->BindParam(2,  SM_CDR_VERSION);
		pExec->BindParam(3,  SM_CDR_TICKETTYPE);//CDR_PUB_INT_TICKETTYPE
		pExec->BindParam(4,  timeStamp);//CDR_PUB_STR_TIMESTAMP
		pExec->BindParam(5,  base->topology);
		pExec->BindParam(6,  0);//CDR_PUB_INT_CORRELATIONID
		pExec->BindParam(7,  0);//CDR_PUB_INT_TICKETSEQUENCEID
		pExec->BindParam(8,  ServiceScenarious);//CDR_PUB_INT_SERVICESCENARIOUS
		pExec->BindParam(9,  base->subUnified);//CDR_PUB_STR_CHARGED_PARTY
		pExec->BindParam(10, userInfo->servID);
		pExec->BindParam(11, userInfo->custID);
		pExec->BindParam(12, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(13, bizMsg->m_sessionID);
		pExec->BindParam(14, SM_SESSION_INITIAL_CODE);
		pExec->BindParam(15, (int)bizMsg->m_requestNumber);
		pExec->BindParam(16, base->originHost);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->subscription.phone);

		sprintf(value, "0%d", base->subscription.area);
		pExec->BindParam(19, value);

		if(data->visitArea)
		{
			sprintf(value, "0%d", data->visitArea);
		}
		else
		{
			if(subVisit->area)
			{
				sprintf(value, "0%d", subVisit->area);
			}
			else
			{
				value[0] = '\0';
			}
		}

		pExec->BindParam(20, value);

		pExec->BindParam(21, base->subscription.carriers);
		pExec->BindParam(22, 1);
		pExec->BindParam(23, (long)bizMsg->timestampCCR);
		pExec->BindParam(24, (long)bizMsg->timestampCCR);
		pExec->BindParam(25, roamType);
		pExec->BindParam(26, (long)bizMsg->m_serial);
		pExec->BindParam(27, userInfo->aocType);
		pExec->BindParam(28, STATUS_IDLE);
		pExec->BindParam(29, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;//IsTraceNum(atol(base->subscription.phone));
		pExec->BindParam(30, traceNumOnff);
		if(0 != strcmp(userInfo->IMSI, ""))
		{
			pExec->BindParam(31, userInfo->IMSI);
		}
		else
		{
			value[0] = '\0';
			pExec->BindParam(31, value);
		}
		pExec->BindParam(32, valid_time);
		pExec->BindParam(33, (long)0);  // SM_LNG_QUOTA_CONSUME_TIME
		pExec->BindParam(34, (long)0);  // SM_LNG_VOLUME_QUOTA_THRESHOLD
		pExec->BindParam(35, (long)0);  // SM_LNG_TIME_QUOTA_THRESHOLD
		pExec->BindParam(36, (long)0);  // SM_LNG_QUOTA_HOLDING_TIME
		pExec->BindParam(37, data->GGSNAddress);//OCP_STR_IP_GGSN_ADDR,lte保存sgsn

		pExec->BindParam(38, data->SGSNAddress);
		pExec->BindParam(39, data->APN);
		pExec->BindParam(40, data->PDPType);
		pExec->BindParam(41, data->PDPAddress);
		pExec->BindParam(42, (long)0);			//OCP_INT_RATING_GROUP
		pExec->BindParam(43, data->QOS);
		pExec->BindParam(44, data->userLocationInfo);
		pExec->BindParam(45, data->RATType);
		pExec->BindParam(46, data->chargingID);
		pExec->BindParam(47, userInfo->isActive);
		pExec->BindParam(48, atol(timeStamp)); //作为上次 CCR 的时间
		pExec->BindParam(49, 0);							//	用来标识主会话
		pExec->BindParam(50, data->PDSNAddress);       //PDSN
		pExec->BindParam(51, data->userCellid);        //CELLID
		pExec->BindParam(52, data->userLac);           //MCS
		pExec->BindParam(53, data->userLocationInfo);  //userLocation_Info
		pExec->BindParam(54, (long)base->timestamp);
		pExec->BindParam(55, nFreeFlag);

		pExec->BindParam(56, data->QoSClassIdentifier);  //userLocation_Info
		pExec->BindParam(57, data->gppChargingID);
		pExec->BindParam(58, userInfo->userType);
		pExec->BindParam(59, userInfo->mvnoID);
		pExec->BindParam(60, (int)bizMsg->m_serviceContextID);
		pExec->BindParam(61, data->userMsc);
		pExec->BindParam(62, userInfo->ilatnid);
		pExec->BindParam(63, userInfo->lnAcctID);
		pExec->BindParam(64, bizMsg->m_szServiceContextIDStr);
		pExec->BindParam(65, bizMsg->addressIpv6);
		pExec->BindParam(66, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		string sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_UNABLE_TO_COMPLY,"", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "insert ok", "");


	bizMsg->m_resultcode = 2001;
	ret = sendInitCCA(bizMsg);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "send CCA ok");

	return ret;
}

int DCReqPGW::Update(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	long nextCCTime							= 0;
	long lastCCRTime 						= 0;
	long lastRsuObject               		= 0;
	long lastRsuTime 			  			= 0;
	char ChildSessionId[BIZ_TEMP_LEN_256] 	= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};

	int rgnumber                            = 0;
    int nSendCCAFlag						= 0;
	char szNewLocInfo[BIZ_DATA_LEN_128]		= {0};
	ocs::SCCRDataUnit TUSU;
	ocs::SCCRDataUnit TOUSU;
	TSERVICE_QUOTA_CONF *conf				= NULL;
	ocs::SUSU *MSCC								= NULL;
	SUserInfo *userInfo					= (SUserInfo*)bizMsg->m_userinfo;

	SSessionCacheData  cacheData        ;
	TSMPara *smpara = bizMsg->m_smpara;
	DCDBManer* dbm 						= (DCDBManer*)bizMsg->m_dbm;
	int roamType						= bizMsg->m_roamtype;

	//PLCA_LOC
	char PLCA_LOC[24]={0};
	char szOldPLCA_LOC[24]={0};
	char PLCA_CELLID[20]={0};
	char PLCA_MSC[20]={0};
	char PLCA_RAT_TYPE[8]={0};

	if(base->MSCC.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "missing MSCC", "");
		return SM_OCP_MISSING_AVP;
	}

	std::vector<long> v_rg;
	for(int i=0; i<base->MSCC.size(); i++)
	{
		MSCC = &(base->MSCC[i]);
		if(!MSCC)
		{
			continue;
		}
		v_rg.push_back(MSCC->ratingGroup);
	}

	bizMsg->m_vectorMsg.clear();
	for(int i=0; i<base->MSCC.size(); i++)
	{
		MSCC = &(base->MSCC[i]);
		if(!MSCC)
		{
			continue;
		}
		memset(&cacheData, 0x0, sizeof(cacheData));


		if(1 ==smpara->GetPSPara()->nRatTypeSwitch)
		{
			if(0 == data->RATType.compare("32876"))
			{
			   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
			}
			else if(0==data->RATType.compare("59"))
			{
			   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
			}
			else if(0==data->RATType.compare("33"))
			{
			   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
			}
			else if(0 == data->RATType.length())
			{
			    cacheData.ServiceScenarious = 0;
			}
			else
			{
			    cacheData.ServiceScenarious = 0;
			}
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ServiceScenarious [%d]", cacheData.ServiceScenarious);

		//子会话ID
		if(MSCC->ProductOfferId.length() != 0)//如果网元上报Product-Offer-Id，按此来批价
		{
			sprintf(ChildSessionId, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
			strcpy(bizMsg->m_ProductOfferId, MSCC->ProductOfferId.c_str());
		}
		else
		{
			sprintf(ChildSessionId, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
			bizMsg->m_ratingGroup = MSCC->ratingGroup;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", ChildSessionId);

		ret = composeRER(bizMsg,MSCC,cacheData, v_rg);
		if(SM_OCP_UNKNOWN_SESSION_ID == ret && bizMsg->m_resultcode == 5002)
		{
			bizMsg->m_requestType = SM_SESSION_UPDATE_FIRST_CODE;
			ret = FirstUpdate(base, data, bizMsg, MSCC);
			if(0 != ret)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "compose FirstUpdate RER failed[%d]", ret);
				return ret;
			}
			
			bizMsg->m_requestType = SM_SESSION_UPDATE_CODE;
			continue;			
		}
		else if(0!=ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "compose RER failed[%d]", ret);
			return ret;
		}

		//当前时间用于会话超时
		time_t et;
		time(&et);
		nextCCTime = et + cacheData.valid_time;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "next cc time[%ld]", nextCCTime);
		sprintf(timeStamp,"%ld",base->timestamp);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "USU0 duration[%d],unitTotal[%ld],unitInput[%ld],unitOutput[%ld]", MSCC->USU0.duration,MSCC->USU0.unitTotal,MSCC->USU0.unitInput,MSCC->USU0.unitOutput);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "USU1 duration[%d],unitTotal[%ld],unitInput[%ld],unitOutput[%ld]", MSCC->USU1.duration,MSCC->USU1.unitTotal,MSCC->USU1.unitInput,MSCC->USU1.unitOutput);

		// 4G 位置变更截单
		sprintf(szNewLocInfo, "%s|%s|%s|%s|%s|%d|%s|%s|%s|%s|%s",
						data->SGSNAddress.c_str(), data->userCellid.c_str(), data->userMsc.c_str(), data->userLac.c_str(),
						data->userLocationInfo.c_str(), cacheData.n_RE_INT_ROAM_TYPE, cacheData.sz_RE_STR_SUB_VISIT_AREA,
						data->GGSNAddress.c_str(), bizMsg->m_eventType, bizMsg->m_visit->sector_id,data->RATType.c_str());
	 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "szNewLocInfo[%s]", szNewLocInfo);

		// 更新子会话
		UDBSQL *pUpdate = dbm->GetSQL(PGW_UpdateSessionStore_RG);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{

				pUpdate->DivTable(bizMsg->m_sessionID);
				pUpdate->UnBindParam();
				pUpdate->BindParam(1, SM_SESSION_UPDATE_CODE);
				pUpdate->BindParam(2, (int)bizMsg->m_requestNumber);
				pUpdate->BindParam(3, TORB_ACTION);
				pUpdate->BindParam(4, (long)bizMsg->m_serial);
				pUpdate->BindParam(5, nextCCTime);
				pUpdate->BindParam(6, (long)bizMsg->timestampCCR);
				pUpdate->BindParam(7, (long)(MSCC->USU0.duration + MSCC->USU1.duration)); // 把所有使用量都存到数据库中
				pUpdate->BindParam(8, (long)(MSCC->USU0.unitTotal + MSCC->USU1.unitTotal));
				pUpdate->BindParam(9, (long)(MSCC->USU0.unitInput + MSCC->USU1.unitInput));
				pUpdate->BindParam(10, (long)(MSCC->USU0.unitOutput + MSCC->USU1.unitOutput));

				pUpdate->BindParam(11, base->timestamp);
				pUpdate->BindParam(12, cacheData.lastCCRTime);
				pUpdate->BindParam(13, cacheData.valid_time);
				pUpdate->BindParam(14, szNewLocInfo);
				pUpdate->BindParam(15, cacheData.ServiceScenarious);
				pUpdate->BindParam(16, cacheData.sz_OCP_STR_3GPP_RAT_TYPE);
				pUpdate->BindParam(17, bizMsg->m_sBatchId.c_str());
				pUpdate->BindParam(18, ChildSessionId);
				pUpdate->Execute();
				pUpdate->Connection()->Commit();
				success = true;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update chilid ok,session[%s]", ChildSessionId);
			}
			catch (UDBException &e)
			{
				pUpdate->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "update execption[%s],SQL[%s],SQLCODE[%d], error code[%d]", e.ToString(), e.GetSqlInfo(), e.GetSqlCode(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_OCP_UNABLE_TO_COMPLY, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			}
		}
	}

	//设置需发送的RG 数目
	if(bizMsg->m_vectorMsg.size() > 0)
	{
		rgnumber = (int)bizMsg->m_vectorMsg.size();
		UDBSQL *Update = dbm->GetSQL(PGW_U_SEND_RGNUM);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				Update->DivTable(bizMsg->m_sessionID);
				Update->UnBindParam();
				Update->BindParam(1, rgnumber);
				Update->BindParam(2, bizMsg->m_sessionID);
				Update->Execute();
				Update->Connection()->Commit();
				success = true;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update main ok, rgnum[%d]", rgnumber);
			}
			catch (UDBException &e)
			{
				Update->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update main execption, exp[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			}
		}
	}

	return RET_SUCCESS;
}

int DCReqPGW::FirstUpdate(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg, ocs::SUSU *MSCC)
{
	int ret 								= RET_SUCCESS;
	long lastCCRTime 						= 0;
	long nextCCTime							= 0;
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char ChildSessionId[BIZ_TEMP_LEN_256] 	= {0};
	char timeStamp[BIZ_TEMP_LEN_16]			= {0};
	char buf[BIZ_TEMP_LEN_1024] 			= {0};
	ocs::SCCRDataUnit TUSU 						;
	SUserInfo *userInfo					= (SUserInfo*)bizMsg->m_userinfo;
	SSessionCacheData  cacheData        ;
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* dbm 						= (DCDBManer*)bizMsg->m_dbm;

	//PLCA_LOC
	char PLCA_LOC[24]={0};
	char PLCA_CELLID[20]={0};
	char PLCA_MSC[20]={0};
	char PLCA_RAT_TYPE[8]={0};

	std::vector<long> v_rg;
	ocs::SUSU *MSCCtmp;
	for(int i=0; i<base->MSCC.size(); i++)
	{
		MSCCtmp = &(base->MSCC[i]);
		if(!MSCCtmp)
		{
			continue;
		}
		v_rg.push_back(MSCCtmp->ratingGroup);
	}

	if(1 == smpara->GetPSPara()->nRatTypeSwitch)
	{
		if(0 == data->RATType.compare("32876"))
		{
		   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
		}
		else if(0 == data->RATType.compare("59"))
		{
		   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
		}
		else if(0 == data->RATType.compare("33"))
		{
		   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
		}
		else if(0 == data->RATType.length())
		{
		    cacheData.ServiceScenarious = 0;
		}
		else
		{
		    cacheData.ServiceScenarious = 0;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ServiceScenarious [%d]", cacheData.ServiceScenarious);

	//子会话ID
	if(MSCC->ProductOfferId.length() != 0)//如果网元上报Product-Offer-Id，按此来批价
	{
		sprintf(ChildSessionId, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
		strcpy(bizMsg->m_ProductOfferId, MSCC->ProductOfferId.c_str());
	}
	else
	{
		sprintf(ChildSessionId, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
		bizMsg->m_ratingGroup = MSCC->ratingGroup;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "firstupdate child session[%s]", ChildSessionId);

	ret = composeRER(bizMsg,MSCC,cacheData,v_rg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RER failed[%d]", ret);
		return ret;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + cacheData.valid_time;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);


	//插入子会话
	UDBSQL *pExec =  dbm->GetSQL(PGW_CCR_FirstUpdate_InsertSession_Child);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, ChildSessionId);
		pExec->BindParam(2, SM_SESSION_UPDATE_FIRST_CODE);
		pExec->BindParam(3, (int)bizMsg->m_requestNumber);
		pExec->BindParam(4, (long)bizMsg->m_serial);
		pExec->BindParam(5, (long)MSCC->ratingGroup);
		pExec->BindParam(6,  nextCCTime);
		pExec->BindParam(7, (long)cacheData.valid_time);
		pExec->BindParam(8, (long)cacheData.conf_QUOTA_CONSUMPTION_TIME);
		pExec->BindParam(9, (long)cacheData.conf_VOLUME_QUOTA_THRESHOLD);
		pExec->BindParam(10, (long)cacheData.conf_TIME_QUOTA_THRESHOLD);
		pExec->BindParam(11, (long)cacheData.conf_QUOTA_HOLDING_TIME);
		pExec->BindParam(12, (long)bizMsg->timestampCCR);
		pExec->BindParam(13, (long)bizMsg->timestampCCR);
		pExec->BindParam(14, base->subscription.phone);
		if(cacheData.rsu_totalOCtets)
		{
			pExec->BindParam(15, (long)cacheData.rsu_totalOCtets); //OCP_LNG_GSU_TOTAL_OCT
		}
		else
		{
			pExec->BindParam(15, (long)0);
		}
		if(cacheData.rsu_duration)
		{
			pExec->BindParam(16, (long)cacheData.rsu_duration); //OCP_LNG_GSU_TIME
		}
		else
		{
			pExec->BindParam(16, (long)0);
		}
		pExec->BindParam(17, lastCCRTime);
		pExec->BindParam(18, base->topology);
		pExec->BindParam(19, base->routeRecord);
		pExec->BindParam(20, 1);				//	用来标识子会话
		pExec->BindParam(21, (long)cacheData.conf_VOLUME_QUOTA_THRESHOLD1);
		pExec->BindParam(22, (long)base->timestamp);
		pExec->BindParam(23, cacheData.nAocType); //SM_INT_AOC_TYPE
		pExec->BindParam(24, bizMsg->m_trace_flag); //SM_TRACE_NUM_ONFF
		pExec->BindParam(25, bizMsg->m_ProductOfferId); //

		//add by linux optimize
		pExec->BindParam(26, SM_CDR_SEQ_NUM); //CDR_PUB_INT_SEQ ,
		pExec->BindParam(27, SM_CDR_VERSION); // CDR_PUB_INT_VERSION CDR_PUB_INT_TICKETTYPE,
		pExec->BindParam(28, SM_CDR_TICKETTYPE); //CDR_PUB_INT_VERSION ,
		pExec->BindParam(29, timeStamp); //CDR_PUB_STR_TIMESTAMP,
		pExec->BindParam(30, cacheData.n_CDR_PUB_INT_CORRELATIONID); //CDR_PUB_INT_CORRELATIONID,
		pExec->BindParam(31, cacheData.n_CDR_PUB_INT_TICKETSEQUENCEID); //CDR_PUB_INT_TICKETSEQUENCEID,
		pExec->BindParam(32, cacheData.ServiceScenarious); //CDR_PUB_INT_SERVICESCENARIOUS,
		pExec->BindParam(33, cacheData.sz_CDR_PUB_STR_CHARGED_PARTY); //CDR_PUB_STR_CHARGED_PARTY,
		pExec->BindParam(34, cacheData.l_CDR_PUB_LNG_SERVID); //CDR_PUB_LNG_SERVID,
		pExec->BindParam(35, cacheData.l_CDR_PUB_LNG_CUSTID); //	 CDR_PUB_LNG_CUSTID,
		pExec->BindParam(36, cacheData.sz_CDR_PUB_STR_MASTERPRODUCTID); //CDR_PUB_STR_MASTERPRODUCTID,
		pExec->BindParam(37, cacheData.sz_OCP_STR_ORIGIN_HOST); //OCP_STR_ORIGIN_HOST,
		pExec->BindParam(38, cacheData.sz_RE_STR_SUB_AREA); //RE_STR_SUB_AREA,
		pExec->BindParam(39, cacheData.sz_RE_STR_SUB_VISIT_AREA); //RE_STR_SUB_VISIT_AREA,
		pExec->BindParam(40, cacheData.n_RE_INT_SUB_OPERATOR); //RE_INT_SUB_OPERATOR,
		pExec->BindParam(41, 1); //RE_INT_PAY_FLAG,
		pExec->BindParam(42, cacheData.n_RE_INT_ROAM_TYPE); //RE_INT_ROAM_TYPE,
		pExec->BindParam(43, TORB_ACTION); //SM_INT_SESSION_STATUS,
		pExec->BindParam(44, cacheData.sz_SM_STR_IMSI); //SM_STR_IMSI,
		pExec->BindParam(45, data->GGSNAddress); //	 OCP_STR_IP_GGSN_ADDR,
		pExec->BindParam(46, data->SGSNAddress); //OCP_STR_IP_SGSN_ADDR,
		pExec->BindParam(47, cacheData.sz_OCP_STR_APN_INFO); //OCP_STR_APN_INFO,
		pExec->BindParam(48, cacheData.sz_OCP_STR_PDP_TYPE); //OCP_STR_PDP_TYPE,
		pExec->BindParam(49, cacheData.sz_OCP_STR_IP_PDP_ADDR); //OCP_STR_IP_PDP_ADDR,
		pExec->BindParam(50, cacheData.sz_OCP_STR_CPRS_QOS); //OCP_STR_CPRS_QOS,
		pExec->BindParam(51, cacheData.sz_OCP_STR_LAC_CODE); //OCP_STR_LAC_CODE,
		pExec->BindParam(52, cacheData.sz_OCP_STR_3GPP_RAT_TYPE); //OCP_STR_3GPP_RAT_TYPE,
		pExec->BindParam(53, cacheData.sz_OCP_STR_CDMA_CHARGING_ID); //OCP_STR_CDMA_CHARGING_ID,
		pExec->BindParam(54, cacheData.n_RE_INT_ISACTIVE_FLAG); //RE_INT_ISACTIVE_FLAG,
		pExec->BindParam(55, cacheData.sz_OCP_STR_IP_PDSN_ADDR); //	 OCP_STR_IP_PDSN_ADDR,
		pExec->BindParam(56, data->userCellid); //CDR_STR_CELLID,
		pExec->BindParam(57, data->userLac); //CDR_STR_LAC,
		pExec->BindParam(58, data->userLocationInfo); //CDR_User_Location_Info,
		pExec->BindParam(59, cacheData.n_SM_INT_FREE_FLAG); //SM_INT_FREE_FLAG,
		pExec->BindParam(60, cacheData.n_OCP_INT_CLASS_IDENTIFIER); //OCP_INT_CLASS_IDENTIFIER,
		pExec->BindParam(61, cacheData.sz_OCP_STR_3GPP_CHARGING_ID); //OCP_STR_3GPP_CHARGING_ID,
		pExec->BindParam(62, cacheData.sz_CDR_STR_USER_TYPE); //CDR_STR_USER_TYPE,
		pExec->BindParam(63, cacheData.sz_CDR_STR_MVNO_ID); //CDR_STR_MVNO_ID,
		pExec->BindParam(64, cacheData.n_SM_INT_SESSION_TYPE); //SM_INT_SESSION_TYPE
		pExec->BindParam(65, base->topology);//CDR_PUB_STR_HOSTID_HISTORY
		pExec->BindParam(66, data->userMsc);//CDR_STR_MSC
		pExec->BindParam(67, cacheData.n_CDR_INT_LATN_ID);
		pExec->BindParam(68, cacheData.ln_CDR_ACCT_ID);
		pExec->BindParam(69, PLCA_LOC);
		sprintf(value,"%ld",MSCC->ratingGroup);
		pExec->BindParam(70, value);
		pExec->BindParam(71, bizMsg->m_szServiceContextIDStr);
        sprintf(value, "%s|%s", bizMsg->m_eventType, bizMsg->m_visit->sector_id);
        pExec->BindParam(72, value); //  OCP_OLD_LOCATION_EXT
        pExec->BindParam(73, cacheData.sz_OCP_STR_IP_PDP_ADDRIpv6); //OCP_STR_IP_PDP_ADDR_IPV6,
		pExec->BindParam(74, cacheData.szCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();
	}
	catch(UDBException& e)
	{
		string sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "insert ok,child session[%s]", ChildSessionId);

	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec = dbm->GetSQL(PGW_UpdateMainSessionNum);
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_sessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY, "", "SQL[%s]", sql.c_str());

			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "insert execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			return SM_OCP_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update session rgnumer ok  session[%s]", bizMsg->m_sessionID);

	return RET_SUCCESS;
}


int DCReqPGW::Term(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	long nextCCTime							= 0;
	int rgnumber							= 0;			//正常RG数
	ocs::SUSU* MSCC								= NULL;
	char ChildSessionId[BIZ_TEMP_LEN_256] 	= {0};
	ocs::SCCRDataUnit TUSU 						;//子会话总使用量
	ocs::SCCRDataUnit TOUSU 						;//主会话总使用量
	int nCdrLongTime 						= 0;
	int nSkipRgNum 							= 0;

	char szNewLocInfo[BIZ_DATA_LEN_128]		= {0};
	SSessionCacheData  cacheData           ;
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* dbm 						= (DCDBManer*)bizMsg->m_dbm;

	if(1 == smpara->GetPSPara()->nRatTypeSwitch)
	{
		if(0 == data->RATType.compare("32876"))
		{
		   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
		}
		else if(0 == data->RATType.compare("59"))
		{
		   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
		}
		else if(0 == data->RATType.compare("33"))
		{
		   cacheData.ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
		}
		else if(0 == data->RATType.length())
		{
		    cacheData.ServiceScenarious = 0;
		}
		else
		{
		    cacheData.ServiceScenarious = 0;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ServiceScenarious [%d]", cacheData.ServiceScenarious);

	std::vector<long> v_rg;
	for(int i=0; i<base->MSCC.size(); i++)
	{
		MSCC = &(base->MSCC[i]);
		if(!MSCC)
		{
			continue;
		}
		v_rg.push_back(MSCC->ratingGroup);
	}

	bizMsg->m_vectorMsg.clear();
	for(int i=0; i<base->MSCC.size(); i++)
	{
		MSCC = &(base->MSCC[i]);
		if(!MSCC)
		{
			continue;
		}

		//子会话ID
		memset(ChildSessionId,0,sizeof(ChildSessionId));
		if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
		{
			sprintf(ChildSessionId, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
		}
		else
		{
			sprintf(ChildSessionId, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", ChildSessionId);


		//若有2组USU，则在指定情况下设置2个USU的值
		if(1 == MSCC->USU0.unused && 1 == MSCC->USU1.unused)
		{
			if(1 == MSCC->USU0.tariffChangeUsage && 0 == MSCC->USU1.tariffChangeUsage)
			{
				SCCRDataUnit temp = MSCC->USU0;
				MSCC->USU0 = MSCC->USU1;
				MSCC->USU1 = temp;
			}
			else if(0 == MSCC->USU0.tariffChangeUsage && 0 == MSCC->USU1.tariffChangeUsage)
			{
				MSCC->USU0.duration += MSCC->USU1.duration;
				MSCC->USU0.unitInput += MSCC->USU1.unitInput;
				MSCC->USU0.unitOutput += MSCC->USU1.unitOutput;
				MSCC->USU0.unitTotal += MSCC->USU1.unitTotal;
			}
			else if(1 == MSCC->USU0.tariffChangeUsage && 1 == MSCC->USU1.tariffChangeUsage)
			{
				MSCC->USU1.duration += MSCC->USU0.duration;
				MSCC->USU1.unitInput += MSCC->USU0.unitInput;
				MSCC->USU1.unitOutput += MSCC->USU0.unitOutput;
				MSCC->USU1.unitTotal += MSCC->USU0.unitTotal;
			}
		}
		else
		{
			if(1 == MSCC->USU0.tariffChangeUsage)
			{
				SCCRDataUnit temp ;
				MSCC->USU1 = MSCC->USU0;
				MSCC->USU0 = temp;
			}
		}

		ret = composeRER(bizMsg,MSCC,cacheData,v_rg);
		if(SM_OCP_UNKNOWN_SESSION_ID == ret)
		{
			/*//全部为非法RG时，返回给网元，未知的会话ID
			if((i == (base->MSCC.size() - 1)) &&  (0 == rgnumber))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  bizMsg->m_sessionID, "ept,all rg is invalid,i[%d],msccsize[%d],ChildSessionId[%s]", i,base->MSCC.size(),ChildSessionId);
				//返回网元5002消息
				bizMsg->m_resultcode = 5002;
				sendTermCCAWithOutRG(base, data, bizMsg);
				return RET_CDR;
			}
			else
			{
				continue;
			}*/
			continue;
		}
		else if(ret)
		{
			return ret;
		}
		else
		{
			/* 多RG场景update过程不再发送term消息 */
			/*/过程中余额不足的RG已经发送term RBR，这里不需要再次发送
			if(2== cacheData.nsendCCAFlag)
			{
				nSkipRgNum ++;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSendCCAFlag[%d],this rg[%ld] skip", cacheData.nsendCCAFlag,MSCC->ratingGroup);
				continue;
			}*/
		}
		rgnumber++;
		//当前时间用于会话超时
		time_t et;
		time(&et);
		nextCCTime = et + cacheData.valid_time;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "next cc time[%ld]", nextCCTime);
		// sprintf(szNewLocInfo, "%s|%s|%s|%s", data->SGSNAddress, data->userCellid, data->userMsc,data->userLac);
		sprintf(szNewLocInfo, "%s|%s|%s|%s|%s|%d|%s|%s|0|0|%s",
					data->SGSNAddress.c_str(), data->userCellid.c_str(), data->userMsc.c_str(), data->userLac.c_str(),
					data->userLocationInfo.c_str(), cacheData.n_RE_INT_ROAM_TYPE, cacheData.sz_RE_STR_SUB_VISIT_AREA,data->GGSNAddress.c_str(),data->RATType.c_str());

		//更新子会话
		UDBSQL *pUpdate =  dbm->GetSQL(PGW_UpdateSessionStore_RG);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				pUpdate->DivTable(bizMsg->m_sessionID);
				pUpdate->UnBindParam();
				pUpdate->BindParam(1, SM_SESSION_TERMINATION_CODE);
				pUpdate->BindParam(2, (int)bizMsg->m_requestNumber);
				pUpdate->BindParam(3, TORB_ACTION);
				pUpdate->BindParam(4, (long)bizMsg->m_serial);
				pUpdate->BindParam(5, nextCCTime);
				pUpdate->BindParam(6, (long)bizMsg->timestampCCR);
				pUpdate->BindParam(7, (long)(MSCC->USU0.duration + MSCC->USU1.duration));	   // OCP_LNG_USU_TIME
				pUpdate->BindParam(8, (long)(MSCC->USU0.unitTotal + MSCC->USU1.unitTotal));	   // OCP_LNG_USU_TOTAL_OCT
				pUpdate->BindParam(9, (long)(MSCC->USU0.unitInput + MSCC->USU1.unitInput));	   // OCP_LNG_USU_INPUT_OCT
				pUpdate->BindParam(10, (long)(MSCC->USU0.unitOutput + MSCC->USU1.unitOutput)); // OCP_LNG_USU_OUTPUT_OCT

				pUpdate->BindParam(11, cacheData.lastCCRTime);
				pUpdate->BindParam(12, (long)base->timestamp);
				pUpdate->BindParam(13, cacheData.valid_time);
				pUpdate->BindParam(14, szNewLocInfo);
				pUpdate->BindParam(15, cacheData.ServiceScenarious);
				pUpdate->BindParam(16, cacheData.sz_OCP_STR_3GPP_RAT_TYPE);
				pUpdate->BindParam(17, bizMsg->m_sBatchId.c_str());
				pUpdate->BindParam(18, ChildSessionId);
				pUpdate->Execute();
				pUpdate->Connection()->Commit();
				success = true;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update chilid ok,session[%s]", ChildSessionId);
			}
			catch (UDBException &e)
			{
				pUpdate->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update execption[%s],SQL[%s],SQLCODE[%d], error code[%d]", e.ToString(), e.GetSqlInfo(), e.GetSqlCode(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			}
		}
	}

	//所有的RG都是余额不足情况，在此出单，否则在处理CCA时无法判断skip的RG数目，导致会话释放不掉
	//有效RG均为余额不足情况
	if(bizMsg->m_vectorMsg.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "skip rg num equal total rg num[%d],ret cdr", nSkipRgNum);
		//返回网元2001消息
		bizMsg->m_iCdrRet = sendTermCCAWithOutRG(base, data, bizMsg);
		return RET_CDR;
	}

    //设置需发送的RG 数目
    if(bizMsg->m_vectorMsg.size() > 0)
	{
		rgnumber = (int)bizMsg->m_vectorMsg.size();
		UDBSQL *Update = dbm->GetSQL(PGW_U_SEND_RGNUM);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				Update->DivTable(bizMsg->m_sessionID);
				Update->UnBindParam();
				Update->BindParam(1, rgnumber);
				Update->BindParam(2, bizMsg->m_sessionID);
				Update->Execute();
				Update->Connection()->Commit();
				success = true;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update main ok, rgnum[%d]", rgnumber);
			}
			catch (UDBException &e)
			{
				Update->Connection()->Rollback();
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update main execption, exp[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			}
		}
	}
	return RET_SUCCESS;
}


int DCReqPGW::composeRER(STBizMsg* bizMsg,ocs::SUSU *MSCC,SSessionCacheData &cacheData, std::vector<long> &v_rg)
{
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char ChildSessionId[128] 			= {0};
	char PLCA_CELLID[20]				={0};
	char PLCA_MSC[20]					={0};
	char szBatchId[32]					= {0};
	ocs::SCCRDataUnit TUSU;
	long lastCCRTime                    = 0;
	int  nLastGsuUnit = 0;
	long lnlastGsuObject = 0;
	long lnlastGsuTime = 0;
	TSERVICE_QUOTA_CONF *conf			= NULL;
       TSERVICE_QUOTA_CONF *ratio_conf		= NULL;
	TSERVICE_QUOTA_CONF  def_conf;  //-7 默认值
	def_conf.MinRatio = -1;
	def_conf.MaxRatio = -1;
	def_conf.THRESHOLD_RATIO = 100;
	ocs::SCCRDATA *data                      = (SCCRDATA *)bizMsg->m_extend;
	TSMPara *smpara                     = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* dbm 						= (DCDBManer*)bizMsg->m_dbm;
	SUserInfo *userInfo				    = (SUserInfo*)bizMsg->m_userinfo;
	ocs::SCCRBase* base 						= (SCCRBase*)bizMsg->m_base;
	AREA_INFO *subVisit					= bizMsg->m_visit;
	int roamType						= bizMsg->m_roamtype;

       char szEVT[20]={0};
       char szRTI[10]={0};

	char szvisitArea[BIZ_TEMP_LEN_64]={0};
	if(data->visitArea)
	{
		if(6 == roamType || 9 == roamType)
		{
			sprintf(szvisitArea, "00%d", data->visitArea);
		}
		else
		{
			sprintf(szvisitArea, "0%d", data->visitArea);
		}
	}
	else
	{
		if(subVisit->area)
		{
			if(6 == roamType || 9 == roamType)
			{
				sprintf(szvisitArea, "00%d", subVisit->area);
			}
			else
			{
				sprintf(szvisitArea, "0%d", subVisit->area);
			}
		}
		else
		{
			szvisitArea[0] = '\0';
		}
	}
	cacheData.n_RE_INT_ROAM_TYPE = roamType;
	strcpy(cacheData.sz_RE_STR_SUB_VISIT_AREA, szvisitArea);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cacheData info roamType[%d],visitArea[%s]", cacheData.n_RE_INT_ROAM_TYPE,cacheData.sz_RE_STR_SUB_VISIT_AREA);

	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdata rbr;
	ocs::rsu_t rsu;

	ocs::usu   u;
	ocs::debit totalusu;
	ocs::rbext ext;
	long balance = 0;

	if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
	{
		sprintf(ChildSessionId, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
		strcpy(bizMsg->m_ProductOfferId, MSCC->ProductOfferId.c_str());
	}
	else
	{
		sprintf(ChildSessionId, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
		bizMsg->m_ratingGroup = MSCC->ratingGroup;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ChildSessionId[%s]", ChildSessionId);

	UDBSQL *pQuery = NULL;
	pQuery =  dbm->GetSQL(PGW_CCR_SelectSession);

	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		if(SM_SESSION_UPDATE_FIRST_CODE==bizMsg->m_requestType)
		{
			pQuery->BindParam(1, bizMsg->m_sessionID);
		}
		else
		{
			bizMsg->m_resultcode = 5002;
			pQuery->BindParam(1, ChildSessionId);
		}
		pQuery->Execute();
		if(pQuery->Next())
		{
			head.type = RE_SERVICE_TYPE_INT_PGW_REQ;
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				head.sreq = 1;
			}
			else
			{
				head.sreq  = bizMsg->m_requestType;
			}

			head.version = 2;
			head.stamp = bizMsg->timestampCCR;
			head.session = ChildSessionId;
			head.serial = bizMsg->m_serial;
			head.trace =  bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = base->topology;

			pQuery->GetValue(64, value);
			head.creditCtlFlag = atoi(value);
			strcpy(cacheData.szCreatFlag ,value);

			uhd.car = "1";
            uhd.uid = bizMsg->m_uid;
			uhd.trace = bizMsg->m_trace_flag;
			uhd.checkKey = bizMsg->m_strCheckKey;
			//prod_inst_id
			pQuery->GetValue(43, value);
			head.prodinstid = userInfo->servID = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

			//latn_id
			pQuery->GetValue(55, value);
                     rbr.latn_id = userInfo->ilatnid = atoi(value);
                     DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose latn_id[%d]", userInfo->ilatnid);
			bizMsg->m_ilatnId = atoi(value);

			//R71   会话开始时间
			if(SM_SESSION_UPDATE_CODE ==bizMsg->m_requestType || SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType)
			{

				pQuery->GetValue(13, value);//RE_INT_LAST_GSU_UNIT
				cacheData.nLastGsuUnit = nLastGsuUnit= atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu unit[%d]", cacheData.nLastGsuUnit);

				//获取上次预占流量
				pQuery->GetValue(14, value);//RE_LNG_LAST_GSU_TOTAL_OCT
				lnlastGsuObject = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu total object[%ld],unitTotal[%ld]", lnlastGsuObject,(MSCC->USU0.unitTotal+MSCC->USU1.unitTotal));

				//判断网元上报的USU是否超大
				if((RB_UNIT_CODE_TOTAL_BYTES == nLastGsuUnit) &&((MSCC->USU0.unitTotal+MSCC->USU1.unitTotal) >2*lnlastGsuObject) &&(1 == smpara->GetPSPara()->nUsuOverloadRefuse))
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "object:usu[%lu+%lu]>2 times gsu[%ld],refuse", MSCC->USU0.unitTotal,MSCC->USU1.unitTotal,lnlastGsuObject);
					return SM_OCP_USU_OVERLOAD;
				}

				//获取上次预占时长
				pQuery->GetValue(15, value);//RE_LNG_LAST_GSU_TOTAL_OCT
				lnlastGsuTime = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu total object[%ld],duration[%ld]",lnlastGsuTime,(MSCC->USU0.duration+MSCC->USU1.duration));
				//判断网元上报的USU是否超大
				if((RB_UNIT_CODE_SECOND == nLastGsuUnit) &&((MSCC->USU0.duration+MSCC->USU1.duration) >2*lnlastGsuTime) &&(1 == smpara->GetPSPara()->nUsuOverloadRefuse))
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "second:usu[%ld+%ld]>2 times gsu[%ld],refuse", MSCC->USU0.duration,MSCC->USU1.duration,lnlastGsuTime);
					return SM_OCP_USU_OVERLOAD;
				}

				pQuery->GetValue(10, value);
			    rbr.sess_start_time = value;
			}
			else
			{
				rbr.sess_start_time = "0";
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sess_start_time[%s]", "0");

			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				balance = -1;
			}
			else
			{
				pQuery->GetValue(61, value);
				balance = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get balance[%ld]", balance);
			}
			pQuery->GetValue(11, value);//OCP_INT_CCA_FLAG
			cacheData.nsendCCAFlag = atoi(value);

                     pQuery->GetValue(10, value);
			lastCCRTime = atol(value);
			cacheData.lastCCRTime = lastCCRTime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last CCR date %s", value);

                     if(SM_SESSION_UPDATE_CODE ==bizMsg->m_requestType && RB_UNIT_CODE_TOTAL_BYTES == nLastGsuUnit && smpara->GetPSPara()->dynamic_camp_on == 1)
                     {
                            long  total = MSCC->USU0.unitTotal+MSCC->USU1.unitTotal;
                            long diffsec = time2sec(bizMsg->timestampCCR) - time2sec(lastCCRTime);
                            diffsec =  diffsec < 1 ? 1 : diffsec;
                            long  bps = (total + diffsec-1)/diffsec;

                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "dynamic lastCCRTIME[%ld],curCCRTIME[%lu],useage[%ld],bps[%ld]", lastCCRTime,bizMsg->timestampCCR,total,bps);

                            // 速率配置信息获取
			       ratio_conf = smpara->GetServiceQuotaConf(PGW_SESSION_RELEASE_RATIO, bps);
			       if(!ratio_conf)
                            {
                                    ratio_conf = &def_conf;
                                    DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "dynamic no find -7 ,get the default!","");
                            }
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "dynamic min_bps[%ld],max_bps[%ld],ratio[%d]",ratio_conf->MinRatio,ratio_conf->MaxRatio,ratio_conf->THRESHOLD_RATIO);
                     }

			//获取配额信息
			if(!MSCC->ProductOfferId.empty())
			{
				conf = smpara->GetServiceQuotaByOfr(MSCC->ProductOfferId.c_str(),balance);
				if(!conf || !conf->VALID_TIME || !(conf->DURATION&&conf->TOTAL_OCTETS))
				{
					if(conf)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "invalid quota conf for ProductOfferId[%s]: RATING_GROUP[%ld], VALID_TIME[%d],"
						" TOKEN[%d], DURATION[%d], TOTAL_OCTETS[%ld], INPUT_OCTETS[%ld], OUTPUT_OCTETS[%ld], QUOTA_HOLDING_TIME[%d], QUOTA_CONSUMPTION_TIME[%d], TIME_QUOTA_THRESHOLD[%d],"
						" VOLUME_QUOTA_THRESHOLD[%d], VOLUME_QUOTA_THRESHOLD_1[%d], szProductOfferId[%s], MinBalance[%ld], MaxBalance[%ld]",  MSCC->ProductOfferId.c_str(), conf->RATING_GROUP, conf->VALID_TIME,
							conf->TOKEN, conf->DURATION, conf->TOTAL_OCTETS, conf->INPUT_OCTETS, conf->OUTPUT_OCTETS, conf->QUOTA_HOLDING_TIME, conf->QUOTA_CONSUMPTION_TIME, conf->TIME_QUOTA_THRESHOLD,
							conf->VOLUME_QUOTA_THRESHOLD, conf->VOLUME_QUOTA_THRESHOLD_1, conf->szProductOfferId, conf->MinBalance, conf->MaxBalance);
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "invalid ofr id[%s],deafult -2", MSCC->ProductOfferId.c_str());
					}

					if((conf = smpara->GetServiceQuotaConf(PGW_SESSION_RELEASE_RATING,balance)) == NULL)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "no find default -2","");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
			}
			else if(-1 != MSCC->ratingGroup)
			{
				if((conf = smpara->GetServiceQuotaConf(MSCC->ratingGroup,balance)) == 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "invalid RG[%u]", MSCC->ratingGroup);
					if((conf = smpara->GetServiceQuotaConf(PGW_SESSION_RELEASE_RATING,balance)) == NULL)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "no find service quota config[%lld]", MSCC->ratingGroup);
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "missing RG", "");
				return SM_OCP_MISSING_AVP;
			}


			//R85   用户付费属性标识
			rbr.pay=1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");

			//R01   付费号码
			pQuery->GetValue(18, value);
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_nbr[%s]", value);

			rbr.latn_id = userInfo->ilatnid;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "latnid[%d]", rbr.latn_id);

			//R504 主叫号码归属费率区
			pQuery->GetValue(19, value);
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_harea[%s]", value);

			//R5012 付费号码归属费率区
			pQuery->GetValue(19, value);
			rbr.charged_harea = value;
			strcpy(cacheData.sz_RE_STR_SUB_AREA, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", value);


			if( SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType )
			{

				//R505 主叫号码拜访费率区
				rbr.calling_varea = szvisitArea;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling visit area[%s]", szvisitArea);

				rbr.roam_type = roamType;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%d]", roamType);

                            strcpy(szEVT, bizMsg->m_eventType);
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose EVT[%s]", szEVT);

                            strcpy(szRTI,  subVisit ->sector_id);
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RTI[%s]", szRTI);
			}
			else
			{

				//R505 主叫号码拜访费率区
				pQuery->GetValue(20, value);
				rbr.calling_varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_varea[%s]", value);

				//R5011 漫游类型
				pQuery->GetValue(21, value);
				rbr.roam_type = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%s]", value);

                            // OCP_OLD_LOCATION_EXT
                            pQuery->GetValue(60, value);
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCP_OLD_LOCATION_EXT[%s]", value);

							if(value[0] && strcmp(value,"|"))
                            {
                                    std::vector<std::string> vs;
                                    DCCommonIF::SplitString(value, '|', vs);
                                    vs.resize(2);
                                    strcpy(szEVT, vs[0].c_str());
                                    strcpy(szRTI, vs[1].c_str());
                            }
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose EVT[%s]", szEVT);
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RTI[%s]", szRTI);
			}

			//R603  会话上次扣费开始时间
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				rbr.pre_dtime = 0;
			}
			else
			{
				pQuery->GetValue(10, value);
				rbr.pre_dtime = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pre_dtime[%ld]", rbr.pre_dtime);

			//R604  本次计费请求开始时间
			rbr.cur_dtime =  bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%ld]",bizMsg->timestampCCR);

			//R605  是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");

			//R606 激活用户
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				pQuery->GetValue(16, value);
				rbr.active_flag = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose active_flag[%s]", value);
			}
			else
			{
				// pREMsg->del(RB_CODE_R_ACTIVE_FLAG);
				rbr.active_flag = 0;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "delete ActiveFlag", "");
			}
			cacheData.n_RE_INT_ISACTIVE_FLAG = rbr.active_flag;

			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				rbr.sgsn_addr = data->SGSNAddress;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sgsn_addr[%s]", rbr.sgsn_addr.c_str());

				rbr.ggsn_addr = data->GGSNAddress;;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ggsn_addr[%s]", rbr.ggsn_addr.c_str());

				//R3010 QoS
				pQuery->GetValue(57, value);
				rbr.qos = value;
				strcpy(cacheData.sz_OCP_STR_CPRS_QOS, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos[%s]", value);


				rbr.cell  = data->userCellid;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", rbr.cell.c_str());

				rbr.location  = data->userLocationInfo;
				strcpy(cacheData.sz_OCP_STR_LAC_CODE,data->userLocationInfo.c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", rbr.location.c_str());

				rbr.msc  = data->userMsc;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", rbr.msc.c_str());

				rbr.lac = data->userLac;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lac[%s]", rbr.lac.c_str());

			}
			else
			{
				//R301 SGSN地址
				pQuery->GetValue(22, value);
				rbr.sgsn_addr = value;
				strcpy(cacheData.sz_OCP_STR_IP_SGSN_ADDR,value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sgsn_addr[%s]", value);

				//R302 GGSN地址
				pQuery->GetValue(23, value);
				rbr.ggsn_addr = value;
				strcpy(cacheData.sz_OCP_STR_IP_GGSN_ADDR,value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ggsn_addr[%s]", value);


				//R3010 QoS
				pQuery->GetValue(57, value);
				rbr.qos = value;
				strcpy(cacheData.sz_OCP_STR_CPRS_QOS, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos[%s]", value);

				//R3012 用户位置信息
				pQuery->GetValue(51, value);
				rbr.location = value;
				strcpy(cacheData.sz_OCP_STR_LAC_CODE, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location[%s]", value);


				//R3015 CELLID
				pQuery->GetValue(31, value);
				rbr.cell = value;
				strcpy(cacheData.sz_CDR_STR_CELLID, value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", value);

				// R1012 MSC
				pQuery->GetValue(30, value);
				rbr.msc = value;
				strcpy(cacheData.sz_RB_CODE_R_CALLING_MSC,data->userMsc.c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", value);

				pQuery->GetValue(59, value);
				rbr.lac = value;
				strcpy(cacheData.sz_CDR_STR_LAC,data->userLac.c_str());
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lac[%s]", value);

			}

			//R305 APN网络标识
			pQuery->GetValue(24, value);
			rbr.apn_id = value;
			strcpy(cacheData.sz_OCP_STR_APN_INFO, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose apn_info[%s]", value);

			//R306 PDP类型
			pQuery->GetValue(25, value);
			rbr.pdp_type = value;
			strcpy(cacheData.sz_OCP_STR_PDP_TYPE, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_type[%s]", value);

			//R307 被服务方的 PDP地址
			pQuery->GetValue(26, value);
			rbr.pdp_addr = value;
			strcpy(cacheData.sz_OCP_STR_IP_PDP_ADDR, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_addr[%s]", value);

			pQuery->GetValue(63, value);
			strcpy(cacheData.sz_OCP_STR_IP_PDP_ADDRIpv6, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "pdp_addr_ipv6[%s]", value);

			//R309 授权有效时间
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				sprintf(value, "%d", conf->VALID_TIME);
			}
			else
			{
				pQuery->GetValue(1, value);
			}
			rbr.valid_time = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose valid_time[%s]", value);

			pQuery->GetValue(28, value);
			//RAT_TYPE在位置变换的时候再更新，否则会影响出单
			strcpy(cacheData.sz_OCP_STR_3GPP_RAT_TYPE, value);

			//R3013 RAT TYPE
			if((1 == smpara->GetPSPara()->nRatTypeSwitch) &&(data->RATType.length()))
			{
				rbr.rat_type = data->RATType;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", data->RATType.c_str());
			}
			else
			{
				//R3013 RAT TYPE
				rbr.rat_type = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", value);
			}



			//R3017 LTE业务标识
			rbr.lte_flag = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lte_flag[1]");

			pQuery->GetValue(32, value);
			rbr.qos_class_id= value;
			cacheData.n_OCP_INT_CLASS_IDENTIFIER = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos_class_id[%s]",rbr.qos_class_id.c_str());
			//总使用时长
			pQuery->GetValue(4, value);
			TUSU.duration = atoi(value);
			cacheData.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%u]", TUSU.duration);

			//总使用总流量
			pQuery->GetValue(5, value);
			TUSU.unitTotal = atol(value);
			cacheData.unitTotal = TUSU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%lld]", TUSU.unitTotal);

			//总使用上行流量
			pQuery->GetValue(6, value);
			TUSU.unitInput = atol(value);
			cacheData.unitInput = TUSU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%lld]", TUSU.unitInput);

			//总使用下行流量
			pQuery->GetValue(7, value);
			TUSU.unitOutput = atol(value);
			cacheData.unitOutput = TUSU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%lld]", TUSU.unitOutput);

			pQuery->GetValue(10, value);
			lastCCRTime = atol(value);
			cacheData.lastCCRTime = lastCCRTime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last CCR date %s", value);

			cacheData.valid_time = conf->VALID_TIME;
			cacheData.rsu_duration = conf->DURATION;
			cacheData.rsu_totalOCtets = conf->TOTAL_OCTETS;
			//R3016
			pQuery->GetValue(33, value);
			strcpy(cacheData.sz_OCP_STR_3GPP_CHARGING_ID, value);
			rbr.charging_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "3GPP_CHARGING_ID[%s]", value);

			pQuery->GetValue(34, value);
			cacheData.nAocType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "aoc type[%s]", value);

			if(cacheData.ServiceScenarious == 0)
			{
				memset(value,0,sizeof(value));
				pQuery->GetValue(29, value);
				cacheData.ServiceScenarious=atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ServiceScenarious[%s]", value);
			}

			if(bizMsg->m_requestType==SM_SESSION_UPDATE_FIRST_CODE)
			{
				cacheData.conf_QUOTA_CONSUMPTION_TIME = conf->QUOTA_CONSUMPTION_TIME;
				cacheData.conf_VOLUME_QUOTA_THRESHOLD = conf->VOLUME_QUOTA_THRESHOLD;
				cacheData.conf_VOLUME_QUOTA_THRESHOLD1 = conf->VOLUME_QUOTA_THRESHOLD_1;
				cacheData.conf_TIME_QUOTA_THRESHOLD   = conf->TIME_QUOTA_THRESHOLD;
				cacheData.conf_QUOTA_HOLDING_TIME     = conf->QUOTA_HOLDING_TIME;

				memset(value,0,sizeof(value));
				pQuery->GetValue(40, value); //CDR_PUB_INT_CORRELATIONID,//
				cacheData.n_CDR_PUB_INT_CORRELATIONID = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_CDR_PUB_INT_CORRELATIONID[%s]", value);

				memset(value,0,sizeof(value));
				pQuery->GetValue(41, value); //CDR_PUB_INT_TICKETSEQUENCEID,//
				cacheData.n_CDR_PUB_INT_TICKETSEQUENCEID = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_CDR_PUB_INT_TICKETSEQUENCEID[%s]", value);

				memset(value,0,sizeof(value));
				pQuery->GetValue(42, value); //CDR_PUB_STR_CHARGED_PARTY,//
				strncpy(cacheData.sz_CDR_PUB_STR_CHARGED_PARTY,value,sizeof(cacheData.sz_CDR_PUB_STR_CHARGED_PARTY));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_CDR_PUB_STR_CHARGED_PARTY[%s]", value);

				memset(value,0,sizeof(value));
				pQuery->GetValue(43, value); //CDR_PUB_LNG_SERVID
				cacheData.l_CDR_PUB_LNG_SERVID = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "l_CDR_PUB_LNG_SERVID[%s]", value);

				memset(value,0,sizeof(value));
				pQuery->GetValue(44, value); //CDR_PUB_LNG_CUSTID
				cacheData.l_CDR_PUB_LNG_CUSTID = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "l_CDR_PUB_LNG_CUSTID[%s]", value);

				pQuery->GetValue(45, cacheData.sz_CDR_PUB_STR_MASTERPRODUCTID); //CDR_PUB_STR_MASTERPRODUCTID
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_CDR_PUB_STR_MASTERPRODUCTID[%s]", cacheData.sz_CDR_PUB_STR_MASTERPRODUCTID);

				pQuery->GetValue(46, cacheData.sz_OCP_STR_ORIGIN_HOST); //OCP_STR_ORIGIN_HOST,//
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_OCP_STR_ORIGIN_HOST[%s]", cacheData.sz_OCP_STR_ORIGIN_HOST);

				memset(value,0,sizeof(value));
				pQuery->GetValue(47, value); //RE_INT_SUB_OPERATOR
				cacheData.n_RE_INT_SUB_OPERATOR = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_RE_INT_SUB_OPERATOR[%s]", value);

				pQuery->GetValue(48, cacheData.sz_SM_STR_IMSI); //SM_STR_IMSI,//
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_SM_STR_IMSI[%s]", cacheData.sz_SM_STR_IMSI);

				pQuery->GetValue(49, cacheData.sz_OCP_STR_CDMA_CHARGING_ID); //OCP_STR_CDMA_CHARGING_ID,//
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_OCP_STR_CDMA_CHARGING_ID[%s]", cacheData.sz_OCP_STR_CDMA_CHARGING_ID);

				memset(value,0,sizeof(value));
				pQuery->GetValue(50, cacheData.sz_OCP_STR_IP_PDSN_ADDR); //OCP_STR_IP_PDSN_ADDR, //
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_OCP_STR_IP_PDSN_ADDR[%s]", cacheData.sz_OCP_STR_IP_PDSN_ADDR);


				pQuery->GetValue(52, cacheData.sz_CDR_STR_USER_TYPE); //CDR_STR_USER_TYPE, //
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_CDR_STR_USER_TYPE[%s]", cacheData.sz_CDR_STR_USER_TYPE);

				pQuery->GetValue(53, cacheData.sz_CDR_STR_MVNO_ID); //CDR_STR_MVNO_ID, //
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sz_CDR_STR_MVNO_ID[%s]", cacheData.sz_CDR_STR_MVNO_ID);

				pQuery->GetValue(54, value); //SM_INT_SESSION_TYPE //
				cacheData.n_SM_INT_SESSION_TYPE = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_SM_INT_SESSION_TYPE[%s]", value);

				pQuery->GetValue(55, value);
				cacheData.n_CDR_INT_LATN_ID = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_CDR_INT_LATN_ID[%s]", value);

				pQuery->GetValue(56, value);
				cacheData.ln_CDR_ACCT_ID = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ln_CDR_ACCT_ID[%s]", value);

				pQuery->GetValue(62, szBatchId);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last BatchId[%s]", szBatchId);

			}
		}
		else
		{
			if(SM_SESSION_UPDATE_CODE != bizMsg->m_requestType)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_UNABLE_TO_COMPLY, "", "session not exist[%s]", ChildSessionId);
			}
			return SM_OCP_UNKNOWN_SESSION_ID;
		}
	}
	catch(UDBException& e)
	{
		string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_UNABLE_TO_COMPLY,  "", "SQL[%s]", sql.c_str());

		DCBIZLOG(DCLOG_LEVEL_ERROR,SM_OCP_UNABLE_TO_COMPLY, "", "query execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	if(SM_SESSION_UPDATE_CODE == bizMsg->m_requestType || SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
	{
		//组装预占组
		int numUnit = 0;
		if(conf->DURATION)
		{
			numUnit++;
		}
		if(conf->TOTAL_OCTETS)
		{
			numUnit++;
		}
		if(conf->INPUT_OCTETS)
		{
			numUnit++;
		}
		if(conf->OUTPUT_OCTETS)
		{
			numUnit++;
		}

		if(numUnit <1)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "invalid rating group :no rsu value [%d]", DSL_RATING_GROUP);
			return SM_OCP_UNABLE_TO_COMPLY;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quota num[%d]", numUnit);

		if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
		{
			sprintf(value, "%s", MSCC->ProductOfferId.c_str());
		}
		else
		{
			sprintf(value, "%lld", MSCC->ratingGroup);
		}
		rsu.rating_group = value;

		//时长
		if(conf->DURATION)
		{
			rsu.unit = 1;
			rsu.amount = conf->DURATION;
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",rsu.amount);
		}

		//总流量
		if(conf->TOTAL_OCTETS)
		{
			rsu.unit = 3;
                     if(ratio_conf && smpara->GetPSPara()->dynamic_camp_on == 1)
                     {
                            rsu.amount = (conf->TOTAL_OCTETS * ratio_conf->THRESHOLD_RATIO + 99) / 100;
                     }
                     else
                     {
                            rsu.amount = conf->TOTAL_OCTETS;
                     }
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_TOTAL_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",rsu.amount);
		}

		//上行流量
		if(conf->INPUT_OCTETS)
		{
			rsu.unit = 4;
                     if(ratio_conf && smpara->GetPSPara()->dynamic_camp_on == 1)
                     {
                            rsu.amount = (conf->INPUT_OCTETS * ratio_conf->THRESHOLD_RATIO + 99) / 100;
                     }
                     else
                     {
                            rsu.amount = conf->INPUT_OCTETS;
                     }
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_UP_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",rsu.amount);
		}

		//下行流量
		if(conf->OUTPUT_OCTETS)
		{
			rsu.unit = 5;
                     if(ratio_conf && smpara->GetPSPara()->dynamic_camp_on == 1)
                     {
                            rsu.amount = (conf->OUTPUT_OCTETS * ratio_conf->THRESHOLD_RATIO + 99) / 100;
                     }
                     else
                     {
                            rsu.amount = conf->OUTPUT_OCTETS;
                     }
			domain.rsv.push_back(rsu);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",rsu.rating_group.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_UP_BYTES);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",rsu.amount);
		}

	}

	if( SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		//超短话单:按流量
		int nShorOctet = 0;
		nShorOctet = smpara->GetPSPara()->shortCDROctet;
		if(nShorOctet && ((TUSU.unitTotal + MSCC->USU0.unitTotal+MSCC->USU1.unitTotal) < nShorOctet))
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short cdr, shortoctet[%d],total usu[%d],mscc usu0[%d],mscc usu1[%d]", nShorOctet,TUSU.unitTotal,MSCC->USU0.unitTotal,MSCC->USU1.unitTotal);
			MSCC->USU0.duration = 0;
			MSCC->USU0.unitInput = 0;
			MSCC->USU0.unitOutput = 0;
			MSCC->USU0.unitTotal = 0;
			MSCC->USU1.duration = 0;
			MSCC->USU1.unitInput = 0;
			MSCC->USU1.unitOutput = 0;
			MSCC->USU1.unitTotal = 0;
			TUSU.duration = 0;
			TUSU.unitTotal = 0;
			TUSU.unitInput = 0;
			TUSU.unitOutput = 0;
		}

	}

	if(SM_SESSION_UPDATE_CODE == bizMsg->m_requestType || SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
		{
			sprintf(value, "%s", MSCC->ProductOfferId.c_str());
		}
		else
		{
			sprintf(value, "%lld", MSCC->ratingGroup);
		}
		u.rating_group = value;

		//时长
		u.unit = 1;
		if(0 == MSCC->USU0.tariffChangeUsage)
		{
			u.amount = MSCC->USU0.duration;
		}

		if(1 == MSCC->USU1.tariffChangeUsage)
		{
			u.amount2 =  MSCC->USU1.duration;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]",u.amount2);


		//总流量
		u.unit = 3;
		if(0 == MSCC->USU0.tariffChangeUsage)
		{
			u.amount = MSCC->USU0.unitTotal;
		}

		if(1 == MSCC->USU1.tariffChangeUsage)
		{
			u.amount2 =  MSCC->USU1.unitTotal;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_TOTAL_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]",u.amount2);

		//上行流量
		u.unit = 4;
		if(0 == MSCC->USU0.tariffChangeUsage)
		{
			u.amount = MSCC->USU0.unitInput;
		}

		if(1 == MSCC->USU1.tariffChangeUsage)
		{
			u.amount2 =  MSCC->USU1.unitInput;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_UP_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]",u.amount2);

		//下行流量
		u.unit = 5;
		if(0 == MSCC->USU0.tariffChangeUsage)
		{
			u.amount = MSCC->USU0.unitOutput;
		}

		if(1 == MSCC->USU1.tariffChangeUsage)
		{
			u.amount2 =  MSCC->USU1.unitOutput;
		}
		domain.usv.push_back(u);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",u.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_DOWN_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",u.amount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount2[%ld]",u.amount2);


	}

	if(SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		//累计总使用量
		TUSU.duration += MSCC->USU0.duration;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

		TUSU.unitTotal += MSCC->USU0.unitTotal;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

		TUSU.unitInput += MSCC->USU0.unitInput;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

		TUSU.unitOutput += MSCC->USU0.unitOutput;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);

		//费率切换点之后的使用量
		if(1 == MSCC->USU1.tariffChangeUsage)
		{
			//累计总使用量
			TUSU.duration += MSCC->USU1.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

			TUSU.unitTotal += MSCC->USU1.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

			TUSU.unitInput += MSCC->USU1.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

			TUSU.unitOutput += MSCC->USU1.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);
		}

		if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
		{
			sprintf(value, "%s", MSCC->ProductOfferId.c_str());
		}
		else
		{
			sprintf(value, "%lld", MSCC->ratingGroup);
		}
		totalusu.rating_group = value;

		//B03
		//时长
		totalusu.unit = 1;
		totalusu.amount = TUSU.duration;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);


		//总流量
		totalusu.unit = 3;
		totalusu.amount = TUSU.unitTotal;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_TOTAL_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);

		//上行流量
		totalusu.unit = 4;
		totalusu.amount = TUSU.unitInput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_UP_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);

		//上行流量
		totalusu.unit = 5;
		totalusu.amount = TUSU.unitOutput;
		domain.dbv.push_back(totalusu);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_DOWN_BYTES);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);
	}

	//info 日志
	DCDATLOG("SM00010:%d%ld%ld%ld", MSCC->USU0.duration+MSCC->USU1.duration,\
									MSCC->USU0.unitTotal+MSCC->USU1.unitTotal,\
									MSCC->USU0.unitInput+MSCC->USU1.unitInput,\
									MSCC->USU0.unitOutput+MSCC->USU1.unitOutput);
	rbr.accumlator_info = 1;
	rbr.tariff_info     = 1;
	rbr.balance_info    = 1;
	rbr.rating_info     = 1;
	rbr.balance_query   = 1;

	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;
	ext.kv["RTI"] = szRTI;
	ext.kv["EVT"] = szEVT;
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;

	for(unsigned int i = 0; i < v_rg.size(); i++)
	{
		char key[32];
		char rgValue[64];
		sprintf(key,"RG%d",i);
		sprintf(rgValue,"%ld",v_rg[i]);
		ext.kv[key] = rgValue;
	}

	if(0 == strlen(szBatchId))
	{
		bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID,smpara->GetCommonPara()->iBatchIdTime, 1);
	}
	else
	{
		bizMsg->m_sBatchId = szBatchId;
	}
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());

	//R3019 转为16进制后赋值
	char hexMsc[32] = {0};
	long mscVal = atol(rbr.msc.c_str()); // R1012
	DCCommonIF::DECTOHEX(mscVal, hexMsc, sizeof(hexMsc));
	ext.kv["R3019"] = hexMsc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "4G[%d] compose R3019[%s] m_serviceContextID[%u] HEX SOURCE[%s]", bizMsg->m_if4Gpp, ext.kv["R3019"].c_str(), bizMsg->m_serviceContextID, rbr.msc.c_str());

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}


    bizMsg->data= (char *)m_en.data();
	bizMsg->m_vectorMsg.push_back(bizMsg->data);

	return 0;
}

int DCReqPGW::XdrEvent(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	int traceNumOnff					= 0;
	long nextCCTime 					= 0;

	TSERVICE_QUOTA_CONF *conf			= NULL;
	ocs::SUSU* MSCC							= NULL;
	int roamType						= 0;
	char value[BIZ_TEMP_LEN_256]		= {0};
	char timeStamp[BIZ_TEMP_LEN_16] 	= {0};
	ocs::SCCRDataUnit TUSU					;
	char ChildSessionId[128]			= {0};
	int nAocType = 0;
	int nFreeFlag = 0;
	SUserInfo *userInfo 				= (SUserInfo*)bizMsg->m_userinfo;
	TSMPara* m_smpara					= (TSMPara*)bizMsg->m_smpara;
	DCDBManer* dbm						= (DCDBManer*)bizMsg->m_dbm;
	AREA_INFO* subVisit					= bizMsg->m_visit;
	int ServiceScenarious  = 700;
	string	iCreatFlag;

	if(base->MSCC.size() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "missing MSCC", "");
		return SM_OCP_MISSING_AVP;
	}

	MSCC = &(base->MSCC[0]);
	if(!MSCC)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "missing MSCC", "");
		return SM_OCP_MISSING_AVP;
	}
	roamType = bizMsg->m_roamtype;

	if("2100" == bizMsg->m_payMentMode)
	{
		iCreatFlag = "3";
	}
	else
	{
		iCreatFlag = "2";
	}
	bizMsg->m_sBatchId = DCCommonIF::GetBatchNo(bizMsg->m_serviceContextID,m_smpara->GetCommonPara()->iBatchIdTime, 2);
	//C网上网话单需要剔除小于10秒的话单
	//超短话单:按时长
	int shorttime = 0;
	shorttime = m_smpara->GetPSShortCdr(MSCC->ratingGroup);
	if(shorttime && (MSCC->USU0.duration  < shorttime))
		ret = sendInitCCA(bizMsg);
	if(ret)
		return ret;

	if(0==strcmp(data->RATType.c_str(),"32876"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
	}
	else if(0==strcmp(data->RATType.c_str(),"59"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_EVDO;
	}
	else if(0==strcmp(data->RATType.c_str(),"33"))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}
	else if((data->RATType.empty()) && (data->userLocationInfo .empty() == 0) && 0==strncmp(data->userLocationInfo.c_str()+5,"EE",2))
	{
		ServiceScenarious = SERVICES_CENARIOUS_DATA_WIFI;
	}
	else if((data->RATType.empty()) && (data->userLocationInfo.empty()))
	{
	   ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}
	else
	{
		ServiceScenarious = SERVICES_CENARIOUS_DATA_1X;
	}


	if((conf = m_smpara->GetServiceQuotaConf(PGW_SESSION_RELEASE_RATING)) == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "no find service quota config[%d]", PGW_SESSION_RELEASE_RATING);
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//当前时间用于会话超时
	time_t et;
	time(&et);
	nextCCTime = et + conf->VALID_TIME;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "next cc time[%ld]", nextCCTime);
	sprintf(timeStamp,"%ld",base->timestamp);

	if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
	{
		sprintf(ChildSessionId, "%s;%s", bizMsg->m_sessionID, MSCC->ProductOfferId.c_str());
	}
	else
	{
		sprintf(ChildSessionId, "%s;%lld", bizMsg->m_sessionID, MSCC->ratingGroup);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ChildSessionId [%s]", ChildSessionId);

	UDBSQL* pExec = dbm->GetSQL(PGW_InsertSessionStoreOFFL);
	try
	{
		pExec->DivTable(bizMsg->m_sessionID);
		pExec->UnBindParam();
		pExec->BindParam(1, SM_CDR_SEQ_NUM);
		pExec->BindParam(2, SM_CDR_VERSION);
		pExec->BindParam(3, SM_CDR_TICKETTYPE);//CDR_PUB_INT_TICKETTYPE
		pExec->BindParam(4, timeStamp);//CDR_PUB_STR_TIMESTAMP
		pExec->BindParam(5, base->topology);
		pExec->BindParam(6, 0);//CDR_PUB_INT_CORRELATIONID
		pExec->BindParam(7, 0);//CDR_PUB_INT_TICKETSEQUENCEID
		pExec->BindParam(8, ServiceScenarious);//CDR_PUB_INT_SERVICESCENARIOUS
		pExec->BindParam(9, base->subUnified);//CDR_PUB_STR_CHARGED_PARTY
		pExec->BindParam(10, userInfo->servID);
		pExec->BindParam(11, userInfo->custID);
		pExec->BindParam(12, MASTER_PRODUCTID_CDMA);
		pExec->BindParam(13, ChildSessionId);
		pExec->BindParam(14, SM_SESSION_XDR_CODE);
		pExec->BindParam(15, (int)bizMsg->m_requestNumber);
		pExec->BindParam(16, base->originHost);
		pExec->BindParam(17, nextCCTime);
		pExec->BindParam(18, base->subscription.phone);

		sprintf(value, "0%d", base->subscription.area);
		pExec->BindParam(19, value);


		if(data->visitArea)
		{
			sprintf(value, "0%d", data->visitArea);
		}
		else
		{
			if(subVisit->area)
			{
				sprintf(value, "0%d", subVisit->area);
			}
			else
			{
				value[0] = '\0';
			}
		}
		pExec->BindParam(20, value);

		pExec->BindParam(21, base->subscription.carriers);
		pExec->BindParam(22, 1);
		pExec->BindParam(23, base->starttime);
		pExec->BindParam(24, (long long)bizMsg->timestampCCR);
		pExec->BindParam(25, roamType);
		pExec->BindParam(26, bizMsg->m_serial);
		pExec->BindParam(27, userInfo->aocType);
		pExec->BindParam(28, STATUS_IDLE);
		pExec->BindParam(29, base->routeRecord);

		traceNumOnff = bizMsg->m_trace_flag;
		pExec->BindParam(30, traceNumOnff);
		if(0 != strcmp(userInfo->IMSI, ""))
		{
			pExec->BindParam(31, userInfo->IMSI);
		}
		else
		{
			value[0] = '\0';
			pExec->BindParam(31, value);
		}
		pExec->BindParam(32, (long)0); // SM_LNG_MSCC_VALIDITY_TIME
   		pExec->BindParam(33, (long)0); // SM_LNG_QUOTA_CONSUME_TIME
   		pExec->BindParam(34, (long)0); // SM_LNG_VOLUME_QUOTA_THRESHOLD
   		pExec->BindParam(35, (long)0); // SM_LNG_TIME_QUOTA_THRESHOLD
   		pExec->BindParam(36, (long)0); // SM_LNG_QUOTA_HOLDING_TIME
		pExec->BindParam(37, data->PDSNAddress);//OCP_STR_IP_GGSN_ADDR
		pExec->BindParam(38, data->SGSNAddress);
		pExec->BindParam(39, data->APN);
		pExec->BindParam(40, data->PDPType);
		pExec->BindParam(41, data->PDPAddress);
		pExec->BindParam(42, (long)MSCC->ratingGroup);
		pExec->BindParam(43, data->QOS);
		pExec->BindParam(44, data->userLocationInfo);
		pExec->BindParam(45, data->RATType);
		pExec->BindParam(46, data->chargingID);
		pExec->BindParam(47, userInfo->isActive);
		pExec->BindParam(48, long(0)); //作为上次 CCR 的时间
		pExec->BindParam(49, 1);							//	用来标识主会话
		pExec->BindParam(50, data->PDSNAddress);	   //PDSN
		pExec->BindParam(51, data->userCellid); 	   //CELLID
		pExec->BindParam(52, data->userLac);		   //MCS
		pExec->BindParam(53, data->userLocationInfo);  //userLocation_Info
		pExec->BindParam(54, bizMsg->billcycle);
		pExec->BindParam(55, nFreeFlag);
		pExec->BindParam(56, (long)(MSCC->USU0.duration));//OCP_LNG_USU_TIME
		pExec->BindParam(57, (long)(MSCC->USU0.unitTotal));//OCP_LNG_USU_TOTAL_OCT
		pExec->BindParam(58, (long)(MSCC->USU0.unitInput));//OCP_LNG_USU_INPUT_OCT
		pExec->BindParam(59, (long)(MSCC->USU0.unitOutput));//OCP_LNG_USU_OUTPUT_OCT
		pExec->BindParam(60, userInfo->lnAcctID);
		pExec->BindParam(61, userInfo->userType);
		pExec->BindParam(62, userInfo->ilatnid);
		pExec->BindParam(63, userInfo->mvnoID);
		pExec->BindParam(64, MSCC->ProductOfferId);//OCP_STR_PRODUCT_OFFER_ID
		pExec->BindParam(65, bizMsg->m_szServiceContextIDStr);
		pExec->BindParam(66, data->userMsc);
		long switchId = atol(base->smExt.kv["SwitchId"].c_str());
		pExec->BindParam(67, switchId);
		pExec->BindParam(68, bizMsg->m_sBatchId.c_str());
		pExec->BindParam(69, bizMsg->addressIpv6);
		pExec->BindParam(70, iCreatFlag);
		pExec->Execute();
		pExec->Connection()->Commit();

		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "insert ok", "");
	}
	catch(UDBException& e)
	{
		string	sql;
		pExec->Connection()->Rollback();
		pExec->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "insert execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}


	//组装RER消息
	ocs::UHead uhd;
	ocs::rbhead head;
	ocs::rbdomain domain;
	ocs::rbdata rbr;
	ocs::rbext ext;
	ocs::rsu_t rsu;

	ocs::usu   u;
	ocs::debit totalusu;

	uhd.car = "1";
	uhd.uid = bizMsg->m_uid;
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	head.type = RE_SERVICE_TYPE_INT_PGW_REQ;
	head.version = 1;
	head.sreq  = 4;
	head.stamp = bizMsg->timestampCCR;
	head.session = ChildSessionId;
	head.session.erase(0,3);
	head.serial = bizMsg->m_serial;
	head.trace =  bizMsg->m_trace_flag;
	head.result = 0;
	head.topology = base->topology;
	head.creditCtlFlag = 3;
	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);
	//R85	用户付费属性标识
	rbr.pay = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");
	//R606用户激活标识
	rbr.active_flag = userInfo->isActive;

	//R01   付费号码
	rbr.charged_nbr = base->subscription.phone;
	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%d]", rbr.latn_id);

	//R504 主叫号码归属费率区
	sprintf(value, "0%d", base->subscription.area);
	rbr.calling_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_harea[%s]", value);

	//R505 主叫号码拜访费率区
	if(data->visitArea)
	{
		sprintf(value, "0%d", data->visitArea);
	}
	else
	{
		if(subVisit->area)
		{
			sprintf(value, "0%d", subVisit->area);
		}
		else
		{
			value[0] = '\0';
		}
	}
	rbr.calling_varea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_varea[%s]", value);

	//R3017 LTE业务标识
	rbr.lte_flag = 1;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lte_flag[1]");

	//R5011 漫游类型
	sprintf(value,"%d",roamType);
	rbr.roam_type = atoi(value);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%s]", value);

	//R5012 付费号码归属费率区
	sprintf(value, "0%d", base->subscription.area);
	rbr.charged_harea = value;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", value);

/*	//R601 重发标记
	pREMsg->set(RB_CODE_R_REPEAT_FLAG, "0");
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,bizMsg->m_sessionID, "compose %s[%s]", RB_CODE_R_REPEAT_FLAG, "0");
*/
	//R602 计费类型
	rbr.sreq = SM_SESSION_TERMINATION_CODE;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose session_state[%s]", SM_SESSION_INITIAL_STRING);

	//R70--R604 本次计费请求结束时间
	rbr.cur_dtime =  bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%u]",bizMsg->timestampCCR);

	 //R71--R603 本次计费请求开始时间
	rbr.pre_dtime= base->starttime;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pre_dtime[%ld]", rbr.pre_dtime);

        //R71  会话开始时间
       sprintf(value, "%ld", rbr.pre_dtime);
       rbr.sess_start_time = value;
       DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose RB_CODE_R_SESSION_START_TIME[%s]", value);

	//R605	是否进行使用量累计标?
	rbr.ratable_flag = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");

	//R301 SGSN地址
	rbr.sgsn_addr = data->SGSNAddress;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sgsn_addr[%s]", rbr.sgsn_addr.c_str());


	//R302 GGSN地址
	rbr.ggsn_addr = data->GGSNAddress;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ggsn_addr[%s]", rbr.ggsn_addr.c_str());

	//R305 APN网络标识
	rbr.apn_id = data->APN;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose apn_info[%s]", rbr.apn_id.c_str());


	//R306 PDP类型
	rbr.pdp_type = data->PDPType;
	  DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_type[%s]", rbr.pdp_type.c_str());

	//R307 被服务方的 PDP地址
	rbr.pdp_addr = data->PDPAddress;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_addr[%s]", rbr.pdp_addr.c_str());


	//R3010 QoS
	rbr.qos = data->QOS;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos[%s]", rbr.qos.c_str());

	//R3012 用户位置信息
	rbr.location = data->userLocationInfo;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location[%s]", rbr.location.c_str());

	//R3013 RAT TYPE
	rbr.rat_type = data->RATType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", rbr.rat_type.c_str());
	//R3016
	rbr.charging_id = data->gppChargingID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charging_id[%s]", rbr.charging_id.c_str());

/*	//R3014 NID
	rbr.
	pREMsg->set(RB_CODE_R_NID, data->userLac);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,bizMsg->m_sessionID, "compose %s[%s]", RB_CODE_R_NID, data->userLac);
*/
	//R3015 CELLID
	rbr.cell = data->userCellid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", rbr.cell.c_str());

	//MSC
	rbr.msc = data->userMsc;
	//LAC
	rbr.lac = data->userLac;

	//B06	累积量信息
	rbr.accumlator_info = 1;
	//B07	资费信息
	rbr.tariff_info = 1;
	//B08	余额帐本改变的详细信息
	rbr.balance_info = 1;
	//B20	费率信息查询命令
	rbr.rating_info = 1;
	//B21	余额查询命令
	rbr.balance_query = 1;
/*
	pREMsg->set(RB_CODE_B_TARIFF_TIME, "1");			//B23	账期时间
	rbr.


*/
	//R07离线批价扣费标识
	string servAttr = string(bizMsg->m_szServiceContextIDStr)+string("CollectAttr");
	int ratingflag = m_smpara->GetBillAttr(servAttr);
	sprintf(value,"%d",ratingflag);
	if(ratingflag>=0)
	{
		ext.kv["ratingflag"] = value;
	}


	//B03总使用量
	//累计总使用量
	TUSU.duration += MSCC->USU0.duration;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

	TUSU.unitTotal += MSCC->USU0.unitTotal;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

	TUSU.unitInput += MSCC->USU0.unitInput;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

	TUSU.unitOutput += MSCC->USU0.unitOutput;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);

	//费率切换点之后的使用量
	if(1 == MSCC->USU1.tariffChangeUsage)
	{
		//累计总使用量
		TUSU.duration += MSCC->USU1.duration;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU druation[%u]", TUSU.duration);

		TUSU.unitTotal += MSCC->USU1.unitTotal;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitTotal[%lld]", TUSU.unitTotal);

		TUSU.unitInput += MSCC->USU1.unitInput;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitInput[%lld]", TUSU.unitInput);

		TUSU.unitOutput += MSCC->USU1.unitOutput;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "TUSU unitOutput[%lld]", TUSU.unitOutput);
	}

	if(MSCC->ProductOfferId.length())//如果网元上报Product-Offer-Id，按此来批价
	{
		sprintf(value, "%s", MSCC->ProductOfferId.c_str());
	}
	else
	{
		sprintf(value, "%lld", MSCC->ratingGroup);
	}
	//B038
	servAttr=string(bizMsg->m_szServiceContextIDStr)+string("CollectArrear");
	rbr.neg_debit= m_smpara->GetBillAttr(servAttr);
	if(rbr.neg_debit == -1)
		rbr.neg_debit = 1;

	totalusu.rating_group = value;
	//时长
//	pREMsg->add(RB_CODE_B_DEBIT, 3);
	totalusu.unit = 1;
	totalusu.amount = TUSU.duration;
	domain.dbv.push_back(totalusu);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);


	//总使用量
	totalusu.unit = 3;
	totalusu.amount = TUSU.unitTotal;
	domain.dbv.push_back(totalusu);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);


	//上行流量
	totalusu.unit = 4;
	totalusu.amount = TUSU.unitInput;
	domain.dbv.push_back(totalusu);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);


	//下行流量
	totalusu.unit = 5;
	totalusu.amount = TUSU.unitOutput;
	domain.dbv.push_back(totalusu);

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rating_group[%s]",totalusu.rating_group.c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose unit[%s]",RB_UNIT_STR_SECOND);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose amount[%ld]",totalusu.amount);
	//CER主叫
	ext.kv["CER"] = bizMsg->m_subNumber;

	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}
	servAttr=string(bizMsg->m_szServiceContextIDStr)+string("OffTime");
	int  eliminateflag = m_smpara->GetBillAttr(servAttr);
	sprintf(value,"%d",eliminateflag);
	if(eliminateflag>=0)
	{
		ext.kv["eliminateflag"] = value;
	}
	ext.kv["RTI"] = subVisit->sector_id;
	ext.kv["EVT"] = bizMsg->m_eventType;
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	ext.kv["sourceId"] = bizMsg->m_sourceId;
	ext.kv["operListId"] = bizMsg->m_operListId;
	ext.kv["batchId"] = bizMsg->m_sBatchId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "BatchID[%s]",bizMsg->m_sBatchId.c_str());

	//R3019 转为16进制后赋值
	char hexMsc[32] = {0};
	long mscVal = atol(rbr.msc.c_str()); // R1012
	DCCommonIF::DECTOHEX(mscVal, hexMsc, sizeof(hexMsc));
	ext.kv["R3019"] = hexMsc;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "4G[%d] compose R3019[%s] m_serviceContextID[%u] HEX SOURCE[%s]", bizMsg->m_if4Gpp, ext.kv["R3019"].c_str(), bizMsg->m_serviceContextID, rbr.msc.c_str());

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);
		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(domain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char *)m_en.data();
	bizMsg->m_vectorMsg.push_back(bizMsg->data);

	return RET_SUCCESS;
}



