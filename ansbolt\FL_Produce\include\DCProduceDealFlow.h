/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCProduceDealFlow.h
*Indentifier：
*
*Description：
*		应答流程组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_PRODUCE_DEAL_FLOW_H__
#define __DC_PRODUCE_DEAL_FLOW_H__
#include "DCBasePlugin.h"
#include "DCOBJSet.h"




class DCProduceDealFlow :  public DCBasePlugin
{
	public:	
		DCProduceDealFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCProduceDealFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
		DCOBJSetPool* m_pool;

};

#endif

