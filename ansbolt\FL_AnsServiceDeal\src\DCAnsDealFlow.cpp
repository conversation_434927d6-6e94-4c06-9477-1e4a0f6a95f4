#include "DCAnsDealFlow.h"
#include "DCAnsFmt.h"
#include "DCAnsVOICETEL.h"
#include "DCAnsSMSTEL.h"
#include "DCAnsDATATEL.h"
#include "DCAnsISMPTEL.h"
#include "DCAnsDSLTEL.h"
#include "DCAnsPGW.h"
#include "DCAns5G.h"
#include "DCRbMsgDef.h"
#include "DCLogMacro.h"

int DCAnsDealFlow::init()
{
	int ret = 0;	
	m_anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));

	DCAnsFmt*  ansfmt = new DCAnsFmt();
	m_ans[0] = ansfmt;
	
	DCAnsVOICETEL*  ansVoiceTel = new DCAnsVOICETEL();
	m_ans[1] = ansVoiceTel;

	DCAnsSMSTEL* ansSmsTel = new DCAnsSMSTEL();
	m_ans[2] = ansSmsTel;

	DCAnsDATATEL* ansDataTel = new DCAnsDATATEL();
	m_ans[3] = ansDataTel;

	DCAnsISMPTEL* ansIsmpTel = new DCAnsISMPTEL();
	m_ans[4] = ansIsmpTel;

	DCAnsDSLTEL* ansDslTel = new DCAnsDSLTEL();
	m_ans[5] = ansDslTel;

	DCAnsPGW* ansPgwTel = new DCAnsPGW();
	m_ans[6] = ansPgwTel;

	DCAns5G* ans5GTel = new DCAns5G();
	m_ans[7] = ans5GTel;
	return ret;
}

int DCAnsDealFlow::process(void* input, void* output)
{
	DCOBJSet* pset = (DCOBJSet*)input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	rbresult *base = pset->get<rbresult>();
	bizMsg->m_base = base;
	bizMsg->m_anspara = m_anspara;
		
	
	int ret = m_ans[0]->Work(pset);	
	if(bizMsg->m_serviceContextID == DATA_5G)
	{
		ret = m_ans[7]->Work(bizMsg);
	}
	else if(bizMsg->m_serviceContextID < VOICE || bizMsg->m_serviceContextID > PGW)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,RB_RBA_MSG_ERROR,"","unknown serviceContextID :%d",bizMsg->m_serviceContextID);
		return RB_RBA_MSG_ERROR;
	}
	else
	{
		ret = m_ans[bizMsg->m_serviceContextID]->Work(bizMsg);
	}
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "CALL RET=%d",ret);
	
	return ret;
}


DYN_PLUGIN_CREATE(DCAnsDealFlow, "FC_ANSDEAL", "FC_AnsServiceDeal", "1.0.0")



