/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCReBolt.h
*Indentifier：
*
*Description：
*      reqbolt主类
*Version：
*       V1.0
*Author:
*       ZYF
*Finished：
*       
*History:
*     
********************************************/

#ifndef DC_REBOLT_H_
#define DC_REBOLT_H_

#include <DCBolt.h>
#include <stdio.h>

#include "DCPluginManer.h"
#include "DCRbMsgDef.h"
using namespace ocs;


class DCReBolt: public tydic::storm::DCBolt {
public:
	int Initialize(const tydic::storm::DCStormConfig& config);
	int Process(tydic::storm::Tuple &tuple);
	DCReBolt();
	virtual ~DCReBolt();
	int Refresh(const char * path);
	int SetWacther();

private:
	int LoadRBAMsg(rbresult& rba);
	DCPluginManer m_pm;	
	int m_resultCode;
	
};


extern "C" {

tydic::storm::DCStormBase* CreateInstance()
{
	return new DCReBolt();
}

void ReleaseInstance(tydic::storm::DCStormBase* pStorm)
{
	if (pStorm != NULL)
		delete pStorm;
}

}

#endif /* TEST_CTESTBOLT_H_ */

