/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*        DCAnsBolt.h
*Indentifier：
*
*Description：
*      reqbolt主类
*Version：
*       V1.0
*Author:
*        ZY.F
*Finished：
*       
*History:
*     
********************************************/
#ifndef DC_ANSBOLT_H_
#define DC_ANSBOLT_H_

#include <DCBolt.h>
#include <stdio.h>
#include "DCPluginManer.h"
#include <time.h>
#include "DCSeriaOp.h"
#include <string.h>
#include <pthread.h>
#include <list>
#include "DCKpiSender.h"
#include "DCCommonIF.h"

class DCPerfTimeStats;
class DCAnsBolt: public tydic::storm::DCBolt {
public:
	int Initialize(const tydic::storm::DCStormConfig& config);
	int Process(tydic::storm::Tuple &tuple);
	DCAnsBolt();
	virtual ~DCAnsBolt();
	int Refresh(const char * path);
	int SetWacther();	
	void GetHostIp(std::string &IP);
	void Compress(std::string& buf);
	void ReSetUid(std::string& strMsgInfo, std::string& uid);
	void SplitString(const std::string& str, char sep, std::list<std::string>& vec);
	void svc();
	int SendToKpiBolt(std::string msginfo);

private:
	DCPluginManer m_pm;
	DCPerfTimeStats*  m_tstat;
	time_t m_checktime;
	char m_Topic[50];
	char m_payflagTopic[50];
	char m_testTopic[50];
	std::string m_strIP;
	DCSeriaEncoder* m_en;
	DCSeriaPrinter m_print;
	pthread_t m_tid;
	DCKpiMon *m_ptrBPMon;
};

extern "C" {

tydic::storm::DCStormBase* CreateInstance()
{
	return new DCAnsBolt();
}

void ReleaseInstance(tydic::storm::DCStormBase* pStorm)
{
	if (pStorm != NULL)
		delete pStorm;
}

}

#endif /* DC_ANSTBOLT_H_ */

