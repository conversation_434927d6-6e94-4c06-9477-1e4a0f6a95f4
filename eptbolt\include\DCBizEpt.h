/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizEpt.h
*Indentifier：
*		
*Description：
*		异常处理功能组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DCBIZ_EPT_H__
#define __DCBIZ_EPT_H__

#include "DCBizMsgDef.h"
#include "DCSeriaOp.h"
#include "DCOcpMsgDef.h"

//
class DCBizEpt
{
	public:

		DCBizEpt();

		virtual ~DCBizEpt();

	public:

		virtual int Work(void *data);

	protected:
		
		int DelSession(STBizMsg* bizMsg);
		int SendErrorResult(STBizMsg* bizMsg);	
		int SendReaTermRER(STBizMsg* bizMsg);
		int SendTermRER(STBizMsg* bizMsg);
		int RERVOICE(STBizMsg* bizMsg);
		int RERDATA(STBizMsg* bizMsg);
		int RERPGW(STBizMsg* bizMsg);
		int RER5G(STBizMsg* bizMsg);
		int RERISMP(STBizMsg* bizMsg);
		int RERDSL(STBizMsg* bizMsg);
		int SendErrorResult5G(STBizMsg* bizMsg);
		void UpdateSession(STBizMsg* bizMsg);
		void UpdateErrorCode(STBizMsg* bizMsg);
		
		//全业务不上送TermRER时更新会话表
		int UpdateSessionWithoutTermRER(STBizMsg* bizMsg);

		void UpdateSessionPS(STBizMsg* bizMsg);

		int GetUSUAmountFromExt(ocs::SCCRDataUnit& USU, const char* szUsuamount);
		
		int GetSessionNum(STBizMsg* bizMsg);
		std::multimap<string,string>* m_pMsendMsg;
		DCSeriaEncoder m_en;		
		DCSeriaPrinter m_print;
};

#endif

