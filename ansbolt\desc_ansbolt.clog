@组件名称：libAnsBolt.so.x.x.x
@创建日期：2016/6/04
@修改日期：2016/12/10
@当前版本：1.0.0
@功能描述：应答bolt，接收rba消息，调用应答流程组件，发送cca和异常消息
@版本历史：
@1.0.0：
---2016/7/06：出单流程测试，组件添加描述信息
---2016/7/11: 参数读取配置文件修改
---2016/7/22: sp RBA超时异常，跨越截单修改
---2016/8/15: 增加ACE接口
---2016/10/27: 添加性能统计日志
---2016/11/04: 接口变动，统一在消息头前加上固定16位的时间戳，用于计算框架上下游消息的等待时间
---2016/11/09: 将ACE日志转接到DCLOG中
---2016/11/22: 增加管道消息积压统计接口
---2016/12/02: cstorm增加获取执行队列的接口
---2016/12/08: 增加bolt监听动态日志级别，数据刷新功能
---2016/12/09: 性能日志接口变更
---2016/12/10: 修正M2DB中的ACE日志级别映射关系