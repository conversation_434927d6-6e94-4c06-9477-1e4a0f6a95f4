#ifndef __BYTESWAP_H__
#define __BYTESWAP_H__
#include <stdint.h>
#include <sys/types.h>

#if defined(__WIN32__)||defined(__WIN64__)
#include <Winsock2.h>
#else
#include <netinet/in.h>
#include <arpa/inet.h>
#endif // defined

/*** define __BYTE_BIG_ENDIAN__ ***/
#undef __BYTE_BIG_ENDIAN__
#undef __BYTE_LITTLE_ENDIAN__
#ifdef __GNUC__
#if defined(__BYTE_ORDER__)
#if __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
#define __BYTE_BIG_ENDIAN__ 1
#endif // __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
#elif __BIG_ENDIAN__ == 1
#define __BYTE_BIG_ENDIAN__ 1
#elif defined(__ia64__)
#define __BYTE_BIG_ENDIAN__ 1
#endif //__BYTE_ORDER__
#endif // __GNUC__

#if defined(__HP_aCC)
#if _BIG_ENDIAN == 1
#define __BYTE_BIG_ENDIAN__  1
#endif // _BIG_ENDIAN
#endif // defined

#if defined(__xlC__) && defined(_AIX)
#define __BYTE_BIG_ENDIAN__  1
#endif // defined

/*** define ntohX, htonX ***/
#ifdef __BYTE_BIG_ENDIAN__
#define ntoh16(x) (x)
#define ntoh32(x) (x)
#define ntoh64(x) (x)

#define hton16(x) (x)
#define hton32(x) (x)
#define hton64(x) (x)

#else
#define __BYTE_LITTLE_ENDIAN__

inline uint16_t ntoh16(uint16_t x)
{
    return ntohs(x);
}

inline uint32_t ntoh32(uint32_t x)
{
    return ntohl(x);
}

inline uint64_t ntoh64(uint64_t x)
{
    return (uint64_t)ntohl((uint32_t)(x&0x0FFFFFFFF))<<32 | (uint64_t)ntohl((uint32_t)(x>>32));
}

inline uint16_t hton16(uint16_t x)
{
    return htons(x);
}

inline uint32_t hton32(uint32_t x)
{
    return htonl(x);
}

inline uint64_t hton64(uint64_t x)
{
	return (uint64_t)htonl((uint32_t)(x&0x0FFFFFFFF))<<32 | (uint64_t)htonl((uint32_t)(x>>32));
}

#endif // __BYTE_BIG_ENDIAN__

/*** define bswapX ***/
inline void _bswap8_(uint8_t &x, uint8_t &y)
{
	uint8_t z = x;
	x = y;
	y = z;
}

inline uint16_t bswap16(uint16_t x)
{
	return (x&0x0FF)<<8 | x>>8;
}

inline uint32_t bswap32(uint32_t x)
{
	uint8_t * v = (uint8_t*)&x;
	_bswap8_(v[0], v[3]);
	_bswap8_(v[1], v[2]);
	return x;
}

inline uint64_t bswap64(uint64_t x)
{
	uint8_t * v = (uint8_t*)&x;
	_bswap8_(v[0], v[7]);
	_bswap8_(v[1], v[6]);
	_bswap8_(v[2], v[5]);
	_bswap8_(v[3], v[4]);
	return x;
}

inline void bswap16(uint16_t *x)
{
	uint8_t * v = (uint8_t*)x;
	_bswap8_(v[0], v[1]);
}

inline void bswap32(uint32_t *x)
{
	uint8_t * v = (uint8_t*)x;
	_bswap8_(v[0], v[3]);
	_bswap8_(v[1], v[2]);
}

inline void bswap64(uint64_t *x)
{
	uint8_t * v = (uint8_t*)x;
	_bswap8_(v[0], v[7]);
	_bswap8_(v[1], v[6]);
	_bswap8_(v[2], v[5]);
	_bswap8_(v[3], v[4]);
}

#endif // __BYTESWAP_H__
