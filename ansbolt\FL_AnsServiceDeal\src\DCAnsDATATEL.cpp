#include "DCAnsDATATEL.h"
#include "ErrorCode.h"
#include "DCRbMsgDef.h"
#include "DCOcpMsgDef.h"
#include "DCAnsPara.h"
#include "TConfig.h"
#include "DCLogMacro.h"
#include "func_sqlindex.h"
#include "DCCommonIF.h"
#include "UStaMsg.h"
//#include "Publiclib.h"


DCAnsDATATEL::DCAnsDATATEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "", "");
}

DCAnsDATATEL::~DCAnsDATATEL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "", "");
}

int DCAnsDATATEL::ComposeCCA(STBizMsg* bizMsg)
{
	int ret = 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "begin compose CCA");
	if(1==bizMsg->m_requestType)
	{
		bizMsg->m_requestType = SM_SESSION_UPDATE_FIRST_CODE;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request type :%d",bizMsg->m_requestType);

	switch(bizMsg->m_requestType)
	{
		case SM_SESSION_INITIAL_CODE:
			{
				ret = InitAns(bizMsg);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
				return  ret;
			}
			break;
		case SM_SESSION_UPDATE_FIRST_CODE:
		case SM_SESSION_UPDATE_CODE:
			{
				ret = UpdateAns(bizMsg);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
				return  ret;
			}
			break;
		case SM_SESSION_TERMINATION_CODE:
			{
				ret = TermAns(bizMsg);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
				return  ret;
			}
			break;
		case SM_SESSION_XDR_CODE:
			{
				ret = XdrEvent(bizMsg);	
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_NULL_MSG, "", "RET[%d]", ret);
				return  ret;
			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "unknow session state", "");
				return RB_SM_UNABLE_TO_COMPLY;
			}
			break;
	}
	return RET_SUCCESS;
}


int DCAnsDATATEL::InitAns(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCAnsDATATEL::UpdateAns(STBizMsg* bizMsg)
{
	int  ret								= RET_SUCCESS;
	long long ratingGroup					= 0;
	char szProductOfferID[128]              ={0};
	long lCurrentCCRTime                    = 0;
	long lastRsuObject 					 	= 0;   	//上次预占流量
	long lastRsuTime           				= 0;	//上次预占时长
	long lastTimestamp 						= 0;	//上次 CCR 时间戳
	int  validityTime						= 0;
	int  quotaConsumeTime					= 0;
	int  volumeQuotaThreshold				= 0;
	int  volumeQuotaThreshold2			    = 0;
	int  timeQuotaThreshold					= 0;
	int  quotaHoldingTime					= 0;
	long long tariffTimeCharge				= 0;
	int  chargeType							= 0;
	int  lastoctCdrFlag						= 0;
	long lastoctCdrTotalOctets				= 0;


	char value[BIZ_TEMP_LEN_256] 		= {0};
	int cost_unit 							= 0;
	int cost_amount 						= 0;
	int balance                 			= 0;	//余额信息
	char szChildSessionID[256]              = {0};
	int nSendCCAFlag                        = 0;
	long discount_fee                       = 0; 
	long discount_totalfee                  = 0; 
	char PLCA_LOC[24]						={0};

	SREAInfo REAMsg ;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	DCAnsPara* m_smpara = (DCAnsPara*)bizMsg->m_anspara;
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;
	
	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend StragegyId[%ld]", StragegyId);
	int payFlag = atoi(ext->kv["PayFlag"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend PayFlag[%d]", payFlag);


	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());

	ocs::SCCRDataUnit USU;
	ocs::SCCRDataUnit TUSU;
	//检查RB返回的信息
	chargeType = base->gsv[0].unit;
	if((chargeType != RB_UNIT_CODE_SECOND) && (chargeType != RB_UNIT_CODE_TOTAL_BYTES) && (chargeType != RB_UNIT_CODE_UP_BYTES) && (chargeType != RB_UNIT_CODE_DOWN_BYTES) )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "chargeType error");
		return RB_RBA_MSG_ERROR;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "chargeType[%d]", chargeType);


	//子会话判定要在return之前
	strcpy(szChildSessionID, bizMsg->m_sessionID);
	for(int i = strlen(bizMsg->m_sessionID)-1; i>=0; i--)
	{
		if(bizMsg->m_sessionID[i] == ';')
		{
			bizMsg->m_sessionID[i] = '\0';
			break;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "child session[%s]", szChildSessionID);

	//查询子会话信息
	UDBSQL *pQuery = dbm->GetSQL(DATA_Select_SessionStoreRecord_RG);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, szChildSessionID);
		pQuery->Execute();
      	if(pQuery->Next())
		{

			pQuery->GetValue(1, nSendCCAFlag);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSendCCAFlag[%d]", nSendCCAFlag);

			pQuery->GetValue(2, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ccr-time[%s]", value);

			//检查是否超时，必须在更新扣费信息到数据库之前做检查。
			/*bizMsg->timestamps = DCC_MSG_TIMEOUT_SEC + DCCommonIF::time2sec(atol(value));
			time_t cursec= time(NULL);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "cur[%ld],dcc[%ld]", cursec, bizMsg->timestamps);
			if(bizMsg->timestamps <= cursec)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "timeout in-time[%ld],cur[%ld]", bizMsg->timestamps-DCC_MSG_TIMEOUT_SEC, cursec);
				ret = RET_ERROR;
				return ret;
			}*/

			pQuery->GetValue(3, ratingGroup);
			bizMsg->m_ratingGroup = ratingGroup;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rating group[%lld]", ratingGroup);

			pQuery->GetValue(4, validityTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "validityTime[%d]", validityTime);

			pQuery->GetValue(5, quotaConsumeTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quotaConsumeTime[%d]", quotaConsumeTime);

			pQuery->GetValue(6, volumeQuotaThreshold);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold[%d]", volumeQuotaThreshold);

			//动态门限SM_LNG_VOLUME_QUOTA_THRESHO_1
			pQuery->GetValue(7, volumeQuotaThreshold2);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold2[%d]", volumeQuotaThreshold2);

			pQuery->GetValue(8, timeQuotaThreshold);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "timeQuotaThreshold[%d]", timeQuotaThreshold);

			pQuery->GetValue(9, quotaHoldingTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quotaHoldingTime[%d]", quotaHoldingTime);

			pQuery->GetValue(10, lastRsuObject);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last Rsu Object[%ld]", lastRsuObject);

			pQuery->GetValue(11, lastRsuTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last Rsu time[%ld]", lastRsuTime);

			//本次 CCR 的时间戳
			pQuery->GetValue(12, lCurrentCCRTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "current CCR time[%ld]", lCurrentCCRTime);

			pQuery->GetValue(13, lastTimestamp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "lastTimestamp[%ld]", lastTimestamp);

			pQuery->GetValue(14, lastoctCdrFlag);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last CDR_OCT_CARD_FLAG[%d]", lastoctCdrFlag);

			pQuery->GetValue(15, lastoctCdrTotalOctets);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last CDR_OCT_CARD_TOTAL_VOLUME[%d]", lastoctCdrTotalOctets);

			//OCP_STR_PRODUCT_OFFER_ID
			pQuery->GetValue(16, szProductOfferID);
			strcpy(bizMsg->m_ProductOfferId, szProductOfferID);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "product offer id[%s]", szProductOfferID);

			pQuery->GetValue(17, value);
			strncpy(REAMsg.sz_balanceInfo,value,sizeof(REAMsg.sz_balanceInfo));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "balanceinfo[%s]", value);

			pQuery->GetValue(18, value);
			strncpy(REAMsg.sz_balanceInfo2,value,sizeof(REAMsg.sz_balanceInfo2));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "balanceinfo2[%s]", value);

			pQuery->GetValue(19, REAMsg.sz_accumlatorInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "accumlatorInfo[%s]", REAMsg.sz_accumlatorInfo);

			pQuery->GetValue(20, REAMsg.sz_tariffIdInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "tariffIdInfo[%s]", REAMsg.sz_tariffIdInfo);

			pQuery->GetValue(21, value);
			strncpy(REAMsg.sz_chargeInfo,value,sizeof(REAMsg.sz_chargeInfo));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "chargeInfo[%s]", value);

			pQuery->GetValue(22, value);
			strncpy(REAMsg.szPricingPlanID,value,sizeof(REAMsg.szPricingPlanID));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "PricingPlanID[%s]", value);

			pQuery->GetValue(23, value);
			USU.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%s]", value);

			pQuery->GetValue(24, value);
			USU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%s]", value);

			pQuery->GetValue(25, value);
			USU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%s]", value);

			pQuery->GetValue(26, value);
			USU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%s]", value);

			pQuery->GetValue(27, value);
			TUSU.duration = atoi(value)+USU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%s]", value);

			pQuery->GetValue(28, value);
			TUSU.unitInput = atol(value)+USU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitInput[%s]", value);

			pQuery->GetValue(29, value);
			TUSU.unitOutput = atol(value)+USU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitOutput[%s]", value);

			pQuery->GetValue(30, value);
			TUSU.unitTotal = atol(value)+USU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitTotal[%s]", value);

			//PLCA_LOC
			pQuery->GetValue(31, PLCA_LOC);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "PLCA_LOC[%s]", PLCA_LOC);

                     //SubNumber
			pQuery->GetValue(32, bizMsg->m_subNumber);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "m_subNumber[%s]", bizMsg->m_subNumber);

                     // OCP_INT_REQ_NBR
			pQuery->GetValue(33, value);
			bizMsg->m_requestNumber = atoi(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RequestNumber[%u]", bizMsg->m_requestNumber);

			pQuery->GetValue(34, value);
			//bizMsg->m_serial = atol(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "serial[%lld]", bizMsg->m_serial);

			pQuery->GetValue(38, value);//SM_INT_SESSION_TYPE
			bizMsg->m_serviceContextID = atoi(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "m_serviceContextID[%d]", bizMsg->m_serviceContextID);

			pQuery->GetValue(39, value);
			strncpy(REAMsg.sz_oriChargeInfo,value,sizeof(REAMsg.sz_oriChargeInfo));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "oriChargeinfo[%s]", value);

			pQuery->GetValue(40, value);
			//bizMsg->m_serial = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "billcycle[%s]", value);


			if(!bizMsg->m_ReDealAns && strlen(value) > 0 && ext->kv["BILLCYCLE"].size() > 0 && ext->kv["BILLCYCLE"] != value)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "BILLCYCLE change[%s],cut cdr", ext->kv["BILLCYCLE"].c_str());
				bizMsg->m_ReDealAns = true;
				bizMsg->m_longCDR = 2;//与流量超长截单方式一样
				return RET_CDR;
			}
  		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	if(SM_SESSION_UPDATE_CODE == bizMsg->m_requestType)
	{
		ret = ModifyREAMsg(bizMsg,REAMsg);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_RATING_FAILED, "", "parse rating info failed");
			return SM_OCP_RATING_FAILED;
		}

		discount_totalfee = discount_fee+base->dist_fee;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total discount fee[%ld]", discount_totalfee);

	}		
	//动态预占处理
	long timesec=abs((long)difftime(DCCommonIF::time2sec(lCurrentCCRTime),DCCommonIF::time2sec(lastTimestamp)));//获取2次update CCR 之间的间隔时间，单位: s
	balance = AccumlateBalance(bizMsg);//获取余额信息
	
	//授权使用量
	if(base->gsv.size()==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED,  "", "no gsu information");
		return SM_OCP_RATING_FAILED;	
	}

	int gUnit                               = base->gsv[0].unit;	
	long long gNum                          = base->gsv[0].amount;
	long lnRelastGsuObject               	= 0;
	long lnRelastGsuTime 			  		= 0;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gunit[%d],gnum[%d]", gUnit, gNum);
	
	if(RB_UNIT_CODE_TOTAL_BYTES == gUnit)
	{
		lnRelastGsuObject = gNum;
	}
	else if(RB_UNIT_CODE_SECOND == gUnit)
	{
		lnRelastGsuTime = gNum;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gsu object[%ld],gsu time[%d]", lnRelastGsuObject, lnRelastGsuTime);

	//info 日志
	DCDATLOG("SM00011:%d%ld", gUnit, gNum);

	
	//info 日志
	DCDATLOG("SM00012:%s%s%s%s%s%s%ld", REAMsg.sz_balanceInfo, REAMsg.sz_accumlatorInfo,\
										REAMsg.sz_tariffIdInfo, REAMsg.sz_chargeInfo,\
										REAMsg.sz_balanceInfo2, REAMsg.szPricingPlanID, base->evt_id);

	UDBSQL *pExec = dbm->GetSQL(DATA_UpdateSessionStoreRbansRg);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			// 更新子会话信息
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, 0); // SM_INT_RESULT_CODE 更新结果码为0
			pExec->BindParam(2, lastRsuObject);
			pExec->BindParam(3, lastRsuTime);
			pExec->BindParam(4, lCurrentCCRTime); // RE_LNG_LAST_CCR_TIME
			pExec->BindParam(5, REAMsg.sz_balanceInfo);
			pExec->BindParam(6, REAMsg.sz_balanceInfo2);
			pExec->BindBlobParam(7, REAMsg.sz_accumlatorInfo);
			pExec->BindBlobParam(8, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(9, REAMsg.sz_chargeInfo);
			pExec->BindParam(10, REAMsg.szPricingPlanID);
			pExec->BindParam(11, base->evt_id);
			pExec->BindParam(12, STATUS_IDLE);		 // SM_INT_SESSION_STATUS
			pExec->BindParam(13, gUnit);			 // RE_INT_LAST_GSU_UNIT
			pExec->BindParam(14, lnRelastGsuObject); // RE_LNG_LAST_GSU_TOTAL_OCT
			pExec->BindParam(15, lnRelastGsuTime);	 // RE_LNG_LAST_GSU_TIME
			int octCdrFlag = 0;
			long octCdrTotalVolume = 0;

			if (SM_SESSION_UPDATE_CODE == bizMsg->m_requestType)
			{
				if (!base->dbv.empty() && 3 == base->dbv[0].unit)
				{
					octCdrFlag = 1;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "B036=1,volum card", "");

					octCdrTotalVolume = base->dbv[0].amount;
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "B036=1,volum=[%d]", octCdrTotalVolume);
				}
			}

			if (1 == lastoctCdrFlag)
			{
				pExec->BindParam(16, lastoctCdrFlag);
			}
			else
			{
				pExec->BindParam(16, octCdrFlag);
			}
			octCdrTotalVolume += lastoctCdrTotalOctets;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total CDR_OCT_CARD_TOTAL_VOLUME[%d]", octCdrTotalVolume);
			pExec->BindParam(17, octCdrTotalVolume);
			pExec->BindParam(18, discount_totalfee);
			pExec->BindParam(19, (long)TUSU.duration);
			pExec->BindParam(20, TUSU.unitTotal);
			pExec->BindParam(21, TUSU.unitInput);
			pExec->BindParam(22, TUSU.unitOutput);
			pExec->BindParam(23, (long)balance);
			pExec->BindParam(24, StragegyId);
			pExec->BindParam(25, payFlag);
			pExec->BindParam(26, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(27, OfrInstId.c_str());
			pExec->BindParam(28, ext->kv["BILLCYCLE"].c_str());
			pExec->BindParam(29, szChildSessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child session charge info OK", "");

	//保存量本信息，累积量信息
	ret = UpsetRatingMsg(bizMsg);
	if(RET_ERROR == ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "",	"UpsetRatingMsg ERROR!","");
	}


	//普通异常处理中更新nSendCCAFlag=1，过程中余额不足异常中更新nSendCCAFlag=2
	//这两种都不需要再返回给网元CCA消息
	if((1 == nSendCCAFlag) || (2 == nSendCCAFlag))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",	"ret cdr","");
		return RET_CDR;
	}



	//组装CCA消息
	//费率切换点
	if(base->tariff_change_time != 0)
	{
		tariffTimeCharge = base->tariff_change_time;
		{
			struct tm t;

			long long temp = tariffTimeCharge%10000000000;

			t.tm_year=(tariffTimeCharge/10000000000)-1900;
			t.tm_mon=(temp/100000000)-1;//0~11
			temp%=100000000;
			t.tm_mday=(temp/1000000);
			temp%=1000000;
			t.tm_hour=temp/10000;
			temp%=10000;
			t.tm_min=temp/100;
			temp%=100;
			t.tm_sec=temp;
			t.tm_isdst=0;

			tariffTimeCharge = mktime(&t);
			tariffTimeCharge += OCP_TIME_BASE;
			
		}
	}
	//获取B036,B037
	ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "error[cost-information]");
	}

	
	ocs::SCCAMsg cca;
	ocs::DATAUSU gsu;
	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = 2001;
       cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 3;
	
	if(tariffTimeCharge)
	{
		gsu.TariffTimeChange = tariffTimeCharge;
	}
	
	gsu.Unit = gUnit;
	if(RB_UNIT_CODE_SECOND == gUnit)
	{
		gsu.CCTime = gNum;
	}
	else if(RB_UNIT_CODE_TOTAL_BYTES == gUnit)
	{
		gsu.Unittotal = gNum;	
	}
	else if(RB_UNIT_CODE_UP_BYTES == gUnit)
	{
		gsu.Unitinput = gNum;		
	}
	else if(RB_UNIT_CODE_DOWN_BYTES == gUnit)
	{
		gsu.Unitoutput = gNum;			
	}

	cca.FinalFlag = base->final_flag;
	SPSPara* para = m_smpara->GetPSPara();
	if (base->final_flag)
	{
		gsu.FinalUnitAction = para->finalUnitAction;
	}

	if(1== para->finalUnitAction)
	{	
		//重定向
		gsu.RedirectAddressType = para->redirectType;
		gsu.RedirectServerAddress = para->redirectServer;
	}
	gsu.ValidityTime = validityTime;
	gsu.QuotaHoldingTime = quotaHoldingTime;
	gsu.QuotaConsumptionTime = quotaConsumeTime;
	gsu.TimeQuotaThreshold = timeQuotaThreshold;
	
	if((volumeQuotaThreshold2 != 0) && (gNum <= volumeQuotaThreshold) && (RB_UNIT_CODE_TOTAL_BYTES == gUnit))//授权量<=授权量，并且是按流量计费的，取门限值2
	{
		gsu.VolumeQuotaThreshold = volumeQuotaThreshold2;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold2[%d]", volumeQuotaThreshold2);
	}
	else
	{	
		gsu.VolumeQuotaThreshold = volumeQuotaThreshold;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold[%d]", volumeQuotaThreshold);
	}

	gsu.ratinggroup    = ratingGroup;
	gsu.ProductOfferId = szProductOfferID;
	if(m_smpara->GetPSPara()->nFinalUintResultCode && base->final_flag)
		gsu.ResultCode = m_smpara->GetPSPara()->nFinalUintResultCode;
	else
		gsu.ResultCode = bizMsg->m_resultcode;
	
	cca.MSCC.push_back(gsu);

	if(bizMsg->m_requestType == SM_SESSION_UPDATE_CODE)
	{
		cca.Cost.costUnit = cost_unit;
		cca.Cost.valueDigits = cost_amount;	
		cca.Cost.exponent = 0;
	}

	if(bizMsg->m_testFlag)
	{
		cca.BalaInfo = REAMsg.sz_balanceInfo;
		cca.AccuInfo = REAMsg.sz_accumlatorInfo; 
		cca.TariInfo = REAMsg.sz_tariffIdInfo;
		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		cca.AcctInfo = flag;
	}

	// 写队列	
	//ret = ProduceCCA(bizMsg, cca, bizMsg->m_anstopic);

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce DATA failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
/*	bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce DATA failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID,  "Produce DATA Successful topic[%s]", bizMsg->m_anstopic);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce DATA Successful topic[%s]", bizMsg->m_anstopic);*/
	return ret;
}


int DCAnsDATATEL::TermAns(STBizMsg* bizMsg)
{
	int ret									= RET_SUCCESS;
	long long ratingGroup					= 0;
	long lCurrentCCRTime                    = 0;
	int aocType 							= 0;
	int totalRgNum							= 0;	//剩余子会话数统计
	int sucesRGNum                          = 0;    //成功子会话数
	int lastoctCdrFlag						= 0;
	long lastoctCdrTotalOctets				= 0;

	long discount_fee                       = 0; 
	long discount_totalfee                  = 0; 
	int  nLastGsuUnit                       = 0;
	
	char value[BIZ_TEMP_LEN_256] 		= {0};
	int cost_unit 							= 0;
	int cost_amount 						= 0;
	int balance                 			= 0;	//余额信息
	long lEventTypeID                       = 0;//事件类型ID
	int nSendCCAFlag                        = 0;
	int iaocflag                            = 0;
	char szChildSessionID[256]              = {0};

	map<long long, STCodeOfr> MSCCResult;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	SREAInfo REAMsg;
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;
	DCAnsPara* anspara =  bizMsg->m_anspara;
	
	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend StragegyId[%ld]", StragegyId);
	int payFlag = atoi(ext->kv["PayFlag"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend PayFlag[%d]", payFlag);


	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());

	ocs::SCCRDataUnit USU;
	ocs::SCCRDataUnit TUSU;	

	//会话拆分需要在return之前进行
	strcpy(szChildSessionID, bizMsg->m_sessionID);//sesseionID 表示子会话,  bizMsg->m_sessionID 表示主会话
	for(int i = strlen(bizMsg->m_sessionID)-1; i>=0; i--)
	{
		if(bizMsg->m_sessionID[i] == ';')
		{
			bizMsg->m_sessionID[i] = '\0';
			break;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "real session[%s]", szChildSessionID);

	//查询子会话信息
	UDBSQL *pQuery = dbm->GetSQL(DATA_Select_SessionStoreRecord_RG);
	try
	{	
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, szChildSessionID);
		pQuery->Execute();
      	       if(pQuery->Next())
		{

			pQuery->GetValue(1,nSendCCAFlag);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSendCCAFlag[%d]", nSendCCAFlag);

			pQuery->GetValue(2, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ccr-time[%s]", value);

			//检查是否超时，必须在更新扣费信息到数据库之前做检查。
			/*bizMsg->timestamps = DCC_MSG_TIMEOUT_SEC + DCCommonIF::time2sec(atol(value));
			time_t cursec= time(NULL);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID, "cur[%ld],dcc[%ld]", cursec, bizMsg->timestamps);
			if(bizMsg->timestamps <= cursec)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  bizMsg->m_sessionID, "timeout in-time[%ld],cur[%ld]", bizMsg->timestamps-DCC_MSG_TIMEOUT_SEC, cursec);
				ret = RET_ERROR;
				return ret;
			}*/

			pQuery->GetValue(3, ratingGroup);
			bizMsg->m_ratingGroup = ratingGroup;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rating group[%ld]", ratingGroup);

			//本次 CCR 的时间戳
			pQuery->GetValue(12, lCurrentCCRTime);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "current CCR time[%ld]", lCurrentCCRTime);


			pQuery->GetValue(14, lastoctCdrFlag);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last CDR_OCT_CARD_FLAG[%d]", lastoctCdrFlag);

			pQuery->GetValue(15, lastoctCdrTotalOctets);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last CDR_OCT_CARD_TOTAL_VOLUME[%d]", lastoctCdrTotalOctets);

			pQuery->GetValue(16, value);
			strcpy(bizMsg->m_ProductOfferId, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "product offer id[%s]", value);

			//CDR_PUB_STR_BALANCEINFO
			pQuery->GetValue(17, value);
			strncpy(REAMsg.sz_balanceInfo,value,sizeof(REAMsg.sz_balanceInfo));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "balanceinfo[%s]", value);

			pQuery->GetValue(18, value);
			strncpy(REAMsg.sz_balanceInfo2,value,sizeof(REAMsg.sz_balanceInfo2));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "balanceinfo2[%s]", value);

			pQuery->GetValue(19, REAMsg.sz_accumlatorInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "accumlatorInfo[%s]", REAMsg.sz_accumlatorInfo);

			pQuery->GetValue(20, REAMsg.sz_tariffIdInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "tariffIdInfo[%s]", REAMsg.sz_tariffIdInfo);

			pQuery->GetValue(21, value);
			strncpy(REAMsg.sz_chargeInfo,value,sizeof(REAMsg.sz_chargeInfo));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "chargeInfo[%s]", value);

			pQuery->GetValue(22, value);
			strncpy(REAMsg.szPricingPlanID,value,sizeof(REAMsg.szPricingPlanID));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "PricingPlanID[%s]", value);


			pQuery->GetValue(23, value);
			USU.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%s]", value);

			pQuery->GetValue(24, value);
			USU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%s]", value);

			pQuery->GetValue(25, value);
			USU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%s]", value);

			pQuery->GetValue(26, value);
			USU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%s]", value);

			pQuery->GetValue(27, value);
			TUSU.duration = atoi(value)+USU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%s]", value);

			pQuery->GetValue(28, value);
			TUSU.unitInput = atol(value)+USU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitInput[%s]", value);

			pQuery->GetValue(29, value);
			TUSU.unitOutput = atol(value)+USU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitOutput[%s]", value);

			pQuery->GetValue(30, value);
			TUSU.unitTotal = atol(value)+USU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitTotal[%s]", value);

			pQuery->GetValue(31, aocType);
			bizMsg->m_naoc_type = aocType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "aoc type[%d]", aocType);

			pQuery->GetValue(32, bizMsg->m_subNumber);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "bizMsg->m_subNumber[%s]", bizMsg->m_subNumber);

			// OCP_INT_REQ_NBR
			pQuery->GetValue(33, value);
			bizMsg->m_requestNumber = atoi(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RequestNumber[%u]", bizMsg->m_requestNumber);

			pQuery->GetValue(34, value);
			//bizMsg->m_serial = atol(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "serial[%lld]", bizMsg->m_serial);

			//转售提醒
			pQuery->GetValue(35, value);
			bizMsg->m_userType= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "usertype [%s]", value);

			//转售提醒
			pQuery->GetValue(36, bizMsg->m_mvnoId);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "mvnoid [%s]", bizMsg->m_mvnoId);

			pQuery->GetValue(37, value);//RE_INT_LAST_GSU_UNIT
			nLastGsuUnit= atoi(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last gsu unit[%d]", nLastGsuUnit);

			pQuery->GetValue(38, value);//SM_INT_SESSION_TYPE
			bizMsg->m_serviceContextID = atoi(value);
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "m_serviceContextID[%d]", bizMsg->m_serviceContextID);

			pQuery->GetValue(39, value);
			strncpy(REAMsg.sz_oriChargeInfo,value,sizeof(REAMsg.sz_oriChargeInfo));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "oriChargeInfo[%s]", value);

			pQuery->GetValue(40, value);
			//bizMsg->m_serial = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "billcycle[%s]", value);
			if(!bizMsg->m_ReDealAns && strlen(value) > 0 && ext->kv["BILLCYCLE"].size() > 0 && ext->kv["BILLCYCLE"] != value)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "BILLCYCLE change[%s],cut cdr", ext->kv["BILLCYCLE"].c_str());
				bizMsg->m_ReDealAns = true;
				bizMsg->m_longCDR = 2;//与流量超长截单方式一样
				return RET_CDR;
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	lEventTypeID= base->evt_id;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "get R616[%ld]", lEventTypeID);

	ret = ModifyREAMsg(bizMsg,REAMsg);
	if(ret)
	{

	}

	discount_totalfee = discount_fee+base->dist_fee;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total discount fee[%ld]", discount_totalfee);


	//超短话单:按时长
	if ( 1== nLastGsuUnit && bizMsg->m_serviceContextID == DATA)
	{
		int iAAAtMinTime = 0;//AAA业务时长计费超短阀值配置参数
		iAAAtMinTime = anspara->GetCommonPara()->iAAAMinTime;

		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short cdr, iAAAtMinTime[%d],total usu[%d]", iAAAtMinTime,TUSU.duration);

		if(iAAAtMinTime > 0  && (TUSU.duration < iAAAtMinTime) )
		{
			TUSU.duration = 0;
			TUSU.unitTotal = 0;
			TUSU.unitInput = 0;
			TUSU.unitOutput = 0;
			 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short usu 0");

		}
	}
	
	
		
	//超短话单:按流量
	int nShorOctet = 0;
	nShorOctet = anspara->GetPSPara()->shortCDROctet;
	if(nShorOctet && (TUSU.unitTotal  < nShorOctet))
	{
		TUSU.duration = 0;
		TUSU.unitTotal = 0;
		TUSU.unitInput = 0;
		TUSU.unitOutput = 0;		
		 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "short usu 0");
	}

	
	//info 日志
	DCDATLOG("SM00012:%s%s%s%s%s%s%ld", REAMsg.sz_balanceInfo, REAMsg.sz_accumlatorInfo,\
										REAMsg.sz_tariffIdInfo, REAMsg.sz_chargeInfo,\
										REAMsg.sz_balanceInfo2, REAMsg.szPricingPlanID, lEventTypeID);

	UDBSQL *pExec = dbm->GetSQL(DATA_UpdateSessionStoreRbansRg);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{

			// 更新子会话信息
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_resultcode); // SM_INT_RESULT_CODE
			pExec->BindParam(4, lCurrentCCRTime);	   // RE_LNG_LAST_CCR_TIME
			pExec->BindParam(5, REAMsg.sz_balanceInfo);
			pExec->BindParam(6, REAMsg.sz_balanceInfo2);
			pExec->BindBlobParam(7, REAMsg.sz_accumlatorInfo);
			pExec->BindBlobParam(8, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(9, REAMsg.sz_chargeInfo);
			pExec->BindParam(10, REAMsg.szPricingPlanID);
			pExec->BindParam(11, lEventTypeID);
			pExec->BindParam(12, RECVRB_ACTION);
			pExec->BindParam(13, nLastGsuUnit);
			int octCdrFlag = 0;
			long octCdrTotalVolume = 0;
			if (base->dbv.size() > 0 && 3 == base->dbv[0].unit)
			{
				octCdrFlag = 1;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "B036=1,volum card", "");

				octCdrTotalVolume = base->dbv[0].amount;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "B036=1,volum=[%d]", octCdrTotalVolume);
			}

			if (1 == lastoctCdrFlag)
			{
				pExec->BindParam(16, lastoctCdrFlag);
			}
			else
			{
				pExec->BindParam(16, octCdrFlag);
			}

			octCdrTotalVolume += lastoctCdrTotalOctets;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total CDR_OCT_CARD_TOTAL_VOLUME[%d]", octCdrTotalVolume);
			pExec->BindParam(17, octCdrTotalVolume);
			pExec->BindParam(18, discount_totalfee);
			pExec->BindParam(19, (long)TUSU.duration);
			pExec->BindParam(20, TUSU.unitTotal);
			pExec->BindParam(21, TUSU.unitInput);
			pExec->BindParam(22, TUSU.unitOutput);
			pExec->BindParam(23, (long)balance);
			pExec->BindParam(24, StragegyId);
			pExec->BindParam(25, payFlag);
			pExec->BindParam(26, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(27, OfrInstId.c_str());

			pExec->BindParam(28, ext->kv["BILLCYCLE"].c_str());
			pExec->BindParam(29, szChildSessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "term sql Code[%s] execption[%s], error code[%d]", DATA_UpdateSessionStoreRbansRg, e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child session charge info OK", "");

	//保存量本信息，累积量信息
	ret = UpsetRatingMsg(bizMsg);
	if(RET_ERROR == ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "",	"UpsetRatingMsg ERROR!","");
	}
	//更新主会话发送RG 数目
	int affect_num = -1;
	pExec = dbm->GetSQL(DATA_U_RECV_RGNUM);
	// update主会话发送RG 数目3次重试
	retryCount = 0;
	success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_sessionID);
			pExec->Execute();
			affect_num = pExec->GetRowCount();
			pExec->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "recv, send rgnum-1, affect_num[%d]", affect_num);
		}
		catch (UDBException &e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "recv execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败	
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	//查询主会话发送RG 数目
	int recv_rg = 0;
	pQuery = dbm->GetSQL(DATA_S_RECV_RGNUM);
	 try
	{
	    pQuery->DivTable(bizMsg->m_sessionID);
	    pQuery->UnBindParam();
	    pQuery->BindParam(1, bizMsg->m_sessionID);
	    pQuery->Execute();
	    if(pQuery->Next())
	    {
	           pQuery->GetValue(1, recv_rg);
	    }
	}
	catch(UDBException& e)
	{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "query recv execption[%s]", e.ToString());
	  return RB_SM_UNABLE_TO_COMPLY;
	}
	 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "recv_rgnum[%d]", recv_rg);

	// RG 未完全返回
	if(affect_num || recv_rg > 1)
	{
	       DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "remain rgnum[%d] >0 ,no need send cca\n", recv_rg);
	    return RET_SUCCESS;
	}
		
	//查询RG数目(所有子会话)		
	long long rating = 0;
	int result = 0;
	
	pQuery = dbm->GetSQL(DATA_SelectSessionStoreRbansRgTerm);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		sprintf(value, "%s;%%", bizMsg->m_sessionID);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "like find RG[%s]", value);
		pQuery->BindParam(1, value);
		pQuery->Execute();
		STCodeOfr stCodeOfr;
	  	while(pQuery->Next())
		{
			memset(&stCodeOfr,0,sizeof(stCodeOfr));
			pQuery->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
			if(stCodeOfr.nSessionStatus == TORB_ACTION)
			{
				return RET_SUCCESS;
			}
			else if(stCodeOfr.nSessionStatus != RECVRB_ACTION)
			{
				continue;
			}
			
			pQuery->GetValue(2, value);//OCP_INT_RATING_GROUP
			rating = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rating group[%s]", value);

			pQuery->GetValue(3, value);
			result = atoi(value);
			if(0 != result)
			{
				sucesRGNum++;
			}
			stCodeOfr.resultcode = result;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "result code[%s]", value);

			pQuery->GetValue(4, value);
			strncpy(stCodeOfr.ofrid,value,sizeof(stCodeOfr.ofrid));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ofr id[%s]", stCodeOfr.ofrid);
			
			if(rating || result)
			{
				MSCCResult.insert(pair<long long, STCodeOfr>(rating, stCodeOfr));
			}
			totalRgNum++;
		}		

	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "term sql Code [%s],execption[%s]",DATA_SelectSessionStoreRbansRgTerm, e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}
	
	//普通异常处理中更新nSendCCAFlag=1，过程中余额不足异常中更新nSendCCAFlag=2
	//这两种都不需要再返回给网元CCA消息
	/*if((1 == nSendCCAFlag) || (2 == nSendCCAFlag))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  bizMsg->m_sessionID,  "ret cdr","");
		return RET_CDR;
	}*/

	
	//获取B036,B037
	ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE,  "", "error cost info");
	}

	//获取余额信息
	balance = AccumlateBalance(bizMsg);

	//组装CCA消息
	ocs::SCCAMsg cca;
	ocs::DATAUSU gsu;
	cca.sessionID = bizMsg->m_sessionID;
	cca.resultCode = 2001;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 3;
	map<long long, STCodeOfr>::iterator iter;
	iter = MSCCResult.begin();
	for(;iter!=MSCCResult.end();iter++)
	{
		gsu.ratinggroup    = iter->first;
		gsu.ProductOfferId = iter->second.ofrid;
		gsu.ResultCode     = iter->second.resultcode ? iter->second.resultcode : 2001;
		
		cca.MSCC.push_back(gsu);
	}


	cca.Cost.costUnit = cost_unit;
	cca.Cost.valueDigits = cost_amount; 
	cca.Cost.exponent = 0;
	cca.AOC.balance = balance;

	if(1==bizMsg->m_testFlag)
	{
		cca.BalaInfo = REAMsg.sz_balanceInfo;
		cca.AccuInfo = REAMsg.sz_accumlatorInfo; 
		cca.TariInfo = REAMsg.sz_tariffIdInfo;
		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		cca.AcctInfo = flag;
	}

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce DATA failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	
	//出清单，释放会话
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "",  "return cdr");
	return RET_CDR;

}
int DCAnsDATATEL::XdrEvent(STBizMsg* bizMsg)
{
    int ret									= RET_SUCCESS;
	long long ratingGroup					= 0;
	long lastRsuObject 					 	= 0;   	//上次预占流量
	long lastRsuTime           				= 0;	//上次预占时长
	long lastTimestamp 						= 0;	//上次 CCR 时间戳
	int chargeType							= 0;
	int traceNumOnff						= 0;
	int lastoctCdrFlag						= 0;
	long lastoctCdrTotalOctets				= 0;

	char value[BIZ_TEMP_LEN_256] 			= {0};

	int cost_unit 							= 0;
	int cost_amount 						= 0;
	int balance                 			= 0;	//余额信息
	char szPricingPlanID[BIZ_TEMP_LEN_128] 				= {0};//定价计划ID
	long lEventTypeID = 0;//事件类型ID
	int nSendCCAFlag = 0;
	char szChildSessionID[256] = {0};
    char szCurrentTime[20]                                ={0};
    char szEndTime[20]                                ={0};
    long nDuration=0;
    long nUnitTotal=0;
    long nUnitInput=0;
    long nUnitOutput=0;
	SREAInfo REAMsg;

	map<long long, STCodeOfr> MSCCResult;
	DCAnsPara* m_smpara = (DCAnsPara*)bizMsg->m_anspara;
	ocs::rbresult* base = (rbresult*)bizMsg->m_base;
	DCDBManer *dbm = (DCDBManer *)bizMsg->m_dbm;
	ocs::rbext *ext = (rbext*)bizMsg->m_extend;
	bizMsg->m_subNumber[0]='\0';
	long nAmount = atol(ext->kv["nAmount"].c_str());
	int Measure = atoi(ext->kv["Measure"].c_str());
	long StragegyId = atol(ext->kv["StragegyId"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend StragegyId[%ld]", StragegyId);
	int payFlag = atoi(ext->kv["PayFlag"].c_str());
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend PayFlag[%d]", payFlag);


	char szTmp[128] = {0};
	strncpy(szTmp,ext->kv["OfrInstId"].c_str(),sizeof(szTmp)-1);
	std::string OfrInstId = szTmp;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get m_extend mocType OfrInstId[%s]", OfrInstId.c_str());

	//会话拆分需要在return之前进行
	strcpy(szChildSessionID, bizMsg->m_sessionID);//sesseionID 表示子会话,  bizMsg->m_sessionID 表示主会话
	for(int i = strlen(bizMsg->m_sessionID)-1; i>=0; i--)
	{
		if(bizMsg->m_sessionID[i] == ';')
		{
			bizMsg->m_sessionID[i] = '\0';
			break;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "real session[%s]", szChildSessionID);

	UDBSQL *pQuery = dbm->GetSQL(DATA_Select_SessionStoreRecord_RG_OffLine);
	try
	{

		//查询子会话信息
		long long serial = 0;
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, szChildSessionID);
		pQuery->Execute();
	     if(pQuery->Next())
		{
			//会话阶段 1 - Initial   2 - Update  3 - Termination   4 - Event
			pQuery->GetValue(1, value);
			bizMsg->m_requestType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "session status[%s]", value);

			pQuery->GetValue(2, value);
			serial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "serial[%s]", value);

			pQuery->GetValue(3, value);
			ratingGroup = atol(value);
			bizMsg->m_ratingGroup = ratingGroup;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rating group[%s]", value);

			pQuery->GetValue(4, value);
			bizMsg->m_requestNumber = atoi(value);//OCP_INT_REQ_NBR
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "request number[%d]", bizMsg->m_requestNumber);

                     pQuery->GetValue(10, bizMsg->m_subNumber);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "m_subNumber[%s]", bizMsg->m_subNumber);

			//上次授权流量
			pQuery->GetValue(11, value);//OCP_LNG_GSU_TOTAL_OCT
			lastRsuObject= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last Rsu Object[%s]", value);

			//上次授权时长
			pQuery->GetValue(12, value);//OCP_LNG_GSU_TIME
			lastRsuTime= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "last Rsu time[%s]", value);

			pQuery->GetValue(18, REAMsg.sz_balanceInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "balanceInfo[%s]", REAMsg.sz_balanceInfo);

			pQuery->GetValue(19, REAMsg.sz_balanceInfo2);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "balanceInfo2[%s]", REAMsg.sz_balanceInfo2);

			pQuery->GetValue(20, REAMsg.sz_accumlatorInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "accumlatorInfo[%s]", REAMsg.sz_accumlatorInfo);

			pQuery->GetValue(21, REAMsg.sz_tariffIdInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "tariffIdInfo[%s]", REAMsg.sz_tariffIdInfo);

			pQuery->GetValue(22, REAMsg.sz_chargeInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "chargeInfo[%s]", REAMsg.sz_chargeInfo);

			pQuery->GetValue(13, szEndTime);//RE_LNG_CURRENT_CCR_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "current CCR time[%s]", szEndTime);

			//CDR_PUB_STR_PRICING_PLAN_ID
			pQuery->GetValue(23, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "db:PricingPlanID[%s]", value);
			strcpy(szPricingPlanID,value);

			pQuery->GetValue(24, value);
			nSendCCAFlag = atoi(value);//OCP_INT_CCA_FLAG
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSendCCAFlag[%d]", nSendCCAFlag);

			pQuery->GetValue(25, value);
			strcpy(bizMsg->m_ProductOfferId, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "product offer id[%s]", value);

			pQuery->GetValue(26, value);
			lastoctCdrFlag = atoi(value);//OCP_INT_CCA_FLAG
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "lastoctCdrFlag[%d]", lastoctCdrFlag);

			pQuery->GetValue(27, value);
			lastoctCdrTotalOctets = atol(value);

                     pQuery->GetValue(28, value);
			nDuration = atol(value);

                     pQuery->GetValue(29, value);
			nUnitTotal = atol(value);

                     pQuery->GetValue(30, value);
			nUnitInput = atol(value);

                     pQuery->GetValue(31, value);
			nUnitOutput = atol(value);

			pQuery->GetValue(33, value);
			bizMsg->m_serviceContextID = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "m_serviceContextID[%d]", bizMsg->m_serviceContextID);

			pQuery->GetValue(34, szCurrentTime);

			pQuery->GetValue(35, REAMsg.sz_oriChargeInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "oriChargeInfo[%s]", REAMsg.sz_oriChargeInfo);
			     
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, RB_RBA_MSG_ERROR , "", "unknow sessionID");
			return RB_RBA_MSG_ERROR;
		}
		
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "term select child ,sqlCode [%s],execption[%s]",DATA_Select_SessionStoreRecord_RG_OffLine, e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}
	lEventTypeID= base->evt_id;
	ret = ModifyREAMsg(bizMsg,REAMsg);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, ret, "", "ModifyREAMsg failed");
	}
	
	if(nAmount > 0 && Measure == 1 && ext->kv["eliminateflag"] == "1")//时长计费
    {
		memset(szCurrentTime,0x00,sizeof(szCurrentTime));
		DCCommonIF::SetSecTimeToDate(DCCommonIF::dateToSec(szEndTime) - nAmount+1, szCurrentTime);
    }

	//更新子会话信息
	UDBSQL *pExec = dbm->GetSQL(DATA_UpdateSessionStoreRbansRg_OffLinec);
	// update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, bizMsg->m_resultcode); // SM_INT_RESULT_CODE
			pExec->BindParam(2, lastRsuObject);
			pExec->BindParam(3, lastRsuTime);
			pExec->BindParam(4, atol(szCurrentTime)); // RE_LNG_CALL_START_TIME
			pExec->BindParam(5, REAMsg.sz_balanceInfo);
			pExec->BindParam(6, REAMsg.sz_balanceInfo2);
			pExec->BindBlobParam(7, REAMsg.sz_accumlatorInfo);
			pExec->BindBlobParam(8, REAMsg.sz_tariffIdInfo);
			pExec->BindParam(9, REAMsg.sz_chargeInfo);
			pExec->BindParam(10, szPricingPlanID);
			pExec->BindParam(11, lEventTypeID);
			pExec->BindParam(12, STATUS_IDLE);
			int octCdrFlag = 0;
			long octCdrTotalVolume = 0;

			if (3 == base->dbv[0].unit)
			{
				octCdrFlag = 1;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "B036=1,volum card", "");

				octCdrTotalVolume = base->dbv[0].amount;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "B036=1,volum=[%d]", octCdrTotalVolume);
			}
			if (1 == lastoctCdrFlag)
			{
				pExec->BindParam(16, lastoctCdrFlag);
			}
			else
			{
				pExec->BindParam(16, octCdrFlag);
			}

			octCdrTotalVolume += lastoctCdrTotalOctets;
			pExec->BindParam(17, (int)octCdrTotalVolume);
			pExec->BindParam(18, StragegyId);
			pExec->BindParam(19, payFlag);
			pExec->BindParam(20, REAMsg.sz_oriChargeInfo);
			pExec->BindParam(21, OfrInstId.c_str());
			pExec->BindParam(22, ext->kv["BILLCYCLE"].c_str());
			pExec->BindParam(23, szChildSessionID);

			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child session charge info OK", "");
		}
		catch (UDBException &e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "term sql code [%s],execption[%s], error code[%d]", DATA_UpdateSessionStoreRbansRg_OffLinec, e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	//普通异常处理中更新nSendCCAFlag=1，过程中余额不足异常中更新nSendCCAFlag=2
	//这两种都不需要再返回给网元CCA消息
	if((1 == nSendCCAFlag) || (2 == nSendCCAFlag))
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "",  "ret cdr","");
		return RET_CDR;
	} 

	//获取B036,B037
	ret = GetCostInfo(bizMsg, cost_unit, cost_amount);
	if(ret != RET_SUCCESS)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "error " );
	}

	//获取余额信息
	balance = AccumlateBalance(bizMsg);;

	//组装CCA消息
	ocs::SCCAMsg cca;
	ocs::DATAUSU gsu;
	cca.sessionID = bizMsg->m_sessionID;
       cca.sessionID.erase(0,3);
	cca.resultCode = bizMsg->m_resultcode;
    cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.ServiceFlowID = bizMsg->m_ServiceFlowID;
	cca.ServiceContextID = 3;
	map<long long, STCodeOfr>::iterator iter;
	iter = MSCCResult.begin();
	for(;iter!=MSCCResult.end();iter++)
	{
		gsu.ratinggroup    = iter->first;
		gsu.ProductOfferId = iter->second.ofrid;
		gsu.ResultCode     = iter->second.resultcode;
		
		cca.MSCC.push_back(gsu);

	}


	cca.Cost.costUnit = cost_unit;
	cca.Cost.valueDigits = cost_amount; 
	cca.Cost.exponent = 0;
	cca.AOC.balance = balance;

/*	if(1==bizMsg->m_testFlag)
	{
		cca.BalaInfo = REAMsg.sz_balanceInfo;
		cca.AccuInfo = REAMsg.sz_accumlatorInfo; 
		cca.TariInfo = REAMsg.sz_tariffIdInfo;
		char flag[10]={0};
		sprintf(flag,"%d",base->acct_flag);
		cca.AcctInfo = flag;


		
		if (SM_SESSION_TERMINATION_CODE ==  bizMsg->m_requestType)
		{
			DelSession(bizMsg);
		}
		
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "test msg,delete session");
		return RET_SUCCESS;
	}
*/
	// 写队列	
	//ret = ProduceCCA(bizMsg, cca, bizMsg->m_anstopic);

	string msg;
	ret = ComposeRMQMsg(bizMsg, cca, bizMsg->m_anstopic, msg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, "",  "Produce DATA failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	/*bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&msg), (void *)bizMsg);
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_RATING_FAILED, bizMsg->m_sessionID,  "Produce DATA failed topic[%s]", bizMsg->m_anstopic);
		return SM_OCP_RATING_FAILED;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID,  "Produce DATA Successful topic[%s]", bizMsg->m_anstopic);*/

	// 根据R71和批价时长计算 出R70
	UDBSQL *pExecR70 = dbm->GetSQL(DATA_UpdateSessionStoreRbansOffLineT);
	// update会话2002错误码(Commit失败)3次重试
	retryCount = 0;
	success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExecR70->DivTable(bizMsg->m_sessionID);
			pExecR70->UnBindParam();

			pExecR70->BindParam(1, atol(szCurrentTime)); // RE_LNG_CALL_START_TIME
			pExecR70->BindParam(2, atol(szEndTime));	 // RE_LNG_CURRENT_CCR_TIME
			pExecR70->BindParam(3, bizMsg->m_sessionID);
			pExecR70->Execute();
			pExecR70->Connection()->Commit();
			success = true;

			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update  DATA Time  session ok", "");
		}
		catch (UDBException &e)
		{
			pExecR70->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update  DATA Time session ,sql code[%s] exception[%s], error code[%d]", DATA_UpdateSessionStoreRbansOffLineT, e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

	if(ext->kv["eliminateflag"] == "1")
	{
		switch(Measure)
		{
			case 1:
				nDuration = nAmount;
			break;
			case 3:
				nUnitTotal = nAmount;
			break;
			case 4:
				nUnitInput = nAmount;
			break;
			case 5:
				nUnitOutput = nAmount;
			break;
			default:
				break;
		}
	}

	pExec = dbm->GetSQL(DATA_UpdateSessionStoreRbansOffLine);
	// update会话2002错误码(Commit失败)3次重试
	retryCount = 0;
	success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			pExec->BindParam(1, nDuration);	  // SM_LNG_ALL_USU_TIME
			pExec->BindParam(2, nUnitTotal);  // SM_LNG_ALL_USU_TOTAL_OCT
			pExec->BindParam(3, nUnitInput);  // SM_LNG_ALL_USU_INPUT_OCT
			pExec->BindParam(4, nUnitOutput); // SM_LNG_ALL_USU_OUTPUT_OCT
			pExec->BindParam(5, szChildSessionID);
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update  DATA session ok", "");
		}
		catch (UDBException &e)
		{
			pExec->Connection()->Rollback();
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update  DATA session ,sql code [%s]exception[%s], error code[%d]", DATA_UpdateSessionStoreRbansOffLine, e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
					return RB_SM_UNABLE_TO_COMPLY;
				}
			}
			return RB_SM_UNABLE_TO_COMPLY;
		}
	}

//出清单，释放会话
return RET_CDR;
}

