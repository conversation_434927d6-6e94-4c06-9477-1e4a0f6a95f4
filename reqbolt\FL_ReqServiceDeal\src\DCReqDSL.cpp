#include "DCReqDSL.h"
#include <sys/time.h>
#include "DCMqProduceServer.h"

DCReqDSL::DCReqDSL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "", "");
}

DCReqDSL::~DCReqDSL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "", "");
}

int DCReqDSL::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG,  "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;

	//PERF LOG	
	//cvar->m_pData->SetSQLInfo(&(bizMsg->m_perf.m_lSQLTime),&(bizMsg->m_perf.m_lSQLTimeMax),&(bizMsg->m_perf.m_lSQLTimeMaxId));
	
	//bizMsg->m_perf.GetTimeT2_B();
	
	int ret = SwitchReqType(bizMsg);
	bizMsg->m_resultcode = ret;	

	//bizMsg->m_perf.GetTimeT2_E();
	
	//流程结束
	//BizPerfLog(bizMsg, __FUNCTION__, __FILE__, __LINE__);
	return ret;
}

int DCReqDSL::SwitchReqType(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

