/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCBizCdrNormalVoice.h
*Indentifier：
*		
*Description：
*		话单处理类
*Version：
*		V1.0
*Author:
*		ZY.F
*Finished：
*		
*History:
********************************************/
#ifndef _DCBIZ_CDR_NORMAL_Voice_H_
#define _DCBIZ_CDR_NORMAL_Voice_H_

#include "DCBizCdrNormal.h"
#include "TCDRDict.h"

//语音业务根据账目类型拆单
struct TaiAcctItemIDCdr
{
	char szSessionStart[32];
	long nLeftDuration;
};

class DCBizCdrNormalVoice:public DCBizCdrNormal
{
	public:

		DCBizCdrNormalVoice();
		~DCBizCdrNormalVoice();
		
	protected:
		
		
		int ComposeVOICELongCdr(STBizMsg * bizMsg,DataCDRInfo &stCdrInfo);
		virtual int ComposeVOICE(STBizMsg* bizMsg);

		int PretreatVOICE(STBizMsg* bizMsg);


		int PutCdr_VOICE(STBizMsg* bizMsg, DataCDRInfo &stCdrInfo);
		int ComposeVoiceCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery1,STariffAccumCDRInfo TaiAuCdrIter,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr);
};

#endif


