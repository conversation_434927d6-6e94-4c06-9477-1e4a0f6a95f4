#include "DCFreeNumber.h"
#include "DCUserBase.h"
#include "TSMPara.h"
#include "DCOBJSet.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "DCUserBase.h"
#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "ErrorCode.h"
using namespace ocs;


int DCFreeNumber::init()
{
	return 0;
}

const char* DCFreeNumber::desc()
{
	return "FC_FreeNumber";
}

//消息头|公共消息|业务消息
int DCFreeNumber::process(void* input, void* output)
{
	SCCRVOICE* pdvoice = NULL;
	SCCRISMP* pdismp = NULL;
	SCCRSMS* pdsms = NULL;
	DCOBJSet* pset = (DCOBJSet*)input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();	
	SCCRBase* base_ccr = pset->get<SCCRBase>();
	TSMPara *smpara = (TSMPara *)bizMsg->m_smpara;
	DCDBManer* pdbm = (DCDBManer*)bizMsg->m_dbm;
	
	DCUserBase base(smpara,pdbm);

	int ret = 0;
	if(VOICE==bizMsg->m_serviceContextID || ISMP==bizMsg->m_serviceContextID || HRS==bizMsg->m_serviceContextID || SMS==bizMsg->m_serviceContextID)
	{
		if(VOICE==bizMsg->m_serviceContextID)
		{
			 pdvoice =(SCCRVOICE*) pset->get<SCCRVOICE>();
			 ret = base.FreeNumber(bizMsg, base_ccr, &pdvoice->calling, &pdvoice->called);
		}
		if(ISMP==bizMsg->m_serviceContextID || HRS==bizMsg->m_serviceContextID )
		{
			 pdismp =(SCCRISMP*) pset->get<SCCRISMP>();
			 ret = base.FreeNumber(bizMsg, base_ccr, &pdismp->calling, &pdismp->called);
		}
		if(SMS==bizMsg->m_serviceContextID)
		{
			 pdsms = (SCCRSMS*)pset->get<SCCRSMS>();
			 ret = base.FreeNumber(bizMsg, base_ccr, &pdsms->calling, &pdsms->called);
		}
	}
	else
	{
		ret = base.FreeNumber(bizMsg, base_ccr);
	}
	
	if(1 == ret)
	{
		bizMsg->m_freeFlag = 1;		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "deal free flow");
		return base.DealFreeNumber(base_ccr->requestAction, bizMsg);
	}
	else if(ret > 0)
	{
		if(ret == SM_OCP_USER_UNKNOWN && HRS == bizMsg->m_serviceContextID)
		{
			bizMsg->m_userType == 1;
			return RET_SUCCESS;
		}
		return RET_ERROR;
	}

	return RET_SUCCESS;
}


DYN_PLUGIN_CREATE(DCFreeNumber, "FUNC_FREENUMBER", "FC_FreeNumber", "1.0.0")


