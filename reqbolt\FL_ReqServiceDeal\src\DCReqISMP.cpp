#include "DCReqISMP.h"
#include "ErrorCode.h"
#include "TSMPara.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include <sys/time.h>
#include "REMsgTypeDef.h"
#include "DCSeriaOp.h"
#include "DCMqProduceServer.h"
#include "UHead.h"
#include <sys/time.h>
#include <stdio.h>

DCReqISMP::DCReqISMP()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "", "");
}

DCReqISMP::~DCReqISMP()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "", "");
}

int DCReqISMP::Work(void *data)
{
	if(!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OCP_NULL_MSG, "", "null msg", "");
		return SM_OCP_NULL_MSG;
	}
	STBizMsg *bizMsg = (STBizMsg*)data;

	//PERF LOG
	// bizMsg->m_perf.GetTimeT2_B();

	int ret = SwitchReqType(bizMsg);
	bizMsg->m_resultcode = ret;

	//PERF LOG
	//bizMsg->m_perf.GetTimeT2_E();


	return ret;
}

int DCReqISMP::SwitchReqType(STBizMsg* bizMsg)
{
	return RET_SUCCESS;
}

int DCReqISMP::Check(SCCRBase* base, STBizMsg* bizMsg)
{
	int ret								= 0;
	char value[BIZ_DATA_LEN_256]			= {0};
	char buf[BIZ_TEMP_LEN_1024]			= {0};
	SUserInfo *userInfo					= NULL;
	userInfo = (SUserInfo * )bizMsg->m_userinfo;

	ocs::UHead uhd;
	ocs::rbquery rbr;
	ocs::rbhead head;
	ocs::rbext ext;
	ocs::rbdomain rbdomain;
	//��װRER��Ϣ

	uhd.uid = bizMsg->m_uid;
	uhd.car = "1";
	uhd.checkKey = bizMsg->m_strCheckKey;
	ext.kv["sourceId"] = base->source;
	ext.kv["operListId"] = bizMsg->m_operListId;
	//100   MsgType
	head.type = RE_SERVICE_TYPE_INT_BALANCE_REQ_ISMP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose MsgType[%d]", RE_SERVICE_TYPE_INT_BALANCE_REQ_ISMP);

	//000   �ỰID
	head.session = bizMsg->m_sessionID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose SessionID[%s]", bizMsg->m_sessionID);

	//101   EventTimeStamp
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose EventTimeStamp[%ld]", bizMsg->timestampCCR);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose Serial[%ld]", bizMsg->m_serial);

	head.sreq = bizMsg->m_requestType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose SessionState[%d]", bizMsg->m_requestType);

	// B14
	head.trace = bizMsg->m_trace_flag;
	uhd.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose TracFlag[%d]", bizMsg->m_trace_flag);

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	rbr.latn_id = userInfo->ilatnid;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose latnid[%d]" , rbr.latn_id);

	head.topology = base->topology;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,	bizMsg->m_sessionID, "compose topology[%s]" , head.topology.c_str() );
	rbr.charged_nbr = base->subscription.phone;
	sprintf(value, "0%d", base->subscription.area);
	rbr.charged_harea = value;
	rbr.balance_query = 1;
	rbr.bill_cycle = "1";
	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	//CER����
	ext.kv["CER"] = bizMsg->m_subNumber;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	// ---------encode ------------ ismp + ihead

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(rbdomain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//��ӡRBR��Ϣ
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(rbdomain);
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char*)m_en.data();

	return RET_SUCCESS;
}


int DCReqISMP::QueryRatable(SCCRBase* base, SCCRISMP* data,STBizMsg* bizMsg)
{
	int ret								= 0;
	char value[BIZ_DATA_LEN_256]		= {0};
	char buf[BIZ_TEMP_LEN_1024]			= {0};
	SUserInfo *userInfo					= NULL;
	userInfo = (SUserInfo * )bizMsg->m_userinfo;

	ocs::UHead uhd;
	ocs::rbext ext;
	ocs::rbhead head;
	ocs::rbdomain rbdomain;
	ocs::rbquery	query;

	//��װRER��Ϣ

	uhd.uid = bizMsg->m_uid;
	uhd.car = "1";
	uhd.trace = bizMsg->m_trace_flag;
	uhd.checkKey = bizMsg->m_strCheckKey;
	ext.kv["sourceId"] = base->source;
	ext.kv["operListId"] = bizMsg->m_operListId;
	//100   MsgType
	head.type = RE_SERVICE_TYPE_INT_RATA_REQ_ISMP;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose MsgType[%d]", RE_SERVICE_TYPE_INT_RATA_REQ_ISMP);

	//000   �ỰID
	head.session = bizMsg->m_sessionID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose SessionID[%s]", bizMsg->m_sessionID);

	//101   EventTimeStamp
	head.stamp = bizMsg->timestampCCR;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose EventTimeStamp[%ld]", bizMsg->timestampCCR);

	// 001 serial
	head.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose Serial[%ld]", bizMsg->m_serial);

	// B14
	head.trace = bizMsg->m_trace_flag;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose TracFlag[%d]", bizMsg->m_trace_flag);

	//prod_inst_id
	head.prodinstid = userInfo->servID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose prod_inst_id[%ld]", userInfo->servID);

	//topology
	head.topology = base->topology;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose topology[%s]", head.topology.c_str());
	//R01
	query.charged_nbr = base->subscription.phone;

	//R504
	sprintf(value, "0%d", base->subscription.area);
	query.charged_harea = value;

	//B24
	query.ratable_query = 1;

	//R1001
	query.bill_cycle = data->billCycle;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "compose RatableBillcycle[%s]", data->billCycle.c_str());
	//CER����
	ext.kv["CER"] = bizMsg->m_subNumber;

	ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
	ext.kv["anstopic"] = bizMsg->m_anstopic;
	ext.kv["taskId"] = bizMsg->m_taskId;
	// ihead + ismp + iquery
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(rbdomain);
		m_en.encode(query);
		m_en.encode(ext);
		//��ӡRBR��Ϣ
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);
		m_print.print(rbdomain);
		m_print.print(query);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char*)m_en.data();

	return RET_SUCCESS;
}

int DCReqISMP::RefundFreeCCA(STBizMsg* bizMsg)
{
	int ret 		= 0;
	int resultcode 	= 5003;
	ocs::UHead uhd;
	ocs::SCCAMsg cca;

	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;

	// servconextID
	cca.ServiceContextID = bizMsg->m_serviceContextID;

	// sessionID
	cca.sessionID = bizMsg->m_sessionID;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCP SessionID[%s]", bizMsg->m_sessionID);

	// requestType
	cca.requestType = bizMsg->m_requestType;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCP RequestType[%d]", bizMsg->m_requestType);

	// requestNum
	cca.requestNumber = bizMsg->m_requestNumber;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCP RequestNumber[%d]", bizMsg->m_requestNumber);

	// serial
	cca.serial = bizMsg->m_serial;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCP Serial[%d]", bizMsg->m_serial);

	// resultCode
	cca.resultCode = resultcode;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "OCP ResultCode[%d]", resultcode);

	cca.trace = bizMsg->m_trace_flag;


	try
	{
		// --- encode -----
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	bizMsg->data = (char *)m_en.data();
	bizMsg->m_RARFlag = RET_OVER;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "send CCA data[%s]",bizMsg->data.c_str());

/*	string str = HexEncode(m_en.data(),m_en.size());
	// д��MQ
	//DCMqProduceServer* producer = bizMsg->m_producer;

       struct timeval tmv;
        char buf[20];

        // ͷ���ӹ̶�16λ
        gettimeofday(&tmv, NULL);
        sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
        str.insert(0, buf);

	//ret = producer->Produce(str.c_str(), str.length(), bizMsg->m_anstopic);
	bizMsg->m_topictype = 1;
	ret = bizMsg->m_plugin->call((void *)(&str), (void *)bizMsg);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, bizMsg->m_sessionID, "Produce failed\n");
		return RET_ERROR;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, bizMsg->m_sessionID, "send CCA to MQ[%s],msglen[%d]", bizMsg->m_anstopic, str.length());
	}*/

	return RET_OVER;

}
