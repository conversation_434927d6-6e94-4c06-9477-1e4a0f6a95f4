include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/SM/release
PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include

REQSERVICE_INC=$(PWD)/include
REQSERVICE_SRC=$(PWD)/src
REQSERVICE_OBJ=$(PWD)/obj

REQSERVICE_CPP=DCUserManaFlow.cpp DCUserAuth.cpp DCUserBase.cpp DCBlackNumber.cpp DCFreeNumber.cpp \
			   desc_BlackNumber.cpp desc_FreeNumber.cpp desc_UserAuth.cpp desc_userManager.cpp
           
REQSERVICE_SRCS=$(addprefix $(REQSERVICE_SRC)/, $(REQSERVICE_CPP))
REQSERVICE_OBJS=$(patsubst $(REQSERVICE_SRC)/%.cpp, $(REQSERVICE_OBJ)/%.o, $(REQSERVICE_SRCS))

TLIB= $(RELEASE_PATH)/plugin/libUserManager.so
TLIBFreeNumber= $(RELEASE_PATH)/plugin/libFreeNumber.so
TLIBBlackNumber = $(RELEASE_PATH)/plugin/libBlackNumber.so
TLIBUserAuth= $(RELEASE_PATH)/plugin/libUserAuth.so

INCLUDE =-I$(REQSERVICE_INC) \
		-I$(COMMON_INC) \
		-I$(ITF)/include \
		-I$(AVRO)/include \
		-I$(DCLOGCLI)/include \
		-I$(DFM_INC_PATH) \
		-I$(MQ)/include \
		-I$(TXML)/include \
		-I$(CTG_CHECK)/inc

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(DCLOGCLI)/lib -L$(MQ)/lib -L$(CTG_CHECK_LIB) -L$(TXML)/lib
LIBSLIST= -lCommonIF -ldclogcli -ldcmq -lCtgCheckSDK -ltinyxml

CFLAGS += -std=c++11

libtarget=$(TLIB) $(TLIBFreeNumber) $(TLIBBlackNumber) $(TLIBUserAuth)

tmpvar:=$(call CreateDir, $(REQSERVICE_OBJ))
.PHONY:all clean dup

all:$(TLIB)	$(TLIBFreeNumber) $(TLIBBlackNumber) $(TLIBUserAuth)
$(TLIB):$(REQSERVICE_OBJ)/DCUserManaFlow.o $(REQSERVICE_OBJ)/desc_userManager.o
	@echo "build libUserManager.so----"
	$(CC)  $(DFLAGS)  -o $(TLIB) $(REQSERVICE_OBJ)/DCUserManaFlow.o $(REQSERVICE_OBJ)/desc_userManager.o $(LIBPATH) $(LIBSLIST)

$(TLIBFreeNumber): $(REQSERVICE_OBJ)/DCUserBase.o $(REQSERVICE_OBJ)/DCFreeNumber.o $(REQSERVICE_OBJ)/desc_FreeNumber.o
	@echo "build libFreeNumber.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBFreeNumber) $(REQSERVICE_OBJ)/DCUserBase.o $(REQSERVICE_OBJ)/DCFreeNumber.o $(REQSERVICE_OBJ)/desc_FreeNumber.o $(LIBPATH) $(LIBSLIST)
	
$(TLIBBlackNumber): $(REQSERVICE_OBJ)/DCUserBase.o $(REQSERVICE_OBJ)/DCBlackNumber.o $(REQSERVICE_OBJ)/desc_BlackNumber.o
	@echo "build libBlackNumber.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBBlackNumber) $(REQSERVICE_OBJ)/DCUserBase.o $(REQSERVICE_OBJ)/DCBlackNumber.o $(REQSERVICE_OBJ)/desc_BlackNumber.o $(LIBPATH) $(LIBSLIST)
	
$(TLIBUserAuth): $(REQSERVICE_OBJ)/DCUserBase.o $(REQSERVICE_OBJ)/DCUserAuth.o $(REQSERVICE_OBJ)/desc_UserAuth.o
	@echo "build libUserAuth.so----"
	$(CC)  $(DFLAGS)  -o $(TLIBUserAuth) $(REQSERVICE_OBJ)/DCUserBase.o $(REQSERVICE_OBJ)/DCUserAuth.o $(REQSERVICE_OBJ)/desc_UserAuth.o $(LIBPATH) $(LIBSLIST)

$(REQSERVICE_OBJS):$(REQSERVICE_OBJ)/%.o:$(REQSERVICE_SRC)/%.cpp
	$(CC) -c $(CFLAGS)  -DBUILD_TIME=`date "+%Y%m%d%H%M%S"` $< -o $@ $(INCLUDE)
	
$(REQSERVICE_SRC)/desc_BlackNumber.cpp:$(REQSERVICE_SRC)/desc_BlackNumber.clog
	$(TOOL)/clogtool -i $< -o $@
$(REQSERVICE_SRC)/desc_FreeNumber.cpp:$(REQSERVICE_SRC)/desc_FreeNumber.clog
	$(TOOL)/clogtool -i $< -o $@
$(REQSERVICE_SRC)/desc_UserAuth.cpp:$(REQSERVICE_SRC)/desc_UserAuth.clog
	$(TOOL)/clogtool -i $< -o $@
$(REQSERVICE_SRC)/desc_userManager.cpp:$(REQSERVICE_SRC)/desc_userManager.clog
	$(TOOL)/clogtool -i $< -o $@

clean:
	@rm -rf $(REQSERVICE_OBJS)
      
dup:
	@cp -pf $(libtarget) $(PROJECT_RPATH)/plugin/SM && echo "dup $(libtarget) to $(PROJECT_RPATH)/plugin/SM"

