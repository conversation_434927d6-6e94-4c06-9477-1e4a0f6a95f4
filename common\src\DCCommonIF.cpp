#include "DCCommonIF.h"
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include <math.h>
#include <sys/types.h>
#include <ifaddrs.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>


int DCCommonIF::SetSecTimeToDate(long lnSecTime, char* pszDate)
{
    struct tm *local = NULL;

    local=localtime(&lnSecTime);
    if (local == NULL)
    {
     return -1;
    }

    //格式化为:年月日时分秒
    sprintf(pszDate, "%04d%02d%02d%02d%02d%02d",
              1900+local->tm_year, local->tm_mon+1, local->tm_mday,
              local->tm_hour, local->tm_min, local->tm_sec);
    return 0;
}

long DCCommonIF::dateToSec(const char* pszDate)
{
    char szTmp[5]={0};
    struct tm tm1;

    time_t ltime;

    strncpy(szTmp,pszDate,4);
    szTmp[4] = 0;
    tm1.tm_year=atoi(szTmp)-1900;

    memset(szTmp,0x00,sizeof(szTmp));

    strncpy(szTmp,pszDate+4,2);
    tm1.tm_mon=atoi(szTmp)-1;

    strncpy(szTmp,pszDate+6,2);
    tm1.tm_mday=atoi(szTmp);

    strncpy(szTmp,pszDate+8,2);
    tm1.tm_hour=atoi(szTmp);

    strncpy(szTmp,pszDate+10,2);
    tm1.tm_min=atoi(szTmp);

    strncpy(szTmp,pszDate+12,2);
    tm1.tm_sec=atoi(szTmp);
    tm1.tm_isdst = 0;
    ltime=mktime(&tm1);

    return ltime;
}
int DCCommonIF::ParseUseInfo(const char* pBuf, SUseInfo* pInfo, const char cdrfence)
{
	int ret					= RET_SUCCESS;
	char value[256]			= {0};

	//获取原先信息
	int len = strlen(pBuf);
	if (len)
	{
		int pre = 0;//子串开始位置
		long tmp[20] = {0};//变量组
		int pos = 0;//变量的位置
		int num = 0;//结构的数目
		for (int i = 0; i < len; i++)
		{
			if(':'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(cdrfence==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(';'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(SUseInfo));
				pos = 0;
				num++;
			}
			else if(i==len-1)          //处理结尾字符为空的情况
			{
				strncpy(value, pBuf+pre, i-pre+1);
				value[i-pre+1] = '\0';
				pre = i+1+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(SUseInfo));
				pos = 0;
				num++;
			}

		}
	}

	return ret;
}


//802017293:10000,1,0,300,0,2163823,166476594,5,0;804777292:894406,1,0,300,0,2354823,177584076,5,0
int DCCommonIF::ParseTariffInfo(const char* pBuf, STariffInfo* pInfo, const char cdrfence)
{
	int ret					= RET_SUCCESS;
	char value[256]			= {0};

	//获取原先信息
	int len = strlen(pBuf);
	if (len)
	{
		int pre = 0;//子串开始位置
		long tmp[20] = {0};//变量组
		int pos = 0;//变量的位置
		int num = 0;//结构的数目
		for (int i = 0; i < len; i++)
		{
			if(':'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(cdrfence==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(';'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(STariffInfo));
				pos = 0;
				num++;
			}
			else if(i==len-1)          //处理结尾字符为空的情况
			{
				strncpy(value, pBuf+pre, i-pre+1);
				value[i-pre+1] = '\0';
				pre = i+1+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(STariffInfo));
				pos = 0;
				num++;
			}

		}
	}

	return ret;
}

int DCCommonIF::ParseOriChargeInfo(const char* pBuf, SOriChargeInfo* pInfo, const char cdrfence)
{
	int ret					= RET_SUCCESS;
	char value[256]			= {0};

	//获取原先信息
	int len = strlen(pBuf);
	if (len)
	{
		int pre = 0;//子串开始位置
		long tmp[20] = {0};//变量组
		int pos = 0;//变量的位置
		int num = 0;//结构的数目
		for (int i = 0; i < len; i++)
		{
			if(':'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(cdrfence==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(';'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(SOriChargeInfo));
				pos = 0;
				num++;
			}
			else if(i==len-1)          //处理结尾字符为空的情况
			{
				strncpy(value, pBuf+pre, i-pre+1);
				value[i-pre+1] = '\0';
				pre = i+1+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(SOriChargeInfo));
				pos = 0;
				num++;
			}

		}
	}

	return ret;
}

int DCCommonIF::ParseAccumlatorInfo(const char* pBuf, SAccumInfo* pInfo, const char cdrfence)
{
	int ret					= RET_SUCCESS;
	char value[256]			= {0};

	//获取原先信息
	int len = strlen(pBuf);
	if (len)
	{
		int pre = 0;//子串开始位置
		long tmp[20] = {0};//变量组
		int pos = 0;//变量的位置
		int num = 0;//结构的数目
		for (int i = 0; i < len; i++)
		{
			if(':'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(cdrfence==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
			}
			else if(';'==pBuf[i])
			{
				strncpy(value, pBuf+pre, i-pre);
				value[i-pre] = '\0';
				pre = i+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(SAccumInfo));
				pos = 0;
				num++;
			}
			else if(i==len-1)          //处理结尾字符为空的情况
			{
				strncpy(value, pBuf+pre, i-pre+1);
				value[i-pre+1] = '\0';
				pre = i+1+1;
				tmp[pos++]  = atol(value);
				memcpy((pInfo+num), &tmp, sizeof(SAccumInfo));
				pos = 0;
				num++;
			}

		}
	}

	return ret;
}





void DCCommonIF::SplitFirstFiled(char * pSour, vector<int>& vecDest, char szSeparator)
{
	string::size_type pos = 0;
	string::size_type prev_pos = 0;
	string strSource = pSour;
	vector<string> vecTemp;
	vector<string>::iterator it;
	int iFirstFiled;
	string str;
	char secSpearator =':';

	vecDest.clear();

	//先分解出以szSeparator分隔的字段
	while (( pos = strSource.find(szSeparator , pos )) != string::npos)
	{
		vecTemp.push_back( strSource.substr( prev_pos, pos - prev_pos ) );
		prev_pos = ++pos;
	}

	if(prev_pos != strSource.size())//pSour最后一位不是分隔符的情况
	{
		vecTemp.push_back( strSource.substr( prev_pos, pos - prev_pos ) );//最后一个
	}


	//再分解出冒号前面的字段
	for(it=vecTemp.begin(); it !=vecTemp.end();it++)
	{
		string::size_type pos = 0, prev_pos = 0;
		if((pos = (*it).find(secSpearator,pos)) != string::npos)
		{
			str = (*it).substr(prev_pos, pos - prev_pos );
			iFirstFiled = atoi(str.c_str());
			vecDest.push_back(iFirstFiled);
		}
		else
		{
			iFirstFiled = atoi((*it).c_str());
			vecDest.push_back(iFirstFiled);
		}
	}

	return ;
}

long DCCommonIF::time2sec(long date)
{
    //20101001231105
    time_t tsec = 0;
    if((date/10100101010101)>=1)
    {
        struct tm strtime = {0};
        strtime.tm_year = date/10000000000-1900;
        strtime.tm_mon = (date%10000000000)/100000000-1;
        strtime.tm_mday = (date%100000000)/1000000;
        strtime.tm_hour = (date%1000000)/10000;
        strtime.tm_min = (date%10000)/100;
        strtime.tm_sec = date%100;

        tsec = mktime(&strtime);
    }
    else
    {
        ;
    }
	return tsec;
}

//获取当前日期
int DCCommonIF::timestampf()
{
	char buf[16] = {0};
	unsigned long long  cur = 0;
	time_t et =  time(NULL);
	struct tm *p = localtime(&et);
	sprintf(buf, "%04d%02d%02d", (1900+p->tm_year), (1+p->tm_mon), p->tm_mday);
	cur = atoll(buf);
	return cur;

}
long long DCCommonIF::SetStartTime( long long lnSecTime)
{
	char value[16]={0};
	sprintf(value,"%lld",lnSecTime);
	char cyear[5] = {0};
	cyear[0] = value[0];
	cyear[1] = value[1];
	cyear[2] = value[2];
	cyear[3] = value[3];
	cyear[4] = '\0';
	char cmonth[3] = {0};
	cmonth[0] = value[4];
	cmonth[1] = value[5];
	cmonth[2] = '\0';
	char cday[3] = {0};
	cday[0] = value[6];
	cday[1] = value[7];
	cday[2] = '\0';
	switch(atoi(cmonth))
	{
		case 1:
		case 3:
		case 5:
		case 7:
		case 8:
		case 10:
		{
			if(atoi(cday) == 31)
			{
				value[5] += 1;
				value[6] = '0';
				value[7] = '1';
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
			else
			{
				int day = atoi(cday)+1;
				sprintf(cday,"%02d",day);
				value[6] = cday[0];
				value[7] = cday[1];
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
		}
		break;
		case 2:
		{
			int year = atoi(cyear);
			if(((year%100 == 0)&&(year%400 == 0))||((year%100 !=0)&&(year%4 == 0)))
			{
				if(atoi(cday) == 29)
				{
					value[5] += 1;
					value[6] = '0';
					value[7] = '1';
					for(int i = 0;i < 6;i++)
						value[8+i] = '0';
				}
				else
				{
					int day = atoi(cday)+1;
					sprintf(cday,"%02d",day);
					value[6] = cday[0];
					value[7] = cday[1];
					for(int i = 0;i < 6;i++)
					value[8+i] = '0';
				}
			}
			else
			{
				if(atoi(cday) == 28)
				{
					value[5] += 1;
					value[6] = '0';
					value[7] = '1';
					for(int i = 0;i < 6;i++)
						value[8+i] = '0';
				}
				else
				{
					int day = atoi(cday)+1;
					sprintf(cday,"%02d",day);
					value[6] = cday[0];
					value[7] = cday[1];
					for(int i = 0;i < 6;i++)
					value[8+i] = '0';
				}
			}
		}
		break;
		case 4:
		case 6:
		case 11:
		{
			if(atoi(cday) == 30)
			{
				value[5] += 1;
				value[6] = '0';
				value[7] = '1';
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
			else
			{
				int day = atoi(cday)+1;
				sprintf(cday,"%02d",day);
				value[6] = cday[0];
				value[7] = cday[1];
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
		}
		break;
		case 9:
		{
			if(atoi(cday) == 30)
			{
				value[4] = '1';
				value[5] = '0';
				value[6] = '0';
				value[7] = '1';
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
			else
			{
				int day = atoi(cday)+1;
				sprintf(cday,"%02d",day);
				value[6] = cday[0];
				value[7] = cday[1];
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
		}
		break;
		case 12:
		{
			if(atoi(cday) == 31)
			{
				int year = atoi(cyear)+1;
				sprintf(cyear,"%04d",year);
				value[0] = cyear[0];
				value[1] = cyear[1];
				value[2] = cyear[2];
				value[3] = cyear[3];
				value[4] = '0';
				value[5] = '1';
				value[6] = '0';
				value[7] = '1';
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
			else
			{
				int day = atoi(cday)+1;
				sprintf(cday,"%02d",day);
				value[6] = cday[0];
				value[7] = cday[1];
				for(int i = 0;i < 6;i++)
					value[8+i] = '0';
			}
		}
		break;
		default:
		break;
	}
	lnSecTime = atoll(value);
	return lnSecTime;
}
long long DCCommonIF::SetEndTime( long long lnSecTime)
{
	char value[16]={0};
	sprintf(value,"%lld",lnSecTime);
	value[8] = '2';
	value[9] = '3';
	value[10]= '5';
	value[11] = '9';
	value[12] = '5';
	value[13] = '9';
	lnSecTime = atoll(value);
	return lnSecTime;
}


int DCCommonIF::DayInYear(int year, int month, int day)
{
    int DAY[12]={31,28,31,30,31,30,31,31,30,31,30,31};
    if((year % 4 ==0 || year % 400 ==0) && (year % 100 !=0))
        DAY[1] = 29;
    for(int i=0; i<month; ++i)
    {
        day += DAY[i];
    }
    return day;
}


//获取日期差
int DCCommonIF::GetDateDiff(char *szTimeA,long usutime,long &mintime,long &lasttime)
{
	long lnsecA = dateToSec(szTimeA);
	long lnsecB = lnsecA+usutime;

	char szTimeB[16]={0};


	struct tm *localB = NULL;


	localB=localtime(&lnsecB);
	if (localB == NULL)
	{
		return -1;
	}

	//格式化为:年月日时分秒
	sprintf(szTimeB, "%04d%02d%02d%02d%02d%02d",
		   1900+localB->tm_year, localB->tm_mon+1, localB->tm_mday,
		   localB->tm_hour, localB->tm_min, localB->tm_sec);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", "timeA[%s],timeB[%s]",szTimeA,szTimeB);
	char A_hour[8] = {0};
	char *p = &szTimeA[8];
	strncpy(A_hour,p,2);
	int hour = atoi(A_hour);

	char A_min[8] = {0};
	p = &szTimeA[10];
	strncpy(A_min,p,2);
	int min = atoi(A_min);

	char A_sec[8] = {0};
	p = &szTimeA[12];
	strncpy(A_sec,p,2);
	int sec = atoi(A_sec);
	if(0==strncmp(szTimeA,szTimeB,8))//不跨天
	{
		return 0;
	}
	else if(0==strncmp(szTimeA,szTimeB,6))//跨天不跨月不跨年
	{

		char A_day[8] = {0};
		p = &szTimeA[6];
		strncpy(A_day,p,2);
		int day = atoi(A_day);

	   mintime =(24-hour)*3600-min*60-sec;//返回跨天前的使用量
	   lasttime =(localB->tm_hour)*3600+localB->tm_min*60+localB->tm_sec;//返回跨天后的使用量
	   return localB->tm_mday-day;//返回天数
	}
	else if(0==strncmp(szTimeA,szTimeB,4))//跨月不跨年
	{
		mintime =(24-hour)*3600-min*60-sec;//返回跨天前的使用量
		lasttime =(localB->tm_hour)*3600+localB->tm_min*60+localB->tm_sec;//返回跨天后的使用量
		int d1 =0;
		int	d2 =0;

		char A_year[8] = {0};
		p = &szTimeA[0];
		strncpy(A_year,p,4);
		int year = atoi(A_year);

		char A_mon[8] = {0};
		p = &szTimeA[4];
		strncpy(A_mon,p,2);
		int mon = atoi(A_mon);

		char A_day[8] = {0};
		p = &szTimeA[6];
		strncpy(A_day,p,2);
		int day = atoi(A_day);

		d1 = DayInYear(year, mon-1, day);
		d2 = DayInYear(localB->tm_year, localB->tm_mon, localB->tm_mday);
		return d2-d1;
	}
	else //跨年
	{
		mintime =(24-hour)*3600-min*60-sec;//返回跨天前的使用量
		lasttime =(localB->tm_hour)*3600+localB->tm_min*60+localB->tm_sec;//返回跨天后的使用量
		int d1,d2,d3,d4;

		char A_year[8] = {0};
		p = &szTimeA[0];
		strncpy(A_year,p,4);
		int year = atoi(A_year);

		char A_mon[8] = {0};
		p = &szTimeA[4];
		strncpy(A_mon,p,2);
		int mon = atoi(A_mon);

		char A_day[8] = {0};
		p = &szTimeA[6];
		strncpy(A_day,p,2);
		int day = atoi(A_day);


		d4 = DayInYear(year, mon-1, day);
		if((year % 4 ==0 || year % 400 ==0) && (year % 100 !=0))
			d1 = 366 - d4; //取得这个日期在该年还剩下多少天
		else
			d1 = 365 - d4;
		d2 = DayInYear(localB->tm_year, localB->tm_mon, localB->tm_mday); //取得在当年中的第几天

		d3 = 0;
		for(int iyear = year + 1; iyear < localB->tm_year; iyear++)
		{
			if((iyear % 4 ==0 || iyear % 400 ==0) && (iyear % 100 !=0))
				d3 += 366;
			else
				d3 += 365;
		}
		return d1 + d2 + d3;

	}

	return 0;
}

void DCCommonIF::ChangeMonth(char* month, int offset)
{
        month[6] = 0;

	int nYear = (month[0]-'0')*1000 + (month[1]-'0')*100 + (month[2]-'0')*10 + month[3]-'0';
	int nMonth = (month[4]-'0')*10 + month[5]-'0';

	if(offset < 0)
	{
		offset = -offset;

		int nLeftYear = offset/12;
		int nLeftMonth = offset%12;

		nYear = nYear - nLeftYear;
		nMonth = nMonth - nLeftMonth;

		if(nMonth <= 0)
		{
			nMonth += 12;
			nYear--;
		}

		sprintf(month, "%4d%02d", nYear, nMonth);
	}
    else if(offset > 0)
    {
        int nLeftYear = offset/12;
        int nLeftMonth = offset%12;

        nYear = nYear + nLeftYear;
        nMonth = nMonth +nLeftMonth;

        if (nMonth > 12)
        {
            nMonth -=  12;
            ++nYear;
        }

        sprintf(month,"%4d%02d",nYear,nMonth);
    }
}


int DCCommonIF::GetServiceFlowID(char *pSour)
{
    //更换时间
    //设置日志时间
    if (pSour == NULL || pSour[0] == '\0')
        return -1;
    char strTime[32] = { 0 };
    struct timeval tv = { 0 };
    struct tm *pTime = NULL;
    gettimeofday(&tv, NULL);
    time_t cur = tv.tv_sec;
    pTime = localtime(&cur);
    sprintf(strTime, "%04d%02d%02d%02d%02d%02d", pTime->tm_year + 1900, pTime->tm_mon + 1, pTime->tm_mday,
            pTime->tm_hour, pTime->tm_min, pTime->tm_sec);
    memcpy(pSour, strTime, 14);       //更换时间
    memcpy(pSour + 37, NETYPE, 3);       //更换网元表示
    memcpy(pSour + 40, SPCODE, 3);       //更换厂家
    return 0;
}

time_t DCCommonIF::GetTimeFromLongStr(const char *strtm)
{

	int year=-1,mon=-1,day=-1,hour=0,min=0,sec=0;
	sscanf(strtm,"%4d%2d%2d%2d%2d%2d",&year,&mon,&day,&hour,&min,&sec);
	tm tmtmp = { sec, min,hour , day,mon-1,year-1900};

	return mktime(&tmtmp);
}


void DCCommonIF::UnifiedCallNum(SPhone &phone, char* unified)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", "phone access[%d],country[00%d],province[0%d],area[0%d],carriers[%d],phone[%s]",
	phone.access, phone.country, phone.province, phone.area, phone.carriers, phone.phone.c_str());

	if (86 == phone.country)
	{
		//手机
		if (phone.phone.size() > 8)
		{
			sprintf(unified, "86%s", phone.phone.c_str());
		}
		else
		{
			if(phone.area)
			{
				sprintf(unified, "86%d%s", phone.area, phone.phone.c_str());
			}
			else
			{
				sprintf(unified, "%s", phone.phone.c_str());
			}
		}
	}
	else
	{
		sprintf(unified, "00%d%s", phone.country, phone.phone.c_str());
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", "unfied number[%s]", unified);
}

long DCCommonIF::OCTTODEC(const char * src)
{
	int len = 0;
	long num = 0;
	len = strlen(src);
	if(len > 16)
	{
		return 0;
	}
	for(int i=0; i<len; i++)
	{
		num = num << 4;
		if(src[i] >= '0' && src[i] <= '9')
		{
			num += (src[i] - '0');
		}
 		else if(src[i] >= 'A' && src[i] <= 'F')
 		{
 			num += (src[i] - 'A' + 10);
 		}
 		else if (src[i] >= 'a' && src[i] <= 'f')
		{
			num += (src[i] - 'a' + 10);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE,"", "OCTTODEC ERROR data:%s", src);
			num = 0;
			break;
		}
	}
	return num;
}

void DCCommonIF::DECTOHEX(long decimal, char *hexStr, int len)
{
	static const char hexChars[] = "0123456789abcdef";

	if (hexStr == NULL || len <= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_OTHER_TYPE, "", "DECTOHEX parameter error: NULL pointer or invalid length");
		return;
	}

	memset(hexStr, 0, len);

	if (decimal == 0)
	{
		hexStr[0] = '0';
		return;
	}

	int i = 0;
	const int maxBufferSize = 32;
	char temp[maxBufferSize] = {0};
	int maxDigits = len > maxBufferSize ? maxBufferSize - 1 : len - 1;

	while (decimal > 0 && i < maxDigits)
	{
		temp[i++] = hexChars[decimal % 16];
		decimal /= 16;
	}

	if (decimal > 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_OTHER_TYPE, "", "DECTOHEX warning: number too large, result truncated");
	}

	for (int j = 0; j < i; j++)
	{
		hexStr[j] = temp[i - j - 1];
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "DECTOHEX result: %s", hexStr);
}

int DCCommonIF::GetFreeUSU(rbresult* pREMsg, char * value)
{
	if(!pREMsg)
	{
		return -1;
	}

	int ret = 0;
	int iGroup = 0;
	int unit = 0;
	double lvalue=0;
	char *pUnit ;

	if(0  == pREMsg->freev.size())
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_OTHER_TYPE, "", "RBA msg [pREMsg->freev] error");
		return -1;
	}

	for(iGroup = 0; iGroup<pREMsg->freev.size(); iGroup++)
	{
		if(pREMsg->freev[iGroup].unit>0)
		{
			unit = pREMsg->freev[iGroup].unit;
			break;
		}
	}

	for(iGroup = 0; iGroup<pREMsg->freev.size(); iGroup++)
	{
		if(pREMsg->freev[iGroup].amount>0)
		{
			lvalue = pREMsg->freev[iGroup].amount;
			break;
		}
	}

	switch(unit)
	{
		case 1:
			if(lvalue>=60)sprintf(value, "%d分%d秒", ((long)lvalue)/60, ((long)lvalue)%60);
			else strcat(value, "秒");
			break;
		case 2:
			strcat(value, "分钟");
			break;
		case 3:
			strcat(value, "次");
			break;
		case 4:;
		case 7:;
		case 8:
			if(lvalue<1024)strcat(value, "字节");
			else if(lvalue>=1024 && lvalue<1048576)
			{
				sprintf(value, "%dKB", ((long)lvalue)/1024);
			}
			else if(lvalue>=1048576 && lvalue<1073741824)
			{
				sprintf(value, "%dMB", ((long)lvalue)/1048576);
			}
			else
			{
				sprintf(value, "%dGB", ((long)lvalue)/1073741824);
			}
			break;
		case 5:
			sprintf(value, "%.2f元", lvalue/100);
			break;
		case -1:
			sprintf(value, "无");
			break;
		default:
			ret = -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", " free USU[%s]", value);

	return 0;
}




int DCCommonIF::AccumlateBalance(rbresult* pREMsg)
{
	if(!pREMsg)
	{
		return -1;
	}

	int totalBalance = 0;
	int balance = 0;
	int iGroup = 0;
	char value[256] 	= {0};

	int size = pREMsg->bal.size();
	while(size)
	{
		acctitem_t acctitem =pREMsg->bal[iGroup];
		balance = acctitem.amount;
		totalBalance += balance;
		iGroup++;
		size--;
	}

	if(iGroup == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_WARN, SM_OTHER_TYPE,"", "RBA msg bal.amount null");
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,"", " balance[%d]", totalBalance);

	return totalBalance;
}



int DCCommonIF::getChargeInfo(char* ALLUSU, char* USU,DCAnsPara* m_smpara, const int no)
{
    int a=strlen(ALLUSU);
    if(';'!=ALLUSU[a-1] && a!=0)
    {
        ALLUSU[a] = ';';
        ALLUSU[a+1] = '\0';
    }

    a=strlen(USU);
    if(';'!=USU[a-1] && a!=0)
    {
        USU[a] = ';';
        USU[a+1] = '\0';
    }

    string strDes(ALLUSU);
    string strOrg(USU);
    char fenceStr[2]= {0};
    char endStr[2]= {0};

    fenceStr[0] = SM_CDR_FENCE;
    endStr[0] = SM_CDR_END_CHAR;

    /*fomat*/
    int pos = 0;
    int nstart=0;
    int len = strOrg.length();
    do
    {
        pos = strOrg.find(":", pos+1);
        if(-1 != pos)
        {
            string id2 = strOrg.substr(nstart, pos-nstart);
            int pos1 = strDes.find(id2);
            if(-1 != pos1)
            {
                pos1 = strDes.find(fenceStr, pos1+1);
                if(-1!=pos1)
                {
                    int pos2 = strDes.find(";", pos1+1);
                    long val1 = atol(strDes.substr(pos1+1, pos2-pos-1).c_str());

                    pos = strOrg.find(fenceStr, pos+1);
                    int pos3 = strOrg.find(";", pos+1);
                    long val2 = atol(strOrg.substr(pos+1, pos3-pos-1).c_str());

                    val1+=val2;
                    char tmpbuf[32] = {0};
                    sprintf(tmpbuf, "%ld\0", val1);
                    strDes.replace(pos1+1, pos2-pos1-1, tmpbuf);
                    pos = pos3+1;
                    nstart = pos;
                }
                else
                {
                    break;
                }
            }
            else
            {
                pos = strOrg.find(";", pos+1);
                if(-1!=pos)
                {
                    string id = strOrg.substr(nstart, pos-nstart);
                    strDes+=id;
                    strDes+=";";

                    pos+=1;
                    nstart = pos;
                    continue;
                }
            }
        }
        else
        {
            break;
        }
       // pos ++;
    }while(pos != len);

    /*find*/
    //sprintf(ALLUSU, "%s", strDes.c_str());
    strcpy(ALLUSU, strDes.c_str());
    int lens = strlen(ALLUSU);
    ALLUSU[lens-1] = SM_CDR_END_CHAR;
    ALLUSU[lens] = '\0';
    return 0;
}




//短信AOC提醒增加累积量信息函数，与话单计算相同，在后续进行整合
int DCCommonIF::getAccumulateInfo(char* ALLUSU, char* USU ,TSMPara* m_smpara, const int no)
{
    int a=strlen(ALLUSU);
    if(';'!=ALLUSU[a-1] && a!=0)
    {
        ALLUSU[a] = ';';
        ALLUSU[a+1] = '\0';
    }

    a=strlen(USU);
    if(';'!=USU[a-1] && a!=0)
    {
        USU[a] = ';';
        USU[a+1] = '\0';
    }

    string strDes(ALLUSU);
    string strOrg(USU);
    char fenceStr[2]= {0};
    char endStr[2]= {0};

    fenceStr[0] = SM_CDR_FENCE;
    endStr[0] = SM_CDR_END_CHAR;

    /*fomat*/
    int pos = 0;
    int nstart=0;
    int len = strOrg.length();
    do
    {
        pos = strOrg.find(":", pos+1);
        if(-1 != pos)
        {
            pos = strOrg.find(fenceStr, pos +1);
            if(-1 != pos)
            {
                string id1 = strOrg.substr(nstart, pos-nstart);
                int tmppos = strDes.find(id1);       //A01:1001找到
                if(-1 != tmppos)
                {
                    tmppos = strDes.find(fenceStr, tmppos+2);
                    int tmppos2 = strDes.find(fenceStr, tmppos+2);
                    long val1 = atol(strDes.substr(tmppos+1, tmppos2-tmppos-1).c_str());

                    int posOrg1 = strOrg.find(fenceStr, pos+2);//累加后替换
                    long valOrg = atol(strOrg.substr(pos+1, posOrg1-pos-1).c_str());
                    val1 +=valOrg;
                    char tmpbuf[32]={0};
                    sprintf(tmpbuf, "%ld\0", val1);
                    strDes.replace(tmppos+1, tmppos2-tmppos-1, tmpbuf);
                    pos = posOrg1;

                    tmppos = strDes.find(fenceStr, tmppos2);
                    tmppos2= strDes.find(";", tmppos);
                    long val2 = atol(strDes.substr(tmppos+1, tmppos2-tmppos-1).c_str());
                    //找到位置后替换
                    posOrg1 = strOrg.find(";", pos+2);
                    valOrg = atol(strOrg.substr(pos+1, posOrg1-pos-1).c_str());
                    sprintf(tmpbuf, "%ld\0", valOrg);
                    strDes.replace(tmppos+1, tmppos2-tmppos-1, tmpbuf);
                    nstart = posOrg1+1;
                    pos = nstart +1;
                }
                else     //找不到，逐个追加
                {
                    pos = strOrg.find(";", pos+1);
                    if(-1!=pos)
                    {
                        string id = strOrg.substr(nstart, pos-nstart);
                        strDes+=id;
                        strDes+=";";

                        pos+=1;
                        nstart = pos;
                        continue;
                    }
                }
            }
        }
        else
        {
            break;
        }
       // pos ++;
    }while(pos != len);

    /*find*/
    //sprintf(ALLUSU, "%s", strDes.c_str());
    strcpy(ALLUSU, strDes.c_str());
    int lens = strlen(ALLUSU);
    ALLUSU[lens-1] = SM_CDR_END_CHAR;
    ALLUSU[lens] = '\0';
    return 0;
}

int DCCommonIF::OLDCheckSpecialFiled(char* pFiled)
{
	if(!strcasecmp(pFiled,"Fee1") )
	{
		return 1;
	}
	else if(!strcasecmp(pFiled,"Fee2"))
	{
		return 2;
	}
	else if(!strcasecmp(pFiled,"Fee3"))
	{
		return 3;
	}
	else if(!strcasecmp(pFiled,"Fee4"))
	{
		return 4;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID1"))
	{
		return 5;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID2"))
	{
		return 6;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID3"))
	{
		return 7;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID4"))
	{
		return 8;
	}

  	else if(!strcasecmp(pFiled,"RE_STR_SUB_VLR"))
	{
		return 9;
	}
	return 0;
}

int DCCommonIF::CheckSpecialFiled(char* pFiled)
{
	if(!strcasecmp(pFiled,"Fee1") )
	{
		return 1;
	}
	else if(!strcasecmp(pFiled,"Fee2"))
	{
		return 2;
	}
	else if(!strcasecmp(pFiled,"Fee3"))
	{
		return 3;
	}
	else if(!strcasecmp(pFiled,"Fee4"))
	{
		return 4;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID1"))
	{
		return 5;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID2"))
	{
		return 6;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID3"))
	{
		return 7;
	}
	else if(!strcasecmp(pFiled,"ACCT_Item_Type_ID4"))
	{
		return 8;
	}

  	else if(!strcasecmp(pFiled,"RE_STR_SUB_VLR"))
	{
		return 9;
	}
	else if(!strcasecmp(pFiled,"CDR_LTE_FLAG"))
	{
		return 10;
	}
	else if(!strcasecmp(pFiled,"CDR_PAYMENT_TYPE"))
	{
		return 11;
	}
	else if(!strcasecmp(pFiled,"CDR_OFFER_ID"))
	{
		return 12;
	}
	else if(!strcasecmp(pFiled,"CDR_ACCT_ITEM_ID"))
	{
		return 13;
	}
	else if(!strcasecmp(pFiled,"CDR_FEE"))
	{
		return 14;
	}
	else if(!strcasecmp(pFiled,"CDR_ACCT_ITEM_UNIT_TYPE"))
	{
		return 15;
	}
	else if(!strcasecmp(pFiled,"CDR_BILLING_DURATION"))
	{
		return 16;
	}
	else if(!strcasecmp(pFiled,"CDR_BAODI_FEE"))
	{
		return 17;
	}
	else if(!strcasecmp(pFiled,"CDR_PUB_STR_ACCUMLATORINFO"))
	{
		return 18;
	}
	else if(!strcasecmp(pFiled,"CDR_PUB_INT_SEQ"))
	{
		return 19;
	}
	else if(!strcasecmp(pFiled,"CDR_PUB_INT_FLAG"))
	{
		return 20;
	}
	else if(!strcasecmp(pFiled,"CDR_CUT_INT_FLAG"))
	{
		return 20;
	}
	else if(!strcasecmp(pFiled,"CDR_RATABLE_VALUE"))
	{
		return 21;
	}
	else if(!strcasecmp(pFiled,"CDR_DISCT_OUT_RATABLE"))
	{
		return 22;
	}
	else if(!strcasecmp(pFiled,"CDR_CALLING_AREA_ID"))
	{
		return 23;
	}
	else if(!strcasecmp(pFiled,"CDR_CALLED_AREA_ID"))
	{
		return 23;
	}
	else if(!strcasecmp(pFiled,"CDR_PUB_STR_FEETYPE"))
	{
		return 23;
	}
	else if(!strcasecmp(pFiled,"CDR_MSC_ID"))
	{
		return 23;
	}
	else if(!strcasecmp(pFiled,"CDR_STR_LAC_ID"))
	{
		return 23;
	}
	else if(!strcasecmp(pFiled,"CDR_COUNTS"))
	{
		return 24;
	}
	else if(!strcasecmp(pFiled,"CDR_PUB_STR_OFRINSTID"))
	{
		return 25;
	}
    else if(!strcasecmp(pFiled,"CDR_PUB_STR_PRICING_PLAN_ID"))
	{
		return 26;
	}
	else if(!strcasecmp(pFiled,"CDR_BASE_FEE"))
	{
		return 27;
	}
	//存在  CDR_MSC_ID
	return 0;
}

void DCCommonIF::LTrim(char *pszStr,char ch)
{
	int nLen = strlen(pszStr);
	int i = 0;
	while(pszStr[i] == ch && i < nLen)
	{
		i++;
	}

	memmove(pszStr,(pszStr + i),nLen - i +1);

	return ;
}

void DCCommonIF::RTrim(char *pszStr,char ch)
{
	int nLen = strlen(pszStr);

	while(nLen > 0 && pszStr[nLen - 1] == ch)
	{
		pszStr[nLen - 1] = 0;
		nLen--;
	}
	return;
}

void DCCommonIF::Trim(char *pszStr,char ch)
{
	LTrim(pszStr,ch);
	RTrim(pszStr,ch);
}

int DCCommonIF::SplitCharStr(const char *pszStr, const char cSplit, map<int,string> &FieldList )
{
	char *pch;
	char szBuf[1024] = {0};
	char szTmp[50] = {0};
	string tmpField;
	int nNum=0;


	strncpy(szBuf, pszStr, sizeof(szBuf) - 1);
    Trim(szBuf,'#');

	while(1)
	{
		pch = strchr(szBuf, cSplit);
		if( NULL == pch )
		{
			break;
		}
		nNum++;
		memset(szTmp, 0, sizeof(szTmp));
		strncpy(szTmp, szBuf, pch - szBuf);
		tmpField = szTmp; //char*到string的转换
		FieldList.insert(map<int,string>::value_type(nNum,tmpField));
		strcpy(szBuf, pch + 1);
	}
	tmpField = szBuf;
	nNum++;
	FieldList.insert(map<int,string>::value_type(nNum,tmpField));

	return 0;
}

void DCCommonIF::SplitString(const std::string& str, char sep, std::vector<std::string>& vec)
{
	size_t pos = 0;
	size_t prev = 0;
	std::string sub;
	bool brun = true;
	vec.clear();
	while(brun)
	{
		pos = str.find(sep, prev);
        if(pos == std::string::npos)
		{
			sub = str.substr(prev);
			brun = false;
		}
		else
		{
			sub = str.substr(prev, pos-prev);
			prev = pos+1;
		}
		vec.push_back(sub);
	}
}

void DCCommonIF::SplitSString(const std::string& str, const std::string& sep, std::vector<std::string>& vec)
{
    size_t head = 0;
    size_t tail = 0;
    while(tail < str.size())
	{
        tail = str.find(sep, head);
        if(tail == std::string::npos)
		{
            vec.push_back(str.substr(head));
            tail = str.size();
		}
		else
		{
			vec.push_back(str.substr(head, tail-head));
			head=tail+sep.size();
		}
	}
}

string DCCommonIF::GetBatchNo(int nServConextId, int iUpdateTime, int iMsgType)
{
	string strBatchNo;
	char szTempBatchNo[24] = {0};

	char buf[16] = {0};
	unsigned long long  cur = 0;
	time_t et =  time(NULL);
	struct tm *p = localtime(&et);
	sprintf(buf, "%04d%02d%02d%02d%02d", (1900+p->tm_year), (1+p->tm_mon), p->tm_mday, p->tm_hour, p->tm_min/iUpdateTime);
	sprintf(szTempBatchNo,"%d%d%s",iMsgType,nServConextId, buf);
	strBatchNo = szTempBatchNo;
	return strBatchNo;
}

//获取主机IP,多个网卡的情况也只取第一个
void DCCommonIF::GetHostIp(string &IP)
{
	struct ifaddrs * ifAddrStruct = NULL,*ifAddrStruct1=NULL;
    void * tmpAddrPtr = NULL;
    getifaddrs(&ifAddrStruct);
	ifAddrStruct1 = ifAddrStruct;

    while (ifAddrStruct != NULL)
    {
        if (ifAddrStruct->ifa_addr->sa_family == AF_INET)
        {
            // check it is IPv4
            // is a valid IPv4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];

            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            if(strcmp(addressBuffer, "127.0.0.1") == 0)
            {
                ;
            }
            else
            {
                IP = addressBuffer;
				break;
            }

        }

       /* else if (ifAddrStruct->ifa_addr->sa_family == AF_INET6)
        {
         // check it is IPv6
         // is a valid IPv6 Address
         tmpAddrPtr=&((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
         char addressBuffer[INET6_ADDRSTRLEN];

         inet_ntop(AF_INET6, tmpAddrPtr, addressBuffer, INET6_ADDRSTRLEN);
         printf("%s IP Address %s\n", ifAddrStruct->ifa_name, addressBuffer);
					}*/

        ifAddrStruct = ifAddrStruct->ifa_next;
    }
	freeifaddrs(ifAddrStruct1);
    return;
}


