/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqVOICETEL.h
*Indentifier：
*
*Description：
*		语音业务处理类
*Version：
*		V1.0
*Author:
*		ZY.F
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_VOICE_TEL_H__
#define __DC_REQ_VOICE_TEL_H__
#include "DCReqVOICE.h"


class DCReqVOICETEL:public DCReqVOICE
{
	public:

		DCReqVOICETEL();
		virtual ~DCReqVOICETEL();

	private:

		virtual int SwitchReqType(STBizMsg* bizMsg);
		int composeRER(STBizMsg* bizMsg,SCCRBase* base,SCCRVOICE* data,SSessionCache &cacheData);
        int Init(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg);
		int Update(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg);
		int Term(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg);
		int Event(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg);
		
		int XdrEvent(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg);
};

#endif

