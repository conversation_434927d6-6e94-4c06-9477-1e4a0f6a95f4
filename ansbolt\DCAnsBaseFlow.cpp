#include "DCAnsBaseFlow.h"
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCRFData.h"
#include "DCRbMsgDef.h"
#include "ErrorCode.h"
#include "DCAnsPara.h"
#include "DCParseXml.h"
#include "UHead.h"
#include "DCEvtCheck.h"
#include "DCCommonIF.h"


using namespace std;
using namespace ocs;
int DCAnsBaseFlow::init()
{
	int ret  =0 ;
	BData* base = new DCAnsPara();
	DCAnsPara *anspara = dynamic_cast<DCAnsPara *>(DCRFData::instance()->get("DCAnsPara"));
	if (NULL == anspara)
	{
		ret= DCRFData::instance()->regist("DCAnsPara",base);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "regist ans param failed!");
			return -1;
		}
	}

	//mq初始化
   /* const char *brokers = DCParseXml::Instance()->GetParam("mqservAddr","Common/mq");

	m_producer = new DCMqProduceServer(MQ_ROCKET);
	ret = m_producer->Init(brokers,"ansBoltGroup");
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init producer failed!");
		return -1;
	}
	m_producer->Start();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init producer successful!");*/

    char sz_combinaSwitch[8] = {0};
    strncpy(sz_combinaSwitch, DCParseXml::Instance()->GetParam("combinaSwitch","SM"), sizeof(sz_combinaSwitch));
    m_combinaSwitch = atoi(sz_combinaSwitch);
    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get combinaSwitch [%d]", m_combinaSwitch);

  	m_pool = DCOBJSetPool::instance();
	m_pool->reg<STBizMsg>();	
	m_pool->reg<rbhead>();
	m_pool->reg<rbresult>();
	m_pool->reg<rbext>();
	m_pool->reg<UHead>();
	m_pool->reg<RatingMessageInfo_t>();
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init ansbaseflow successful!");
	return 0;
}

int DCAnsBaseFlow::process(void* input, void* output)
{
	std::string &recvRBAMsg =  *(std::string *)input;	
	
	DCOBJSet* pset = m_pool->get();
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	rbhead   *head   = pset->get<rbhead>();
	rbresult *base = pset->get<rbresult>();
	rbext *ext = pset->get<rbext>();
	UHead* uhd = pset->get<UHead>();	
	RatingMessageInfo_t* ratingMessage = pset->get<RatingMessageInfo_t>();
	bizMsg->m_pSendMsg = (std::multimap<string,string>*)output ;
	bizMsg->m_base = base;
	bizMsg->m_dbm = this->dbm();
	//bizMsg->m_producer = m_producer;	
	bizMsg->m_eptType = 2;
	bizMsg->m_extend = ext;
	bizMsg->m_ratingMsg = ratingMessage;
	bizMsg->m_iRollFlag = uhd->flag;
	bizMsg->m_operListId = ext->kv["operListId"];
	// memcpy(bizMsg->m_topic, m_Topic, sizeof(bizMsg->m_topic));

	int ret = 0;

	vector<uint8_t> vectorMsg;
	vectorMsg = HexDecode(recvRBAMsg.c_str(),recvRBAMsg.size());
		
    //公共消息反序列化
	//m_de.set(( uint8_t* )recvRBAMsg.c_str(),recvRBAMsg.size());	
	try
    {
		m_de.set(vectorMsg);
		m_de.decode(*uhd);
		m_de.decode(*head);
		m_de.decode(*base);
		m_de.decode(*ext);
		m_de.decode(*ratingMessage);
		//std::string skey = head->session+ "|" + uhd->uid;
		std::string skey = uhd->uid;
		DCLOG_SETKEY(skey.c_str());
		DCLOG_SETCTL(DCLOG_MASK_TRACE, head->trace);	//号码跟踪
		 
		//打印base内容
		m_print.clear();
		m_print.print(*uhd);
		m_print.print(*head);
		m_print.print(*base);
		m_print.print(*ext);
		m_print.print(*ratingMessage);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBA[%s]", m_print.data());	
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_DECODE_CORE, "","decode failed");
              m_pool->put(pset);
		return ERR_DECODE_CORE;
	}

	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","srart dealing[%s]",uhd->uid.c_str());
	bizMsg->m_topology = head->topology.c_str();
	bizMsg->m_uid = uhd->uid.c_str();
	bizMsg->m_anstopic = ext->kv["anstopic"].c_str();
	strcpy(bizMsg->m_taskId,ext->kv["taskId"].c_str());
	bizMsg->m_strservingCNPlmnId = ext->kv["CDR_CNPLMN"];
    bizMsg->m_offline = head->mode;
	int nType = head->type;
	//bizMsg->m_plugin = (m_flows.back()).start;
    if(uhd->checkKey.size() > 0)
	{
		DCEvtCheck::instance()->SetByCheckKey(uhd->checkKey);
		DCEvtCheck::instance()->Set_ID(DCEvtCheck::instance()->CreateUUID());
		DCEvtCheck::instance()->Set_P("SMA",uhd->uid);
		DCEvtCheck::instance()->Set_ID_T(DCEvtCheck::instance()->CreateTimeStamps());
		bizMsg->m_strCheckKey = DCEvtCheck::instance()->GetCheckKey();
	}
	ret = call_all(pset,NULL);
	if(bizMsg->m_ReDealAns)
	{
		bizMsg->m_longCDR = 0;
		ret = call_all(pset,NULL);
	}
	bizMsg->m_pSendMsg->insert(pair<string,string>("TaskId",bizMsg->m_taskId));
	bizMsg->m_pSendMsg->insert(pair<string,string>("AnsTopic",bizMsg->m_anstopic));
	bizMsg->m_pSendMsg->insert(pair<string,string>("UID",bizMsg->m_uid));

	string str = bizMsg->data;
	string sendmsg = HexEncode((const uint8_t*)str.c_str(),str.size());
	bizMsg->m_pSendMsg->insert(pair<string,string>("SENDMSG",sendmsg));
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send to dcf msg:%s", sendmsg.c_str());

	//sendmsg = bizMsg->data;
	char stmp[12] = {0};
	sprintf(stmp, "%d", bizMsg->m_resultcode);
	bizMsg->m_pSendMsg->insert(pair<string,string>("ResultCode",stmp));

    composeStaMsg(bizMsg, nType, uhd->ext["IMPORT_P"], uhd->ext["OnlineMod5g"]);
	m_pool->put(pset);

	return ret;
}

int DCAnsBaseFlow::call_all(void* input, void* output)
{
	STFlowItem* iflow = &m_flows.front();
    int ret = 0;
	void* mid = input;
	std::map<std::string, STFlowItem*>::iterator ntflow;
	while(iflow)
	{
		ret = iflow->start->call(mid, NULL);
    	if(ret == 0 || ret == RET_OVER)
		{
			ntflow = iflow->next.find("OK");
			ret = 0;
		}
		else if(ret==RET_CDR)
		{			
			ntflow = iflow->next.find("CDR");
		}
		else
		{
			ntflow = iflow->next.find("FAIL");		
		}

		if(ntflow == iflow->next.end())
		{
			iflow = NULL;
		}
		else
		{
			iflow = ntflow->second;
			mid = input;
		}
	}
	return ret;
}

int DCAnsBaseFlow::composeStaMsg(STBizMsg* bizMsg, int nType, std::string sImportType, std::string sOnlineMod5G)
{

	char value[256] = {0};
	ocs::SComHead pSComhead;
	ocs::StatRecord pStatRecord;
	ocs::RatingMessageInfo_t bodySM;
	bool checkSum = false;
	pSComhead.checkKey =bizMsg->m_strCheckKey;
	pSComhead.AckEndBoltFlag = 0;
	pSComhead.collectid = 0;
	pSComhead.latnid = bizMsg->m_ilatnId;
	pSComhead.operlistid = atoi(bizMsg->m_operListId.c_str());
	pSComhead.switchid = atoi(bizMsg->m_operListId.c_str());
	pSComhead.opertype = bizMsg->m_szServiceContextIDStr;
	pSComhead.sourceid = atol(bizMsg->m_sourceId.c_str());
	pSComhead.recordid = 0;
	pSComhead.sourcefile = "";
	pSComhead.m_sProcerName = "B";
	pSComhead.msgtype = "idx";
	pSComhead.epttype = 0;
	pSComhead.resultcode = bizMsg->m_resultcode;
	pSComhead.BatchNo = bizMsg->m_sBatchId;
	pSComhead.uid = bizMsg->m_uid;
	pSComhead.trace = bizMsg->m_trace_flag;
	pSComhead.modid = 0;
	pSComhead.ip = "";
	pSComhead.ext["IMPORT_P"] = sImportType;
	pSComhead.ext["OnlineMod5g"] = sOnlineMod5G;
	pStatRecord.m_iNormalRecords = 0;
	pStatRecord.m_iInvalidRecords = 0;
	pStatRecord.m_iAbNormalRecords = 0;
	pStatRecord.m_iNoUserRecords = 0;
	pStatRecord.m_iDualRecords = 0;
	pStatRecord.m_iTotalRecords = 0;

	if(bizMsg->m_serviceContextID == VOICE && bizMsg->m_anspara->GetINPara()->volteCdrFalg == 1 && bizMsg->m_volteFlag == 1)
	{
		pSComhead.opertype += ".volte";
	}

	m_print.clear();
	m_print.print(pSComhead);
	m_print.print(pStatRecord);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LOGRATING:%s", m_print.data());

	m_en.clear();
	m_en.encode(pSComhead);
	m_en.encode(pStatRecord);
	

	string strMsg = HexEncode(m_en.data(),m_en.size());
	//头部加上固定16位时间戳
	char buf[30]={0};
	struct timeval tmv;
	gettimeofday(&tmv, NULL);
	sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
	strMsg.insert(0,buf,16);
	bizMsg->m_pSendMsg->insert(pair<string,string>("LOGRATING",strMsg));
	
	std::multimap<string,string>::iterator itBeg;
	std::multimap<string,string>::iterator itEnd;
	std::multimap<string,string>::iterator iter;

	//if(bizMsg->m_longCDR != 0 || (bizMsg->m_longCDR == 0 && bizMsg->m_requestType == 3) || SMS == bizMsg->m_serviceContextID || ISMP == bizMsg->m_serviceContextID)
	if(bizMsg->m_requestType == 3 || bizMsg->m_requestType == 4 || bizMsg->m_requestType == 5 || bizMsg->m_longCDR != 0)
	{
		if(bizMsg->m_testFlag == 0)
		{
			pSComhead.msgtype = "SM";	  // SM 正常清单
			if(51==nType)
			{
				pSComhead.msgtype = "SM_5G";
			}
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "msgtype:%s", pSComhead.msgtype.c_str());

            if(m_combinaSwitch == 1)
                checkSum = checkSumFile(pSComhead,bizMsg, bodySM);

            if(!checkSum)
            {
                itBeg = bizMsg->m_pSendMsg->lower_bound("SUMINFO");
                itEnd = bizMsg->m_pSendMsg->upper_bound("SUMINFO");
                for(iter = itBeg; iter != itEnd; ++iter)
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SUMINFO MSG:%s", iter->second.c_str());
                    bodySM.vSumInfo.push_back(iter->second);
                }

                itBeg = bizMsg->m_pSendMsg->lower_bound("PAYFLAGTOPIC");
                itEnd = bizMsg->m_pSendMsg->upper_bound("PAYFLAGTOPIC");
				if (bizMsg->m_iRollFlag == 1)  // 回退清单
				{
					for(iter = itBeg; iter != itEnd; ++iter)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC roll MSG:%s", iter->second.c_str());
						bodySM.vRollCHGMessage.push_back(iter->second);
					}
				}
				else
				{
					for(iter = itBeg; iter != itEnd; ++iter)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC MSG:%s", iter->second.c_str());
						bodySM.vCHGmessage.push_back(iter->second);
					}
				}

                itBeg = bizMsg->m_pSendMsg->lower_bound("ACCUMULATION");
                itEnd = bizMsg->m_pSendMsg->upper_bound("ACCUMULATION");
                for(iter = itBeg; iter != itEnd; ++iter)
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ACCUMULATION MSG:%s", iter->second.c_str());
                    bodySM.vAccunumlationInfo.push_back(iter->second);
                }

                itBeg = bizMsg->m_pSendMsg->lower_bound("RATABLEINFO");
                itEnd = bizMsg->m_pSendMsg->upper_bound("RATABLEINFO");
                for(iter = itBeg; iter != itEnd; ++iter)
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RATABLEINFO MSG:%s", iter->second.c_str());
                    bodySM.vRatableInfo.push_back(iter->second);
                }
            }
		}
		else
		{
			pSComhead.msgtype = "SMTest";	  // SMTest 模拟拨测清单
			itBeg = bizMsg->m_pSendMsg->lower_bound("TESTCDRTOPIC");
			itEnd = bizMsg->m_pSendMsg->upper_bound("TESTCDRTOPIC");
			for(iter = itBeg; iter != itEnd; ++iter) 
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "TESTCDRTOPIC MSG:%s", iter->second.c_str());
				bodySM.vCHGmessage.push_back(iter->second);
			}
		}	

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "vCHGmessage.size[%d], vRatableInfo.size[%d]", bodySM.vCHGmessage.size(), bodySM.vRatableInfo.size());

		m_en.clear();
		m_en.encode(pSComhead);
		m_en.encode(bodySM);
		
		string cdrMsg = HexEncode(m_en.data(),m_en.size());
		cdrMsg.insert(0,buf,16);
		
		bizMsg->m_pSendMsg->insert(pair<string,string>("CDRMSG",cdrMsg));
		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CDRMSG:%s", cdrMsg.c_str());
	}
	
	return 0;	
}

bool DCAnsBaseFlow::checkSumFile(ocs::SComHead& pSComhead,STBizMsg* bizMsg,ocs::RatingMessageInfo_t& bodySM)
{
    int realtime = 0;
    ocs::CombineRecord bodySMRecord;
    std::vector<std::string> vfileInfo;
    std::multimap<string,string>::iterator itBeg;
    std::multimap<string,string>::iterator itEnd;
    std::multimap<string,string>::iterator iter;
    iter = bizMsg->m_pSendMsg->find("SUMINFO");

    if(iter != bizMsg->m_pSendMsg->end())
    {
        //判断n_if_realtime_disct是否为1
        vfileInfo.clear();
        DCCommonIF::SplitString(iter->second, '|', vfileInfo);
        realtime = atoi(vfileInfo[21].c_str());
        if(realtime == 1)
        {
            itBeg = bizMsg->m_pSendMsg->lower_bound("SUMINFO");
            itEnd = bizMsg->m_pSendMsg->upper_bound("SUMINFO");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                //sum文件出两次,组合一次，单独一次
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SUMINFO MSG:%s", iter->second.c_str());
                bodySM.vSumInfo.push_back(iter->second);
                bodySMRecord.vSumInfo.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("PAYFLAGTOPIC");
            itEnd = bizMsg->m_pSendMsg->upper_bound("PAYFLAGTOPIC");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "PAYFLAGTOPIC MSG:%s", iter->second.c_str());
                bodySMRecord.vCHGmessage.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("ACCUMULATION");
            itEnd = bizMsg->m_pSendMsg->upper_bound("ACCUMULATION");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ACCUMULATION MSG:%s", iter->second.c_str());
                bodySMRecord.vAccunumlationInfo.push_back(iter->second);
            }

            itBeg = bizMsg->m_pSendMsg->lower_bound("RATABLEINFO");
            itEnd = bizMsg->m_pSendMsg->upper_bound("RATABLEINFO");
            for(iter = itBeg; iter != itEnd; ++iter)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RATABLEINFO MSG:%s", iter->second.c_str());
                bodySMRecord.vRatableInfo.push_back(iter->second);
            }

            //编码
            m_en.clear();
			m_en.encode(pSComhead);
            m_en.encode(bodySMRecord);
            bodySM.m_sCombineMsg = HexEncode(m_en.data(),m_en.size());
        }
        else
            return false;
    }
    else
        return false;

    return true;
}



DYN_PLUGIN_CREATE(DCAnsBaseFlow, "FL_ANSBASEFLOW", "FL_AnsBaseFlow", "1.0.0")
