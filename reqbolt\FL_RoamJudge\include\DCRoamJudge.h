/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCRoamjudge.h
*Indentifier：
*
*Description：
*		应答流程组件
*Version：
*		V1.0
*Author:
*

*Finished：
*
*History:
********************************************/
#ifndef __DC_ROAM_JUDGE_H__
#define __DC_ROAM_JUDGE_H__

#include "DCBizMsgDef.h"
#include "DCOcpMsgDef.h"
using namespace ocs;
enum ROAM_TYPE
{
	ROAM_BORDER_PROV = 7,       // 省内边界漫游
	ROAM_PROV = 1,              // 省内漫游
	ROAM_DIFF_PROV = 4,         // 省际漫游
	ROAM_DIFF_BORDER_PROV = 8,  // 省际边漫
	ROAM_PROV_GAN_AO_TAI = 9,   // 中国港澳台漫游

};
class DCRoamJudge
{
	public:
		DCRoamJudge();

		virtual ~DCRoamJudge();

		int RoamVoice(STBizMsg* bizMsg,SCCRBase* base,SCCRVOICE* data);
		int RoamMscVlr(STBizMsg* bizMsg,const SPhone& home, const SPhone& called, AREA_INFO* visit, char* MSCorVLR, const char* LAC, const char* CELLID,int calltype);
		int RoamLAC(STBizMsg* bizMsg,const SPhone& home,const SPhone& called, AREA_INFO* visit, const char* lac, const char* cellid,int provflag=1);
		int RoamCELLID(STBizMsg* bizMsg, const SPhone& home,const SPhone& called, AREA_INFO* visit, const char* lac, const char* cellid, int provflag =1);
		int RoamEdge(STBizMsg* bizMsg, const SPhone& home,const SPhone& called, AREA_INFO* visit, const char* lac, const char* cellid, const char* MSCorVLR, int provflag = 1);
		int LongVOICE(STBizMsg* bizMsg, const SPhone& calling, const SPhone& called, AREA_INFO* visit,int roamtype);

		int RoamDATA(STBizMsg* bizMsg,SCCRBase* base,SCCRDATA* data);
        int RoamDATA_5G(STBizMsg* bizMsg,ocs::SCCRBase* base,ocs::SCCR5GInfo* data);
		int RoamDataCELLID(STBizMsg* bizMsg, const SPhone& home, const char* cellid);
		int RoamDataPDSN(STBizMsg* bizMsg, const SPhone& sub, AREA_INFO* visit, const char* PDSN);
		int RoamDataSGSN(STBizMsg* bizMsg, const SPhone& sub, AREA_INFO* visit, const char* SGSN, const char* MSC);
		int RoamDataCellMsc(STBizMsg* bizMsg, const char* userinfo, const SPhone& sub,const char* cellid,const char* msc,AREA_INFO* visit,int roamtype);
		int RoamDataMsc(STBizMsg* bizMsg,   const SPhone& sub,const char* msc,AREA_INFO* visit);
		int RoamByTac(STBizMsg* bizMsg, const SPhone& sub,const char* tac,AREA_INFO* visit);

		int RoamDsl(STBizMsg* bizMsg, const SPhone & home, AREA_INFO* visit, const char * nasIP);

		int RoamSMS(STBizMsg* bizMsg, SCCRSMS *data);
		int RoamSMSMsc( char* MSCorVLR, AREA_INFO* visit,  int iCCRRoamType, STBizMsg* bizMsg);
		int RoamSMSByRoamingType(char* MSCorVLR,int &roamingtype, AREA_INFO* visit,TSMPara* smpara);
		int GetAreaCode(SPhone & phone, STBizMsg * bizMsg);
        bool Judge5GCNPLMN(STBizMsg * bizMsg,ocs::SCCRBase* base);
		void GetVistInfo(STBizMsg* bizMsg,string nrCellID,string strCNPLmn);

};

#endif

