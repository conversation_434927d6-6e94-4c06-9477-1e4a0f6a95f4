﻿#include <map>
#include "DCBizEpt.h"
#include "DCLogMacro.h"
#include "DCMqProduceServer.h"
#include "ErrorCode.h"
#include "BizLenDef.h"
#include "DCOcpMsgDef.h"
#include "DCAnsPara.h"
#include "func_sqlindex.h"
#include "TConfig.h"
#include "DCRbMsgDef.h"
#include "REMsgTypeDef.h"
#include "ErrorCode.h"
#include "OCPMsgDef.h"
#include "UHead.h"
#include <sys/time.h>
#include <stdio.h>
#include "DCKpiSender.h"
#include "DCEptMsgDef.h"


using namespace ocs;

DCBizEpt::DCBizEpt():m_en(ESeriaBinString)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE, "", "");
}

DCBizEpt::~DCBizEpt()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "");
}

int DCBizEpt::Work(void *data)
{
	return 0;
}

int DCBizEpt::SendErrorResult5G(STBizMsg* bizMsg)
{
	char value[BIZ_TEMP_LEN_1024] = {0};
	int termFlag 		          = 0;
	int updateFlag				  = 0;
	int resultcode = bizMsg->m_anspara->GetOCPResultCode(bizMsg->m_resultcode);
	if (resultcode == -1)
	{
		resultcode = 3004;
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Useless result code [%d]", bizMsg->m_resultcode);
	}

	DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "OCP Result-Code[%d]", resultcode);

	if (SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
	{
		bizMsg->m_requestType = SM_SESSION_UPDATE_CODE;
	}

	ocs::UHead uhd;
	ocs::SCCA5G cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.sessionID = bizMsg->m_sessionID;
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.resultCode = 2001;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.trace = DCLOG_GETCTL(DCLOG_MASK_TRACE);
	cca.msgType = SM_OCP_ERR_MSG_ID;
	cca.resultCode = resultcode;

	map<string, STCodeOfr> map_rg;
	if (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
	{
		termFlag = 1;
	}
	else if (SM_SESSION_UPDATE_CODE == bizMsg->m_requestType && 2 == bizMsg->m_eptType) // bizMsg->m_eptType为2表示是从批价返回的ept
	{
		updateFlag = 1;
	}

	UDBSQL* pQuery2 = bizMsg->m_dbm->GetSQL(_5G_SelectSessionStoreRbansRg);

	STCodeOfr stCodeOfr;
	long long ratinggroup = 0;
	if (updateFlag == 1)
	{
		try
		{
			pQuery2->DivTable(bizMsg->m_sessionID);
			pQuery2->UnBindParam();
			pQuery2->BindParam(1, bizMsg->m_sessionID);
			pQuery2->Execute();
			while (pQuery2->Next())
			{
				memset(&stCodeOfr,0,sizeof(stCodeOfr));
				pQuery2->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "nSessionStatus[%d]", stCodeOfr.nSessionStatus);

				pQuery2->GetValue(2, value);//OCP_INT_RATING_GROUP
				ratinggroup = atol(value);
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "rating group[%s]", value);

				if (stCodeOfr.nSessionStatus != RECVRB_ACTION)
				{
					continue;
				}

				pQuery2->GetValue(3, value);
				stCodeOfr.resultcode = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "rg result code[%s]", value);

				if (ratinggroup == bizMsg->m_ratingGroup)
				{
					stCodeOfr.resultcode = resultcode;
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "rg[%ld] result code[%d]", ratinggroup, stCodeOfr.resultcode);
				}

				pQuery2->GetValue(4, stCodeOfr.gUnit);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "gUnit[%d]", stCodeOfr.gUnit);

				pQuery2->GetValue(5, stCodeOfr.gTotal);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "gTotal[%ld]", stCodeOfr.gTotal);

				pQuery2->GetValue(6, stCodeOfr.gTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "gTime[%ld]", stCodeOfr.gTime);

				pQuery2->GetValue(7, stCodeOfr.validityTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "validityTime[%d]", stCodeOfr.validityTime);

				pQuery2->GetValue(8, stCodeOfr.quotaHoldingTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "quotaHoldingTime[%d]", stCodeOfr.quotaHoldingTime);

				pQuery2->GetValue(9, stCodeOfr.quotaConsumeTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "quotaConsumeTime[%d]", stCodeOfr.quotaConsumeTime);

				pQuery2->GetValue(10, stCodeOfr.timeQuotaThreshold);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "timeQuotaThreshold[%d]", stCodeOfr.timeQuotaThreshold);

				pQuery2->GetValue(11, stCodeOfr.volumeQuotaThreshold);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "volumeQuotaThreshold[%d]", stCodeOfr.volumeQuotaThreshold);

				pQuery2->GetValue(12, stCodeOfr.volumeQuotaThreshold2);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "volumeQuotaThreshold2[%d]", stCodeOfr.volumeQuotaThreshold2);

				sprintf(value, "%lld", ratinggroup);
				map_rg.insert(map<string, STCodeOfr>::value_type(value, stCodeOfr));
			}
		}
		catch (UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "select execption[%s]", e.ToString());
		}
	}
	else
	{
		try
		{
			pQuery2->DivTable(bizMsg->m_sessionID);
			pQuery2->UnBindParam();
			pQuery2->BindParam(1, bizMsg->m_sessionID);
			pQuery2->Execute();
			while (pQuery2->Next())
			{
				memset(&stCodeOfr,0,sizeof(stCodeOfr));
				pQuery2->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "nSessionStatus[%d]", stCodeOfr.nSessionStatus);

				pQuery2->GetValue(2, value);//OCP_INT_RATING_GROUP
				ratinggroup = atol(value);
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "rating group[%s]", value);

				if(stCodeOfr.nSessionStatus != RECVRB_ACTION)
				{
					continue;
				}

				//result code
				pQuery2->GetValue(3, value); 
				stCodeOfr.resultcode = termFlag ? atoi(value) : resultcode;
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "rg result code[%d]", stCodeOfr.resultcode);

				sprintf(value, "%lld", ratinggroup);
				map_rg.insert(map<string, STCodeOfr>::value_type(value, stCodeOfr));
			}
		}
		catch (UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "select execption[%s]", e.ToString());
		}
	}

	std::map<string, STCodeOfr>::iterator itRG;
	if (termFlag)
	{
		for (itRG = map_rg.begin(); itRG != map_rg.end(); itRG++)
		{
			AUSU usu;
			usu.ratinggroup = atol((itRG->first).c_str());
			usu.ResultCode = itRG->second.resultcode ? itRG->second.resultcode : 2001;
			cca.MUU.push_back(usu);
		}
	}
	else if (updateFlag)
	{
		for (itRG = map_rg.begin(); itRG != map_rg.end(); itRG++)
		{
			AUSU gsu;
			gsu.ratinggroup = atol((itRG->first).c_str());
			gsu.ResultCode = itRG->second.resultcode ? itRG->second.resultcode : 2001;
			gsu.Unit = itRG->second.gUnit;
			if (RB_UNIT_CODE_SECOND == gsu.Unit)
			{
				gsu.CCTime = itRG->second.gTime;
			}
			else if (RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit)
			{
				gsu.Unittotal = itRG->second.gTotal;
			}
			else if (RB_UNIT_CODE_UP_BYTES == gsu.Unit)
			{
				gsu.Unitinput = itRG->second.gTotal;
			}
			else if (RB_UNIT_CODE_DOWN_BYTES == gsu.Unit)
			{
				gsu.Unitoutput = itRG->second.gTotal;
			}

			gsu.ValidityTime = itRG->second.validityTime;
			gsu.QuotaHoldingTime = itRG->second.quotaHoldingTime;
			gsu.QuotaConsumptionTime = itRG->second.quotaConsumeTime;
			gsu.TimeQuotaThreshold = itRG->second.timeQuotaThreshold;

			if ((itRG->second.volumeQuotaThreshold2 != 0) 
				&& (gsu.Unittotal <= itRG->second.volumeQuotaThreshold) 
				&& (RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit))//授权量<=授权量，并且是按流量计费的，取门限值2
			{
				gsu.VolumeQuotaThreshold = itRG->second.volumeQuotaThreshold2;
			}
			else
			{
				gsu.VolumeQuotaThreshold = itRG->second.volumeQuotaThreshold;
			}

			cca.MUU.push_back(gsu);
		}
	}
	else
	{
		AUSU usu;
		usu.ratinggroup = ratinggroup;
		usu.ResultCode = resultcode;
		cca.MUU.push_back(usu);
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);

		m_print.clear();
		m_print.print(uhd);
		m_print.print(cca);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CCA[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	int topicnum = 0;
	string strCCA = HexEncode(m_en.data(),m_en.size());
	m_pMsendMsg->insert(pair<string,string>("CCAMSG", strCCA));

    char monGroup[50] = {0};
    DCKpiMon *m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "DCM", "SM");
    DCKpiSender::instance()->GetFullGroup("TraceKpi", bizMsg->m_ilatnId, monGroup);
    DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon,monGroup, "", "SMOnA", NULL, 1); // 接收CHFProxy的online消息量

	return RET_SUCCESS;
}


int DCBizEpt::SendErrorResult(STBizMsg* bizMsg)
{
	if((DATA_5G == bizMsg->m_serviceContextID))
		return SendErrorResult5G(bizMsg);
		
	int ret 		              = 0;
	int MSCCCount                 = 0;
	int resultcode 	              = 0;
	char value[BIZ_TEMP_LEN_1024] = {0};
	char ProductOfferId[BIZ_TEMP_LEN_128] = {0};
	int termFlag 		          = 0;
	int updateFlag				  = 0;
	int aocType			          = 0;
	int resultcode2 	          = 0;
	char rg[BIZ_TEMP_LEN_64]	  = {0};
	DCAnsPara* anspara = (DCAnsPara *)bizMsg->m_anspara;

	if(SM_SESSION_XDR_CODE != bizMsg->m_requestType)
	{
		resultcode = anspara->GetOCPResultCode(bizMsg->m_resultcode);
	}
	else
	{
		resultcode = bizMsg->m_resultcode;
	}
	resultcode2 = bizMsg->m_resultcode;
	if(resultcode == -1)
	{
		resultcode = 3004;
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Useless result code [%d]", bizMsg->m_resultcode);
	}
	 DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCP Result-Code[%d]", resultcode);

	//请求类型
	if(bizMsg->m_requestType > SM_SESSION_TERMINATION_CODE && bizMsg->m_version != 1)
	{
		if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
		{
			bizMsg->m_requestType = SM_SESSION_UPDATE_CODE;
		}
		else
		{
			bizMsg->m_requestType = SM_SESSION_EVENT_CODE;
		}
	}
	
	ocs::UHead uhd;
	ocs::SCCAMsg cca;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	cca.sessionID   = bizMsg->m_sessionID;
	if(bizMsg->m_version == 1)
		cca.sessionID.erase(0,3);
	cca.requestType = bizMsg->m_requestType;
	cca.requestNumber = bizMsg->m_requestNumber;
	cca.serial = bizMsg->m_serial;
	cca.resultCode = 2001;
	cca.ServiceContextID = bizMsg->m_serviceContextID;
	cca.trace = DCLOG_GETCTL(DCLOG_MASK_TRACE);
	
	if((DATA==bizMsg->m_serviceContextID)||(CCG == bizMsg->m_serviceContextID)||(PGW == bizMsg->m_serviceContextID))
	{
		if(1 == bizMsg->m_eptType && SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
		{
			cca.msgType = SM_OCP_MSG_ID_DATA_TERM_ANS;
		}
		else if(2 == bizMsg->m_eptType && SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType)
		{
			cca.msgType = SM_OCP_MSG_ID_DATA_TERM_ANS;
		}
		else if(2 == bizMsg->m_eptType && SM_SESSION_UPDATE_CODE == bizMsg->m_requestType)
		{
			cca.msgType = SM_OCP_MSG_ID_DATA_UPDATE_ANS;
		}
		else if(2 == bizMsg->m_eptType && SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
		{
			cca.msgType = SM_OCP_MSG_ID_DATA_INIT_ANS;
		}
		else
		{
			cca.msgType = SM_OCP_ERR_MSG_ID;
			cca.resultCode = resultcode;
		}

	}
	else
	{
		cca.msgType = SM_OCP_ERR_MSG_ID;
		cca.resultCode = resultcode;
	}
	
	map<string, STCodeOfr> map_rg;	
	map<string, STCodeOfr>::iterator iter;
	if(SM_SESSION_TERMINATION_CODE==bizMsg->m_requestType && ((DATA==bizMsg->m_serviceContextID)||(CCG == bizMsg->m_serviceContextID)||(PGW == bizMsg->m_serviceContextID)))
	{
		termFlag = 1;
	}
	else if(SM_SESSION_UPDATE_CODE==bizMsg->m_requestType && PGW == bizMsg->m_serviceContextID && 2 == bizMsg->m_eptType)
	{
		updateFlag = 1;
	}
		
	UDBSQL *pQuery2  = NULL;
	if(DATA==bizMsg->m_serviceContextID || CCG==bizMsg->m_serviceContextID)
	{		
		pQuery2 =  bizMsg->m_dbm->GetSQL(DATA_SelectSessionStoreRbansRgTerm);
	}
	else if(PGW==bizMsg->m_serviceContextID && SM_SESSION_UPDATE_CODE==bizMsg->m_requestType)
	{
		pQuery2 = bizMsg->m_dbm->GetSQL(PGW_SelectSessionStoreRbansRgUpdate);
	}
	else if(PGW==bizMsg->m_serviceContextID)
	{
		pQuery2 =  bizMsg->m_dbm->GetSQL(PGW_SelectSessionStoreRbansRgTerm);		
	}

	STCodeOfr stCodeOfr;		
	long long ratinggroup = 0;
	int result = 0;	
	char tmp[1024] = {0};
	if(PGW == bizMsg->m_serviceContextID && updateFlag == 1)
	{
		try
		{
			pQuery2->DivTable(bizMsg->m_sessionID);
			pQuery2->UnBindParam();
			sprintf(tmp, "%s;%%", bizMsg->m_sessionID);
			pQuery2->BindParam(1, tmp);
			pQuery2->Execute();
			while(pQuery2->Next())
			{
				memset(&stCodeOfr,0,sizeof(stCodeOfr));
				pQuery2->GetValue(2, value);//OCP_INT_RATING_GROUP
				ratinggroup = atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rating group[%s]", value);
				
				pQuery2->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSessionStatus[%d]", stCodeOfr.nSessionStatus);
				
				if(stCodeOfr.nSessionStatus == TORB_ACTION)
				{
					//return RET_SUCCESS;
				}
				else if(stCodeOfr.nSessionStatus != RECVRB_ACTION)
				{
					continue;
				}

				MSCCCount++;
				
				//OCP_INT_RATING_GROUP
				if(-1 == ratinggroup)
				{
					stCodeOfr.nHaveRgFlag = 0;
				}
				else
				{
					stCodeOfr.nHaveRgFlag = 1;
				}
				
				pQuery2->GetValue(3, value);
				result = atoi(value);
				stCodeOfr.resultcode = result ? result : resultcode;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg result code[%s]", value);

				pQuery2->GetValue(4, value);
				strncpy(stCodeOfr.ofrid,value,sizeof(stCodeOfr.ofrid));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "product_ofr_id[%s]", stCodeOfr.ofrid);

				pQuery2->GetValue(5, stCodeOfr.gUnit);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gUnit[%d]", stCodeOfr.gUnit);

				pQuery2->GetValue(6, stCodeOfr.gTotal);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gTotal[%ld]", stCodeOfr.gTotal);

				pQuery2->GetValue(7, stCodeOfr.gTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "gTime[%ld]", stCodeOfr.gTime);

				pQuery2->GetValue(8, stCodeOfr.validityTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "validityTime[%d]", stCodeOfr.validityTime);

				pQuery2->GetValue(9, stCodeOfr.quotaHoldingTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quotaHoldingTime[%d]", stCodeOfr.quotaHoldingTime);

				pQuery2->GetValue(10, stCodeOfr.quotaConsumeTime);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "quotaConsumeTime[%d]", stCodeOfr.quotaConsumeTime);

				pQuery2->GetValue(11, stCodeOfr.timeQuotaThreshold);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "timeQuotaThreshold[%d]", stCodeOfr.timeQuotaThreshold);

				pQuery2->GetValue(12, stCodeOfr.volumeQuotaThreshold);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold[%d]", stCodeOfr.volumeQuotaThreshold);

				pQuery2->GetValue(13, stCodeOfr.volumeQuotaThreshold2);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "volumeQuotaThreshold2[%d]", stCodeOfr.volumeQuotaThreshold2);
				
				if(stCodeOfr.nHaveRgFlag)//有RG
				{
					
					sprintf(value, "%lld", ratinggroup);
					map_rg.insert(map<string, STCodeOfr>::value_type(value, stCodeOfr));
				
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "map_rg insert<%ld,%d|%s>", ratinggroup,stCodeOfr.resultcode,stCodeOfr.ofrid);
				}
				else//无RG || RG  与Product-Offer-Id 同时存在
				{
				
					map_rg.insert(map<string, STCodeOfr>::value_type(stCodeOfr.ofrid, stCodeOfr));					
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "map_rg insert<%s,%d|%s>", stCodeOfr.ofrid,stCodeOfr.resultcode,stCodeOfr.ofrid);
				}
			}
		}
		catch(UDBException& e)	
		{		
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());		
			//return SM_OCP_UNABLE_TO_COMPLY;	
		}
	}
	else if ( DATA==bizMsg->m_serviceContextID || CCG == bizMsg->m_serviceContextID || PGW == bizMsg->m_serviceContextID )
	{
		try
		{
			pQuery2->DivTable(bizMsg->m_sessionID);
			pQuery2->UnBindParam();
			if (SM_SESSION_TERMINATION_CODE == bizMsg->m_requestType )
			{
				sprintf(tmp, "%s;%%", bizMsg->m_sessionID);
				pQuery2->BindParam(1, tmp);
			}
			else
			{
				pQuery2->BindParam(1, bizMsg->m_childsessionID);
			}
			pQuery2->Execute();
			while(pQuery2->Next())
			{
				memset(&stCodeOfr,0,sizeof(stCodeOfr));
				
				//rg
				pQuery2->GetValue(2, value);//OCP_INT_RATING_GROUP
				ratinggroup=atol(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rating group[%s]", value);

				pQuery2->GetValue(1, stCodeOfr.nSessionStatus);//SM_INT_SESSION_STATUS
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "nSessionStatus[%d]", stCodeOfr.nSessionStatus);

				if(stCodeOfr.nSessionStatus == TORB_ACTION)
				{
					//return RET_SUCCESS;
				}
				else if(stCodeOfr.nSessionStatus == STATUS_IDLE && termFlag != 1)
				{
					//need return cca
				}
				else if(stCodeOfr.nSessionStatus != RECVRB_ACTION)
				{
					continue;
				}
				
				MSCCCount++;
				
				if(-1 == ratinggroup)
				{
					stCodeOfr.nHaveRgFlag = 0;
				}
				else
				{
					stCodeOfr.nHaveRgFlag = 1;
				}
				
				//result code
				pQuery2->GetValue(3, value);
				result = atoi(value);
				stCodeOfr.resultcode = termFlag ? result : resultcode;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rg result code[%d]", stCodeOfr.resultcode);
				
				//ofrid
				pQuery2->GetValue(4, ProductOfferId);//OCP_STR_PRODUCT_OFFER_ID
				strcpy(stCodeOfr.ofrid,ProductOfferId);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "","ofr id[%s]", stCodeOfr.ofrid);

				if(stCodeOfr.nHaveRgFlag)//有RG
				{
					sprintf(value, "%lld", ratinggroup);
					map_rg.insert(map<string, STCodeOfr>::value_type(value, stCodeOfr));
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "map_rg insert<%ld,%d|%s>", ratinggroup,stCodeOfr.resultcode,stCodeOfr.ofrid);
				}
				else//无RG || RG  与Product-Offer-Id 同时存在
				{
					map_rg.insert(map<string, STCodeOfr>::value_type(stCodeOfr.ofrid, stCodeOfr));					
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "map_rg insert<%s,%d|%s>", stCodeOfr.ofrid,stCodeOfr.resultcode,stCodeOfr.ofrid);
				}
			}	
		}
		catch(UDBException& e)	
		{		
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());		
			//return SM_OCP_UNABLE_TO_COMPLY;	
		}
	}
	
	if((DATA==bizMsg->m_serviceContextID)||(CCG == bizMsg->m_serviceContextID)||(PGW == bizMsg->m_serviceContextID))
	{
		if(termFlag)
		{	
			for(iter = map_rg.begin();iter!=map_rg.end();iter++)
			{
				DATAUSU usu;
				if(1 == iter->second.nHaveRgFlag)
				{
					usu.ratinggroup = atol((iter->first).c_str());
				}
				else 
				{
					usu.ProductOfferId = iter->second.ofrid;
				}
				usu.ResultCode = iter->second.resultcode ? iter->second.resultcode : 2001;
				
				cca.MSCC.push_back(usu);
			}
		}
		else if(updateFlag)
		{
			for(iter = map_rg.begin();iter!=map_rg.end();iter++)
			{
				DATAUSU gsu;
				if(1 == iter->second.nHaveRgFlag)
				{
					gsu.ratinggroup = atol((iter->first).c_str());
				}
				else 
				{
					gsu.ProductOfferId = iter->second.ofrid;
				}
				gsu.ResultCode = iter->second.resultcode ? iter->second.resultcode : 2001;

				gsu.Unit		   = iter->second.gUnit;

				if(RB_UNIT_CODE_SECOND == gsu.Unit)
				{
					gsu.CCTime = iter->second.gTime;
				}
				else if(RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit)
				{
					gsu.Unittotal = iter->second.gTotal;	
				}
				else if(RB_UNIT_CODE_UP_BYTES == gsu.Unit)
				{
					gsu.Unitinput = iter->second.gTotal;		
				}
				else if(RB_UNIT_CODE_DOWN_BYTES == gsu.Unit)
				{
					gsu.Unitoutput = iter->second.gTotal;			
				}

				gsu.ValidityTime = iter->second.validityTime;
				gsu.QuotaHoldingTime = iter->second.quotaHoldingTime;
				gsu.QuotaConsumptionTime = iter->second.quotaConsumeTime;
				gsu.TimeQuotaThreshold = iter->second.timeQuotaThreshold;
				
				if((iter->second.volumeQuotaThreshold2 != 0) && (gsu.Unittotal <= iter->second.volumeQuotaThreshold) && (RB_UNIT_CODE_TOTAL_BYTES == gsu.Unit))//授权量<=授权量，并且是按流量计费的，取门限值2
				{
					gsu.VolumeQuotaThreshold = iter->second.volumeQuotaThreshold2;
				}
				else
				{	
					gsu.VolumeQuotaThreshold = iter->second.volumeQuotaThreshold;
				}
				
				cca.MSCC.push_back(gsu);
			}
		}
		else
		{
			DATAUSU usu;
			if (strlen(ProductOfferId) > 0)
			{
				usu.ProductOfferId = ProductOfferId;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ProductOfferId[%s]", ProductOfferId);
			}
			else
			{
				usu.ratinggroup = ratinggroup;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ratinggroup[%lld]", ratinggroup);
			}
			usu.ResultCode = resultcode;		  
			cca.MSCC.push_back(usu);
		}
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(cca);

		m_print.clear();
		m_print.print(uhd);
		m_print.print(cca);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CCA[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	
	int topicnum = 0;
	string str = HexEncode(m_en.data(),m_en.size());
	m_pMsendMsg->insert(pair<string,string>("CCAMSG",str));

	UDBSQL *pQuery	= NULL;	
	UDBSQL *pExec	= NULL;
	DCDBManer *dbm = bizMsg->m_dbm;
	long subNbr	= 0;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				if((1 == bizMsg->m_requestType) && (-40200 == resultcode2))
				{
					long calltime = 0;
					long callingNbr = 0;
					
					pQuery = dbm->GetSQL(Voice_GetSessionInfo_Trace);	
					try
					{
						pQuery->DivTable(bizMsg->m_sessionID);
						pQuery->UnBindParam();
						pQuery->BindParam(1, bizMsg->m_sessionID);
						pQuery->Execute();
						if(pQuery->Next())
						{
							pQuery->GetValue(4, value);
							subNbr = atol(value);
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "get subscription number[%s]", value);
							pQuery->GetValue(14, value);
							calltime = atol(value);
							pQuery->GetValue(15, value);
							callingNbr = atol(value);
						}
				
						
						pExec = dbm->GetSQL(Reject_Calls_Insert);	
						pExec->DivTable(bizMsg->m_sessionID);
						pExec->UnBindParam();
						pExec->BindParam(1, subNbr);
						pExec->BindParam(2, callingNbr);
						pExec->BindParam(3, calltime);
						pExec->BindParam(4, 0);
						pExec->Execute();
						pExec->Connection()->Commit();				
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "pInsert_tb_reject_calls ok, subNbr[%ld], callingnbr[%ld], calltime[%ld]", subNbr, callingNbr, calltime);

					}
					catch(UDBException& e)
					{
						pExec->Connection()->Rollback();
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update execption[%s]", e.ToString());
						//return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
			}
			break;
		case SMS:
			{				
				pQuery = dbm->GetSQL(SMS_GetSessionInfo_Trace);		
				try
				{	
					pQuery->DivTable(bizMsg->m_sessionID);
					pQuery->UnBindParam();
					pQuery->BindParam(1, bizMsg->m_sessionID);
					pQuery->Execute();
					if(pQuery->Next())
					{
						pQuery->GetValue(4, value);
						subNbr = atol(value);				
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "get subscription number[%s]", value);

						//无线上网卡套餐AOC提醒
						strcpy(bizMsg->m_subNumber, value);
						pQuery->GetValue(10, value);
						aocType = atoi(value);
						if((aocType >= 8)
							&&(-30222 == resultcode2
								||-30232 == resultcode2))				//余额不足，判定AOCTYPE
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_P2PSMS_TYPE, bizMsg->m_sessionID, "send sms aoc", "");						
							return RET_AOC;
						}
					}
				}
				catch(UDBException& e)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
					//return SM_OCP_UNABLE_TO_COMPLY;
				}
			}
			break;
		case PGW:
			{
				if(updateFlag == 1)
				{
					// update会话2002错误码(Commit失败)3次重试
					int retryCount = 0;
					bool success = false;
					while (!success && retryCount < 3)
					{
						// 更新子会话状态
						pExec = dbm->GetSQL(PGW_Update_SESSION_RBANS_STATUS);
						try
						{
							pExec->DivTable(bizMsg->m_sessionID);
							pExec->UnBindParam();
							sprintf(value, "%s;%%", bizMsg->m_sessionID);
							DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update status to idle RG[%s]", value);
							pExec->BindParam(1, STATUS_IDLE);
							pExec->BindParam(2, value);
							pExec->BindParam(3, RECVRB_ACTION);
							pExec->Execute();
							pExec->Connection()->Commit();
							success = true;
						}
						catch (UDBException &e)
						{
							// std::string sql;
							pExec->Connection()->Rollback();
							// pExec->GetSqlString(sql);
							// DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "SQL[%s]", sql.c_str());

							DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "UpdateSessionRBANSStatus execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
							if (e.GetErrorCode() == 2002) // Commit失败
							{
								retryCount++;
								if (retryCount < 3)
								{
									DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
									continue;
								}
								else
								{
									DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
									//return RB_SM_UNABLE_TO_COMPLY;
								}
							}
							// return RB_SM_UNABLE_TO_COMPLY;
							break;
						}
					}
				}
			}
			break;
		default:
			break;
	}

	return RET_SUCCESS;
}

int DCBizEpt::SendReaTermRER(STBizMsg* bizMsg)//仅供数据业务使用
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "", "%d",bizMsg->m_serviceContextID);
	
	int ret 							= RET_SUCCESS;
	long nextCCTime						= 0;
	char ratingGroup[BIZ_TEMP_LEN_64]	= {0};
	char ofrid[128]						={0};
	char rg[BIZ_TEMP_LEN_64]			= {0};
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char buf[BIZ_TEMP_LEN_1024] 		= {0};
	char tmp[BIZ_TEMP_LEN_256]			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	SCCRDataUnit USU;
	long curData = 0;
	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;
	sprintf(tmp, "%lld", curData);
	
	UDBSQL* pQuery = NULL;
	if(bizMsg->m_serviceContextID == CCG || bizMsg->m_serviceContextID == DATA)
	{
		pQuery = bizMsg->m_dbm->GetSQL(DATA_SelectSessionStoreRecord_002);
	}
	else
		pQuery = bizMsg->m_dbm->GetSQL(PGW_CCR_FirstUpdate_SelectSession_main);
	sprintf(value, "%s;%%", bizMsg->m_sessionID);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, value);
		pQuery->Execute();	
		while(pQuery->Next())
		{
			//组装RER消息
			UHead uhd;
			rbhead head;
			rbdomain domain;
			rbdata rbr;
			rsu_t rsu;
			usu   u;
			debit totalusu;
			rbext ext;

			uhd.uid = bizMsg->m_uid;
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_DATA_REQ;
			head.version= 2;
			head.sreq  = 3;

			//current time	
			head.stamp = atol(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NOW TIME [%s]", tmp);
			
			
			head.serial = bizMsg->m_serial;
			head.result = 0;
			head.topology = bizMsg->m_topology;

			//prod_inst_id
			pQuery->GetValue(45, value);
			head.prodinstid = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "prod_inst_id[%s]", value);
			
			pQuery->GetValue(59, value);
			strncpy(ratingGroup, value, sizeof(ratingGroup));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RatingGroup[%s]",ratingGroup);

			pQuery->GetValue(62, value);
			head.session = value;
			if(bizMsg->m_version == 1)
				head.session.erase(0,3);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionID[%s]", value);

			pQuery->GetValue(1, value);
			if(0 == bizMsg->m_trace_flag)bizMsg->m_trace_flag=atoi(value);	
			head.trace =  bizMsg->m_trace_flag;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "traceNumber [%s]", value);

			//R71	会话开始时间
			pQuery->GetValue(2, value);
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sess_start_time[%s]", value);
			
			//R85	用户付费属性标识
			rbr.pay=1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");
			
			pQuery->GetValue(3, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_SM_INT_SESSION_STATUS[%s]",value); 
					
			//R01	付费号码
			pQuery->GetValue(10, value);
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_nbr[%s]", value);
			
			//R504 主叫号码归属费率区
			pQuery->GetValue(11, value);
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_harea[%s]", value);
			
			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);	
			rbr.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", value);
			
			//R5011 漫游类型
			pQuery->GetValue(12, value);
			rbr.roam_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%s]", value);
			
			//R505 主叫号码拜访费率区
			pQuery->GetValue(13, value);
			rbr.calling_varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_varea[%s]", value);
			
			pQuery->GetValue(14, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_RE_INT_PAY_FLAG[%s]",value);
			
			
			//R603	会话上次扣费开始时间
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				rbr.pre_dtime = 0;
			}
			else
			{
				pQuery->GetValue(3, value);
				rbr.pre_dtime = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pre_dtime[%lld]", rbr.pre_dtime);
			
			//R604	本次计费请求开始时间
			pQuery->GetValue(3, value);
			rbr.cur_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose cur_dtime[%s]", value);
			
			//R605	是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");
			
			//R606 激活用户
			pQuery->GetValue(15, value);
			rbr.active_flag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose active_flag[%s]", value);
			
			//R301 SGSN地址
			pQuery->GetValue(17, value);			
			rbr.sgsn_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sgsn_addr[%s]", value);
			
			//R302 GGSN地址
			pQuery->GetValue(18, value);			
			rbr.ggsn_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ggsn_addr[%s]", value);
			
			//R305 APN网络标识
			pQuery->GetValue(19, value);
			rbr.apn_id = value;		
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose apn_info[%s]", value);
			
			//R306 PDP类型
			pQuery->GetValue(20, value);
			rbr.pdp_type = value;	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_type[%s]", value);
			
			//R307 被服务方的 PDP地址
			pQuery->GetValue(21, value);
			rbr.pdp_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_addr[%s]", value);
			
			//R309 授权有效时间
			pQuery->GetValue(5, value);
			rbr.valid_time = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose valid_time[%s]", value);
			
			//R3010 QoS  
			pQuery->GetValue(23, value);
			rbr.qos = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos[%s]", value);
			
			//R3012 用户位置信息
			pQuery->GetValue(24, value);
			rbr.location = value; 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location[%s]", value);
			
			//R3013 RAT TYPE
			pQuery->GetValue(25, value);
			rbr.rat_type = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", value);
			
			//R3015 CELLID
			pQuery->GetValue(27, value);
			rbr.cell = value;			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", value);
			
			// R1012 MSC
			pQuery->GetValue(28, value);
			rbr.msc = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", value);

			// 
			pQuery->GetValue(23, value);
			USU.duration= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", value);
			
			pQuery->GetValue(34, value);
			USU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitTotal);

			pQuery->GetValue(35, value);
			USU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%ld]", USU.unitInput);

			pQuery->GetValue(36, value);
			USU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%ld]", USU.unitOutput);

			//R3016
			pQuery->GetValue(41, value);
			rbr.charging_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charging_id[%s]", value);

			pQuery->GetValue(57, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose latn_id[%s]", value);

			pQuery->GetValue(61, value);//OCP_STR_PRODUCT_OFFER_ID
			strncpy(ofrid,value,strlen(value));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get ofr id[%s]", value);

			pQuery->GetValue(63, value);//OCP_STR_PRODUCT_OFFER_ID
			ext.kv["strID"] = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "str context id[%s]", value);
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["taskId"] = bizMsg->m_taskId;
			
			rbr.accumlator_info = 1;
			rbr.tariff_info     = 1;
			rbr.balance_info    = 1;
			rbr.rating_info     = 1;
			rbr.balance_query   = 1;

                     // CCG 业务LTE_FLAG = 2
                     if(bizMsg->m_serviceContextID == CCG)
                     {
                            rbr.lte_flag = 2;
                     }

			//B30更新使用量
			//时长33
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_SECOND;
			u.amount = 0;
			domain.usv.push_back(u);

			//总流量34
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_TOTAL_BYTES;
			u.amount = 0;	
			domain.usv.push_back(u);
			
			//上行流量35
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_UP_BYTES;
			u.amount = 0;	
			domain.usv.push_back(u);
			
			//下行流量36
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_DOWN_BYTES;
			u.amount = 0;	
			domain.usv.push_back(u);

			
			//B03总使用时长
			pQuery->GetValue(6, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_SECOND;
			totalusu.amount = atol(value);


			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用总流量
			pQuery->GetValue(7, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_TOTAL_BYTES;
			totalusu.amount = atol(value);


			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用上行流量
			pQuery->GetValue(8, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_UP_BYTES;
			totalusu.amount = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "up bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用下行流量
			pQuery->GetValue(9, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_DOWN_BYTES;
			totalusu.amount = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "down bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);

			try
			{
				m_en.clear();
				m_en.encode(uhd);
				m_en.encode(head);
				m_en.encode(domain);
				m_en.encode(rbr);
				m_en.encode(ext);

				//打印head消息
				m_print.clear();
				m_print.print(uhd);
				m_print.print(head);	
				m_print.print(domain);	
				m_print.print(rbr);
				m_print.print(ext);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RBR[%s]", m_print.data());
			}
			catch(exception& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
				return ERR_ENCODE_CODE;
			}

			bizMsg->data = (char *)m_en.data();
			bizMsg->m_vectorMsg.push_back(bizMsg->data);		
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	
	//更新会话表字段
	UpdateSession(bizMsg);
	return RET_SUCCESS;
}

//
int DCBizEpt::SendTermRER(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "", "%d",bizMsg->m_serviceContextID);
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				RERVOICE(bizMsg);
			}
			break;
		case SMS:
			{
			}
			break;
		case DATA:
		case CCG:
			{
				RERDATA(bizMsg);
			}
			break;
		case PGW:
			{
				RERPGW(bizMsg);
			}
			break;
		case DATA_5G:
			{
				RER5G(bizMsg);
			}
			break;
		case ISMP:
		case HRS:
			{
				RERISMP(bizMsg);
			}
			break;
		case DSL:
			{
				RERDSL(bizMsg);
			}
			break;
	}

	return RET_SUCCESS;
}

int DCBizEpt::RERVOICE(STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	int lastReqType						= 0;

	unsigned int RERcallType				= 0;
	unsigned int earea						= 0;
	int TUSU								= 0;
	int usedTUSU							= 0;
	int longCDRflag                         = 0;
	long usutime = 0;
	char tmp[BIZ_TEMP_LEN_256]			= {0};
	char value[BIZ_TEMP_LEN_256] 			= {0};
	char buf[BIZ_TEMP_LEN_1024]			= {0};
	char szHostId[32]                   = {0};

	//组装RER消息
	UHead uhd;
	rbhead head;
	rbdomain domain;
	rbvoice rbr;
	rsu_t rsu;
	usu   u;
	debit totalusu;
	rbext ext;

	long curData = 0;
	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;
	sprintf(tmp, "%lld", curData);
	
	UDBSQL* pQuery = NULL;	
	try
	{
		pQuery = bizMsg->m_dbm->GetSQL(Voice_GetSessionInfo);
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();			
		if(pQuery->Next())
		{	
			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_VOICE_REQ;
			head.version= 2;
			head.sreq = 3;
			//current time	
			head.stamp = atol(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NOW TIME [%s]", tmp);

			head.session = bizMsg->m_sessionID;
			if(bizMsg->m_version == 1)
				head.session.erase(0,3);
			head.serial = bizMsg->m_serial;
			head.trace =  bizMsg->m_trace_flag;
			uhd.trace = bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = bizMsg->m_topology;
			
			//prod_inst_id
			pQuery->GetValue(69, value); 
			head.prodinstid = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose prod_inst_id[%s]", value);
			
			//R65   服务代码
			pQuery->GetValue(31, value); 
            if(strcmp(value, "8") == 0)
			{
				strcpy(value, "7");
			}
		
			rbr.service_code = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose service_code[%s]", value);
			
			//R71	会话开始时间
			pQuery->GetValue(6, value); 
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose sess_start_time[%s]", value);

			//R75     被叫接入号码
			pQuery->GetValue(22, value); 
			rbr.access_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose access_nbr[%s]", value);

			//R85	用户付费属性标识
			rbr.pay=1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE,  bizMsg->m_sessionID, "compose payflag[1]");
			
			//R01   付费号码 
			pQuery->GetValue(9, bizMsg->m_subNumber);
			rbr.charged_nbr = bizMsg->m_subNumber;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose charged_nbr[%s]",  bizMsg->m_subNumber);
			bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", bizMsg->m_subNumber));
			//R103  话单类型
			pQuery->GetValue(8, value);		
			RERcallType = atoi(value);
			rbr.call_type =  RERcallType;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calltype[%s]", value);
			
			//会话状态
			pQuery->GetValue(1, value);
			lastReqType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "last req type [%s]", value);

			//总使用时长
			pQuery->GetValue(4, value);
			TUSU = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "TUSU [%d]", TUSU);

			if(2 == RERcallType)
			{
				//R02   主叫号码 
				pQuery->GetValue(13, value);
				rbr.calling_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling_nbr[%s]", value);

				//R03   被叫号码 
				pQuery->GetValue(17, value);
				rbr.called_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called_nbr[%s]", value);
				
				//R504  主叫号码归属费率区
				//R505  主叫号码拜访区号
				pQuery->GetValue(14, value);
				if (86 != atoi(value))
				{
				}
				else
				{
					pQuery->GetValue(15, value); 
				}
				rbr.calling.harea = value;
				rbr.calling.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling harea[%s]", value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling varea[%s]", value);

				//R506  主叫号码归属运营商
				pQuery->GetValue(16, value); 
				rbr.calling.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling hcarrier[%s]", value);

				//R507  被叫号码归属费率区
				pQuery->GetValue(19, value);
				if (86 != atoi(value))
				{
				}
				else
				{
					pQuery->GetValue(20, value); 
				}
				rbr.called.harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called harea[%s]", value);

				//R508  被叫号码拜访费率区
				pQuery->GetValue(23, value); 
				rbr.called.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called varea[%s]", value);

				//R509  被叫号码归属运营商
				pQuery->GetValue(21, value);
				rbr.called.hcarrier= atoi(value);			
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called hcarrier[%s]", value);

				//R1013  被叫交换机
				pQuery->GetValue(38, value);
				rbr.called.msc =  value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called msc[%s]", value);

			}

			//呼转流程
			else if((3 == RERcallType) || (4 == RERcallType))
			{
				//R02   主叫号码
				pQuery->GetValue(9, value);
				rbr.calling_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling_nbr[%s]", value);

				//R03   被叫号码 
				pQuery->GetValue(33, value);
				rbr.called_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called_nbr[%s]", value);

				//R105  连接号码
				pQuery->GetValue(13, value);
				rbr.connect_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose collect_nbr[%s]", value);

				//R1012  主叫交换机
				pQuery->GetValue(37, value);
				rbr.calling.msc = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling msc[%s]", value);

				//R504  主叫号码归属费率区
				pQuery->GetValue(11, value);
				rbr.calling.harea =  value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling harea[%s]", value);

				//R505  主叫号码拜访费率区
				pQuery->GetValue(23, value);
				rbr.calling.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling visit area[%s]", value);

				//R506  主叫号码归属运营商
				pQuery->GetValue(12, value);
				rbr.calling.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "calling hcarrier[%s]", value);

				//R507  被叫号码归属费率区
				//R508  被叫号码拜访费率区
				pQuery->GetValue(34, value);
				if (86 != atoi(value))
				{
				}
				else
				{
					pQuery->GetValue(35, value); 
				}			
				rbr.called.harea = value;
				rbr.called.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called harea[%s]", value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called.varea[%s]", value);

				//R509  被叫号码归属运营商
				pQuery->GetValue(36, value);
				rbr.called.hcarrier = atoi(value); 
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose %s[%s]", value);

				//R5013 连接号码归属区
				pQuery->GetValue(14, value);
				if (86 != atoi(value))
				{
				
				}
				else
				{
					pQuery->GetValue(15, value);
				}
				rbr.connect_harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose connect harea[%s]", value);

				//R5014  连接号码拜访区
				rbr.connect_varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose connect varea[%s]", value);

			}
			else
			{
				//R02   主叫号码 
				pQuery->GetValue(13, value);		
				rbr.calling_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling_nbr[%s]", value);

				//R03   被叫号码 
				pQuery->GetValue(17, value);
				rbr.called_nbr = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called_nbr[%s]", value);
				
				//R504  主叫号码归属费率区
				pQuery->GetValue(14, value);
				if (86 != atoi(value))
				{
				}
				else
				{
					pQuery->GetValue(15, value); 
				}
				rbr.calling.harea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling harea[%s]", value);

				//R505  主叫号码拜访区号
				pQuery->GetValue(23, value); 
				rbr.calling.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling varea[%s]", value);

				//R506  主叫号码归属运营商
				pQuery->GetValue(16, value); 
				rbr.calling.hcarrier = atoi(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling hcarrier[%s]", value);

				//R507  被叫号码归属费率区
				//R508  被叫号码拜访费率区
				pQuery->GetValue(19, value);
				if (86 != atoi(value))
				{
				}
				else
				{
					pQuery->GetValue(20, value); 
				}
				rbr.called.harea = value;
				rbr.called.varea = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called harea[%s]", value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called.varea[%s]", value);

				//R509  被叫号码归属运营商
				pQuery->GetValue(21, value);
				rbr.called.hcarrier = atoi(value); 
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called hcarrier[%s]", value);

				//R1012  主叫交换机
				pQuery->GetValue(37, value);
				rbr.calling.msc = value;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling msc[%s]", value);

			}

					
			//R5010 长途类型
			pQuery->GetValue(24, value);
			rbr.long_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose long_type[%s]", value);

			//R5011 漫游类型
			pQuery->GetValue(25, value);
			rbr.roam_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose roam_type[%s]", value);

			//R5015  拜访地运营商
			pQuery->GetValue(64, value);
			rbr.charged_vcarrier = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose charged_vcarrier[%s]", value);

			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);
			rbr.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose charged harea[%s]", value);


			//R602 计费类型
			rbr.sreq= SM_SESSION_TERMINATION_CODE;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose charge type[%s]", SM_SESSION_TERMINATION_STRING);

			//R603  会话上次扣费开始时间
			pQuery->GetValue(7, value);
			rbr.pre_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose pre_dtime[%s]", value);

			//R604  本次计费请求开始时间
			rbr.cur_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose cur_dtime[%s]", value);
		
			//R605  是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose ratable_flag[%s]", "0");

			//R615 小区优惠标识
			if(bizMsg->m_anspara->GetINPara()->favCellIDSwitch)
			{
				rbr.ratable_flag =1;
			}
			else
			{
				rbr.ratable_flag =0;
			}

			//R106  主叫小区(或基站) 
			pQuery->GetValue(26, value);
			rbr.calling.cell = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling cell[%s]", value);

			//R107  被叫小区(或基站)
			pQuery->GetValue(27, value);
			rbr.called.cell = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called cell[%s]", value);

			//R108  主叫用户位置区
			pQuery->GetValue(37, value);
			rbr.calling.lai = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose calling lai[%s]", value);

			//R109  被叫用户位置区
			pQuery->GetValue(38, value);
			rbr.called.lai = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called lai[%s]", value);

			//R1116 短号处理
			pQuery->GetValue(18, value);
			rbr.called_short_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose called_short_nbr[%s]", value);

			//标识是否是超长话单
			pQuery->GetValue(39, value);
			longCDRflag= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "long CDR flag[%s]", value);

			// CDR_PUB_STR_TARIFFID
			pQuery->GetValue(57, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "tarifInfo [%s]", value);

			// CDR_PUB_STR_CHARGEINFO
			pQuery->GetValue(58, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "szCdrChargeInfo [%s]", value);

			
			// CDR_PUB_STR_PRICING_PLAN_ID
			pQuery->GetValue(59, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "szCdrPlanId [%s]", value);

			// CDR_LNG_EVENT_TYPE_ID
			pQuery->GetValue(61, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "nEventTypeId [%s]", value);

			pQuery->GetValue(62, value);
			strcpy(szHostId, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "szHostId [%s]", value);

			pQuery->GetValue(41, value);
			usutime=atoi(value); // 本次使用时长OCP_LNG_USU_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "usedTUSU [%s]", value);


			pQuery->GetValue(65, value);
			usedTUSU=atoi(value);// CDR_LNG_ALL_USU_TIME
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "usedTUSU [%s]", value);


			
			pQuery->GetValue(68, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "latn_id [%d]", rbr.latn_id);

			pQuery->GetValue(70, value);
			ext.kv["strID"] = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "str context id [%s]", value);



            // VoLTE 新增字段
            //R501	
            pQuery->GetValue(73, value);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "ims_charging_identifier [%s]", value);
            if (strlen(value)>0)
        	{
        		ext.kv["R501"] = "3";
        	}
            
            //callaccesstype
            pQuery->GetValue(74, value);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "callaccesstype [%s]", value);
        	if (strlen(value))
        	{
        		ext.kv["R623"] = value;
        	}

             //RTI
            pQuery->GetValue(75, value);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "RTI [%s]", value);
        	if (strlen(value))
        	{
        		ext.kv["RTI"] = value;
        	}
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["taskId"] = bizMsg->m_taskId;            
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "", "query session failed [%d]", ret);
			return ret;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	if(bizMsg->m_resultcode == -30232)
	{
		TUSU+=usutime;
	}
	TUSU+=usedTUSU;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "TUSU druation[%d]", TUSU);

	/*
	if(longCDRflag)
	{//如果有超长话单，则需要把使用量累加
		TUSU+= usedTUSU;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "TUSU druation[%d]", TUSU);
	}*/


	if(bizMsg->m_resultcode == -30232)
	{
		//更新使用量
		u.unit = atoi(RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
		u.amount = usutime;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose usutime[%ld]",usutime);

		//B03总使用量
		totalusu.unit = atoi(RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose unit[%s]", RB_UNIT_STR_SECOND);
		totalusu.amount = TUSU;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose amount[%d]",TUSU);
		

	}
	else
	{
		//更新使用量
		u.unit = atoi(RB_UNIT_STR_SECOND);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose usu unit[%s]", RB_UNIT_STR_SECOND);
		u.amount = 0;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose usu amount[0]");

		//B03总使用量
		totalusu.unit = u.unit ;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose totalusu.unit[%s]", RB_UNIT_STR_SECOND);
		sprintf(value, "%d", TUSU);
		if(0 == strlen(value))//为空的填写0
		{
			totalusu.amount = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose totalusu.amount[0],empty");
		}
		else
		{
			totalusu.amount = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "compose totalusu.amount[%s]",value);
		}
	}
	domain.dbv.push_back(totalusu);
	domain.usv.push_back(u);
	rbr.accumlator_info =1;
	rbr.tariff_info = 1;
	rbr.balance_info = 1;
	rbr.rating_info =1;
	rbr.balance_query =1;

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);	
		m_print.print(domain);	
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	
	
    bizMsg->data = (char *)m_en.data();
	bizMsg->m_vectorMsg.push_back(bizMsg->data);

	//更新会话表字段
	UpdateSession(bizMsg);

	return ret;
}

int DCBizEpt::RERPGW(STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	long nextCCTime						= 0;
	char ratingGroup[BIZ_TEMP_LEN_64]	= {0};
	char ofrid[128]={0};
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char buf[BIZ_TEMP_LEN_2048] 			= {0};
	char tmp[BIZ_TEMP_LEN_256]			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	long curData = 0;
	char productofferID[BIZ_TEMP_LEN_64] ={0};
	char rg[BIZ_TEMP_LEN_64]			= {0};
	int count = 0;
	SCCRDataUnit USU;
	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;
	sprintf(tmp, "%lld", curData);


	int i=strlen(bizMsg->m_childsessionID)-1;
	for(; i>=0; i--)
	{
		if(';' == bizMsg->m_childsessionID[i])
		{
			i++;
			break;
		}
	}
	strncpy(rg, bizMsg->m_childsessionID+i, BIZ_TEMP_LEN_64);

	UDBSQL* pQuery = bizMsg->m_dbm->GetSQL(PGW_CCR_FirstUpdate_SelectSession_main);
	try
	{
        // 应答流程只处理当前RG 
        if(bizMsg->m_eptType == 2)
        {
              strcpy(value, bizMsg->m_childsessionID);
        }
        else
        {
              sprintf(value,"%s;%%",bizMsg->m_sessionID);
        }
              
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1,value);

		pQuery->Execute();
		while(pQuery->Next())
		{
			count++;
			//组装RER消息
			UHead uhd;
			rbhead head;
			rbdomain domain;
			rbdata rbr;
			rsu_t rsu;
			usu   u;
			debit totalusu;
			rbext ext;
			
			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_PGW_REQ;
			head.version= 2;
			head.sreq  = 3;

			//prod_inst_id
			pQuery->GetValue(59, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "prod_inst_id[%s]", value); 
	    	head.prodinstid = atol(value);
			
			//session ID
			pQuery->GetValue(57, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCP_STR_SESSION_ID[%s]", value); 
	    	head.session = value;
			if(bizMsg->m_version == 1)
				head.session.erase(0,3);
			strcpy(bizMsg->m_childsessionID,value);
			
			head.serial = bizMsg->m_serial;
			head.trace =  bizMsg->m_trace_flag;
			uhd.trace = bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = bizMsg->m_topology;
            size_t psep = head.topology.find(';');
            if(psep != std::string::npos)
            {
                   // 移除top名附加的字段
                   head.topology.erase(psep);
            }
                     
			//current time	
			head.stamp = atol(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NOW TIME [%s]", tmp);

			//R71	会话开始时间
			pQuery->GetValue(8, value);
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sess_start_time[%s]", value);
			
			//R85	用户付费属性标识
			rbr.pay=1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");
			
			pQuery->GetValue(3, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_SM_INT_SESSION_STATUS[%d]", value); 				

			//R01	付费号码
			pQuery->GetValue(10, value);
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_nbr[%s]", value);
			bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", value));
			//R504 主叫号码归属费率区
			pQuery->GetValue(11, value);
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_harea[%s]", value);
			
			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);	
			rbr.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", value);
			
			//R5011 漫游类型
			pQuery->GetValue(12, value);
			rbr.roam_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%s]", value);
			
			//R505 主叫号码拜访费率区
			pQuery->GetValue(13, value);
			rbr.calling_varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_varea[%s]", value);
			
			pQuery->GetValue(14, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_RE_INT_PAY_FLAG[%ld]", value);

			//rating group
			pQuery->GetValue(15, ratingGroup);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "OCP_INT_RATING_GROUP[%s]", ratingGroup);
			
			//R603	会话上次扣费开始时间
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				rbr.pre_dtime = 0;
			}
			else
			{
				pQuery->GetValue(9, value);
				rbr.pre_dtime = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pre_dtime[%ld]", rbr.pre_dtime);
			
			//R604	本次计费请求开始时间
			rbr.cur_dtime =  rbr.pre_dtime;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cur_dtime[%u]",bizMsg->timestampCCR);
			
			//R605	是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");
			
			//R606 激活用户
			//pQuery->GetValue(15, value);
			rbr.active_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose active_flag[%s]", value);
			
			//R301 SGSN地址
			pQuery->GetValue(17, value);			
			rbr.sgsn_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sgsn_addr[%s]", value);
			
			//R302 GGSN地址
			pQuery->GetValue(18, value);			
			rbr.ggsn_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ggsn_addr[%s]", value);
			
			//R305 APN网络标识
			pQuery->GetValue(19, value);
			rbr.apn_id= value;		
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose apn_info[%s]", value);
			
			//R306 PDP类型
			pQuery->GetValue(20, value);
			rbr.pdp_type = value;	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_type[%s]", value);
			
			//R307 被服务方的 PDP地址
			pQuery->GetValue(21, value);
			rbr.pdp_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_addr[%s]", value);
			
			//R309 授权有效时间
			pQuery->GetValue(22, value);
			rbr.valid_time = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose valid_time[%s]", value);
			
			//R3010 QoS  
			pQuery->GetValue(23, value);
			rbr.qos = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos[%s]", value);
			
			//R3012 用户位置信息
			pQuery->GetValue(50, value);
			rbr.location = value; 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location[%s]", value);
			
			//R3013 RAT TYPE
			pQuery->GetValue(25, value);
			rbr.rat_type = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", value);
			
			//R3015 CELLID
			pQuery->GetValue(36, value);
			rbr.cell = value;			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", value);
			
			// R1012 MSC
			pQuery->GetValue(54, value);
			rbr.msc = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", value);

			//R3016
			pQuery->GetValue(38, value);
			rbr.charging_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charging_id[%s]", value);

			pQuery->GetValue(55, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose latn_id[%s]", value);
			
			//R3017 LTE业务标识
			rbr.lte_flag = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lte[1]");					

			pQuery->GetValue(30, value);
			USU.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%d]",USU.duration);
			
			pQuery->GetValue(31, value);
			USU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]",USU.unitTotal);

			pQuery->GetValue(32, value);
			USU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%ld]",USU.unitInput);

			pQuery->GetValue(33, value);
			USU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%ld]",USU.unitOutput);

			pQuery->GetValue(58, value);
			strncpy(productofferID, value, sizeof(productofferID));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "productofferID[%s]", productofferID);

			pQuery->GetValue(60, value);
			ext.kv["strID"] = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "str context id [%s]", value);
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["taskId"] = bizMsg->m_taskId;			
			//B30更新使用量
			//时长
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_SECOND;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, productofferID)))
			{
				u.amount = USU.duration;
			}
			else
			{
				u.amount = 0;
			}
			domain.usv.push_back(u);

			//总流量
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_TOTAL_BYTES;
			u.amount = 0;
			domain.usv.push_back(u);
			
			//上行流量
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_UP_BYTES;
			u.amount = 0;
			domain.usv.push_back(u);
			
			//下行流量
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_DOWN_BYTES;
			u.amount = 0;
			domain.usv.push_back(u);

			
			//B03总使用时长
			pQuery->GetValue(3, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_SECOND;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, productofferID)))
			{
				totalusu.amount = atol(value) + USU.duration;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%ld]",totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用总流量
			pQuery->GetValue(4, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_TOTAL_BYTES;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, productofferID)))
			{
				totalusu.amount = atol(value) + USU.unitTotal;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total bytes[%ld]",totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用上行流量
			pQuery->GetValue(5, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_UP_BYTES;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, productofferID)))
			{
				totalusu.amount = atol(value) + USU.unitInput;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total up bytes[%ld]",totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用下行流量
			pQuery->GetValue(6, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_DOWN_BYTES;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, productofferID)))
			{
				totalusu.amount = atol(value) + USU.unitOutput;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total down bytes[%ld]",totalusu.amount);
			domain.dbv.push_back(totalusu);


			rbr.accumlator_info = 1;
			rbr.tariff_info     = 1;
			rbr.balance_info    = 1;
			rbr.rating_info     = 1;
			rbr.balance_query   = 1;

			try
			{
				m_en.clear();
				m_en.encode(uhd);
				m_en.encode(head);
				m_en.encode(domain);
				m_en.encode(rbr);
				m_en.encode(ext);

				//打印head消息
				m_print.clear();
				m_print.print(uhd);	
				m_print.print(head);	
				m_print.print(domain);	
				m_print.print(rbr);
				m_print.print(ext);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
			}
			catch(exception& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
				return ERR_ENCODE_CODE;
			}
			
		    bizMsg->m_vectorMsg.push_back((char *)m_en.data());
				

		}
		if(count == 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "invalid session id", bizMsg->m_sessionID);
			ret = SM_OCP_UNKNOWN_SESSION_ID;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
    
	if(-30232 == bizMsg->m_resultcode)//余额不足
	{	
		UpdateSessionPS(bizMsg);
	}
	else
	{
		//更新会话表字段
		UpdateSession(bizMsg);
	}

	return 0;
}
int DCBizEpt::RER5G(STBizMsg* bizMsg)
{
	SEPTMsg *base = (SEPTMsg*)bizMsg->m_base;
	rbdata rbr;
	rbhead head;

	int iResultCode = 0;
	int iSessionState = 0;
	char szSessionId[BIZ_TEMP_LEN_512] = {0};
	char szValue[BIZ_TEMP_LEN_512] = {0};
	char szRatingGroup[BIZ_TEMP_LEN_256] = {0};
	UDBSQL* pQuery = NULL;
	try
	{
		pQuery = bizMsg->m_dbm->GetSQL(_5G_EPT_Select_ChildSessionAll);
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		while (pQuery->Next())
		{ // 组装RER消息
			UHead uhd;
			rbdomain domain;
			rsu_t rsu;
			usu   u;
			debit totalusu;
			rbext ext;

			ext.kv["strID"] = bizMsg->m_szServiceContextIDStr;
			ext.kv["sourceId"] = bizMsg->m_sourceId;
			ext.kv["switchId"] = bizMsg->m_switchId;
			ext.kv["operListId"] = bizMsg->m_operListId;
			ext.kv["opertype"] = bizMsg->m_operType;
			ext.kv["batchId"] = bizMsg->m_sBatchId;
			ext.kv["taskId"] = bizMsg->m_taskId;
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["RBR_SOURCE"] = "EPT";
			ext.kv["AnsFlag"] = "0";

			std::map<string,string>::iterator itExt = base->EptExt.find("opertype");
			if (itExt != base->EptExt.end())
			{
				ext.kv["opertype"] = itExt->second;
			}

			itExt = base->EptExt.find("dNNID");
			if (itExt != base->EptExt.end())
			{
				ext.kv["dNNID"] = itExt->second;
			}

			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.trace = bizMsg->m_trace_flag;
			uhd.checkKey = bizMsg->m_strCheckKey;
			
			head.type = RE_SERVICE_TYPE_INT_5G_REQ;
			head.version= 2;
			head.sreq = SM_SESSION_TERMINATION_CODE;
			head.serial = bizMsg->m_serial;
			head.trace = bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = bizMsg->m_topology;
			size_t psep = head.topology.find(';');
			if (psep != std::string::npos)
			{ // 移除top名附加的字段
			    head.topology.erase(psep);
			}

			pQuery->GetValue(1, szSessionId); // OCP_STR_SESSION_ID
			head.session = szSessionId;
			rbr.pay = 1;
			rbr.ratable_flag = 0;
			rbr.active_flag = 0;

			pQuery->GetValue(2, szValue); // SM_INT_RESULT_CODE
			iResultCode = atoi(szValue);

			pQuery->GetValue(3, szValue); // SM_INT_SESSION_STATUS
			iSessionState = atoi(szValue);

			if (iSessionState != RECVRB_ACTION)
			{
				continue;
			}

			if (bizMsg->m_eptType == 2 && iResultCode == 2001)
			{
				continue;
			}

			pQuery->GetValue(4, szValue);  // CDR_PUB_LNG_SERVID
			head.prodinstid = atol(szValue);

			pQuery->GetValue(5, szValue);  // CDR_INT_LATN_ID
			rbr.latn_id = atoi(szValue);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose latn_id[%d]", rbr.latn_id);

			pQuery->GetValue(6, szValue); // RE_STR_SUB_NBR
			rbr.charged_nbr = szValue;

			pQuery->GetValue(7, szValue); // RE_STR_SUB_AREA
			rbr.calling_harea = szValue;
			rbr.charged_harea = szValue;

			pQuery->GetValue(8, szValue); // RE_STR_SUB_VISIT_AREA
			rbr.calling_varea = szValue;

			pQuery->GetValue(9, szValue); // OCP_STR_CHARGING_ID
			rbr.charging_id = szValue;

			bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", rbr.charged_nbr));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_nbr[%s]", rbr.charged_nbr.c_str());

			pQuery->GetValue(10, szRatingGroup); // OCP_LNG_RATING_GROUP

			pQuery->GetValue(11, szValue); // RE_INT_ROAM_TYPE
			rbr.roam_type = atoi(szValue);

			pQuery->GetValue(12, szValue); // SM_LNG_MSCC_VALIDITY_TIME
			rbr.valid_time = atol(szValue);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose valid_time[%s]", szValue);

			pQuery->GetValue(13, szValue); // CDR_USER_LOCATION_INFO
			rbr.location = szValue;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location[%s]", szValue);

			pQuery->GetValue(14, szValue); // OCP_STR_RATTYPE
			rbr.rat_type = szValue;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", szValue);

			pQuery->GetValue(15, szValue); // CDR_STR_CELLID
			rbr.cell = szValue;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", szValue);

			pQuery->GetValue(16, szValue); // CDR_STR_MSC
			rbr.msc = szValue;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", szValue);

			pQuery->GetValue(17, szValue); // RE_LNG_LAST_CCR_TIME
			rbr.sess_start_time = szValue;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sess_start_time[%s]", szValue);

			if (SM_SESSION_INITIAL_CODE == bizMsg->m_requestType)
			{
				rbr.pre_dtime = 0;
			}
			else
			{
				rbr.pre_dtime = atol(szValue);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pre_dtime[%s]", szValue);
			}

			//R604	本次计费请求开始时间
			rbr.cur_dtime = rbr.pre_dtime;
			head.stamp = rbr.pre_dtime;

			rbr.lte_flag = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose lte[1]");

			SCCRDataUnit totalUSU;
			pQuery->GetValue(18, szValue); // SM_LNG_ALL_USU_TIME
			totalUSU.duration = atoi(szValue);
			pQuery->GetValue(19, szValue); // SM_LNG_ALL_USU_TOTAL_OCT
			totalUSU.unitTotal = atol(szValue);
			pQuery->GetValue(20, szValue); // SM_LNG_ALL_USU_INPUT_OCT
			totalUSU.unitInput = atol(szValue);
			pQuery->GetValue(21, szValue); // SM_LNG_ALL_USU_OUTPUT_OCT
			totalUSU.unitOutput = atol(szValue);

			if (bizMsg->m_eptType == 2)
			{
				itExt = base->EptExt.find("cutcdr_flag");
				if (itExt != base->EptExt.end())
				{
					ext.kv["cutcdr_flag"] = itExt->second;
				}

				itExt = base->EptExt.find("5gusu");
				if (itExt != base->EptExt.end())
				{
					ext.kv["5gusu"] = itExt->second;
				}
				else
				{
					ext.kv["5gusu"] = "0";
				}

				for (itExt = base->EptExt.begin(); itExt != base->EptExt.end(); ++itExt)
				{
					if (strncmp(itExt->first.c_str(), "USUAMOUNT", strlen("USUAMOUNT")) == 0)
					{
						ext.kv[itExt->first] = itExt->second;
					}

					if (strncmp(itExt->first.c_str(), "NewLocInfo", strlen("NewLocInfo")) == 0)
					{
						ext.kv[itExt->first] = itExt->second;
					}
				}

				SCCRDataUnit usu0, usu1;
				itExt = base->EptExt.find("USUAMOUNT0");
				if (itExt != base->EptExt.end())
				{
					GetUSUAmountFromExt(usu0, itExt->second.c_str());
				}

				itExt = base->EptExt.find("USUAMOUNT1");
				if (itExt != base->EptExt.end())
				{
					GetUSUAmountFromExt(usu1, itExt->second.c_str());
				}

				u.rating_group = szRatingGroup;
				
				// 时长
				u.unit = RB_UNIT_CODE_SECOND;
				u.amount = (-30232 == bizMsg->m_resultcode) ? usu0.duration : 0;
				u.amount2 = (-30232 == bizMsg->m_resultcode) ? usu1.duration : 0;
				totalUSU.duration += usu0.duration;
				totalUSU.duration += usu1.duration;
				domain.usv.push_back(u);
				
				// 总流量
				u.unit = RB_UNIT_CODE_TOTAL_BYTES;
				u.amount = (-30232 == bizMsg->m_resultcode) ? usu0.unitTotal : 0;
				u.amount2 = (-30232 == bizMsg->m_resultcode) ? usu1.unitTotal : 0;
				totalUSU.unitTotal += usu0.unitTotal;
				totalUSU.unitTotal += usu1.unitTotal;
				domain.usv.push_back(u);
				
				// 上行流量
				u.unit = RB_UNIT_CODE_UP_BYTES;
				u.amount = (-30232 == bizMsg->m_resultcode) ? usu0.unitInput : 0;
				u.amount2 = (-30232 == bizMsg->m_resultcode) ? usu1.unitInput : 0;
				totalUSU.unitInput += usu0.unitInput;
				totalUSU.unitInput += usu1.unitInput;
				domain.usv.push_back(u);
				
				// 下行流量
				u.unit = RB_UNIT_CODE_DOWN_BYTES;
				u.amount = (-30232 == bizMsg->m_resultcode) ? usu0.unitOutput : 0;
				u.amount2 = (-30232 == bizMsg->m_resultcode) ? usu1.unitOutput : 0;
				totalUSU.unitOutput += usu0.unitOutput;
				totalUSU.unitOutput += usu1.unitOutput;
				domain.usv.push_back(u);
			}
			else
			{
				u.rating_group = szRatingGroup;
				// 时长
				u.unit = RB_UNIT_CODE_SECOND;
				u.amount = 0;
				u.amount2 = 0;
				domain.usv.push_back(u);
				
				// 总流量
				u.unit = RB_UNIT_CODE_TOTAL_BYTES;
				u.amount = 0;
				u.amount2 = 0;
				domain.usv.push_back(u);
				
				// 上行流量
				u.unit = RB_UNIT_CODE_UP_BYTES;
				u.amount = 0;
				u.amount2 = 0;
				domain.usv.push_back(u);
				
				// 下行流量
				u.unit = RB_UNIT_CODE_DOWN_BYTES;
				u.amount = 0;
				u.amount2 = 0;
				domain.usv.push_back(u);
			}

			totalusu.rating_group = szRatingGroup;
			totalusu.unit = RB_UNIT_CODE_SECOND;
			totalusu.amount = totalUSU.duration;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);

			// 总使用量
			totalusu.rating_group = szRatingGroup;
			totalusu.unit = RB_UNIT_CODE_TOTAL_BYTES;
			totalusu.amount = totalUSU.unitTotal;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);

			//总使用上行流量
			totalusu.rating_group = szRatingGroup;
			totalusu.unit = RB_UNIT_CODE_UP_BYTES;
			totalusu.amount = totalUSU.unitInput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total up bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);

			//总使用下行流量
			totalusu.rating_group = szRatingGroup;
			totalusu.unit = RB_UNIT_CODE_DOWN_BYTES;
			totalusu.amount = totalUSU.unitOutput;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total down bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);

			rbr.accumlator_info = 1;
			rbr.tariff_info 	= 1;
			rbr.balance_info	= 1;
			rbr.rating_info 	= 1;
			rbr.balance_query	= 1;

			try
			{
				m_en.clear();
				m_en.encode(uhd);
				m_en.encode(head);
				m_en.encode(domain);
				m_en.encode(rbr);
				m_en.encode(ext);

				//打印head消息
				m_print.clear();
				m_print.print(uhd);
				m_print.print(head);
				m_print.print(domain);
				m_print.print(rbr);
				m_print.print(ext);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
			}
			catch(exception& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
				return ERR_ENCODE_CODE;
			}

			bizMsg->m_vectorMsg.push_back((char *)m_en.data());
		}
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}

int DCBizEpt::RERDATA(STBizMsg* bizMsg)
{
	int ret 							= RET_SUCCESS;
	long nextCCTime						= 0;
	char ratingGroup[BIZ_TEMP_LEN_64]	= {0};
	char ofrid[128]						={0};
	char rg[BIZ_TEMP_LEN_64]			= {0};
	char value[BIZ_TEMP_LEN_256] 		= {0};
	char buf[BIZ_TEMP_LEN_1024] 		= {0};
	char tmp[BIZ_TEMP_LEN_256]			= {0};
	char timeStamp[BIZ_TEMP_LEN_16]		= {0};
	SCCRDataUnit USU;
	long curData = 0;
	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;
	sprintf(tmp, "%lld", curData);

	int i=strlen(bizMsg->m_childsessionID)-1;
	for(; i>=0; i--)
	{
		if(';' == bizMsg->m_childsessionID[i])
		{
			i++;
			break;
		}
	}
	strncpy(rg, bizMsg->m_childsessionID+i, BIZ_TEMP_LEN_64);

	UDBSQL* pQuery = bizMsg->m_dbm->GetSQL(DATA_SelectSessionStoreRecord_002);
	try
	{
               // 应答流程只处理当前RG 
              if(bizMsg->m_eptType == 2)
              {
                    strcpy(value, bizMsg->m_childsessionID);
              }
              else
              {
                    sprintf(value,"%s;%%",bizMsg->m_sessionID);
              }
              
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, value);
		pQuery->Execute();	
		while(pQuery->Next())
		{
			//组装RER消息
			UHead uhd;
			rbhead head;
			rbdomain domain;
			rbdata rbr;
			rsu_t rsu;
			usu   u;
			debit totalusu;
			rbext ext;

			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_DATA_REQ;
			head.version= 2;
			head.sreq  = 3;

			//current time	
			head.stamp = atol(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NOW TIME [%s]", tmp);
			
			
			head.serial = bizMsg->m_serial;
			head.result = 0;
			head.topology = bizMsg->m_topology;
                     size_t psep = head.topology.find(';');
                     if(psep != std::string::npos)
                     {
                            // 移除top名附加的字段
                            head.topology.erase(psep);
                     }

			//prod_inst_id
			pQuery->GetValue(45, value);
			head.prodinstid = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "prod_inst_id[%s]", value);
			
			pQuery->GetValue(59, value);
			strncpy(ratingGroup, value, sizeof(ratingGroup));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RatingGroup[%s]",ratingGroup);

			pQuery->GetValue(62, value);
			head.session = value;
			if(bizMsg->m_version == 1)
				head.session.erase(0,3);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sessionID[%s]", value);

			pQuery->GetValue(1, value);
			if(0 == bizMsg->m_trace_flag)bizMsg->m_trace_flag=atoi(value);	
			head.trace =  bizMsg->m_trace_flag;
			uhd.trace = bizMsg->m_trace_flag;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "traceNumber [%s]", value);

			//R71	会话开始时间
			pQuery->GetValue(2, value);
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sess_start_time[%s]", value);
			
			//R85	用户付费属性标识
			rbr.pay=1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose payflag[1]");
			
			pQuery->GetValue(3, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_SM_INT_SESSION_STATUS[%s]",value); 
					
			//R01	付费号码
			pQuery->GetValue(10, value);
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_nbr[%s]", value);
			bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", value));
			//R504 主叫号码归属费率区
			pQuery->GetValue(11, value);
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_harea[%s]", value);
			
			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);	
			rbr.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charged_harea[%s]", value);
			
			//R5011 漫游类型
			pQuery->GetValue(12, value);
			rbr.roam_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose roam_type[%s]", value);
			
			//R505 主叫号码拜访费率区
			pQuery->GetValue(13, value);
			rbr.calling_varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose calling_varea[%s]", value);
			
			pQuery->GetValue(14, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "n_RE_INT_PAY_FLAG[%s]",value);
			
			
			//R603	会话上次扣费开始时间
			if(SM_SESSION_UPDATE_FIRST_CODE == bizMsg->m_requestType)
			{
				rbr.pre_dtime = 0;
			}
			else
			{
				pQuery->GetValue(3, value);
				rbr.pre_dtime = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pre_dtime[%lld]", rbr.pre_dtime);
			
			//R604	本次计费请求开始时间
			pQuery->GetValue(3, value);
			rbr.cur_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cur_dtime[%s]", value);
			
			//R605	是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ratable_flag[%s]", "0");
			
			//R606 激活用户
			pQuery->GetValue(15, value);
			rbr.active_flag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose active_flag[%s]", value);
			
			//R301 SGSN地址
			pQuery->GetValue(17, value);			
			rbr.sgsn_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose sgsn_addr[%s]", value);
			
			//R302 GGSN地址
			pQuery->GetValue(18, value);			
			rbr.ggsn_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose ggsn_addr[%s]", value);
			
			//R305 APN网络标识
			pQuery->GetValue(19, value);
			rbr.apn_id = value;		
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose apn_info[%s]", value);
			
			//R306 PDP类型
			pQuery->GetValue(20, value);
			rbr.pdp_type = value;	
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_type[%s]", value);
			
			//R307 被服务方的 PDP地址
			pQuery->GetValue(21, value);
			rbr.pdp_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose pdp_addr[%s]", value);
			
			//R309 授权有效时间
			pQuery->GetValue(5, value);
			rbr.valid_time = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose valid_time[%s]", value);
			
			//R3010 QoS  
			pQuery->GetValue(23, value);
			rbr.qos = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose qos[%s]", value);
			
			//R3012 用户位置信息
			pQuery->GetValue(24, value);
			rbr.location = value; 
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose location[%s]", value);
			
			//R3013 RAT TYPE
			pQuery->GetValue(25, value);
			rbr.rat_type = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose rat_type[%s]", value);
			
			//R3015 CELLID
			pQuery->GetValue(27, value);
			rbr.cell = value;			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose cell[%s]", value);
			
			// R1012 MSC
			pQuery->GetValue(28, value);
			rbr.msc = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", value);

			// 
			pQuery->GetValue(23, value);
			USU.duration= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose msc[%s]", value);
			
			pQuery->GetValue(34, value);
			USU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitTotal);

			pQuery->GetValue(35, value);
			USU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%ld]", USU.unitInput);

			pQuery->GetValue(36, value);
			USU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%ld]", USU.unitOutput);

			//R3016
			pQuery->GetValue(41, value);
			rbr.charging_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose charging_id[%s]", value);

			pQuery->GetValue(57, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose latn_id[%s]", value);

			pQuery->GetValue(61, value);//OCP_STR_PRODUCT_OFFER_ID
			strncpy(ofrid,value,strlen(value));
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "get ofr id[%s]", value);

			pQuery->GetValue(63, value);//OCP_STR_PRODUCT_OFFER_ID
			ext.kv["strID"] = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "str context id[%s]", value);
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["taskId"] = bizMsg->m_taskId;
			
			rbr.accumlator_info = 1;
			rbr.tariff_info     = 1;
			rbr.balance_info    = 1;
			rbr.rating_info     = 1;
			rbr.balance_query   = 1;

                     // CCG 业务 LTE_FLAG  =2 
                     if(bizMsg->m_serviceContextID == CCG)
                     {
                            rbr.lte_flag = 2;
                     }

			//B30更新使用量
			//时长33
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_SECOND;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, ofrid)))
			{
				u.amount = USU.duration;
			}
			else
			{
				u.amount = 0;
			}
			domain.usv.push_back(u);

			//总流量34
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_TOTAL_BYTES;
			u.amount = 0;	
			domain.usv.push_back(u);
			
			//上行流量35
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_UP_BYTES;
			u.amount = 0;	
			domain.usv.push_back(u);
			
			//下行流量36
			u.rating_group = ratingGroup;
			u.unit = RB_UNIT_CODE_DOWN_BYTES;
			u.amount = 0;	
			domain.usv.push_back(u);

			
			//B03总使用时长
			pQuery->GetValue(6, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_SECOND;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, ofrid)))
			{
				totalusu.amount = atol(value) + USU.duration;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用总流量
			pQuery->GetValue(7, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_TOTAL_BYTES;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, ofrid)))
			{
				totalusu.amount = atol(value) + USU.unitTotal;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用上行流量
			pQuery->GetValue(8, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_UP_BYTES;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, ofrid)))
			{
				totalusu.amount = atol(value) + USU.unitInput;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "up bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);
			
			//总使用下行流量
			pQuery->GetValue(9, value);
			totalusu.rating_group = ratingGroup;
			totalusu.unit = RB_UNIT_CODE_DOWN_BYTES;
			if(-30232 == bizMsg->m_resultcode && !(strcmp(rg,ratingGroup) && strcmp(rg, ofrid)))
			{
				totalusu.amount = atol(value) + USU.unitOutput;
			}
			else
			{
				totalusu.amount = atol(value);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "down bytes[%ld]", totalusu.amount);
			domain.dbv.push_back(totalusu);

			try
			{
				m_en.clear();
				m_en.encode(uhd);
				m_en.encode(head);
				m_en.encode(domain);
				m_en.encode(rbr);
				m_en.encode(ext);

				//打印head消息
				m_print.clear();
				m_print.print(uhd);
				m_print.print(head);	
				m_print.print(domain);	
				m_print.print(rbr);
				m_print.print(ext);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RBR[%s]", m_print.data());
			}
			catch(exception& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
				return ERR_ENCODE_CODE;
			}

			bizMsg->data = (char *)m_en.data();
			bizMsg->m_vectorMsg.push_back(bizMsg->data);
		
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}
	
	if(-30232 == bizMsg->m_resultcode)//余额不足
	{	
		UpdateSessionPS(bizMsg);
	}
	else
	{
		//更新会话表字段
		UpdateSession(bizMsg);
	}
       return 0;
}

int DCBizEpt::RERISMP(STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	char value[BIZ_TEMP_LEN_1024] 			= {0};
	char buf[BIZ_TEMP_LEN_1024] 			= {0};
	SCCRDataUnit TUSU 					;
	char szHostId[32]                   ={0};
	char tmp[BIZ_TEMP_LEN_256]			= {0};
	//组装RER	消息
	rbismp rbr 		;
	rbhead head		;
	rbdomain domain	;
	usu u;
	debit d;
	rbext ext;
	UHead uhd;

	long curData = 0;
	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;
	sprintf(tmp, "%lld", curData);

    UDBSQL * pQuery = bizMsg->m_dbm->GetSQL(ISMP_GetSessionRecord);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();
		if(pQuery->Next())
		{
			head.topology = bizMsg->m_topology;
			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.checkKey = bizMsg->m_strCheckKey;

			//100	MsgType
			head.type = RE_SERVICE_TYPE_INT_ISMP_REQ;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose msgtype[%s]", RE_SERVICE_TYPE_STR_ISMP_REQ);

			head.version= 2;
			head.sreq = 3;
			//000	会话ID
			head.session = bizMsg->m_sessionID;
			if(bizMsg->m_version == 1)
				head.session.erase(0,3);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose sessionid[%s]", bizMsg->m_sessionID);
			
			//current time	
			head.stamp = atol(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NOW TIME [%s]", tmp);
			
			// 001 serial
			head.serial = bizMsg->m_serial;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose serial[%ld]", bizMsg->m_serial);
			
			//设置信令跟踪标志，通知RB需要信令跟踪
			head.trace = bizMsg->m_trace_flag;
			uhd.trace = bizMsg->m_trace_flag;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose trace[%d]", bizMsg->m_trace_flag);

			//prod_inst_id
			pQuery->GetValue(48, value);
			head.prodinstid = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose prod_inst_id[%s]", value);
			
			//R85   用户付费属性标识
			rbr.pay = 1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose payplag[1]");

			//R71	会话开始时间
			pQuery->GetValue(8, value);
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose sess_start_time[%s]", value);
			
			//R01	付费号码
			pQuery->GetValue(10, value);
			rbr.charged_nbr= value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose charged_nbr[%s]", value);
			bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", value));
			//R02	主叫号码
			pQuery->GetValue(13, value);
			rbr.calling_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose calling_nbr[%s]", value);
			
			//R03	被叫号码
			pQuery->GetValue(17, value);
			rbr.called_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose %s[%s]", value);
			
			//R401 产品
			pQuery->GetValue(21, value);
			rbr.product_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose product_id[%s]", value);
			
			//R402 业务类型
			pQuery->GetValue(22, value);
			rbr.service_type = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose service_type[%s]", value);
			
			//R405 内容代码
			pQuery->GetValue(24, value);
			rbr.content_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose content_id[%s]", value);
			
			//R406 SP代码
			pQuery->GetValue(25, value);
			rbr.sp_id= value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose sp_id[%s]", value);
			
			//R407媒体类型
			pQuery->GetValue(26, value);
			rbr.media_type = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose media_type[%s]", value);
			
			//R408 客户端IP
			pQuery->GetValue(27, value);
			rbr.client_addr= value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose client_addr[%s]", value);
			
			//R409 消息ID
			pQuery->GetValue(28, value);
			rbr.msg_id= value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose msg_id[%s]", value);
			
			//R503	销售品
			pQuery->GetValue(23, value);
			rbr.product_offer_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose product_offer_id[%s]", value);
			
			//R504 主叫号码归属费率区
			pQuery->GetValue(15, value);
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose calling_harea[%s]", value);
			
			//R506	主叫号码归属运营商
			pQuery->GetValue(16, value);
			rbr.calling_hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose calling_hcarrier[%s]", value);
			
			//R507	被叫号码归属费率区
			pQuery->GetValue(19, value);
			rbr.called_harea= value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose called_harea[%s]", value);
			
			//R509 被叫号码归属运营商
			pQuery->GetValue(20, value);
			rbr.called_hcarrier = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose called_hcarrier[%s]", value);
			
			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);
			if (86 != atoi(value))
			{
			}
			else
			{
				pQuery->GetValue(12, value);
			}
			rbr.charged_harea=value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose charged_harea[%s]", value);
			
			//R601 重发标记
			
			//R602 计费类型
			rbr.sreq = SM_SESSION_UPDATE_CODE;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose sreq[%s]", SM_SESSION_UPDATE_STRING);
			
			//R603	会话上次扣费开始时间
			pQuery->GetValue(9, value);
			rbr.pre_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose last ccr time[%s]", value);
			
			//R604	本次计费请求开始时间
			rbr.cur_dtime = bizMsg->timestampCCR;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose current ccr time[%s]", value);
			
			//R605	是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose ratable_flag[%s]", "0");

			
			pQuery->GetValue(47, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "compose latn_id[%s]", value);

			pQuery->GetValue(49, value);
			ext.kv["strID"] = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_ISMP_TYPE, "", "str context id[%s]", value);
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["taskId"] = bizMsg->m_taskId;
			ext.kv["RBR_SOURCE"] = "EPT";
			if (-30232 == bizMsg->m_resultcode)
			{
				ext.kv["AnsFlag"] = "1";
			}
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//B03总使用量
	//时长
	d.unit = RB_UNIT_CODE_SECOND;
	d.amount = TUSU.duration;
	domain.dbv.push_back(d);

	d.unit = RB_UNIT_CODE_CENT;
	d.amount = TUSU.duration;
	domain.dbv.push_back(d);


	//总使用量
	d.unit = RB_UNIT_CODE_TOTAL_BYTES;
	d.amount = TUSU.unitTotal;
	domain.dbv.push_back(d);

	//上行流量
	d.unit = RB_UNIT_CODE_UP_BYTES;
	d.amount = TUSU.unitInput;
	domain.dbv.push_back(d);

	//下行流量
	d.unit = RB_UNIT_CODE_DOWN_BYTES;
	d.amount = TUSU.unitOutput;
	domain.dbv.push_back(d);


	//B30
	//时长
	u.unit = RB_UNIT_CODE_SECOND;
	u.amount = 0;
	domain.usv.push_back(u);

	u.unit = RB_UNIT_CODE_CENT;
	u.amount = 0;
	domain.usv.push_back(u);


	//总使用量
	u.unit = RB_UNIT_CODE_TOTAL_BYTES;
	u.amount = 0;
	domain.usv.push_back(u);

	//上行流量
	u.unit = RB_UNIT_CODE_UP_BYTES;
	u.amount = 0;
	domain.usv.push_back(u);

	//下行流量
	u.unit = RB_UNIT_CODE_DOWN_BYTES;
	u.amount = 0;
	domain.usv.push_back(u);


	rbr.accumlator_info 	= 1;		//B06	累积量信息
	rbr.tariff_info 		= 1;		//B07	资费信息
	rbr.balance_info 		= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 		= 1;		//B20	费率信息查询命令
	rbr.balance_query 		= 1;		//B21	余额查询命令

	//模拟测试消息组装B09=1给RE
	if(1 == bizMsg->m_testFlag)
	{
		rbr.acct_info = 1;
		rbr.test_info = 1;
	}

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//打印head消息
		m_print.clear();
		m_print.print(uhd);
		m_print.print(head);	
		m_print.print(domain);	
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	
    bizMsg->data = (char *)m_en.data();	
	bizMsg->m_vectorMsg.push_back(bizMsg->data);

	//更新会话表字段
	UpdateSession(bizMsg);

	return ret;
}

int DCBizEpt::RERDSL(STBizMsg* bizMsg)
{
	int ret 								= RET_SUCCESS;
	char value[BIZ_TEMP_LEN_1024] 			= {0};
	char buf[BIZ_TEMP_LEN_1024] 			= {0};
	SCCRDataUnit TUSU 					;
	char szHostId[32]                   ={0};
	char tmp[BIZ_TEMP_LEN_256]			= {0};
	SCCRDataUnit USU;
	//组装RER	消息
	rbdsl rbr 		;
	rbhead head		;
	rbdomain domain	;
	usu u;
	debit d;
	rbext ext;
	UHead uhd;
	
	long curData = 0;
	time_t et;
	time(&et);
	struct tm *p = localtime(&et);
	curData = (1900+p->tm_year)*10000000000 + (1+p->tm_mon)*100000000 + (p->tm_mday)*1000000 + (p->tm_hour)*10000 + (p->tm_min)*100 + p->tm_sec;
	sprintf(tmp, "%lld", curData);

    UDBSQL * pQuery = bizMsg->m_dbm->GetSQL(DSL_GetSessionInfo);
	try
	{
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, bizMsg->m_sessionID);
		pQuery->Execute();

		if(pQuery->Next())
		{
			uhd.uid = bizMsg->m_uid;
			uhd.car = "1";
			uhd.checkKey = bizMsg->m_strCheckKey;
			head.type = RE_SERVICE_TYPE_INT_DSL_REQ;
			head.version= 2;
			head.sreq  =3;

			//current time	
			head.stamp = atol(tmp);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "NOW TIME [%s]", tmp);

			head.session = bizMsg->m_sessionID;
			if(bizMsg->m_version == 1)
				head.session.erase(0,3);
			head.serial = bizMsg->m_serial;
			head.trace =  bizMsg->m_trace_flag;
			uhd.trace = bizMsg->m_trace_flag;
			head.result = 0;
			head.topology = bizMsg->m_topology;

			//prod_inst_id	
			pQuery->GetValue(39, value);
			head.prodinstid = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose prod_inst_id[%s]", value);
			
			//R71	会话开始时间	
			pQuery->GetValue(8, value);
			rbr.sess_start_time = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose sess_start_time[%s]", "0");
			
			//R85	用户付费属性标识
			rbr.pay=1;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "compose payflag[1]");


			//R01 /R04  付费号码
			pQuery->GetValue(10, value);
			rbr.charged_nbr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose charged_nbr[%s]", value);
			bizMsg->m_pSendMsg->insert(pair<string,string>("BillingNbr", value));


			//R504 主叫号码归属费率区
			pQuery->GetValue(11, value);
			rbr.calling_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose calling_harea[%s]", value);

			//R505主叫号码计费费率区
			pQuery->GetValue(13, value);
			rbr.calling_varea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose calling_varea[%s]", value);

			//R5011 漫游类型
			pQuery->GetValue(12, value);
			rbr.roamtype = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose romtype[%s]", value);

			//R5012 付费号码归属费率区
			pQuery->GetValue(11, value);
			rbr.charged_harea = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose charged_harea[%s]", value);


			//101   EventTimeStamp		
			//R603  会话上次扣费开始时间
			pQuery->GetValue(9, value);
			rbr.pre_dtime= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose pre_dtime[%s]", value);

			//R604  本次计费请求开始时间
		    rbr.cur_dtime = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose cur_dtime[%s]", value);

			//R605  是否进行使用量累计标识
			rbr.ratable_flag = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose ratable_flag[%s]", "0");

			//R901  接入服务器IP
			pQuery->GetValue(14, value);
			rbr.access_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose access_addr[%s]" ,value);

			//R902  用户IP
			pQuery->GetValue(15, value);
			rbr.user_addr = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose user_addr[%s]",value);

			//R904  ProductSpecID
			pQuery->GetValue(16, value);
			rbr.product_spec_id = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose product_spec_id[%s]",value);

			pQuery->GetValue(38, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "compose latn_id[%s]", value); 


			pQuery->GetValue(27, value);// CDR_PUB_STR_PRICING_PLAN_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "szCdrPlanId[%s]", value);
	          
			pQuery->GetValue(29, value);// CDR_PUB_STR_TARIFFID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "szCdrTariffnfo[%s]", value);

			pQuery->GetValue(30, value);// CDR_PUB_STR_CHARGEINFO
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "szCdrChargeInfo[%s]", value);

			pQuery->GetValue(33, value);// CDR_LNG_EVENT_TYPE_ID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "nEventTypeId[%s]", value);

			pQuery->GetValue(34, value);
			strcpy(szHostId, value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "szHostId [%s]", value);
			
			//总使用时长
			pQuery->GetValue(3, value);
			TUSU.duration =  atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "duration[%u]", TUSU.duration);

			//总使用总流量
			pQuery->GetValue(4, value);
			TUSU.unitTotal =  atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "unitTotal[%lld]", TUSU.unitTotal);

			//总使用上行流量
			pQuery->GetValue(5, value);
			TUSU.unitInput =  atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "unitInput[%lld]", TUSU.unitInput);

			//总使用下行流量
			pQuery->GetValue(6, value);
			TUSU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, bizMsg->m_sessionID, "unitOutput[%lld]", TUSU.unitOutput);

			pQuery->GetValue(38, value);
			rbr.latn_id = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "compose latn_id[%s]", value);

			pQuery->GetValue(40, value);
			ext.kv["strID"] = value;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "str context id[%s]", value);
			ext.kv["anstopic"] = bizMsg->m_anstopic;
			ext.kv["taskId"] = bizMsg->m_taskId;
			ext.kv["RBR_SOURCE"] = "EPT";
			if (-30232 == bizMsg->m_resultcode)
			{
				ext.kv["AnsFlag"] = "1";
			}

			pQuery->GetValue(18, value);
			USU.duration= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%d]", USU.duration);

			pQuery->GetValue(19, value);
			USU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitTotal);
			
			pQuery->GetValue(20, value);
			USU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%ld]", USU.unitInput);
					
			pQuery->GetValue(21, value);
			USU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%ld]", USU.unitOutput);
		}
		else
		{
			ret = SM_OCP_UNKNOWN_SESSION_ID;
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return SM_OCP_UNABLE_TO_COMPLY;
	}

	//B03总使用量
	if(-30232 == bizMsg->m_resultcode)//余额不足
	{
		//时长
		d.unit = RB_UNIT_CODE_SECOND;
		d.amount = TUSU.duration + USU.duration;
		domain.dbv.push_back(d);

		//总使用量
		d.unit = RB_UNIT_CODE_TOTAL_BYTES;
		d.amount = TUSU.unitTotal + USU.unitTotal;
		domain.dbv.push_back(d);

		//上行流量
		d.unit = RB_UNIT_CODE_UP_BYTES;
		d.amount = TUSU.unitInput + USU.unitInput;
		domain.dbv.push_back(d);

		//下行流量
		d.unit = RB_UNIT_CODE_DOWN_BYTES;
		d.amount = TUSU.unitOutput + USU.unitOutput;
		domain.dbv.push_back(d);
	}
	else
	{
		//B03总使用量
		//时长
		d.unit = RB_UNIT_CODE_SECOND;
		d.amount = TUSU.duration;
		domain.dbv.push_back(d);

		//总使用量
		d.unit = RB_UNIT_CODE_TOTAL_BYTES;
		d.amount = TUSU.unitTotal;
		domain.dbv.push_back(d);

		//上行流量
		d.unit = RB_UNIT_CODE_UP_BYTES;
		d.amount = TUSU.unitInput;
		domain.dbv.push_back(d);

		//下行流量
		d.unit = RB_UNIT_CODE_DOWN_BYTES;
		d.amount = TUSU.unitOutput;
		domain.dbv.push_back(d);
	}

	//B30
	//时长
	u.unit = RB_UNIT_CODE_SECOND;
	u.amount = 0;
	domain.usv.push_back(u);

	//总使用量
	u.unit = RB_UNIT_CODE_TOTAL_BYTES;
	u.amount = 0;
	domain.usv.push_back(u);

	//上行流量
	u.unit = RB_UNIT_CODE_UP_BYTES;
	u.amount = 0;
	domain.usv.push_back(u);

	//下行流量
	u.unit = RB_UNIT_CODE_DOWN_BYTES;
	u.amount = 0;
	domain.usv.push_back(u);


	rbr.accumlator_info 	= 1;		//B06	累积量信息
	rbr.tariff_info 		= 1;		//B07	资费信息
	rbr.balance_info 		= 1;		//B08	余额帐本改变的详细信息
	rbr.rating_info 		= 1;		//B20	费率信息查询命令
	rbr.balance_query 		= 1;		//B21	余额查询命令

	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(domain);
		m_en.encode(rbr);
		m_en.encode(ext);

		//打印head消息
		m_print.clear();
		m_print.print(uhd);	
		m_print.print(head);	
		m_print.print(domain);	
		m_print.print(rbr);
		m_print.print(ext);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "RBR[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}
	
    bizMsg->data = (char *)m_en.data();
	bizMsg->m_vectorMsg.push_back(bizMsg->data);
	//更新会话表字段
	UpdateSession(bizMsg);

	return ret;
}


int DCBizEpt::GetSessionNum(STBizMsg* bizMsg)
{
	int rgnum = 0;
	int reqType = 0;
	char sql[256]={0};
	sprintf(sql, "%s;%%", bizMsg->m_sessionID);

	UDBSQL *pQuery  = NULL;	
	if(CCG==bizMsg->m_serviceContextID || DATA==bizMsg->m_serviceContextID)
	{
		pQuery = bizMsg->m_dbm->GetSQL(DATA_SelectSessionStoreEpt_001);
	}
	else if (PGW==bizMsg->m_serviceContextID)
	{
		pQuery = bizMsg->m_dbm->GetSQL(PGW_EXP_SelectSessionReqType);
	}

	try
	{	
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();
		pQuery->BindParam(1, sql);
		pQuery->Execute();
		while(pQuery->Next())
		{
			rgnum++;
			pQuery->GetValue(1, reqType);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "reqType[%d]", reqType);
		}
	}
	catch(UDBException& e)
	{
		std::string sql;
		pQuery->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "rgnum[%d]", rgnum);

	if( 1 == rgnum && 7 == reqType )//	只有一个RG且为firstupdate
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RET[1]");
		return 1;	
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "RET[0]");
		return 0;
	}

	return 0;
}

int DCBizEpt::DelSession(STBizMsg* bizMsg)
{
	char value[128] = {0};
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "delete session", "");
	if((DATA==bizMsg->m_serviceContextID)||(CCG == bizMsg->m_serviceContextID)||(PGW == bizMsg->m_serviceContextID))
	{
		sprintf(value, "%s%%", bizMsg->m_sessionID);
	}
	else if(bizMsg->m_spiltflag > 1 && ((VOICE==bizMsg->m_serviceContextID)||(DSL == bizMsg->m_serviceContextID)))
	{
		sprintf(value, "%s", bizMsg->m_childsessionID);
	}
	else
	{
		sprintf(value, "%s", bizMsg->m_sessionID);
	}
	
	UDBSQL *pDelete  = NULL;		
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pDelete  = bizMsg->m_dbm->GetSQL(Voice_DeleteSession);
			}
			break;
		case SMS:
			{
				pDelete  = bizMsg->m_dbm->GetSQL(SMS_DeleteSession);
			}
			break;
		case DATA:
		case CCG:
			{
				pDelete  = bizMsg->m_dbm->GetSQL(DATA_DeleteSessionStoreRG);
			}
			break;
		case PGW:
			{
				pDelete  = bizMsg->m_dbm->GetSQL(PGW_CDR_DeleteSession);				
			}
			break;
		case ISMP:
		case HRS:
			{			
				pDelete  = bizMsg->m_dbm->GetSQL(ISMP_DeleteSession);				
			}
			break;
		case DSL:
			{
				pDelete  = bizMsg->m_dbm->GetSQL(DSL_DeleteSession);								
			}
			break;
		case DATA_5G:
			{
				pDelete = bizMsg->m_dbm->GetSQL(_5G_DeleteChildSession);
			}
			break;
		default:
			{
				return RET_ERROR;
			}
			break;
	}

	if(NULL==pDelete)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "delete failed , delete sql is null");	
	}

	try
	{
		string strSql;
		pDelete->DivTable(bizMsg->m_sessionID);
		pDelete->UnBindParam();
		pDelete->BindParam(1, value);	
		pDelete->GetSqlString(strSql);
		pDelete->Execute();
		pDelete->Connection()->Commit();
		DCBIZLOG(DCLOG_LEVEL_INFO, SM_DATA_TYPE, "", "delete session ok. sql[%s]", strSql.c_str());
	}
	catch(UDBException& e)
	{
		std::string sql;
		pDelete->Connection()->Rollback();
		pDelete->GetSqlString(sql);
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "delete execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	return RET_SUCCESS;
}

void DCBizEpt::UpdateSession(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "UpdateSession");

	UDBSQL * pExec = NULL;	
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pExec = bizMsg->m_dbm->GetSQL(Voice_UpdateSession_Ept);
			}
			break;
		case SMS:
			{
			}
			break;
		case DATA:
		case CCG:
			{				
				pExec = bizMsg->m_dbm->GetSQL(DATA_UpdateSessionStoreEpt_001);
			}
			break;
		case PGW:
			{			
				pExec = bizMsg->m_dbm->GetSQL(PGW_EXP_UpdateSessionFlag);
			}
			break;
		case ISMP:
		case HRS:
			{						
				pExec = bizMsg->m_dbm->GetSQL(ISMP_UpdateSession2);
			}
			break;
		case DSL:
			{
				pExec = bizMsg->m_dbm->GetSQL(DSL_UpdateSession_Ept);
			}
			break;
	}
	

	if(NULL == pExec)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "deal ept execption", "");
		return;
	}

	//update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{
			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			if ((DATA == bizMsg->m_serviceContextID) || (CCG == bizMsg->m_serviceContextID) || (PGW == bizMsg->m_serviceContextID))
			{

				char tmp[1024] = {0};

				// 应答流程仅更新当前RG
				if (bizMsg->m_eptType == 2)
				{
					strcpy(tmp, bizMsg->m_childsessionID);
				}
				else
				{
					sprintf(tmp, "%s%%", bizMsg->m_sessionID);
				}

				pExec->BindParam(3, tmp);
				pExec->BindParam(1, 3); // SM_INT_REQ_TYPE =term
				pExec->BindParam(2, 1); // OCP_INT_CCA_FLAG =不需要发送CCA
			}

			else
			{
				pExec->BindParam(3, bizMsg->m_sessionID);
				pExec->BindParam(1, 3); // SM_INT_REQ_TYPE =term
				pExec->BindParam(2, 1); // OCP_INT_CCA_FLAG =不需要发送CCA
			}
			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
				}
			}
			break;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "UpdateSession ok", "");
}

void DCBizEpt::UpdateSessionPS(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "UpdateSessionData");

	char szChlidSession[BIZ_TEMP_LEN_256]		= {0};
	UDBSQL * pExec = NULL;	

	if(DATA==bizMsg->m_serviceContextID || CCG==bizMsg->m_serviceContextID)
	{
		pExec = bizMsg->m_dbm->GetSQL(DATA_UpdateSessionStoreRbrNoenough);
	}
	else if(PGW==bizMsg->m_serviceContextID)
	{
		pExec = bizMsg->m_dbm->GetSQL(PGW_EXP_UpdateSessionCCAFlag);

	}


	if(NULL == pExec)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "deal ept execption", "");
		return;
	}

	//update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{
		try
		{

			// 更新会话表状态
			if (bizMsg->m_childsessionID[0] && strcmp(bizMsg->m_childsessionID, bizMsg->m_sessionID) > 0)
			{
				strcpy(szChlidSession, bizMsg->m_childsessionID);
			}
			else
			{
				sprintf(szChlidSession, "%s;%lld", bizMsg->m_sessionID, bizMsg->m_ratingGroup);
			}

			pExec->DivTable(bizMsg->m_sessionID);
			pExec->UnBindParam();
			if (DATA_5G == bizMsg->m_serviceContextID)
			{
				pExec->BindParam(1, TORB_ACTION); // SM_INT_SESSION_STATUS
				pExec->BindParam(2, 2);			  // OCP_INT_CCA_FLAG =不需要发送CCA ,并且标识网元上报term时该RG不需要发送RBR
				pExec->BindParam(3, bizMsg->m_sessionID);
				pExec->BindParam(4, szChlidSession);
			}
			else
			{
				pExec->BindParam(1, TORB_ACTION); // SM_INT_SESSION_STATUS
				pExec->BindParam(2, 2);			  // OCP_INT_CCA_FLAG =不需要发送CCA ,并且标识网元上报term时该RG不需要发送RBR
				pExec->BindParam(3, szChlidSession);
			}

			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update child[%s] status=2,req_type=3,cca_flag=2 OK", szChlidSession);
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
				}
			}
			break;
		}
	}
}

//全业务不上送TermRER时更新会话表
int DCBizEpt::UpdateSessionWithoutTermRER(STBizMsg* bizMsg)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "UpdateSession", "");
	
    //ccg data pgw  子回话ID
    char szChildSessionId[256] = {0};
    if ( CCG == bizMsg->m_serviceContextID || DATA == bizMsg->m_serviceContextID || PGW == bizMsg->m_serviceContextID || DATA_5G == bizMsg->m_serviceContextID)
    {
    	//优先级判断  存在proDID则用prodID，否则用RG
    	if(bizMsg->m_childsessionID[0])
    	{
            strcpy(szChildSessionId, bizMsg->m_childsessionID);
    	}
    	else if ( strlen(bizMsg->m_ProductOfferId) )
    	{
    		sprintf(szChildSessionId,"%s;%s",bizMsg->m_sessionID, bizMsg->m_ProductOfferId);
    	}
    	else
    	{
    		sprintf(szChildSessionId,"%s;%d",bizMsg->m_sessionID, bizMsg->m_ratingGroup);	
    	}
    }

	char value[256]={0};
	UDBSQL* pExec = NULL;
	switch(bizMsg->m_serviceContextID)
    {
        case VOICE:
			{				
				long TUSU							= 0;	
				long USU							= 0;
				UDBSQL *pQuery = bizMsg->m_dbm->GetSQL(Voice_GetSessionInfo);
				try
				{
					long long serial = 0;
					pQuery->DivTable(bizMsg->m_sessionID);
					pQuery->UnBindParam();
					pQuery->BindParam(1, bizMsg->m_sessionID);
					pQuery->Execute();
					if(pQuery->Next())
					{
						pQuery->GetValue(41, value);
						USU = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "current USU[%ld]", USU);

						pQuery->GetValue(4, value);
						TUSU=USU+atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_VOICE_TYPE, "", "TOTOL USU[%ld]", TUSU);

			  		}
				}
				catch(UDBException& e)
				{
					std::string sql;
					pQuery->GetSqlString(sql);
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "select execption[%s]", e.ToString());
					return RB_SM_UNABLE_TO_COMPLY;
				}
				
				pExec = bizMsg->m_dbm->GetSQL(VOICE_UpdateSessionWithoutRER);
				// update会话2002错误码(Commit失败)3次重试
				int retryCount = 0;
				bool success = false;
				while (!success && retryCount < 3)
				{
					try
					{
						pExec->DivTable(bizMsg->m_sessionID);
						pExec->UnBindParam();
						pExec->BindParam(1, STATUS_IDLE);
						pExec->BindParam(2, TUSU);
						pExec->BindParam(3, bizMsg->m_sessionID);
						pExec->Execute();
						pExec->Connection()->Commit();
						success = true;
					}
					catch (UDBException &e)
					{
						std::string sql;
						pExec->Connection()->Rollback();
						pQuery->GetSqlString(sql);
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "update execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
						if (e.GetErrorCode() == 2002) // Commit失败
						{
							retryCount++;
							if (retryCount < 3)
							{
								DCBIZLOG(DCLOG_LEVEL_WARN, SM_VOICE_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
								continue;
							}
							else
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "Max retries reached for error code 2002");
								return RB_SM_UNABLE_TO_COMPLY;
							}
						}
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
			}
			break;
		case DATA:
		case CCG:
			{	
				
				SCCRDataUnit USU;
				SCCRDataUnit TUSU;	
				UDBSQL *pQuery = bizMsg->m_dbm->GetSQL(DATA_Select_SessionStoreRecord_RG);
				try
				{
					pQuery->DivTable(bizMsg->m_sessionID);
					pQuery->UnBindParam();
					pQuery->BindParam(1, szChildSessionId);
					pQuery->Execute();
			      	if(pQuery->Next())
					{
						pQuery->GetValue(23, value);
						USU.duration = atoi(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%s]", value);
						
						pQuery->GetValue(24, value);
						USU.unitInput = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%s]", value);
						
						pQuery->GetValue(25, value);			
						USU.unitOutput = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%s]", value);
						
						pQuery->GetValue(26, value);
						USU.unitTotal = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%s]", value);
						
						pQuery->GetValue(27, value);
						TUSU.duration = atoi(value)+USU.duration;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%s]", value);
						
						pQuery->GetValue(28, value);
						TUSU.unitInput = atol(value)+USU.unitInput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitInput[%s]", value);
						
						pQuery->GetValue(29, value);			
						TUSU.unitOutput = atol(value)+USU.unitOutput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitOutput[%s]", value);
						
						pQuery->GetValue(30, value);
						TUSU.unitTotal = atol(value)+USU.unitTotal;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitTotal[%s]", value);
					
		      		}
				}
				catch(UDBException& e)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
					return RB_SM_UNABLE_TO_COMPLY;
				}

				// update会话2002错误码(Commit失败)3次重试
				int retryCount = 0;
				bool success = false;
				while (!success && retryCount < 3)
				{
					try
					{
						pExec = bizMsg->m_dbm->GetSQL(DATA_UpdateSessionWithoutRER);
						pExec->DivTable(bizMsg->m_sessionID);
						pExec->UnBindParam();
						pExec->BindParam(1, STATUS_IDLE); // SM_INT_SESSION_STATUS
						pExec->BindParam(2, (long)TUSU.duration);
						pExec->BindParam(3, (long)TUSU.unitTotal);
						pExec->BindParam(4, (long)TUSU.unitInput);
						pExec->BindParam(5, (long)TUSU.unitOutput);
						pExec->BindParam(6, szChildSessionId);
						pExec->Execute();
						pExec->Connection()->Commit();
						success = true;
					}
					catch (UDBException &e)
					{
						pExec->Connection()->Rollback();
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
						if (e.GetErrorCode() == 2002) // Commit失败
						{
							retryCount++;
							if (retryCount < 3)
							{
								DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
								continue;
							}
							else
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
								return RB_SM_UNABLE_TO_COMPLY;
							}
						}
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
			}
			break;
		case PGW:
			{
				
				SCCRDataUnit USU;
				SCCRDataUnit TUSU;	
				UDBSQL *pQuery = bizMsg->m_dbm->GetSQL(PGW_SelectSession_RG);
				try
				{
					pQuery->DivTable(bizMsg->m_sessionID);
					pQuery->UnBindParam();
					pQuery->BindParam(1, szChildSessionId);
					pQuery->Execute();
			      	if(pQuery->Next())
					{
						pQuery->GetValue(23, value);
						USU.duration = atoi(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%s]", value);
						
						pQuery->GetValue(24, value);
						USU.unitInput = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitInput[%s]", value);
						
						pQuery->GetValue(25, value);			
						USU.unitOutput = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitOutput[%s]", value);
						
						pQuery->GetValue(26, value);
						USU.unitTotal = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%s]", value);
						
						pQuery->GetValue(27, value);
						TUSU.duration = atoi(value)+USU.duration;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total duration[%s]", value);
						
						pQuery->GetValue(28, value);
						TUSU.unitInput = atol(value)+USU.unitInput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitInput[%s]", value);
						
						pQuery->GetValue(29, value);			
						TUSU.unitOutput = atol(value)+USU.unitOutput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitOutput[%s]", value);
						
						pQuery->GetValue(30, value);
						TUSU.unitTotal = atol(value)+USU.unitTotal;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "total unitTotal[%s]", value);
					
		      		}
				}
				catch(UDBException& e)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s]", e.ToString());
					return RB_SM_UNABLE_TO_COMPLY;
				}

				// update会话2002错误码(Commit失败)3次重试
				int retryCount = 0;
				bool success = false;
				while (!success && retryCount < 3)
				{
					try
					{
						pExec = bizMsg->m_dbm->GetSQL(PGW_UpdateSessionWithoutRER);
						pExec->DivTable(bizMsg->m_sessionID);
						pExec->UnBindParam();
						pExec->BindParam(1, STATUS_IDLE); // SM_INT_SESSION_STATUS
						pExec->BindParam(2, (long)TUSU.duration);
						pExec->BindParam(3, (long)TUSU.unitTotal);
						pExec->BindParam(4, (long)TUSU.unitInput);
						pExec->BindParam(5, (long)TUSU.unitOutput);
						pExec->BindParam(6, szChildSessionId);
						pExec->Execute();
						pExec->Connection()->Commit();
						success = true;
					}
					catch (UDBException &e)
					{
						pExec->Connection()->Rollback();
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
						if (e.GetErrorCode() == 2002) // Commit失败
						{
							retryCount++;
							if (retryCount < 3)
							{
								DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
								continue;
							}
							else
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
								return RB_SM_UNABLE_TO_COMPLY;
							}
						}
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
			}
			break;
		case DSL:
			{
				SCCRDataUnit USU;
				SCCRDataUnit TUSU;	
				UDBSQL *pQuery = bizMsg->m_dbm->GetSQL(DSL_GetSessionInfo);
				try
				{
					pQuery->DivTable(bizMsg->m_sessionID);
					pQuery->UnBindParam();
					pQuery->BindParam(1, bizMsg->m_sessionID);
					pQuery->Execute();
			      	if(pQuery->Next())
					{
						pQuery->GetValue(23, value);
						USU.duration = atoi(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "duration[%s]", value);

						pQuery->GetValue(24, value);
						USU.unitInput = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitInput[%s]", value);

						pQuery->GetValue(25, value);
						USU.unitOutput = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitOutput[%s]", value);

						pQuery->GetValue(26, value);
						USU.unitTotal = atol(value);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitTotal[%s]", value);

						pQuery->GetValue(27, value);
						TUSU.duration = atoi(value)+USU.duration;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "total duration[%s]", value);

						pQuery->GetValue(28, value);
						TUSU.unitInput = atol(value)+USU.unitInput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "total unitInput[%s]", value);

						pQuery->GetValue(29, value);
						TUSU.unitOutput = atol(value)+USU.unitOutput;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "total unitOutput[%s]", value);

						pQuery->GetValue(30, value);
						TUSU.unitTotal = atol(value)+USU.unitTotal;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "total unitTotal[%s]", value);

		      		}
				}
				catch(UDBException& e)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
					return RB_SM_UNABLE_TO_COMPLY;
				}

				// update会话2002错误码(Commit失败)3次重试
				int retryCount = 0;
				bool success = false;
				while (!success && retryCount < 3)
				{
					try
					{
						pExec = bizMsg->m_dbm->GetSQL(DSL_UpdateSessionWithoutRER);
						pExec->DivTable(bizMsg->m_sessionID);
						pExec->UnBindParam();
						pExec->BindParam(1, STATUS_IDLE); // SM_INT_SESSION_STATUS
						pExec->BindParam(2, (long)TUSU.duration);
						pExec->BindParam(3, (long)TUSU.unitTotal);
						pExec->BindParam(4, (long)TUSU.unitInput);
						pExec->BindParam(5, (long)TUSU.unitOutput);
						pExec->BindParam(6, bizMsg->m_sessionID);
						pExec->Execute();
						pExec->Connection()->Commit();
						success = true;
					}
					catch (UDBException &e)
					{
						pExec->Connection()->Rollback();
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
						if (e.GetErrorCode() == 2002) // Commit失败
						{
							retryCount++;
							if (retryCount < 3)
							{
								DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
								continue;
							}
							else
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "Max retries reached for error code 2002");
								return RB_SM_UNABLE_TO_COMPLY;
							}
						}
						return RB_SM_UNABLE_TO_COMPLY;
					}
				}
			}
			break;
			case ISMP://不支持会话类
			{

			}
			break;
		default:
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "unknow service type %d", bizMsg->m_serviceContextID);
				return -1;
			}
			break;
	}
    DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "UpdateSession ok", "");    
}

//更新异常结果码
void DCBizEpt::UpdateErrorCode(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update error code", "");
	char szChlidSession[BIZ_TEMP_LEN_256] 		= {0};

	int ocp_resultcode =bizMsg->m_anspara->GetOCPResultCode(bizMsg->m_resultcode);
	if(ocp_resultcode == -1)
	{
		ocp_resultcode = 3004;
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "useless ocs result code [%d]", bizMsg->m_resultcode);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "ocp result code[%d]", ocp_resultcode);	

	UDBSQL *pExec = NULL;
	switch(bizMsg->m_serviceContextID)
	{
		case VOICE:
			{
				pExec = bizMsg->m_dbm->GetSQL(Voice_UpdateSession_EptResult);
			}
			break;
		case SMS:
			{
			}
			break;
		case DATA:
		case CCG:
			{
				pExec = bizMsg->m_dbm->GetSQL(DATA_UpdateSessionStoreEpt_002);
                            strcpy(szChlidSession, bizMsg->m_childsessionID);
			}
			break;
		case PGW:
			{				
				pExec = bizMsg->m_dbm->GetSQL(PGW_EPT_UPDATE_CODE);
                            strcpy(szChlidSession, bizMsg->m_childsessionID);
			}
			break;
		case ISMP:
		case HRS:
			{

			}
			break;
		case DSL:
			{				
				pExec = bizMsg->m_dbm->GetSQL(DSL_UpdateSession_EptResult);				
			}
			break;
	}

	if(!pExec)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "sql is null", ocp_resultcode);
		return ;
	}

	//update会话2002错误码(Commit失败)3次重试
	int retryCount = 0;
	bool success = false;
	while (!success && retryCount < 3)
	{

		try
		{
			if (bizMsg->m_serviceContextID == DATA_5G)
			{
				if (bizMsg->m_childsessionID[0])
				{
					pExec = bizMsg->m_dbm->GetSQL(_5G_EPT_UPDATE_CODE_CHILD); // u_5g_ept_child
					if (!pExec)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "no find sql[%s]", _5G_EPT_UPDATE_CODE_CHILD);
						return;
					}
					pExec->DivTable(bizMsg->m_sessionID);
					pExec->UnBindParam();
					pExec->BindParam(1, ocp_resultcode); // SM_INT_RESULT_CODE
					pExec->BindParam(2, TORB_ACTION);	 // SM_INT_SESSION_STATUS
					pExec->BindParam(3, bizMsg->m_sessionID);
					pExec->BindParam(4, bizMsg->m_childsessionID);
				}
				else
				{
					pExec = bizMsg->m_dbm->GetSQL(_5G_EPT_UPDATE_CODE_CHILD_ALL); // u_5g_ept_child_all
					if (!pExec)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "no find sql[%s]", _5G_EPT_UPDATE_CODE_CHILD);
						return;
					}
					pExec->DivTable(bizMsg->m_sessionID);
					pExec->UnBindParam();
					pExec->BindParam(1, ocp_resultcode); // SM_INT_RESULT_CODE
					pExec->BindParam(2, TORB_ACTION);	 // SM_INT_SESSION_STATUS
					pExec->BindParam(3, bizMsg->m_sessionID);
				}
			}
			else if (bizMsg->m_serviceContextID == PGW)
			{
				pExec->DivTable(bizMsg->m_sessionID);
				pExec->UnBindParam();
				pExec->BindParam(1, ocp_resultcode); // SM_INT_SESSION_STATUS
				pExec->BindParam(2, TORB_ACTION);
				pExec->BindParam(3, szChlidSession);
			}
			else
			{
				pExec->DivTable(bizMsg->m_sessionID);
				pExec->UnBindParam();
				pExec->BindParam(1, ocp_resultcode); // SM_INT_SESSION_STATUS
				if (szChlidSession[0])
				{
					pExec->BindParam(2, szChlidSession);
				}
				else
				{
					pExec->BindParam(2, bizMsg->m_sessionID);
				}
			}

			pExec->Execute();
			pExec->Connection()->Commit();
			success = true;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "update error code %d successful", ocp_resultcode);
		}
		catch (UDBException &e)
		{
			std::string sql;
			pExec->Connection()->Rollback();
			pExec->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "delete execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
			if (e.GetErrorCode() == 2002) // Commit失败
			{
				retryCount++;
				if (retryCount < 3)
				{
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
					continue;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
				}
			}
			break;
		}
	}
}


int DCBizEpt::GetUSUAmountFromExt(SCCRDataUnit& USU, const char* szUsuamount)
{
	char cnull = '\0';
	char* p = (char*)szUsuamount;
	USU.duration = atoi(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "duration[%d]", USU.duration);

	p = strchr(p, '|');
	if(p) 
	{
		p++;
	}
	else 
	{
		p = &cnull;
	}
	USU.unitTotal = atol(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitTotal);

	p = strchr(p, '|');
	if(p)
	{
		p++;
	}
	else
	{
		p = &cnull;
	}
	USU.unitInput = atol(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitInput);
 
 	p = strchr(p, '|');
	if(p)
	{
		p++;
	}
	else 
	{
		p = &cnull;
	}
	USU.unitOutput = atol(p);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "unitTotal[%ld]", USU.unitOutput);

	return 0;
}

