/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqISMP.h
*Indentifier：
*
*Description：
*		增值业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_ISMP_H__
#define __DC_REQ_ISMP_H__
#include "DCReq.h"
#include "DCBizMsgDef.h"
#include "DCOcpMsgDef.h"

class DCReqISMP : public DCReq
{
	public:

		DCReqISMP();
		virtual ~DCReqISMP();

	public:

		virtual int Work(void *data);

	protected:
		
		virtual int SwitchReqType(STBizMsg* bizMsg);
		int Check(SCCRBase* base, STBizMsg* bizMsg);
		int QueryRatable(SCCRBase* base, SCCRISMP* data,STBizMsg* bizMsg);

		int RefundFreeCCA(STBizMsg* bizMsg);
};

#endif

