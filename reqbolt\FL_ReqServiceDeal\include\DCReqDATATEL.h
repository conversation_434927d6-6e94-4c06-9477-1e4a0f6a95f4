/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCReqDATATEL.h
*Indentifier：
*
*Description：
*		数据业务处理类
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_REQ_DATA_TEL_H__
#define __DC_REQ_DATA_TEL_H__
#include "DCReqDATA.h"

class DCReqDATATEL:public DCReqDATA
{
	public:

		DCReqDATATEL();
		virtual ~DCReqDATATEL();

	private:

		virtual int SwitchReqType(STBizMsg* bizMsg);
		
		virtual int composeRER(STBizMsg* bizMsg,SUSU *MSCC,SSessionCacheData& cacheData);

		 int XdrEvent(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);
		
		int Init(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);
		
		int Update(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);

		int FirstUpdate(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);
		
		int Term(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);
		

};

#endif

