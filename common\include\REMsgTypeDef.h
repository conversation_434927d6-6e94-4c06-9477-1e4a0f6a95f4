/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       REMsgTypeDef.h
*Indentifier：
*
*Description：
*       RE消息字段命令码定义
*Version：
*       V1.0
*Author:
*		
		
*Finished：
*       
*History:
*      
********************************************/
#ifndef _RE_MSG_DEF_TYPE_H_
#define _RE_MSG_DEF_TYPE_H_


//RE消息业务类型定义
#define RE_SERVICE_TYPE_INT_PGW_REQ			    40
#define RE_SERVICE_TYPE_INT_PGW_ANS			    41

#define RE_SERVICE_TYPE_INT_5G_REQ			    50
#define RE_SERVICE_TYPE_INT_5G_ANS			    51
#define RE_SERVICE_TYPE_INT_VOICE_REQ			60
#define RE_SERVICE_TYPE_INT_VOICE_ANS			61
#define RE_SERVICE_TYPE_INT_SMS_REQ				70
#define RE_SERVICE_TYPE_INT_SMS_ANS				71
#define RE_SERVICE_TYPE_INT_DATA_REQ			80
#define RE_SERVICE_TYPE_INT_DATA_ANS			81
#define RE_SERVICE_TYPE_INT_ISMP_REQ			90
#define RE_SERVICE_TYPE_INT_ISMP_ANS			91
#define RE_SERVICE_TYPE_INT_DSL_REQ				120
#define RE_SERVICE_TYPE_INT_DSL_ANS				121
#define RE_SERVICE_TYPE_INT_BALANCE_REQ			130
#define RE_SERVICE_TYPE_INT_BALANCE_ANS			131
#define RE_SERVICE_TYPE_INT_BALANCE_REQ_ISMP	132
#define RE_SERVICE_TYPE_INT_BALANCE_ANS_ISMP	133
#define RE_SERVICE_TYPE_INT_RATA_REQ_ISMP	    134
#define RE_SERVICE_TYPE_INT_RATA_ANS_ISMP	    135


#define RE_SERVICE_TYPE_STR_PGW_REQ			    "40"
#define RE_SERVICE_TYPE_STR_PGW_ANS			    "41"
#define RE_SERVICE_TYPE_STR_5G_REQ			    "50"
#define RE_SERVICE_TYPE_STR_5G_ANS			    "51"
#define RE_SERVICE_TYPE_STR_VOICE_REQ			"60"
#define RE_SERVICE_TYPE_STR_VOICE_ANS			"61"
#define RE_SERVICE_TYPE_STR_SMS_REQ				"70"
#define RE_SERVICE_TYPE_STR_SMS_ANS				"71"
#define RE_SERVICE_TYPE_STR_DATA_REQ			"80"
#define RE_SERVICE_TYPE_STR_DATA_ANS			"81"
#define RE_SERVICE_TYPE_STR_ISMP_REQ			"90"
#define RE_SERVICE_TYPE_STR_ISMP_ANS			"91"
#define RE_SERVICE_TYPE_STR_DSL_REQ				"120"
#define RE_SERVICE_TYPE_STR_DSL_ANS				"121"
#define RE_SERVICE_TYPE_STR_BALANCE_REQ			"130"
#define RE_SERVICE_TYPE_STR_BALANCE_ANS			"131"


#endif

