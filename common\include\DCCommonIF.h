/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                  SM项目组
*All rights reserved.
*
*Filename：
*       DCCommonIF.h
*Indentifier：
*
*Description：
*
*Version：
*       V1.0
*Author:
*       ZY.F
*Finished：
*
*History:
*
********************************************/
#ifndef _DCCOMMON_IF_H_
#define _DCCOMMON_IF_H_

#include <unistd.h>
#include <stdlib.h>
#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <algorithm>
#include <string>

#include "BizDataDef.h"
#include "SMParaStruct.h"
#include "DCOcpMsgDef.h"
#include "DCRbMsgDef.h"
#include "TSMPara.h"
#include "DCAnsPara.h"
using namespace ocs;


#define NETYPE "003"
#define SPCODE "003"

#define SM_CDR_FENCE    	m_smpara->GetCommonPara()->CdrFence			//字符型，非字符串
#define SM_CDR_END_CHAR     m_smpara->GetCommonPara()->CdrEndchar		//字符型，非字符串
#define SM_SMS_SEND_NO   	m_smpara->GetCommonPara()->SmsSendNo

#define SM_SMS_SEND_NO_AOC 	m_smpara->GetCommonPara()->SmsExperienceSendNo

using namespace ocs;



class DCCommonIF
{
	public:

		DCCommonIF();
		~DCCommonIF();
		static int SetSecTimeToDate(long lnSecTime, char* pszDate);
		static long dateToSec(const char* pszDate);
		static int DayInYear(int year, int month, int day);
		static int GetDateDiff(char *szTimeA,long usutime,long &mintime,long &lasttime);
		static void ChangeMonth(char* month, int offset);

		static int ParseUseInfo(const char* pBuf, SUseInfo* pInfo, const char cdrfence);
		static int ParseTariffInfo(const char* pBuf, STariffInfo* pTariffInfo, const char cdrfence);
		static int ParseOriChargeInfo(const char* pBuf, SOriChargeInfo* pInfo, const char cdrfence);
        static int ParseAccumlatorInfo(const char* pBuf, SAccumInfo* pAccumInfo, const char cdrfence);
		static void SplitFirstFiled(char * pSour, vector<int>& vecDest, char szSeparator);
		static long time2sec(long date);
		static int timestampf();
		static long long SetEndTime( long long lnSecTime);
		static long long SetStartTime( long long lnSecTime);
		static int GetServiceFlowID(char *pSour);
		static void UnifiedCallNum(SPhone &phone, char* unified);

		static time_t GetTimeFromLongStr(const char *strtm);

		static long OCTTODEC(const char * src);
		static void DECTOHEX(long decimal, char* hexStr, int len);

		static int GetFreeUSU(rbresult* pREMsg, char * value);
		static int AccumlateBalance(rbresult* pREMsg);
		static int getChargeInfo(char* ALLUSU, char* USU, DCAnsPara* m_smpara,const int no=3);
		static int getAccumulateInfo(char* ALLUSU, char* USU,TSMPara* m_smpara, const int no=3);

		static int CheckSpecialFiled(char* pFiled);
		static int OLDCheckSpecialFiled(char* pFiled);

		static void LTrim(char *pszStr,char ch);
		static void RTrim(char *pszStr,char ch);
		static void Trim(char *pszStr,char ch);

		static int SplitCharStr(const char *pszStr, const char cSplit, map<int,string> &FieldList );

		static void SplitString(const std::string& str, char sep, std::vector<std::string>& vec);
		static void SplitSString(const std::string& str, const std::string& sep, std::vector<std::string>& vec);

		static string GetBatchNo(int nServConextId, int iUpdateTime, int iMsgType);

		static void GetHostIp(string &IP);
};

#endif

