#include "DCBizCdrNormal5G.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"
#include "DCEvtCheck.h"
#include "DCUUIDUtil.h"
using namespace ocs;

static void selftimeAdd(char* begin_time,int seconds)
{
  int year,mon,day,hh,mm,ss;
  char temp[5] = {0};
  char szOldTime[15] = {0};
  struct tm tm1;
  struct tm tm2;
  time_t ltime;

  strcpy(szOldTime,begin_time);
  strncpy(temp,begin_time,4);

  year=atol(temp);
  year=year-1900;
  memset(temp,'\0',5);
  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;
  strncpy(temp,begin_time+6,2);

  day=atol(temp);
  strncpy(temp,begin_time+8,2);

  hh=atol(temp);
  strncpy(temp,begin_time+10,2);

  mm=atol(temp);
  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  memset(&tm1,0,sizeof(tm));

  tm1.tm_sec=ss;
  tm1.tm_min=mm;
  tm1.tm_hour=hh;
  tm1.tm_mday=day;
  tm1.tm_mon=mon;
  tm1.tm_year=year;

  ltime=mktime(&tm1);
  ltime+=seconds;

  localtime_r(&ltime,&tm2);

  sprintf(begin_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2.tm_year+1900,tm2.tm_mon+1,
		tm2.tm_mday,tm2.tm_hour,tm2.tm_min,tm2.tm_sec);

}

DCBizCdrNormal5G::DCBizCdrNormal5G()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormal5G::~DCBizCdrNormal5G()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

int DCBizCdrNormal5G::Compose5G(STBizMsg* bizMsg)
{
	if (bizMsg->m_bImsFilter || bizMsg->m_iOfflineXDREptFlag || bizMsg->m_bExistRGFiltered)
	{
		ComposeAllCdrMsg(bizMsg);
		return bizMsg->m_iCdrRet;
	}

	if (bizMsg->m_bExistMSCCFiltered)
	{
		ComposeFilterCDR(bizMsg);
		return bizMsg->m_iCdrRet;
	}

	return bizMsg->m_iCdrRet;
}

void DCBizCdrNormal5G::TimestampToDate(time_t timestamp, char *pszDate)
{
	//使用线程安全函数localtime_r
	timestamp -= 2208988800L;
	struct tm tmTime ;
	localtime_r(&timestamp, &tmTime);
	sprintf(pszDate, "%04d%02d%02d%02d%02d%02d", (1900 + tmTime.tm_year), (1 + tmTime.tm_mon), tmTime.tm_mday, tmTime.tm_hour, tmTime.tm_min, tmTime.tm_sec);

	return ;
}

int DCBizCdrNormal5G::Compose5GReqCdrMsg(STBizMsg* bizMsg, int iRG, int iUseq, char *szCdrMsg)
{
	vector<SCDRField*> *pAll5GCdrField = TCDRDict::instance()->GetDATA5GField();
	vector<SCDRField*>::iterator iterField;
	char szValue[256] = {0};
	SUserInfo *userInfo	= (SUserInfo*)bizMsg->m_userinfo;
	if (!userInfo)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "find null user info");
		return -1;
	}

	SCCR5GInfo* data = (SCCR5GInfo*)bizMsg->m_extend;
	if (!data)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "null pointer, not find 5g data info");
		return -1;
	}

	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	if (!base)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_5G_TYPE, "", "null pointer, not find 5g base info");
		return -1;
	}

	SUSU *MSCC = &base->MSCC[iRG];
	
	sprintf(szValue, "%ld", bizMsg->timestampCCR);
	szValue[6] = '\0';
	strcat(szCdrMsg, szValue);
	strcat(szCdrMsg, "^");
	for (iterField = pAll5GCdrField->begin(); iterField != pAll5GCdrField->end(); iterField++)
	{
		if (0 == strcmp("OCP_STR_SESSION_ID", (*iterField)->value))
		{
			strcat(szCdrMsg, bizMsg->m_childsessionID);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_SESSION_ID[%s]", bizMsg->m_childsessionID);
			continue;
		}

		if (0 == strcmp("OCP_STR_CHARGING_ID", (*iterField)->value))
		{
			sprintf(szValue, "%d", data->chargingId);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_CHARGING_ID[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_INT_LATN_ID", (*iterField)->value))
		{
			sprintf(szValue, "%d", userInfo->ilatnid);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_INT_LATN_ID[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_LNG_ACCT_ID", (*iterField)->value))
		{
			sprintf(szValue, "%ld", userInfo->lnAcctID);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_LNG_ACCT_ID[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_PUB_LNG_SERVID", (*iterField)->value))
		{
			sprintf(szValue, "%ld", userInfo->servID);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_PUB_LNG_SERVID[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_PUB_LNG_CUSTID", (*iterField)->value))
		{
			sprintf(szValue, "%ld", userInfo->custID);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_PUB_LNG_CUSTID[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_PUB_STR_HOSTID", (*iterField)->value))
		{
			strcat(szCdrMsg, base->topology.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_PUB_STR_HOSTID[%s]", base->topology.c_str());
			continue;
		}

		if (0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iterField)->value))
		{
			if (strncmp(base->subUnified.c_str(), "86", 2) == 0)
			{
				strcpy(szValue, base->subUnified.c_str() + 2);
			}
			else
			{
				strcpy(szValue, base->subUnified.c_str());
			}
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_PUB_STR_CHARGED_PARTY[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_STR_CUT_TYPE", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->triggerInfoMation.triggerType.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_CUT_TYPE[%s]", MSCC->triggerInfoMation.triggerType.c_str());
			continue;
		}

		if (0 == strcmp("SM_INT_RESULT_CODE", (*iterField)->value))
		{
			if(bizMsg->m_bExistRGFiltered)
			{
				strcat(szCdrMsg, "010-30818");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30818]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_DUP_MSG_CDR)
			{
				strcat(szCdrMsg, "010-30813");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30813]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_UNKNOW_USER_CDR)
			{
				strcat(szCdrMsg, "010-30814");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30813]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_FILTER_STATUS_CD)
			{
				strcat(szCdrMsg, "010-30815");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30813]"); 
			
			}
			else if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_FILTER_AFTER_PAY)
			{
				strcat(szCdrMsg, "010-30813");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30813]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == SM_5G_RG_PAR_SM_FILTER_CONDITION)
			{
				strcat(szCdrMsg, "010-30818");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30818]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag != 0 && bizMsg->m_iOfflineXDREptFlag != EPT_5G_OFFLINE_XDR_FAIL_CDR)
			{
				strcat(szCdrMsg, "010-30812");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30812]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_OFFLINE_XDR_FAIL_CDR)
			{
				strcat(szCdrMsg, "010-30811");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30811]");
			}
			else if (bizMsg->m_bExistMSCCFiltered)
			{
				strcat(szCdrMsg, "010-30819");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30819]");
			}
			else if (bizMsg->m_bImsFilter)
			{
				strcat(szCdrMsg, "010-30810");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_RESULT_CODE[010-30810]");
			}

			strcat(szCdrMsg, "^");
			continue;
		}
		
		if(0 == strcmp("CDR_STR_PROCER_NAME", (*iterField)->value))
		{
			if(bizMsg->m_bExistRGFiltered)
			{
				strcat(szCdrMsg, "C");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_PROCER_NAME[C]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == SM_5G_RG_PAR_SM_FILTER_CONDITION)
			{
				strcat(szCdrMsg, "C");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_PROCER_NAME[C]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag != 0 && bizMsg->m_iOfflineXDREptFlag != EPT_5G_OFFLINE_XDR_FAIL_CDR)
			{
				strcat(szCdrMsg, "A");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_PROCER_NAME[A]");
			}
			else if (bizMsg->m_iOfflineXDREptFlag == EPT_5G_OFFLINE_XDR_FAIL_CDR)
			{
				strcat(szCdrMsg, "C");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_PROCER_NAME[C]");
			}
			else if (bizMsg->m_bExistMSCCFiltered)
			{
				strcat(szCdrMsg, "C");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_PROCER_NAME[C]");
			}
			else if (bizMsg->m_bImsFilter)
			{
				strcat(szCdrMsg, "C");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_PROCER_NAME[C]");
			}

			strcat(szCdrMsg, "^");
			continue;
		}

		if (0 == strcmp("SM_LNG_ALL_USU_TIME", (*iterField)->value))
		{
			sprintf(szValue, "%ld", MSCC->VecUSU[iUseq].duration);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_LNG_ALL_USU_TIME[%s]", szValue);
			continue;
		}

		if (0 == strcmp("SM_LNG_ALL_USU_TOTAL_OCT", (*iterField)->value))
		{
			long lnUnitTotal = MSCC->VecUSU[iUseq].unitTotal + 1023;
			lnUnitTotal = lnUnitTotal / 1024;
			sprintf(szValue, "%ld", lnUnitTotal);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_LNG_ALL_USU_TOTAL_OCT[%s] kb", szValue);
			continue;
		}

		if (0 == strcmp("SM_LNG_ALL_USU_INPUT_OCT", (*iterField)->value))
		{
			sprintf(szValue, "%ld", MSCC->VecUSU[iUseq].unitInput);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_LNG_ALL_USU_INPUT_OCT[%s] byte", szValue);
			continue;
		}

		if (0 == strcmp("SM_LNG_ALL_USU_OUTPUT_OCT", (*iterField)->value))
		{
			sprintf(szValue, "%ld", MSCC->VecUSU[iUseq].unitOutput);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_LNG_ALL_USU_OUTPUT_OCT[%s] byte", szValue);
			continue;
		}

		if (0 == strcmp("SM_STR_IMSI", (*iterField)->value))
		{
			string imsi = base->smExt.kv["IMSI"];
			if(imsi.find("imsi-") != string::npos)
			{
				imsi = imsi.substr(5);
			}
			strcat(szCdrMsg, imsi.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_STR_IMSI[%s]", imsi.c_str());
			continue;
		}

		if (0 == strcmp("CDR_CNPLMN", (*iterField)->value))
		{
			string strCnp = base->smExt.kv["servingCNPlmnId"];
			strcat(szCdrMsg, strCnp.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_CNPLMN[%s]", strCnp.c_str());
			continue;
		}

		if (0 == strcmp("RE_INT_ROAM_TYPE", (*iterField)->value))
		{
			sprintf(szValue, "%d", bizMsg->m_roamtype);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_INT_ROAM_TYPE[%s]", szValue);
			continue;
		}

		if (0 == strcmp("RE_INT_PAY_FLAG", (*iterField)->value))
		{
			int iPaylag = 0;
			if("2100" == bizMsg->m_payMentMode)
			{
				iPaylag = 1;
			}
			else
			{
				iPaylag = 0;
			}
			sprintf(szValue, "%d", iPaylag);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_INT_PAY_FLAG[%s]", szValue);
			continue;
		}

		if (0 == strcmp("RE_INT_SUB_OPERATOR", (*iterField)->value))
		{
			sprintf(szValue, "%d", base->subscription.carriers);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_INT_SUB_OPERATOR[%s]", szValue);
			continue;
		}

		if (0 == strcmp("RE_LNG_CURRENT_CCR_TIME", (*iterField)->value))
		{
			char sTimeVal[256] = {0};
			long long llTime = atoll(MSCC->VecUSU[iUseq].lastUseOfTime.c_str());
			TimestampToDate(llTime, sTimeVal);
			sprintf(szValue, "%s", sTimeVal);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_LNG_CURRENT_CCR_TIME[%s]", szValue);
			continue;
		}
		
		if (0 == strcmp("RE_STR_SUB_AREA", (*iterField)->value))
		{
			sprintf(szValue, "0%d", base->subscription.area);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_STR_SUB_AREA[%s]", szValue);
			continue;
		}

		if (0 == strcmp("RE_STR_SUB_VISIT_AREA", (*iterField)->value))
		{
			if (!bizMsg->m_visit)
			{
				strcat(szCdrMsg, "^");
				continue;
			}

			AREA_INFO *subVisit	= bizMsg->m_visit;
			memset(szValue, 0, sizeof(szValue));
			if(subVisit->area)
			{
				if(4 == bizMsg->m_roamtype || 3 == bizMsg->m_roamtype)
				{
					sprintf(szValue, "00%d", subVisit->area);
				}
				else
				{
					sprintf(szValue, "0%d", subVisit->area);
				}
			}
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_STR_SUB_VISIT_AREA[%s]", szValue);
			continue;
		}

		if (0 == strcmp("OCP_LNG_RATING_GROUP", (*iterField)->value))
		{
			sprintf(szValue, "%ld", (long)MSCC->ratingGroup);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_LNG_RATING_GROUP[%s]", szValue);
			continue;
		}

		if (0 == strcmp("OCP_INT_PDU_5QI", (*iterField)->value))
		{
			sprintf(szValue, "%d", data->qi5g);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_INT_PDU_5QI[%s]", szValue);
			continue;
		}

		if (0 == strcmp("OCP_STR_NF_ADDR", (*iterField)->value))
		{
			strcat(szCdrMsg, base->nFIPv4Address.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_NF_ADDR[%s]", base->nFIPv4Address.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_NF_ADDR_IPV6", (*iterField)->value))
		{
			strcat(szCdrMsg, base->nFIPv6Address.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_NF_ADDR_IPV6[%s]", base->nFIPv6Address.c_str());
			continue;
		}

		if (0 == strcmp("CDR_SOURCE_ID", (*iterField)->value))
		{
			strcat(szCdrMsg, base->source.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_SOURCE_ID[%s]", base->source.c_str());
			continue;
		}

		if (0 == strcmp("RE_LNG_CALL_START_TIME", (*iterField)->value))
		{
			char sTimeVal[256] = {0};
			long long llTime = atoll(MSCC->VecUSU[iUseq].firstUseOfTime.c_str());
			TimestampToDate(llTime, sTimeVal);
			long lnCallStartTime = atol(sTimeVal);
			sprintf(szValue, "%ld", lnCallStartTime);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "RE_LNG_CALL_START_TIME[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_ONLINE_MOD5G", (*iterField)->value))
		{
			if (bizMsg->m_offline == 1)
			{
				strcat(szCdrMsg, "1");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_ONLINE_MOD5G=[1]");
			}
			else
			{
				strcat(szCdrMsg, "0");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_ONLINE_MOD5G=[0]");
			}
			strcat(szCdrMsg, "^");
			continue;
		}

		if (0 == strcmp("SM_INT_REQ_TYPE", (*iterField)->value))
		{
			sprintf(szValue, "%ld", bizMsg->m_requestType);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "SM_INT_REQ_TYPE[%s]", szValue);
			continue;
		}

		if (0 == strcmp("CDR_LNG_AMBRUL", (*iterField)->value))
		{
			strcat(szCdrMsg, data->AuthorizedSessionAMBR.uplink.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_LNG_AMBRUL[%s]", data->AuthorizedSessionAMBR.uplink.c_str());
			continue;
		}

		if (0 == strcmp("CDR_LNG_AMBRDL", (*iterField)->value))
		{
			strcat(szCdrMsg, data->AuthorizedSessionAMBR.downlink.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_LNG_AMBRDL[%s]", data->AuthorizedSessionAMBR.downlink.c_str());
			continue;
		}

		if (0 == strcmp("OCP_INT_REQ_NBR", (*iterField)->value))
		{
			sprintf(szValue, "%ld", bizMsg->m_requestNumber);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_INT_REQ_NBR[%s]", szValue);
			continue;
		}

		if (0 == strcmp("OCP_STR_PDU_ADDR", (*iterField)->value))
		{
			strcat(szCdrMsg, data->ServingNFIDInfo.pduIPv4Address.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_PDU_ADDR[%s]", data->ServingNFIDInfo.pduIPv4Address.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_PDU_ADDR_IPV6", (*iterField)->value))
		{
			strcat(szCdrMsg, data->ServingNFIDInfo.pduIPv6Address.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_PDU_ADDR_IPV6[%s]", data->ServingNFIDInfo.pduIPv6Address.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_PDU_SESSION_ID", (*iterField)->value))
		{
			strcat(szCdrMsg, data->pduSessionID.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_PDU_SESSION_ID[%s]", data->pduSessionID.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_PDU_TYPE", (*iterField)->value))
		{
			strcat(szCdrMsg, data->pduType.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_PDU_TYPE[%s]", data->pduType.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_RATTYPE", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].rATType.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_RATTYPE[%s]", MSCC->VecUSU[iUseq].rATType.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_NR_CELLID", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.nrCellId.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_NR_CELLID[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.nrCellId.c_str());
			continue;
		}

		if (0 == strcmp("CDR_STR_BSID", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.nrCellId.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_BSID[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.nrCellId.c_str());
			continue;
		}

		if (0 == strcmp("CDR_STR_MSC", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.msc.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_MSC[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.msc.c_str());
			continue;
		}

		if (0 == strcmp("CDR_STR_CELLID", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.cellid.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "CDR_STR_CELLID[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.cellid.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_TAC", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.tac.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_TAC[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.tac.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_MCC", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.mcc.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_TAC[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.tac.c_str());
			continue;
		}

		if (0 == strcmp("OCP_STR_MNC", (*iterField)->value))
		{
			strcat(szCdrMsg, MSCC->VecUSU[iUseq].userLocInfoMation.mnc.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_MNC[%s]", MSCC->VecUSU[iUseq].userLocInfoMation.mnc.c_str());
			continue;
		}

		if(0 == strcmp("CDR_QUOTA_MANAGE_INDICATOR", (*iterField)->value))
		{
			if (bizMsg->m_offline == 1)
			{
				strcat(szCdrMsg, "OFFLINE_CHARGING");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "CDR_QUOTA_MANAGE_INDICATOR=[OFFLINE_CHARGING]");
			}
			else
			{
				strcat(szCdrMsg, "ONLINE_CHARGING");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "CDR_QUOTA_MANAGE_INDICATOR=[ONLINE_CHARGING]");
			}

			strcat(szCdrMsg, "^");
			continue;
		}

		if (0 == strcmp("OCP_STR_DNNID", (*iterField)->value))
		{
			strcat(szCdrMsg, data->dNNID.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "OCP_STR_DNNID[%s]", data->dNNID.c_str());
			continue;
		}

		if (0 == strcmp("TICKET_ID", (*iterField)->value))
		{
			UUIDUtil uidUtil;
			strcpy(szValue, uidUtil.CreateShortUUID().c_str());
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "add item TICKET_ID[%s]", szValue);
			continue;
		}

		if (0 == strcmp("TICKET_SEQ", (*iterField)->value))
		{
			strcat(szCdrMsg, "0");
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "add item TICKET_SEQ[0]");
			continue;
		}

		if(0 == strcmp("CDR_STR_BATCH_ID", (*iterField)->value))
		{
			strcat(szCdrMsg, bizMsg->m_sBatchId.c_str());
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "CDR_STR_BATCH_ID=[%s]", bizMsg->m_sBatchId.c_str());
			continue;
		}

		if(0 == strcmp("SOURCE_FILE_TYPE", (*iterField)->value))
		{
			sprintf(szValue, "%d", bizMsg->m_iRollFlag);
			strcat(szCdrMsg, szValue);
			strcat(szCdrMsg, "^");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "SOURCE_FILE_TYPE=[%s]", szValue);
			continue;
		}

		strcat(szCdrMsg, "^");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "[%s][]", (*iterField)->value);
	}
	
	int iLen = strlen(szCdrMsg);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_5G_TYPE, "", "cdr len[%d]", iLen);
	if (iLen > 0)
	{
		szCdrMsg[iLen - 1] = '\0';
	}
	else
	{
		szCdrMsg[0] = '\0';
	}

    return 0;
}

int DCBizCdrNormal5G::ComposeAllCdrMsg(STBizMsg* bizMsg)
{
	UCDRData scdr;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	if (!base)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "null pointer, not find base info");
		return -1;
	}

	char szInterceptCdr[SM_CDR_MSG_LEN_MAX] = {0};

	for (int iRG = 0; iRG < base->MSCC.size(); iRG++)
	{
		bool bRgFilt =false;
		if(bizMsg->m_bExistRGFiltered && bizMsg->m_RgFiltSet.size() > 0)
		{
		 	bRgFilt = bizMsg->m_RgFiltSet.count(base->MSCC[iRG].ratingGroup) > 0 ? true : false;
		}

		if(!bizMsg->m_bExistRGFiltered || (bizMsg->m_bExistRGFiltered && bRgFilt))
		{

            std::vector<SUSU >::iterator it = base->MSCC.begin();
			if (it == base->MSCC.end())
			{
			     DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "ERROR! base->MSCC is null pointer, continue");
                 continue;
			}
			if (base->MSCC[iRG].VecUSU.size() == 0) 
			{
				base->MSCC[iRG].VecUSU.push_back(base->MSCC[iRG].USU0);
				memset(szInterceptCdr, 0, sizeof(szInterceptCdr));
				Compose5GReqCdrMsg(bizMsg, iRG, 0, szInterceptCdr);
				scdr.body = szInterceptCdr;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "intercept cdr field:[%s]", szInterceptCdr);
				ProduceCdr(bizMsg, scdr, 0, 0);
				if (0 != base->MSCC[iRG].USU1.duration || 0 != base->MSCC[iRG].USU1.unitTotal)
				{
					base->MSCC[iRG].VecUSU.push_back(base->MSCC[iRG].USU1);
					memset(szInterceptCdr, 0, sizeof(szInterceptCdr));
					Compose5GReqCdrMsg(bizMsg, iRG, 1, szInterceptCdr);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "intercept cdr field:[%s]", szInterceptCdr);
					scdr.body = szInterceptCdr;
					ProduceCdr(bizMsg, scdr, 0, 0);
				}

				base->MSCC[iRG].VecUSU.clear();
			}
			else
			{
				if ( base->MSCC[iRG].VecUSU.size() > RE_5GUSU_MAXSIZE ) //循环外面判断VecUSU size值，过大则异常退出不循环
				{
				    DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "ERROR! base->MSCC[%d] VecUSU size[%d] > 100, break!", iRG, base->MSCC[iRG].VecUSU.size());
				}
				else
				{
                    for (int iUseq = 0; iUseq < base->MSCC[iRG].VecUSU.size(); iUseq++)
					{
						memset(szInterceptCdr, 0, sizeof(szInterceptCdr));
						Compose5GReqCdrMsg(bizMsg, iRG, iUseq, szInterceptCdr);
						scdr.body = szInterceptCdr;
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "intercept cdr field:[%s]", szInterceptCdr);
						ProduceCdr(bizMsg, scdr, 0, 0);
						if (iUseq >= RE_5GUSU_MAXSIZE) //循环里面也判断，防止VecUSU size值可能被改，过大则循环终止退出
						{
						    DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE,  "", "ERROR! base->MSCC[%d] VecUSU size[%d] > 100, break!", iRG, base->MSCC[iRG].VecUSU.size());
							break;
						}
					}

				}

			}
		}
	}
    
    return 0;
}

int DCBizCdrNormal5G::ComposeFilterCDR(STBizMsg* bizMsg)
{
	UCDRData scdr;
	SCCRBase* base = (SCCRBase*)bizMsg->m_base;
	if (!base)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "null pointer, not find base info");
		return -1;
	}

	char szFilterCdr[SM_CDR_MSG_LEN_MAX] = {0};
	for (int iRG = 0; iRG < base->MSCC.size(); iRG++)
	{
		if (base->MSCC[iRG].VecUSU.size() == 0)
		{
			if (!base->MSCC[iRG].USU0.filterFlag && !base->MSCC[iRG].USU1.filterFlag)
			{
				continue;
			}

			if (base->MSCC[iRG].USU0.filterFlag)
			{
				base->MSCC[iRG].VecUSU.push_back(base->MSCC[iRG].USU0);
				memset(szFilterCdr, 0, sizeof(szFilterCdr));
				Compose5GReqCdrMsg(bizMsg, iRG, 0, szFilterCdr);
				scdr.body = szFilterCdr;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "filter cdr field:[%s]", szFilterCdr);
				ProduceCdr(bizMsg, scdr, 0, 0);
				base->MSCC[iRG].VecUSU.clear();
			}

			if ((0 != base->MSCC[iRG].USU1.duration || 0 != base->MSCC[iRG].USU1.unitTotal) && base->MSCC[iRG].USU1.filterFlag)
			{
				base->MSCC[iRG].VecUSU.push_back(base->MSCC[iRG].USU1);
				memset(szFilterCdr, 0, sizeof(szFilterCdr));
				Compose5GReqCdrMsg(bizMsg, iRG, 0, szFilterCdr);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "filter cdr field:[%s]", szFilterCdr);
				scdr.body = szFilterCdr;
				ProduceCdr(bizMsg, scdr, 0, 0);
				base->MSCC[iRG].VecUSU.clear();
			}
		}
		else
		{
			for (int iUseq = 0; iUseq < base->MSCC[iRG].VecUSU.size(); iUseq++)
			{
				if (!base->MSCC[iRG].VecUSU[iUseq].filterFlag)
				{
					continue;
				}

				memset(szFilterCdr, 0, sizeof(szFilterCdr));
				Compose5GReqCdrMsg(bizMsg, iRG, iUseq, szFilterCdr);
				scdr.body = szFilterCdr;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE, "", "filter cdr field:[%s]", szFilterCdr);
				ProduceCdr(bizMsg, scdr, 0, 0);
			}
		}
	}

	return 0;
}

