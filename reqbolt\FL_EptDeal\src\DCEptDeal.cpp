#include "DCEptDeal.h"
#include "DCLogMacro.h"
#include "DCOBJSet.h"
#include "BizDataDef.h"
#include "DCEptMsgDef.h"
#include <stdio.h>
#include "DCLogMacro.h"
#include "ErrorCode.h"
#include "UHead.h"

using namespace std;
using namespace ocs;

int DCEptDeal::init()
{	
	return 0;
}

const char* DCEptDeal::desc()
{
	return "FC_EptDeal";
}

//消息头|公共消息|业务消息
int DCEptDeal::process(void* input, void* output)
{	
	DCOBJSet* pset = (DCOBJSet*)input;
	STBizMsg* bizMsg = pset->get<STBizMsg>();
	
	int ret  = 0;
	ret = composeEptMsg(bizMsg);
	output = input;
	return ret;
}


int DCEptDeal::composeEptMsg(STBizMsg* bizMsg)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","begin compose exception msg");

	char buf[1024] = {0};
	char value[256] = {0};
	int ret = 0;

	UHead uhd;
	SEPTMsg msg;
	msg.type = bizMsg->m_eptType+10*bizMsg->m_testFlag;
	uhd.uid = bizMsg->m_uid;
	uhd.checkKey = bizMsg->m_strCheckKey;
	
	// sessionID
	if((DATA==bizMsg->m_serviceContextID || PGW==bizMsg->m_serviceContextID || DATA_5G==bizMsg->m_serviceContextID) && bizMsg->m_requestType!=SM_SESSION_INITIAL_CODE)
	{
		char szChildSessionID[256]			= {0};

		if (strlen(bizMsg->m_ProductOfferId) > 0)
		{
			sprintf(szChildSessionID, "%s;%s", bizMsg->m_sessionID, bizMsg->m_ProductOfferId);
		}
		else
		{
			sprintf(szChildSessionID, "%s;%lld", bizMsg->m_sessionID, bizMsg->m_ratingGroup);
		}
	
		msg.sessionID = szChildSessionID;
	}
	else
	{
		msg.sessionID = bizMsg->m_sessionID;	
	}
	if(bizMsg->m_version == 1)
	{
		msg.sessionID.erase(0,3);
	}
	msg.szServiceContextIDStr = bizMsg->m_szServiceContextIDStr;
	// result code
	msg.result =  bizMsg->m_resultcode;

	// request type
	msg.sreq = bizMsg->m_requestType;

       msg.requestnumber = bizMsg->m_requestNumber;
	
	// service context id
	msg.servicecontextid = bizMsg->m_serviceContextID;

	msg.serial = bizMsg->m_serial;

	msg.trace = DCLOG_GETCTL(DCLOG_MASK_TRACE);

	msg.topology = bizMsg->m_topology;

	msg.version = bizMsg->m_version;

	msg.spiltflag = bizMsg->m_spiltflag;

	msg.anstopic = bizMsg->m_anstopic;

	msg.taskId = bizMsg->m_taskId;
		
	msg.EptExt.insert(pair<string,string>("sourceId", bizMsg->m_sourceId));
	msg.EptExt.insert(pair<string,string>("switchId", bizMsg->m_switchId));
	msg.EptExt.insert(pair<string,string>("operListId", bizMsg->m_operListId));
	msg.EptExt.insert(pair<string,string>("batchId",bizMsg->m_sBatchId));

	char stmp[8] = {0};
	sprintf(stmp, "%d", bizMsg->m_ilatnId);
	msg.EptExt.insert(pair<string,string>("LatnId", stmp));
	
	try
	{
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(msg);
		bizMsg->m_vectorMsg.clear();	
		bizMsg->data = (char *)m_en.data();

		m_print.clear();
		m_print.print(uhd);
		m_print.print(msg);	

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "exception msg[%s]", m_print.data());
	}
	catch(exception& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, ERR_ENCODE_CODE, "","encode failed");
		return ERR_ENCODE_CODE;
	}

	return RET_ERROR;
}

DYN_PLUGIN_CREATE(DCEptDeal, "FC_EPTDEAL", "FC_EptDeal", "1.0.0")
