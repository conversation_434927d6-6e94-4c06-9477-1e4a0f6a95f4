/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAnsDealFlow.h
*Indentifier：
*
*Description：
*		应答流程组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_ANS_DEAL_FLOW_H__
#define __DC_ANS_DEAL_FLOW_H__
#include "DCBasePlugin.h"
#include "DCMqProduceServer.h"
#include "DCOBJSet.h"
#include "DCAnsPara.h"
#include "DCAns.h"


class DCAnsDealFlow :  public DCBasePlugin
{
	public:	
		DCAnsDealFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)        :DCBasePlugin(category,func,version)
		{
		
		}
		
		virtual ~DCAnsDealFlow()
		{
		
		}
	protected:	
		virtual int init();
		virtual int process(void* input, void* output);
	private:
		DCAns *m_ans[8];
		DCAnsPara *m_anspara;
		DCOBJSetPool* m_pool;
		DCMqProduceServer* m_producer;

};

#endif

