#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCEptMsgDef.h"
#include "DCSeriaOp.h"
#include "DCPluginManer.h"

using namespace std;

DCPluginManer m_pm;
//
int Initialize()
{
	int ret = 0;
	char *buf =getenv("OCS_CONFIG");
	if(NULL==buf)
	{
	
	}

	
	// 日志初始化
	ret = DCLOGINIT("ocs","eptbolt",7,"/public/ocs_ah/log/eptlog");
	if(ret)
	{
	  
	}
	
	// 插件初始化
	ret = m_pm.init(buf, "eptbolt",DFM_USE_DBM|DFM_USE_REFRESH);
	if(ret)
	{
		return 1;
	}
	bizMsg->m_plugin = (m_flows.end()).start;

	return 0;
}

int main() 
{
	int ret = Initialize();
	if(ret)
	{
		return ret;
	}
	
	SEPTMsg msg;
	
	msg.type = 99;
	msg.sessionID = "sms.chinatelecom.com;1207666037;191744830;3681;65637";
	msg.result = 501200;
	msg.sreq = 4;
	msg.servicecontextid = 2;
	msg.serial = 2;
	
	//序列化
	DCSeriaEncoder en(ESeriaBinString);
	en.encode(msg);

	std::string recvCCRMsg = (char*)en.data();
	std::string sendmsg;
	
	DCBaseFlow* flow = m_pm.get_flow("FL_EptBaseFLOW");
	if(!flow)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find flow[FL_EptBaseFLOW]");
		return 1;
	}

    ret = flow->call(&recvCCRMsg, &sendmsg);
	
	

	return 0;
}