/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCAocBase.h
*Indentifier：
*
*Description：
*		应答流程组件
*Version：
*		V1.0
*Author:
*		
		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_AOC_BASE_H__
#define __DC_AOC_BASE_H__




#include <stdlib.h>
#include "DCBizMsgDef.h"
#include "BizDataDef.h"
#include "DCCommonIF.h"
#include "ErrorCode.h"
#include "func_sqlindex.h"
#include "numcheck.h"




class DCAocBase 
{
	public:	
		DCAocBase();
		
		~DCAocBase();

		int Work(void *data);


		int PGWAOC(STBizMsg* bizMsg,int aocType);
		int PGWAhAOC(STBizMsg* bizMsg,int aocType);
		int DATAAOC(STBizMsg* bizMsg,int aocType);
		int DataAhAOC(STBizMsg* bizMsg,int aocType);
		int BalanceAOC(STBizMsg* bizMsg, char* subStr, int balance);
		int ReSaleBalanceAOC(STBizMsg* bizMsg, char* subStr, char *mvnoid,int balance);
		int ISMPAOC(STBizMsg* bizMsgr, int aocType);
		int IsmpAhAOC(STBizMsg* bizMsg, int aocType);
		int INAOC(STBizMsg* bizMsg, int aocType);
		int VoiceAhAOC(STBizMsg* bizMsg, int aocType);
		int P2PSMSAOC(STBizMsg*bizMsg, int aocType);
		int ExperienceAOC(STBizMsg* bizMsg);
		int AOC(STBizMsg* bizMsg);

		int isRemind(STBizMsg* bizMsg);
		void ParseString(string& szSourString,vector<long>&vecDest ,const char* szSeparator);
		

};

#endif

