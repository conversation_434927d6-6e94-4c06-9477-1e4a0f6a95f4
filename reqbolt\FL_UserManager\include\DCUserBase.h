/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCUserBase.h
*Indentifier：
*
*Description：
*		用户鉴权类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/

#ifndef __DC_USERBASE_H__
#define __DC_USERBASE_H__
#include "DCUDB.h"
#include "DCOcpMsgDef.h"
#include "DCBizMsgDef.h"
#include "DCLogMacro.h"
#include "DCRbMsgDef.h"
#include "func_sqlindex.h"
#include "BizCdrDefTEL.h"
#include "TSMPara.h"
#include "DCSeriaOp.h"
#include "DCCommonIF.h"
using namespace ocs;



struct SUserInfo_u
{
	UDBSQL* query;		//input	查询SQL
	int		bizType;	//input	业务类型
	int		isActive;	//ouput	是否首次使用
	int		resultCode;	//ouput	用户状态处理结果
	int		aocType;	//ouput	AOC类型
	long	servID;		//ouput
	long	custID;		//ouput
	char	IMSI[64];	//ouput
	int		province;	//ouput
	int		area;		//ouput
	int		isRemind;	//ouput
	int     iBrandType; //品牌标识:2G或3G
	char    szBasicState[4];    //basic_state
	int     ilatnid;
	char	szBalanceAocType[16];
	char    userType[8];//用户类型
	char    mvnoID[16];//转售商ID
	long    lnAcctID;
	long    lnBillingTypeId;	// 计费用户类型: 100000001 公免
	char    LongDistance[16];	//  固网用户允许呼打的长途类型
	char    AttachServiceNbr[32]; //固网附加主号
	char    szRangeCode[8]; //手机号码归属营业区
	char    segment_id[8]; //
};

class DCUserBase
{
public:
	DCUserBase();
	DCUserBase(TSMPara *psmpara,DCDBManer* pdbm);
	~DCUserBase();

	/*******************************************************************************
	@Description: 判断用户有效期及提醒日期
	@Input:
		currentDate:当前日期
		effectDate:有效期
		remindDay:提醒时间
	@Output:无
	@Return: 0:有效期内，1:提醒期，-1:过期
	*******************************************************************************/ 
	int EffectDate(char* currentDate, char* effectDate, int remindDay);
	
	
	/*******************************************************************************
	@Description: 查询用户信息及状态
	@Input:
		cvar:命令
		phone:号码
		active:激活
		type:业务类型
		bizMsg:业务消息
	@Output:无
	@Return: =0 成功
	*******************************************************************************/ 
	int GetUserInfo(SUserInfo* userInfo, STBizMsg* bizMsg);

	/*******************************************************************************
	@Description: 根据账号查询用户信息及状态不查区号
	@Input:
		cvar:命令
		phone:号码
		active:激活
		type:业务类型
		bizMsg:业务消息
	@Output:无
	@Return: =0 成功
	*******************************************************************************/ 

	int GetUserAccountInfo(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg);

	/*******************************************************************************
	@Description: 根据用户号码查用户信息及状态不查区号
	@Input:
		cvar:命令
		phone:号码
		active:激活
		type:业务类型
		bizMsg:业务消息
	@Output:无
	@Return: =0 成功
	*******************************************************************************/ 

	int GetUserAccNbr(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg);
	
	/*******************************************************************************
	@Description: 根据用户号码以及区号查询用户信息及状态不查区号
	@Input:
		cvar:命令
		phone:号码
		active:激活
		type:业务类型
		bizMsg:业务消息
	@Output:无
	@Return: =0 成功
	*******************************************************************************/ 
	int GetUserAccArea(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg);
	

	int GetAreaCode(SPhone & phone, STBizMsg * bizMsg);
	
	int GetIMSI(SUserInfo* userInfo, STBizMsg* bizMsg);
	/*******************************************************************************
	@Description: 黑名单，白名单，免费号码处理
	@Input:
		cvar:命令
		home:号码
		nasIP:服务类型
	@Output:无
	@Return: -1 失败，1:服务在黑名单中，0:服务不在黑名单中
	*******************************************************************************/ 
	int BlackNumber(const SPhone& Phone, STBizMsg* bizMsg,	const SPhone* calling = NULL, const SPhone* called = NULL);
		

	
	void UnifiedCallNum(SPhone &phone, char* unified);
	int FreeNumber(STBizMsg* bizMsg, SCCRBase* base, const SPhone* calling = NULL, const SPhone* called = NULL);

	int DealFreeNumber(int RequestAction, STBizMsg* bizMsg);

	int DealFreeVoiceInit(SCCRBase* base, SCCRVOICE* data, STBizMsg* bizMsg);

	int DealFreeDataInit(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);

	int DealFreePGWInit(SCCRBase* base, SCCRDATA* data, STBizMsg* bizMsg);

    int DealFree5GInit(SCCRBase* base, SCCR5GInfo* data, STBizMsg* bizMsg);

	int DealFreeIsmpInit(SCCRBase* base, SCCRISMP* data,STBizMsg* bizMsg);

	int DealFreeDslInit(SCCRBase* base, SCCRDSL* data, STBizMsg* bizMsg);

	int DealFreeSmsEvent(SCCRBase* base, SCCRSMS* data, STBizMsg* bizMsg,int RequestAction);

	int DealFreeDealSession(STBizMsg* bizMsg);

	int GetAuthUser(SUserInfo* userInfo, STBizMsg* bizMsg);
	int sendCCA(STBizMsg* bizMsg);

	int IsRollUser(const SPhone &phone, SUserInfo* userInfo, STBizMsg* bizMsg);
	private:
		TSMPara *m_psmpara ;
		DCDBManer* m_dbm;
		DCSeriaEncoder m_en;		
		DCSeriaPrinter m_print;

};
#endif
