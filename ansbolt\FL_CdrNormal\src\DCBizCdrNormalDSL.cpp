#include "DCBizCdrNormalDSL.h"
#include "TCDRDict.h"
#include "DCBizMsgDef.h"
#include "TConfig.h"
#include "CDR.h"
#include "BizDataDef.h"
#include "DCOcpMsgDef.h"
#include "ErrorCode.h"
#include "DCLogMacro.h"
#include "DCSeriaOp.h"
#include "func_sqlindex.h"
#include "DCDBManer.h"
#include "DCUDB.h"
#include "BizCdrDef.h"
#include "DCCommonIF.h"


using namespace ocs;

DCBizCdrNormalDSL::DCBizCdrNormalDSL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}

DCBizCdrNormalDSL::~DCBizCdrNormalDSL()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_OTHER_TYPE,  "", "", "");
}


int DCBizCdrNormalDSL::PretreatDSL(STBizMsg* bizMsg, DataCDRInfo &stCdrInfo)
{
	int roamType = 0;
	SCCRDataUnit TUSU;
	char value[BIZ_TEMP_LEN_256] = {0};
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	UDBSQL *pQuery = dbm->GetSQL(DSL_GetSessionInfo_Cdr);
	try
	{		
		pQuery->DivTable(bizMsg->m_sessionID);
		pQuery->UnBindParam();

              if(bizMsg->m_spiltflag > 1)
              {
                    pQuery->BindParam(1, bizMsg->m_childsessionID);
              }
              else
              {
                    pQuery->BindParam(1, bizMsg->m_sessionID);
              }
              
		pQuery->Execute();
		if(pQuery->Next())
		{
			pQuery->GetValue(1, value);
			roamType = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "roamType[%s]", value);

			pQuery->GetValue(2, value);
			TUSU.duration = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "USU Time[%s]", value);
			
			pQuery->GetValue(3, value);
			TUSU.unitTotal = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitTotal[%s]", value);

			pQuery->GetValue(4, value);
			TUSU.unitInput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitInput[%s]", value);

			pQuery->GetValue(5, value);
			TUSU.unitOutput = atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "unitOutput[%s]", value);

			pQuery->GetValue(7, stCdrInfo.planInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "planInfo[%s]", stCdrInfo.planInfo);

			pQuery->GetValue(8, stCdrInfo.tarifInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "tarifInfo[%s]", stCdrInfo.tarifInfo);

			pQuery->GetValue(9, stCdrInfo.chargeInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "chargeInfo[%s]", stCdrInfo.chargeInfo);

			pQuery->GetValue(10, value);
			stCdrInfo.lCdrVersionSerial = atoll(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "OCS_SM_CDR_VERSION_SEQ_ID.nextval[%ld]", stCdrInfo.lCdrVersionSerial);


			pQuery->GetValue(11, value);
			stCdrInfo.usedoutput= atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "usedoutput[%s]", value);
			
			pQuery->GetValue(12, value);
			stCdrInfo.usedinput= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "usedinput[%s]", value);

			pQuery->GetValue(13, value);
			stCdrInfo.usedtotal= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "usedtotal[%s]", value);

			pQuery->GetValue(14, value);
			stCdrInfo.usedduration= atol(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "usedduration[%s]", value);

			if(bizMsg->m_requestType != 5)
			{
                            // 在线
				//RE_LNG_CURRENT_CCR_TIME
				pQuery->GetValue(15, value);
				strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "sessionCurrent[%s]", value);

                             stCdrInfo.Day[0] = value[6];			
			        stCdrInfo.Day[1] = value[7];			
			        stCdrInfo.Day[2] = '\0';
			        int day = atoi(stCdrInfo.Day);
			        sprintf(stCdrInfo.Day,"%d",day);

                            value[6]=0;
				stCdrInfo.Payment= atol(value);
			}
			else
			{
                            // 离线
				//RE_LNG_CURRENT_CCR_TIME
				pQuery->GetValue(15, value);
				strncpy(stCdrInfo.sessionCurrent,value,sizeof(stCdrInfo.sessionCurrent));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "sessionCurrent[%s]", value);
				//RE_LNG_SYS_CCR_TIME
				pQuery->GetValue(17, stCdrInfo.Payment);

                             stCdrInfo.Day[0] = value[6];			
			        stCdrInfo.Day[1] = value[7];			
			        stCdrInfo.Day[2] = '\0';

                             value[6] = 0;
                             if(atoi(value) < stCdrInfo.Payment)
                             {
                                    // 封账后上月话单使用本月账期DAY = 0
                                    stCdrInfo.Day[0] = '0';			
			               stCdrInfo.Day[1] = '0';		
                             }
                             int day = atoi(stCdrInfo.Day);
			        sprintf(stCdrInfo.Day,"%d",day);
				
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "Payment[%ld]", stCdrInfo.Payment);

                     DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "Day[%s]", stCdrInfo.Day);
                    
			pQuery->GetValue(16, value);			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "session_start_time[%s]", value);

			pQuery->GetValue(18, stCdrInfo.accumuInfo);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "accumuInfo[%s]", stCdrInfo.accumuInfo);

			pQuery->GetValue(19, stCdrInfo.seqNum);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "seqNum[%d]", stCdrInfo.seqNum);

			pQuery->GetValue(20, stCdrInfo.user_domain);  //OCP_STR_PRODUCT_SPECID
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "user_domain[%d]", stCdrInfo.user_domain);

			if(0 == strcmp(stCdrInfo.user_domain,"2201"))
			{
				stCdrInfo.oper_list_id = 7627;
			}
			else
			{
				pQuery->GetValue(21, value);   //CDR_PUB_INT_SERVICESCENARIOUS
				stCdrInfo.oper_list_id= atol(value);

			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "oper_list_id[%d]", stCdrInfo.oper_list_id);

			pQuery->GetValue(22, value);
			stCdrInfo.PayFlag = atoi(value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "payflag[%d]", stCdrInfo.PayFlag);

			pQuery->GetValue(23, value);
			strcpy(stCdrInfo.CallingArea,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "CallingArea[%s]", stCdrInfo.CallingArea);

			pQuery->GetValue(24, value);
			strcpy(stCdrInfo.servID,value);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "servID[%s]", stCdrInfo.servID);

            
            pQuery->GetValue(27, value);
            strcpy(stCdrInfo.orichargeInfo,value);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "orichargeInfo[%s]", value);

		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE,  bizMsg->m_sessionID, "invalid session id", "");
			return -2;	
		}
	}
	catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
		return RB_SM_UNABLE_TO_COMPLY;
	}

	stCdrInfo.duration=TUSU.duration-stCdrInfo.usedduration;
	stCdrInfo.output=TUSU.unitOutput-stCdrInfo.usedoutput;
	stCdrInfo.input=TUSU.unitInput-stCdrInfo.usedinput;
	stCdrInfo.total=TUSU.unitTotal-stCdrInfo.usedtotal;
	if(stCdrInfo.duration <= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "skip dsl cdr, duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]", stCdrInfo.duration, stCdrInfo.total,stCdrInfo.input,stCdrInfo.output);
		return -1;
	}
    
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "USU duration[%d], unitTotal[%ld], unitInput[%ld], unitOutput[%ld]", stCdrInfo.duration, stCdrInfo.total,stCdrInfo.input,stCdrInfo.output);
/*
	switch(roamType) //漫游
	{
		case 0://非漫游
			roamType = 0; 
			break;
		case 1://省内漫游
			roamType = 1;
			break;
		case 3://省际漫游来访
			roamType = 2;
			break;
		case 4://省际漫游出访
			roamType = 5;
			break;
		case 5://国际漫游来访
			roamType = 7;
			break;
		case 6://国际漫游出访
			roamType = 4;
			break;
		case 7://省内边界漫游
			roamType = 8;
			break;
		case 8://省际边界漫游
			roamType = 9;
			break;
		case 9://港澳台漫游
			roamType = 3;
			break;
		default:
			break;
	}
	*/
	if(6== roamType)
		roamType = 1;
	else 
		roamType = 0;
	stCdrInfo.nRomatype = roamType;
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "Cdr roamType[%d]", stCdrInfo.nRomatype);

	
	//info 日志
	DCDATLOG("SM00015:%d%s%s%ld%s%s%s%s%s", bizMsg->m_longCDR,stCdrInfo.sessionStart, stCdrInfo.sessionCurrent,\
							stCdrInfo.total,stCdrInfo.planInfo, stCdrInfo.tarifInfo, stCdrInfo.chargeInfo, stCdrInfo.accumuInfo, stCdrInfo.orichargeInfo);
	return 0;
}

int DCBizCdrNormalDSL::ComposeDSL(STBizMsg* bizMsg)
{
	int ret = 0;
	SCDRData cdr = {0};
	SCCRDataUnit tusu;
	vector<SCDRField*> *field;
	
	DataCDRInfo stCdrInfo;
	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;

	
/*	//模拟拨测消息不发送AOC提醒，不组装话单
	if(1 == bizMsg->m_testFlag)
	{
		return RET_SUCCESS;
	}
*/
	ret = PretreatDSL(bizMsg,stCdrInfo);

	if(ret == 0)
	{
		vector<STariffAccumCDRInfo> vecTaiAuCdr;
		vector<STariffAccumCDRInfo>::iterator TaiAuCdrIter;

		//取B07拆单分组信息，B06累积量信息,存放在vecTaiAu，拆单用
		ParaseTariffAccumCdrInfo(bizMsg,stCdrInfo,vecTaiAuCdr);
		
		long nLeftDuration = stCdrInfo.duration;
		long nDuration = stCdrInfo.duration;

		long nLeftTotal = stCdrInfo.total;
		long nTotal = stCdrInfo.total;
		

		field = TCDRDict::instance()->GetDSLField();
		
		UDBSQL *pQuery = dbm->GetSQL(COM_CDR_DSL);
		try
		{
			string  sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "SQL[%s]", sql.c_str());
			pQuery->DivTable(bizMsg->m_sessionID);
			pQuery->UnBindParam();

                     if(bizMsg->m_spiltflag > 1)
                     {
                            pQuery->BindParam(1, bizMsg->m_childsessionID);
                     }
                     else
                     {
                            pQuery->BindParam(1, bizMsg->m_sessionID);
                     }
			
			pQuery->Execute();
			int i = 1;
			if(pQuery->Next())
			{
				int cdrSeqNum=0;				

				if(stCdrInfo.seqNum > 0)
				{
					cdrSeqNum = stCdrInfo.seqNum;
				}

				if(vecTaiAuCdr.size() > 0)
				{
					long nSize = vecTaiAuCdr.size();
					long nIndex = 0;
					for(TaiAuCdrIter=vecTaiAuCdr.begin();TaiAuCdrIter!=vecTaiAuCdr.end();TaiAuCdrIter++)
					{
						nIndex ++ ;
						cdrSeqNum++;
						SCDRData cdr = {0};	
						stCdrInfo.nCutnum = nSize;

						if(nIndex < nSize)
						{
							if(TaiAuCdrIter->measure == 3)
							{
								stCdrInfo.total = TaiAuCdrIter->billingDuration; 
								nLeftTotal -= TaiAuCdrIter->billingDuration;
                                                        if(nLeftTotal<0) nLeftTotal = 0;
							}
							else if (TaiAuCdrIter->measure == 1)
							{
								stCdrInfo.duration = TaiAuCdrIter->billingDuration; 
								nLeftDuration -= TaiAuCdrIter->billingDuration;
                                                        if(nLeftDuration < 0) nLeftDuration = 0;
							}
						}
						else
						{
							//最后一个
							if(TaiAuCdrIter->measure == 3)
							{
								stCdrInfo.total = nLeftTotal;
							}
							else if (TaiAuCdrIter->measure == 1)
							{
								stCdrInfo.duration = nLeftDuration;	
							}	
							
						}

						ComposeDSLCDR(bizMsg,stCdrInfo,pQuery,*TaiAuCdrIter,field,cdrSeqNum,cdr);

						int len = strlen(cdr.m_body);
						/*
						cdr.m_body[len - 1] = '\r';
						cdr.m_body[len] = '\n';
						cdr.m_body[len + 1] = '\0';
						*/	
						cdr.m_body[len - 1] = '\0';
						DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "cdr field:\n[%s]", cdr.m_body);
						UCDRData scdr;
						scdr.body = cdr.m_body;
						ret = ProduceCdr(bizMsg, scdr,stCdrInfo.PayFlag);


					}
				}
				else
				{
					cdrSeqNum++;
					SCDRData cdr = {0};	
					STariffAccumCDRInfo TaiAuCdrInfo;
                    
                    DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
                    SCommonPara* commonPara = smpara->GetCommonPara();
                    char fence = commonPara->CdrFence;
                    SOriChargeInfo tmpOriChargeInfo[100];//扩大一点。避免因数据异常导致core
                    //增强实扣为0的acct_item_Id
                    DCCommonIF::ParseOriChargeInfo(stCdrInfo.orichargeInfo,tmpOriChargeInfo,fence);
                    TaiAuCdrInfo.acctItemId = tmpOriChargeInfo[0].acctItemId;
					TaiAuCdrInfo.basefee = tmpOriChargeInfo[0].oriAmount;

					stCdrInfo.nCutnum = 1;
					ComposeDSLCDR(bizMsg,stCdrInfo,pQuery,TaiAuCdrInfo,field,cdrSeqNum,cdr);

					int len = strlen(cdr.m_body);
					//cdr.m_body[len - 1] = '\r';
					//cdr.m_body[len] = '\n';
					//cdr.m_body[len + 1] = '\0';
					cdr.m_body[len - 1] = '\0';

					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "cdr field:\n[%s]", cdr.m_body);
					UCDRData scdr;
					scdr.body = cdr.m_body;
					ret = ProduceCdr(bizMsg, scdr,stCdrInfo.PayFlag);

				}							

				stCdrInfo.seqNum = cdrSeqNum;

				stCdrInfo.total = nTotal;
				stCdrInfo.duration = nDuration;
				
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE,  bizMsg->m_sessionID, "invalid session id", "");
				return -2;
			}

		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE,  "", "select  execption[%s]", e.ToString());
			return SM_OCP_UNABLE_TO_COMPLY;
		} 
		
			
	}


	if((bizMsg->m_longCDR == 3 || bizMsg->m_longCDR == 1) && ret != -1)
	{
		DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
		
		UDBSQL *pExec = dbm->GetSQL(DSL_CDR_UpdateUSU);
		// update会话2002错误码(Commit失败)3次重试
		int retryCount = 0;
		bool success = false;
		while (!success && retryCount < 3)
		{
			try
			{
				pExec->DivTable(bizMsg->m_sessionID);
				pExec->UnBindParam();
				pExec->BindParam(1, (long)stCdrInfo.output);
				pExec->BindParam(2, (long)stCdrInfo.input);
				pExec->BindParam(3, (long)stCdrInfo.total);
				pExec->BindParam(4, (long)stCdrInfo.duration); // CDR_LNG_ALL_USU_TIME = CDR_LNG_ALL_USU_TIME+?
				pExec->BindParam(5, atol(stCdrInfo.sessionCurrent));
				pExec->BindParam(6, stCdrInfo.seqNum);
				pExec->BindParam(7, "");
				pExec->BindParam(8, bizMsg->m_sessionID);
				pExec->Execute();
				pExec->Connection()->Commit();
				success = true;
			}
			catch (UDBException &e)
			{
				std::string sql;
				pExec->Connection()->Rollback();
				pExec->GetSqlString(sql);
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_VOICE_TYPE, "", "SQL[%s]", sql.c_str());
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "select  execption[%s], error code[%d]", e.ToString(), e.GetErrorCode());
				if (e.GetErrorCode() == 2002) // Commit失败
				{
					retryCount++;
					if (retryCount < 3)
					{
						DCBIZLOG(DCLOG_LEVEL_WARN, SM_DATA_TYPE, "", "Error code 2002 encountered, retry attempt %d of 3", retryCount);
						continue;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DATA_TYPE, "", "Max retries reached for error code 2002");
						return SM_OCP_UNABLE_TO_COMPLY;
					}
				}
				return SM_OCP_UNABLE_TO_COMPLY;
			}
		}

		return RET_SUCCESS;
	}

	if(ret != -2 && bizMsg->m_longCDR != 3)
	{
		DeleteSession(bizMsg);
	}
	return RET_SUCCESS;
}
int DCBizCdrNormalDSL::ComposeDSLCDR(STBizMsg* bizMsg,DataCDRInfo &stCdrInfo,UDBSQL *pQuery,STariffAccumCDRInfo TaiAuCdrInfo,vector<SCDRField*> *field,int cdrSeqNum,SCDRData &cdr)
{
	char value[BIZ_TEMP_LEN_256] = {0};
	int nFieldNum;
	DCAnsPara *smpara = (DCAnsPara *)bizMsg->m_anspara;
	int iCdrVersionType = smpara->GetCommonPara()->iCdrVersionType;
	long long iCdrVersionSerial = 1;
	vector<SCDRField*>::iterator iter;

	DCDBManer* dbm = (DCDBManer*)bizMsg->m_dbm;
	UDBSQL *pAccNbrQuery = dbm->GetSQL(COM_SELECT_ACC_NBR);

	int i = 1;

	//账目类型和费用字段
	vector<STFeeItem> vecFeeItem;
	ParaseFeeItem(bizMsg,stCdrInfo,vecFeeItem);

	char tmp[12] = {0};
	sprintf(tmp, "%ld", stCdrInfo.Payment);
	strcat(cdr.m_body, tmp);
	strcat(cdr.m_body, "|");
	
	for(iter=field->begin(); iter!=field->end(); iter++)
	{

		if((0 == strcmp("CDR_PUB_INT_VERSION", (*iter)->value)) && iCdrVersionType>0)
		{
			char szVersion[32] = {0};
			int iDay = timestampf();
			sprintf(szVersion, "%d%d%09lld", iDay,iCdrVersionType,stCdrInfo.lCdrVersionSerial);//组装version信息
			strcpy(value, szVersion);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		}
		if(0 == strcmp("SM_LNG_ALL_USU_TIME", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.duration);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);					  
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_PRODUCT_SPECID", (*iter)->value))
		{
			sprintf(value, "%s",stCdrInfo.user_domain);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);					  
			i++;
			continue;
		}
		if(0 == strcmp("CDR_PUB_INT_SERVICESCENARIOUS", (*iter)->value))
		{
			sprintf(value, "%d",stCdrInfo.oper_list_id);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);					  
			i++;
			continue;
		}
		if(0 == strcmp("OCP_STR_SESSION_ID", (*iter)->value))
	 	{
	 		pQuery->GetValue(i, value);
			if(bizMsg->m_spiltflag > 1 )
			{
				 char* p = strrchr(value, ';');
   				 if(p) *p = 0x0;
			}
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			i++;
	 		continue;
	 	}
		if((0 == strcmp("SOURCEFILE", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%s",bizMsg->m_xdrsource);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
	 		continue;
	 	}	
		if(0 == strcmp("SM_LNG_ALL_USU_TOTAL_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",(stCdrInfo.total+1023)/1024);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);					  
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_INPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.input);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);					  
			i++;
			continue;
		}

		if(0 == strcmp("SM_LNG_ALL_USU_OUTPUT_OCT", (*iter)->value))
		{
			sprintf(value, "%lld",stCdrInfo.output);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);					  
			i++;
			continue;
		}
		 if((0 == strcmp("CDR_BILL_CYCLE", (*iter)->value)))
		 {
		 	memset(value,0x00,sizeof(value));
		 	sprintf(value,"%ld", stCdrInfo.Payment);
		 	DealwithField(value);
		 	strcat(cdr.m_body, value);
		 	strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
		 	continue;
		 }
		
		if((0 == strcmp("RE_INT_ROAM_TYPE", (*iter)->value)))
		{
			memset(value,0x00,sizeof(value));
			sprintf(value,"%d", stCdrInfo.nRomatype);
			DealwithField(value);
			strcat(cdr.m_body, value);
			strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;
		} 
		if((0 == strcmp("DAY", (*iter)->value)))
	 	{
	 		memset(value,0x00,sizeof(value));
	 		sprintf(value,"%s",stCdrInfo.Day);
	 		DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
	 		continue;
	 	}	
		if(0 == strcmp("CDR_PUB_INT_SEQ", (*iter)->value))
		{
			sprintf(value,"%d",cdrSeqNum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "add item CDR_PUB_INT_SEQ[%s]", value);
			//i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_ACCUMLATORINFO", (*iter)->value))
		{
			//sprintf(value,"%s",TaiAuCdrInfo.sz_accumInfo);
			sprintf(value, "%s", stCdrInfo.accumuInfo);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "add item CDR_PUB_STR_ACCUMLATORINFO[%s]", value);
			//i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",bizMsg->m_longCDR);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "add item CDR_PUB_INT_FLAG[%s]", value);			
			continue;						
		}
		if(0 == strcmp("CDR_CUT_INT_FLAG", (*iter)->value))
		{
			sprintf(value,"%d",stCdrInfo.nCutnum);
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE,  "", "add item CDR_CUT_INT_FLAG[%s]", value);			
			continue;						
		}
		if(0 == strcmp("RE_STR_SUB_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CHARGED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLING_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("OCP_STR_ORIGIN_CALLED_NBR", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CALLING_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		if(0 == strcmp("CDR_PUB_STR_CALLED_PARTY", (*iter)->value))
		{
			char temp[128]={0};
			pQuery->GetValue(i, temp);
			if(strncmp(temp,"86",2))
			{
				strcpy(value, temp);
			}
			else
			{
				strcpy(value, temp+2);
			}	
			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			i++;
			continue;						
		}
		
		if(0 == strcmp("CDR_STR_ACC_NBR", (*iter)->value))
		{
			try
			{				
				pAccNbrQuery->UnBindParam();
				long  nProdInstId = atol(stCdrInfo.servID);
				int   nLanId = atoi(stCdrInfo.CallingArea);
				pAccNbrQuery->BindParam(1, nProdInstId);
				pAccNbrQuery->BindParam(2, nLanId);

				pAccNbrQuery->Execute();
				if(pAccNbrQuery->Next())
				{					
					pAccNbrQuery->GetValue(1, value);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "ACC_NBR[%s]", value);
					
				}
				else
				{
					value[0] = '\0';
					DCBIZLOG(DCLOG_LEVEL_WARN, SM_DSL_TYPE,  bizMsg->m_sessionID, "invalid CallingArea[%s],servID[%s]", stCdrInfo.CallingArea,stCdrInfo.servID);						
				}
			}
			catch(UDBException& e)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, SM_DSL_TYPE, "", "select execption[%s]", e.ToString());
				return RB_SM_UNABLE_TO_COMPLY;
			}			

			DealwithField(value);
	 		strcat(cdr.m_body, value);
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],set", (*iter)->value,value,i);
			
			continue;						
		}

		if(0 == strcmp("CDR_SOURCE_ID", (*iter)->value))
		{
	 		strcat(cdr.m_body, bizMsg->m_sourceId.c_str());
	 		strcat(cdr.m_body, "|");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DATA_TYPE,  "", "CDR_SOURCE_ID=[%s]", bizMsg->m_sourceId.c_str());			
			continue;			
		}

		if((*iter)->flag)
		{
			//i++;
			nFieldNum = DCCommonIF::CheckSpecialFiled((*iter)->value);
			if(nFieldNum)////话单特殊字段处理
			{
				if(nFieldNum < 9)
				//清单中加入Fee字段或者ACCT_Item_Type_ID字段
					AddFeeItem(vecFeeItem,&cdr,nFieldNum);
				else
					AddExtCdrItem(TaiAuCdrInfo,&cdr,nFieldNum);
				//i--;
				continue;
			}
			else
			{
				pQuery->GetValue(i, value);
				DealwithField(value);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],get", (*iter)->value,value,i);
				i++;
				if(0 == strcmp("CDR_INT_LATN_ID", (*iter)->value))
				{
					bizMsg->m_userinfo->ilatnid = atoi(value);
				}
			}
		}
			else
			{
				strcpy(value, (*iter)->value);
				DealwithField(value);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, SM_DSL_TYPE, "", "[%s]=[%s],pos[%d],default", (*iter)->value,value,i);
				strcat(cdr.m_body, value);
				strcat(cdr.m_body, "|");
			}
		}
}


